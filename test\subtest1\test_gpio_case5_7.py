# -*- coding: utf-8 -*-
"""
GPIO Case-5: External Signal from Q68 to Multiple S68 GPIOs

This test verifies that an external signal input to a Q68 GPIO pin can be
simultaneously transmitted to multiple specified GPIO pins on all connected
S68 devices.
"""
import logging
import time
import pytest

# 测试配置 - 可以手动修改
TEST_CONFIG = {
    'active_links': [0],           # 默认使用Link0
    'q68_source_gpio': 0,          # Q68源GPIO
    's68_target_gpios': [0, 1, 2, 3, 4, 5, 6 , 7 ,8],  # S68目标GPIO列表
    'signal_id': 11,               # GPIO信号ID
    'observation_time': 30          # 观察时间(秒)
}

@pytest.mark.fast
def test_gpio_q68_external_to_s68(devices):
    """
    Configures a signal path from an external source via Q68 to multiple S68 GPIOs.

    Test Steps:
    1.  Define the source Q68 GPIO, the target S68 GPIOs, and the signal ID.
    2.  Configure the Q68 GPIO as a remote transmitter.
    3.  Iterate through all connected S68 links.
    4.  For each S68, iterate through the list of target GPIOs and configure each
        as a remote receiver.
    5.  Wait for 3 seconds for observation.
    6.  Check and assert that all communication links remain stable.
    """
    print(f"\n[CASE-5] 开始执行: 外部信号 Q68 -> S68 (可配置多个S68 GPIO)")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")

    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']

    # ---------------------------------------------------------------------
    # 0. Configure specific Links for this test
    # ---------------------------------------------------------------------
    print(f"\n步骤0: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")

    # 检查GPIO15/16特殊配置
    q68_source_gpio = TEST_CONFIG['q68_source_gpio']

    # 根据GPIO类型确定i2c_bus配置
    if q68_source_gpio in [15, 16]:
        print(f"\n⚠️  检测到Q68 GPIO{q68_source_gpio} (I2C1引脚)")
        print("=" * 60)
        print("🔌 硬件连接提示:")
        print("   请确保已连接 Q68 GPIO 11/12 (SDA1/SCL1)")
        print("   - GPIO 11: SDA1 (I2C1数据线)")
        print("   - GPIO 12: SCL1 (I2C1时钟线)")
        print("=" * 60)

        # 自动设置i2c_bus为1
        i2c_bus_config = 1
        print(f"✅ 自动设置 i2c_bus = 1 (用于GPIO{q68_source_gpio})")
    else:
        # 其他GPIO使用默认配置
        i2c_bus_config = 0
        print(f"\n使用标准GPIO{q68_source_gpio}配置 (i2c_bus = 0)")

    # 设置地址转换 (关键步骤！)
    print(f"\n步骤0.5: 设置激活Links的地址转换...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            # 设置地址转换：转译地址 -> 实际设备地址
            q68_remote.S68_AddrTrans(
                link=link,
                q68_iic_addr=0x73,                    # Q68地址
                s68_iic_addr=0x40,                    # S68实际地址
                s68_retrans_addr=s68_res_dev[link],   # S68转译地址 (0x20, 0x21, 0x22, 0x23)
                sensor_addr=0x24,                     # sensor地址 (如果需要)
                sensor_retrans_addr=0x24 + link,      # sensor转译地址
                i2c_bus=i2c_bus_config                # 根据GPIO类型动态设置
            )
            print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40 (i2c_bus={i2c_bus_config})")
    print("  - 地址转换设置完成")

    # ---------------------------------------------------------------------
    # 1. Q68 Side Configuration (Transmitter)
    # ---------------------------------------------------------------------
    print(f"\n步骤1: 配置 Q68 GPIO-{q68_source_gpio} 为发送端 (Signal ID: {TEST_CONFIG['signal_id']})...")
    print(f"    - I2C总线配置: {i2c_bus_config}")

    q68.MFNSet(gpio=q68_source_gpio, mfn=0)

    # 使用第一个激活的Link作为发送Link
    primary_link = TEST_CONFIG['active_links'][0]
    q68.GPIORemoteTx(gpio=q68_source_gpio, tx_id=TEST_CONFIG['signal_id'],
                     link_id=primary_link, dly_comp_en=0)
    print(f"    - Q68 GPIO{q68_source_gpio} 配置完成，使用Link{primary_link}")

    # ---------------------------------------------------------------------
    # 2. S68 Side Configuration (Receivers)
    # ---------------------------------------------------------------------
    print(f"\n步骤2: 配置激活Links上的S68 GPIOs {TEST_CONFIG['s68_target_gpios']} 为接收端...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            target_addr = s68_res_dev[link]
            print(f"    - 配置 S68 Link-{link} (地址: 0x{target_addr:02X})...")

            # 设置设备地址
            q68_remote.dongle.devAddr = target_addr
            print(f"      * 设备地址已设置为: 0x{target_addr:02X}")

            # 等待一小段时间确保地址设置生效
            time.sleep(0.1)

            for s68_pin in TEST_CONFIG['s68_target_gpios']:
                print(f"      * 配置GPIO{s68_pin}...")

                # 设置MFN为0 (GPIO功能)
                try:
                    q68_remote.M2CMFNSet(gpio=s68_pin, mfn=0)
                    print(f"        - GPIO{s68_pin} MFN设置为0 (GPIO功能)")

                    # 验证MFN设置
                    # 注意：这里可能需要根据实际API调整读取方法
                    # current_mfn = q68_remote.M2CMFNGet(gpio=s68_pin)  # 如果有这个方法
                    # print(f"        - GPIO{s68_pin} 当前MFN: {current_mfn}")

                except Exception as e:
                    print(f"        - 错误: GPIO{s68_pin} MFN设置失败: {e}")

                # 设置远程接收
                try:
                    q68_remote.M2CGPIORemoteRx(gpio=s68_pin, rx_id=TEST_CONFIG['signal_id'])
                    print(f"        - GPIO{s68_pin} 远程接收设置完成 (信号ID: {TEST_CONFIG['signal_id']})")
                except Exception as e:
                    print(f"        - 错误: GPIO{s68_pin} 远程接收设置失败: {e}")

        else:
            print(f"    - 警告: Link{link} 超出s68_res_dev范围，跳过")
    print("    - 激活Links上的S68 GPIO 配置完成。")

    # ---------------------------------------------------------------------
    # 3. Observation and Verification
    # ---------------------------------------------------------------------
    print(f"\n步骤3: 等待{TEST_CONFIG['observation_time']}秒以便观察信号...")
    time.sleep(TEST_CONFIG['observation_time'])

    # 只检查激活Links的状态
    print(f"\n步骤4: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    active_statuses = {}
    for link_id in TEST_CONFIG['active_links']:
        if 0 <= link_id <= 3:
            status = link_status_funcs[link_id]()
            active_statuses[f'link{link_id}'] = status
            print(f"    - Link{link_id} 状态: {status}")

    # 验证激活Links的状态
    failed_links = [link for link, status in active_statuses.items() if status != 5]
    assert len(failed_links) == 0, \
        f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"

    print(f"\n==> [CASE-5] 测试通过: Q68 GPIO{TEST_CONFIG['q68_source_gpio']} -> S68 GPIOs{TEST_CONFIG['s68_target_gpios']} 信号路径已配置到Links {TEST_CONFIG['active_links']}，链路状态正常。")


@pytest.mark.parametrize("link_combination", [
    [0],           # 只测试Link0
    # [1],           # 只测试Link1 
    # [2],           # 只测试Link2
    # [3],           # 只测试Link3
    # [0, 1],        # 测试Link0和Link1
    # [0, 1,2,3],     # 测试Link1, 2, 3
])
def test_gpio_case5_parametrized(devices, link_combination):
    """参数化测试 - 可以测试不同的Link组合"""
    # 临时修改配置
    original_links = TEST_CONFIG['active_links']
    TEST_CONFIG['active_links'] = link_combination

    try:
        test_gpio_q68_external_to_s68(devices)
        print(f"✅ Link组合 {link_combination} 测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['active_links'] = original_links


if __name__ == "__main__":
    """
    使用说明:

    1. 测试默认配置 (Link1):
       pytest test_gpio_case5_7.py::test_gpio_q68_external_to_s68 -v -s

    2. 参数化测试特定Link组合:
       pytest test_gpio_case5_7.py::test_gpio_case5_parametrized[link_combination1] -v -s

    3. 测试所有Link组合:
       pytest test_gpio_case5_7.py::test_gpio_case5_parametrized -v -s

    4. 手动修改TEST_CONFIG中的active_links来测试特定组合
    """
    print("GPIO Case-5 灵活Link测试")
    print("请使用pytest运行测试")