#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MIPI接收模块面试问答 - 记忆辅助脚本
功能：解析markdown文件，提供交互式问答练习
"""

import re
import random
import os
import sys
from typing import List, Tuple

class MIPIQuiz:
    def __init__(self, markdown_file: str):
        self.markdown_file = markdown_file
        self.questions = []
        self.current_index = 0
        self.correct_count = 0
        self.total_answered = 0
        self.difficult_questions = []
        
    def parse_markdown(self):
        """解析markdown文件，提取问答对"""
        try:
            with open(self.markdown_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except FileNotFoundError:
            print(f"错误：找不到文件 {self.markdown_file}")
            return False
            
        # 使用正则表达式匹配 Q: 和 A: 模式
        pattern = r'\*\*Q:\s*(.*?)\*\*\s*\n\s*A:\s*(.*?)(?=\n\*\*Q:|\n##|\Z)'
        matches = re.findall(pattern, content, re.DOTALL)
        
        for i, (question, answer) in enumerate(matches):
            # 清理问题和答案中的多余空白
            question = question.strip()
            answer = answer.strip()
            
            # 获取章节信息
            chapter = self.get_chapter(content, question)
            
            self.questions.append({
                'id': i + 1,
                'chapter': chapter,
                'question': question,
                'answer': answer,
                'difficulty': 0  # 用户标记的难度
            })
            
        print(f"成功解析 {len(self.questions)} 个问答对")
        return True
        
    def get_chapter(self, content: str, question: str) -> str:
        """获取问题所属的章节"""
        lines = content.split('\n')
        chapter = "未知章节"
        
        for line in lines:
            if line.startswith('##'):
                chapter = line.replace('##', '').strip()
            elif question in line:
                break
                
        return chapter
        
    def display_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🎯 MIPI接收模块面试问答 - 记忆助手")
        print("="*60)
        print("1. 顺序练习")
        print("2. 随机练习") 
        print("3. 章节选择")
        print("4. 复习难题")
        print("5. 统计信息")
        print("6. 退出")
        print("="*60)
        
    def sequential_practice(self):
        """顺序练习模式"""
        print(f"\n📚 开始顺序练习 (共{len(self.questions)}题)")
        self.practice_questions(self.questions)
        
    def random_practice(self):
        """随机练习模式"""
        shuffled = self.questions.copy()
        random.shuffle(shuffled)
        print(f"\n🎲 开始随机练习 (共{len(shuffled)}题)")
        self.practice_questions(shuffled)
        
    def chapter_practice(self):
        """章节选择练习"""
        chapters = {}
        for q in self.questions:
            chapter = q['chapter']
            if chapter not in chapters:
                chapters[chapter] = []
            chapters[chapter].append(q)
            
        print("\n📖 选择章节：")
        chapter_list = list(chapters.keys())
        for i, chapter in enumerate(chapter_list, 1):
            print(f"{i}. {chapter} ({len(chapters[chapter])}题)")
            
        try:
            choice = int(input("\n请选择章节编号: ")) - 1
            if 0 <= choice < len(chapter_list):
                selected_chapter = chapter_list[choice]
                questions = chapters[selected_chapter]
                print(f"\n📚 开始练习: {selected_chapter} (共{len(questions)}题)")
                self.practice_questions(questions)
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
            
    def practice_questions(self, questions: List[dict]):
        """练习问题"""
        for i, q in enumerate(questions, 1):
            print(f"\n{'='*60}")
            print(f"📝 第 {i}/{len(questions)} 题 | 章节: {q['chapter']}")
            print(f"{'='*60}")
            print(f"❓ {q['question']}")
            print("\n" + "-"*40)
            
            while True:
                user_input = input("按 [Enter] 显示答案, [s] 跳过, [d] 标记难题, [q] 退出: ").strip().lower()
                
                if user_input == '':
                    # 显示答案
                    print(f"\n✅ 答案:")
                    print(f"{q['answer']}")
                    print("-"*40)
                    
                    # 询问掌握程度
                    while True:
                        mastery = input("掌握程度 [1-容易/2-一般/3-困难]: ").strip()
                        if mastery in ['1', '2', '3']:
                            q['difficulty'] = int(mastery)
                            if mastery == '3' and q not in self.difficult_questions:
                                self.difficult_questions.append(q)
                            self.total_answered += 1
                            if mastery in ['1', '2']:
                                self.correct_count += 1
                            break
                        else:
                            print("请输入 1、2 或 3")
                    break
                    
                elif user_input == 's':
                    print("⏭️  跳过此题")
                    break
                    
                elif user_input == 'd':
                    if q not in self.difficult_questions:
                        self.difficult_questions.append(q)
                        print("🔖 已标记为难题")
                    else:
                        print("🔖 此题已在难题列表中")
                        
                elif user_input == 'q':
                    print("👋 退出练习")
                    return
                    
                else:
                    print("❌ 无效输入，请重新选择")
                    
    def review_difficult(self):
        """复习难题"""
        if not self.difficult_questions:
            print("\n🎉 太棒了！目前没有难题需要复习")
            return
            
        print(f"\n🔥 开始复习难题 (共{len(self.difficult_questions)}题)")
        self.practice_questions(self.difficult_questions)
        
    def show_statistics(self):
        """显示统计信息"""
        print(f"\n📊 学习统计")
        print("="*40)
        print(f"总题数: {len(self.questions)}")
        print(f"已练习: {self.total_answered}")
        print(f"掌握良好: {self.correct_count}")
        print(f"标记难题: {len(self.difficult_questions)}")
        
        if self.total_answered > 0:
            accuracy = (self.correct_count / self.total_answered) * 100
            print(f"掌握率: {accuracy:.1f}%")
            
        print("="*40)
        
    def run(self):
        """运行主程序"""
        print("🚀 正在加载问答数据...")
        if not self.parse_markdown():
            return
            
        while True:
            self.display_menu()
            try:
                choice = input("\n请选择功能 (1-6): ").strip()
                
                if choice == '1':
                    self.sequential_practice()
                elif choice == '2':
                    self.random_practice()
                elif choice == '3':
                    self.chapter_practice()
                elif choice == '4':
                    self.review_difficult()
                elif choice == '5':
                    self.show_statistics()
                elif choice == '6':
                    print("👋 再见！祝面试顺利！")
                    break
                else:
                    print("❌ 请输入 1-6 之间的数字")
                    
            except KeyboardInterrupt:
                print("\n\n👋 程序已退出")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    # 默认使用当前目录下的markdown文件
    default_file = "MIPI接收模块面试问答.md"
    
    if len(sys.argv) > 1:
        markdown_file = sys.argv[1]
    else:
        markdown_file = default_file
        
    if not os.path.exists(markdown_file):
        print(f"❌ 找不到文件: {markdown_file}")
        print(f"请确保文件存在，或使用: python {sys.argv[0]} <markdown文件路径>")
        return
        
    quiz = MIPIQuiz(markdown_file)
    quiz.run()

if __name__ == "__main__":
    main()
