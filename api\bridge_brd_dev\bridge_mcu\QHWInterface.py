import csv
from USBInterface import USBImpl

class QHWinterface:
    def __init__(self):
        self.isInit:bool  = False
        self.save_data:bool  = False
        self.device_index:int  = 0
        self.data_file = "./save_data.csv"

    def __del__(self):
        self.uninitHWDevice()

    # 退出模块时，释放模块接口资源
    def uninitHWDevice(self):
        print("QRegisterAccess on_exit")
        if self.isInit == True:
            print("QRegisterAccess close serial")
            USBImpl.usbdev_close()
            self.isInit = False
    
    # 使用模块前，初始化模块接口
    def initHWDevice(self, device_index=0, save_data=False):
        self.save_data = save_data
        self.device_index = device_index
    
        try:
            devlist = USBImpl.usbdev_scan()
            print("探测到 USBCOMM 设备列表:")
            for i in range(0, len(devlist)):
                print("    ", devlist[i])

            # 使用第一个设备
            USBImpl.usbdev_open(devlist[self.device_index].name)
            ret,sndata = USBImpl.usbdev_get_sn()
            print("return=", ret, " get serial number result: ", ''.join(format(byte, '02x') for byte in sndata))
        except Exception as e:
            print("QHWinterface", e)
            # 捕获所有其他类型的异常
            self.isInit = False
        else:
            # 如果没有发生异常，则执行该代码块
            self.isInit = True
        finally:
            print("USBImpl initiation end: isInit", self.isInit)
    
        # 如果使能了保存数据的功能，程序运行时，应处理文件的创建
        if save_data:
            with open(self.data_file, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                name = ['DeviceAddr','Reg_Addr', 'R_or_W', 'Write_or_Read_Value']
                csv_writer.writerow(name)
    
    
    def setGpio(self, index, value):
        v = 0 if value == 0 else 1
        USBImpl.usbdev_set_gpio(index, v)
    
    
    def getGpio(self, index):
        value = 0
        if self.isInit:
            value = USBImpl.usbdev_get_gpio(index)
        else:
            print("USBImpl Not Open")
    
        return value
    
    
    def readI2CR16V8(self, bus, devAddr, regAddr):
        mode = 1
        regValue = 0
        if self.isInit:
            # mode:0:r8v8 1:r16v8
            ret, regValue = USBImpl.usbdev_i2c_get(mode, bus, devAddr, regAddr)
            if ret != 0:
                print("I2C Get Error:", " mode=", mode, " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr)
        else:
            print("USBImpl Not Open")
    
        if self.save_data:
            with open(self.data_file, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(devAddr), hex(regAddr), 'R', hex(regValue)]
                csv_writer.writerow(row) 
        return regValue
    
    
    def writeI2CR16V8(self, bus, devAddr, regAddr, regValue):
        mode = 1
        if self.isInit:
            # mode:0:r8v8 1:r16v8
            ret = USBImpl.usbdev_i2c_set(mode, bus, devAddr, regAddr, regValue)
            if ret != 0:
                print("I2C Get Error:", " mode=", mode, " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr)
        else:
            print("USBImpl Not Open")
    
        if self.save_data:
            with open(self.data_file, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(devAddr), hex(regAddr), 'W', hex(regValue)]
                csv_writer.writerow(row) 
        return True
    
    
    def readI2CR8V8(self, bus, devAddr, regAddr):
        mode = 0
        regValue = 0
        if self.isInit:
            # mode:0:r8v8 1:r16v8
            ret, regValue = USBImpl.usbdev_i2c_get(mode, bus, devAddr, regAddr)
            if ret != 0:
                print("I2C Get Error:", " mode=", mode, " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr)
        else:
            print("USBImpl Not Open")
    
        if self.save_data:
            with open(self.data_file, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(devAddr), hex(regAddr), 'R', hex(regValue)]
                csv_writer.writerow(row) 
        return regValue
    
    
    def writeI2CR8V8(self, bus, devAddr, regAddr, regValue):
        mode = 0
        if self.isInit:
            # mode:0:r8v8 1:r16v8
            ret = USBImpl.usbdev_i2c_set(mode, bus, devAddr, regAddr, regValue)
            if ret != 0:
                print("I2C Get Error:", " mode=", mode, " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr)
        else:
            print("USBImpl Not Open")
    
        if self.save_data:
            with open(self.data_file, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(devAddr), hex(regAddr), 'W', hex(regValue)]
                csv_writer.writerow(row) 
        return True

