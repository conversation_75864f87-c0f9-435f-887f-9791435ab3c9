#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from stm32 import USBDataDeal
from stm32 import CmdFuncation

class USBImpl:
    def __init__(self):
        self.usbDataDeal = USBDataDeal.USBDataDeal()
        self.cmdFuncation = CmdFuncation.CmdFuncation()
        
        self.cmdFuncation.setCMDFuncation(self.usbDataDeal)
        self.usbDataDeal.setUSBDataDeal(self.cmdFuncation)
        pass

    def __del__(self):
        pass
    
    # 获取连接的设备列表
    @staticmethod
    def usbdev_scan():
        return USBDataDeal.USBDataDeal.ser_ee_dev_scan()
    
    
    # 开启指定的连接设备
    def usbdev_open(self, port_name):
        self.usbDataDeal.serial_open(port_name)
    
    
    # 关闭指令的连接设备
    def usbdev_close(self):
        self.usbDataDeal.serial_close()
    
    
    # 显示设备信息
    def usbdev_showinfo(self):
        self.usbDataDeal.serial_showinfo()
    
    # ID:0x0000: 获取设备SN号
    def usbdev_get_sn(self):
        return self.cmdFuncation.send_cmd_get_sn()
    
    # ID:0x0001: 设置指定输出引脚的值
    def usbdev_set_gpio(self, pin_num: int, pin_val: int):
        return self.cmdFuncation.send_cmd_set_gpio(pin_num, pin_val)
    
    
    # ID:0x0002: 获取指定输入引脚的值
    def usbdev_get_gpio(self, pin_num: int):
        return self.cmdFuncation.send_cmd_get_gpio(pin_num)
    
    
    # ID:0x0003:设置I2C寄存器的值
    # mode:0:r8v8 1:r8v16
    def usbdev_i2c_set(self, mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):
        return self.cmdFuncation.send_cmd_i2c_set(mode, bus, dev_addr, reg_addr, reg_val)
    
    
    # ID:0x0004:获取I2C寄存器的值
    # mode 0:r8v8 1:r16v8
    # return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
    def usbdev_i2c_get(self, mode: int, bus: int, dev_addr: int, reg_addr: int):
        return self.cmdFuncation.send_cmd_i2c_get(mode, bus, dev_addr, reg_addr)
    
    # ID:0x0005:设置数据的模式
    # mode 0:真实数据(默认) 1:虚拟数据
    def usbdev_set_mode(self, mode:int):
        return self.cmdFuncation.send_cmd_set_mode(mode)
    
    # ID:0x0006:设置I2C速率
    # bus:需要设置的总线
    # speed：需要设置的频率(单位Kbps)
    def usbdev_i2c_setbusspeed(self, bus: int, speed: int):
        return self.cmdFuncation.send_cmd_i2c_setbusspeed(bus, speed)
    
    # ID:0x0007: SPI Master 收发数据
    def usbdev_spi_master_tr(self, index: int, tx_buffer: bytearray, len: int):
        return self.cmdFuncation.send_cmd_spi_master_tr(index, tx_buffer, len)
    
    # ID:0x0008: SPI Slave 发数据
    # return: >0 是发送成功的数量, <=0:是错误编号
    def usbdev_spi_slave_t(self, index: int, tx_buffer: bytearray, len: int):
        return self.cmdFuncation.send_cmd_spi_slave_t(index, tx_buffer, len)
    
    # ID:0x0009: SPI Slave 收数据
    # return: >0 是接收成功的数量, <=0:是错误编号
    def usbdev_spi_slave_r(self, index: int, len: int):
        return self.cmdFuncation.send_cmd_spi_slave_r(index, len)
    
    
    # ID:0x000A: 让MCU打印内部SPI信息到MCU串口上
    def usbdev_spi_showinfo(self):
        return self.cmdFuncation.send_cmd_spi_showinfo()
    
    # ID:0x000B: 设置对应编号的GPIO的输入输出方向
    # pin[0~23]
    # mode[0:input 1:output_pp 2:output_od 3:ad_pp 4:ad_od 5:TI_RISING 6:IT_FALLING 7:IT_RISING_FALLING]
    # pull[0:nopull 1:pullup 2:pulldown]
    # 支持中断的GPIO:GPIO0、4、11、13、14、15、16、17、18、19、20、21、22、23
    def usbdev_init_gpio(self, pin_num:int = 0, pin_mode:int = 0, pin_pull:int = 0):
        return self.cmdFuncation.send_cmd_init_gpio(pin_num, pin_mode, pin_pull)
    
    # ID:0x000C: 设置SPI Master的主频率
    # cpol:    0:Low 1:Hight
    # clph:    0:1Edga 1:2Edga
    # nss:     0:soft 1:hw input 2:hw output
    # rate:    kbps
    def usbdev_spi_master_setmode(self, index:int = 0, cpol: int = 0, clph: int = 0, nss: int = 1, rate:int = 32000):
        return self.cmdFuncation.send_cmd_spi_master_setmode(index, cpol, clph, nss, rate)
    
    # ID:0x000D: 获取指定通道的ADC的值
    # 0~3300 代表 0~3.3V
    def usbdev_adc_getvol(self, index:int = 0):
        return self.cmdFuncation.send_cmd_adc_getvol(index)
    
    # ID:0x000E: 设置指定通道DAC的电压
    # 0~3300 代表 0~3.3V
    def usbdev_dac_setvol(self, index:int = 0, vol:int = 0):
        return self.cmdFuncation.send_cmd_dac_setvol(index, vol)
    
    
    # ID:0x000F: 发送数据帧到CAN
    # tx_len: can帧数据不能大于8
    def usbdev_can_put_oneframe(self, id: int, tx_buffer: bytearray, tx_len: int):
        return self.cmdFuncation.send_cmd_can_put_oneframe(id, tx_buffer, tx_len)
    
    # ID:0x0010: 从CAN接收缓冲区获取帧
    # return: ret, id, len, buff
    def usbdev_can_get_oneframe(self):
        return self.cmdFuncation.send_cmd_can_get_oneframe()
    
    # ID:0x0011: 让MCU打印内部CAN信息到MCU串口上
    def usbdev_can_showinfo(self):
        return self.cmdFuncation.send_cmd_can_showinfo()
    
    # ID:0x0012: 设置SPI Master的主频率
    # cpol:    0:Low 1:Hight
    # clph:    0:1Edga 1:2Edga
    # nss:     0:soft 1:hw input 2:hw output
    # # devtype: 0:stream 1:reg_dev_r8v8 2:reg_dev_r16v8 3:reg_dev_r8v16  4:reg_dev_r16v16
    def usbdev_spi_slave_setmode(self, index:int = 0, cpol: int = 0, clph: int = 0, nss: int = 1, devtype:int = 1):
        return self.cmdFuncation.send_cmd_spi_slave_setmode(index, cpol, clph, nss, devtype)
    
    # ID:0x0013: reset mcu
    def usbdev_mcu_reset(self):
        return self.cmdFuncation.send_cmd_mcu_reset()
    
    # ID:0x0014: 让MCU打印内部I2C信息到MCU串口上
    def usbdev_i2c_showinfo(self):
        return self.cmdFuncation.send_cmd_i2c_showinfo()
    
    # ID:0x0015: 获取MCU版本号
    def usbdev_get_version(self):
        return self.cmdFuncation.send_cmd_get_version()
    
    # ID:0x0016: 显示SD卡信息到串口
    def usbdev_sdcard_showinfo(self):
        return self.cmdFuncation.send_cmd_sdcard_showinfo()
    
    # ID:0x0017: 清除gpio对应的中断次数
    def usbdev_clear_gpio_ticount(self, index:int):
        return self.cmdFuncation.send_cmd_clear_gpio_ticount(index)
    
    # ID:0x0018: 获取gpio对应的中断次数
    def usbdev_get_gpio_ticount(self, index:int):
        return self.cmdFuncation.send_cmd_get_gpio_ticount(index)
    
    # ID:0x0019:批量设置I2C寄存器的值
    # mode:0:r8v8 1:r8v16
    def usbdev_i2c_muilt_set(self, mode: int, bus: int, dev_addr: int, reg_addr: int, reg_vals: list):
        return self.cmdFuncation.send_cmd_i2c_muilt_set(mode, bus, dev_addr, reg_addr, reg_vals, len(reg_vals))

    # ID:0x001A:批量获取I2C寄存器的值
    # mode 0:r8v8 1:r16v8
    # return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
    def usbdev_i2c_muilt_get(self, mode: int, bus: int, dev_addr: int, reg_addr: int, count: int):
        return self.cmdFuncation.send_cmd_i2c_muilt_get(mode, bus, dev_addr, reg_addr, count)

    # ID:0x001B:Uart接口交互，传入需要发送的数据，将返回的数据接收回来
    # index：那一路Uart tx_buffer，需要发送的数据 
    # return: ret,rx_buffer,  第一个值是ret值，第二个值为获取的rx_buffer
    def usbdev_uart_transfer(self, index: int, tx_buffer: list, rx_len:int=2, rx_timeout:int=0x10):
        return self.cmdFuncation.send_cmd_uart_transfer(index, tx_buffer, len(tx_buffer), rx_len, rx_timeout)

    # ID:0x001C: 设置debug串口状态，是否开启mcu串口debug， 使用哪个串口
    # debug_en:是否使能调试信息， uart_port:调试的串口选择
    def usbdev_set_debug_status(self, debug_en: int, uart_port: int, debug_level:int):
        return self.cmdFuncation.send_cmd_set_debug_status(debug_en, uart_port, debug_level)

    # ID:0x001D: 设置串口信息
    # default:115200, stop:1 parity:None hw:None
    # rate:(baudrate) stop(STOPBITS 0.5 1.0 1.5 2.0)  parity(0:None 1:EVEN 2:ODD) hwflow(0:None 1:RTS 2:CTS 3:RTS_CTS)
    def usbdev_set_uart_mode(self, index:int, rate:int, stop:float, parity:int, hwflow:int):
        return self.cmdFuncation.send_cmd_set_uart_mode(index, rate, stop, parity, hwflow)

    # ID:0x001E: SPI Master 发CMD
    # 
    # cmd : 要发送的指令
    # addr: 发送到的目的地址
    # mode: 模式,详细位定义如下:
    #   mode[1:0]: 指令模式; 00,无指令;  01,单线传输指令; 10,双线传输指令; 11,四线传输指令.
    #   mode[3:2]: 地址模式; 00,无地址;  01,单线传输地址; 10,双线传输地址; 11,四线传输地址.
    #   mode[5:4]: 地址长度; 00,8位地址; 01,16位地址;     10,24位地址;  11,32位地址.
    #   mode[7:6]: 数据模式; 00,无数据;  01,单线传输数据; 10,双线传输数据; 11,四线传输数据.
    # dmcycle: 空指令周期数
    def usbdev_qspi_master_cmd(self, index: int, cmd: int, addr:int, mode:int, dmcycle:int):
        return self.cmdFuncation.send_cmd_qspi_master_cmd(index, cmd, addr, mode, dmcycle)
    
    # ID:0x001F: SPI Master 发数据
    def usbdev_qspi_master_t(self, index: int, tx_buffer: bytearray, len: int):
        return self.cmdFuncation.send_cmd_qspi_master_t(index, tx_buffer, len)
    
    # ID:0x0020: SPI Master 收数据
    def usbdev_qspi_master_r(self, index: int, len: int):
        return self.cmdFuncation.send_cmd_qspi_master_r(index, len)

    # ID:0x0021: SPI Master 收数据
    def usbdev_qspi_master_set_mode(self, qspi_blk_mode, ClockPrescaler, FifoThreshold, SampleShifting, FlashSize, ChipSelectHighTime, ClockMode):
        return self.cmdFuncation.send_cmd_qspi_master_set_mode(qspi_blk_mode, ClockPrescaler, FifoThreshold, SampleShifting, FlashSize, ChipSelectHighTime, ClockMode)


    # ID:0x0022: SPI Master Set Test Times
    def usbdev_qspi_master_set_test_times(self, times:int, hdlow:int, read_before:int=1000, read_after:int=1000, read_funmode:int=0):
        return self.cmdFuncation.send_cmd_qspi_master_set_test_times(times, hdlow, read_before, read_after, read_funmode)

    # ID:0x0023: SPI Master Get Test Status
    def usbdev_qspi_master_get_test_status(self):
        return self.cmdFuncation.send_cmd_qspi_master_get_test_status()

    # ID:0x0024: SPI Master Get Test Buffer
    def usbdev_qspi_master_get_test_buffer(self,pos_start, length):
        return self.cmdFuncation.send_cmd_qspi_master_get_test_buffer(pos_start, length)

    # ID:0x0025: 
    def usbdev_i2c_set_readstop_flag(self, stop_flag:int = 0):
        return self.cmdFuncation.send_cmd_i2c_set_readstop_flag(stop_flag)

