# 1.先上电
# 2.com错误拔插试试

import sys
import csv
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'api', 'm65q68_a0'))  # Path to Common
sys.path.append(os.path.join(project_root, 'api', 'm66s68_a0'))  # Path to Common_var
sys.path.append(os.path.join(project_root, 'api', 'instr'))      # Path to instr_drv

from Common.M65Q68_Common_Fuction_A0 import *
from Common_var.M66S68_Common_Fuction_A0 import *
 
from instr_drv.IT6322B import *
from instr_drv.Keithley2230_py3 import *

# # 导入测试用例
# from subtest5.test_case1 import test_5a
# 不再直接导入subtest_gpio，避免循环导入问题
# 测试用例将在运行时动态导入

instr_sel = True  # False: instr not selected
if instr_sel:
    from instr_drv.Keithley2230_py3 import *
    from instr_drv.IT6322B import *
    # from instr_drv.oven_haituo import *

power_camera = IT6322B(usb_addr="USB0::0x2EC7::0x6300::800068020757210071::INSTR")
power_q68 = Keithley2230(usb_addr="USB0::0x05E6::0x2230::9211112::INSTR")

# 全局变量定义
q68 = None
q68_remote = None
s68_res_dev = None
q68_i2c2 = None
q68_I2C2remote = None
s680 = None

VOLTAGE      =   [1.2,1.8,1.8]

KLINK        =   [0,]              # 表示接几个c3的lane link0~3

RATE         =   [2,2,2,2]              # link rate,  initial(default:cfg1), 1-3G, 2-6G; link0, link1, link2, link3
RATE_final   =   [2,2,2,2]              # link rate,  finally set(change when Linked)
BCRATE       =   [0,0,0,0]              # q68 -> S68 data rate: 0:150M, 1:187.5M, 2/3:200M  
DTbypass     =   [0,0,0,0]              # 多data type时必须改为1; link0, link1, link2, link3
vcid         =   [0,1,2,3]              # set vcid of each link(active when Camera's vcid=0)

pcs_set      =   [0,0,0,0]              # 0:8b/10b (0,0,0,0),  1: 66/64d [(1,1,1,1)
fec_bypass   =   [0,0,0,0]              # 1: fec bypass

# GPIO测试相关配置
q68_iic_addr = 0x73                        # Q68地址, 0x31#0x73
s68_iic_dev = [0x40, 0x40, 0x40, 0x40]     # s68地址，需要根据实际s68地址进行改动
# s68_res_sensor_dev = [0x24, 0x25, 0x26, 0x27]  # sensor转译地址可自行定义，link0,link1,link2,link3



# # 初始化S68设备对象
# s680 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link0
# s681 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link1
# s682 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link2
# s683 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link3

# # 摄像头配置
# link_camera = {0:'Huayang_031', 1:'Huayang_031', 2:'Huayang_031', 3:'Huayang_031'}  #需要根据link上接的camera进行修改
# sensor_dev = {'NIO_031':0, 'PHYbrd_NIO_031':0, 'xiaopeng_031_RAW12': 0x1A, 'NIO_1Mega':0,
#               'NIO_2Mega':0x36, 'NIO_8Mega':16, 'x8B':0x36, 'SG2_IMX390C':0, 
#               'Huayang_031':0, 's68_VG':0, 'q68_VG':0}  # sensor iic address
# s68_sensor_dev = [sensor_dev[link_camera[0]], sensor_dev[link_camera[1]], 
#                  sensor_dev[link_camera[2]], sensor_dev[link_camera[3]]]
# s68_res_dev_i2c2
# # MIPI和聚合模式配置
# aggregation_mode = 'RoundRobin_2csi'
# csi_mode = 'dphy_2csi_1c4d'
# MIPI_timing_dterm = [0, 0, 0, 0]            # S68 MIPI timing: d-term-en
# MIPI_timing_hssettle = [0, 0, 0, 0]         # S68 MIPI timing: hs-settle

def AddrTrans_new(s68_retrans_des, s68_iic_dev, i2cid, link):
    q68.dongle.devAddr = 0x73

    # 配置 I2C0 控制器的 Link0 通道控制寄存器。
    q68.c2m.wr_i2c0_ctrl6(i=0, value=3)
    q68.c2m.wr_i2c0_ctrl6(i=1, value=0)
    q68.c2m.wr_i2c0_ctrl6(i=2, value=0)
    q68.c2m.wr_i2c0_ctrl6(i=3, value=0)

    # if link==0:
    #     q68_remote.IICRemoteConfig(devAddr=q68_iic_addr, link0=3, link1=0, link2=0, link3=0)
    #     q68_remote.dongle.devAddr = s68_iic_dev
    # elif link==1:
    #     q68_remote.IICRemoteConfig(devAddr=q68_iic_addr, link0=0, link1=3, link2=0, link3=0)
    #     q68_remote.dongle.devAddr = s68_iic_dev
    # elif link==2:
    #     q68_remote.IICRemoteConfig(devAddr=q68_iic_addr, link0=0, link1=0, link2=3, link3=0)
    #     q68_remote.dongle.devAddr = s68_iic_dev    
    # elif link==3:
    #     q68_remote.IICRemoteConfig(devAddr=q68_iic_addr, link0=0, link1=0, link2=0, link3=3)
    #     q68_remote.dongle.devAddr = s68_iic_dev 
    # else:
    #     raise('link num is over range')

    q68_remote.dongle.devAddr = 0x40

    
    # 不需要配置其他iic mfn 不需要接上拉
    # logic probe不能插引脚上面，否则iic通信失败
    # q68_remote.M2CMFNSet(gpio=7, mfn=6)
    # q68_remote.M2CMFNSet(gpio=8, mfn=6)

    # q68_remote.M2CMFNSet(gpio=0, mfn=3)
    # q68_remote.M2CMFNSet(gpio=4, mfn=5)
    # s680 and s680 camera0 dev trans



    if i2cid==0:
        q68_remote.m2c.wr_i2c0_dev_addr_tran0_fields(src = s68_retrans_des)
        q68_remote.m2c.wr_i2c0_dev_addr_tran1_fields(des = s68_iic_dev) 
        q68_remote.m2c.wr_i2c0_ctrl6_fields(dev_addr_tran_en = 1)               
    elif i2cid==1:
        q68_remote.m2c.wr_i2c1_dev_addr_tran0_fields(src = s68_retrans_des)
        q68_remote.m2c.wr_i2c1_dev_addr_tran1_fields(des = s68_iic_dev) 
        q68_remote.m2c.wr_i2c1_ctrl6_fields(dev_addr_tran_en = 1) 
    elif i2cid==2:
        q68_remote.m2c.wr_i2c2_dev_addr_tran0_fields(src = s68_retrans_des)
        q68_remote.m2c.wr_i2c2_dev_addr_tran1_fields(des = s68_iic_dev) 
        q68_remote.m2c.wr_i2c2_ctrl6_fields(dev_addr_tran_en = 1) 

    time.sleep(0.2)  
    if link==0:
        q68.c2m.wr_i2c0_ctrl6(i=0, value=3)
        q68.c2m.wr_i2c1_ctrl6(i=0, value=3)
        q68.c2m.wr_i2c2_ctrl6(i=0, value=3)
    elif link==1:
        q68.c2m.wr_i2c0_ctrl6(i=1, value=3)
        q68.c2m.wr_i2c1_ctrl6(i=1, value=3)
        q68.c2m.wr_i2c2_ctrl6(i=1, value=3)
    elif link==2:
        q68.c2m.wr_i2c0_ctrl6(i=2, value=3)
        q68.c2m.wr_i2c1_ctrl6(i=2, value=3)
        q68.c2m.wr_i2c2_ctrl6(i=2, value=3)
    elif link==3:
        q68.c2m.wr_i2c0_ctrl6(i=3, value=3) 
        q68.c2m.wr_i2c1_ctrl6(i=3, value=3)
        q68.c2m.wr_i2c2_ctrl6(i=3, value=3)
    else:
        raise('link num is over range') 
    time.sleep(1)          #需要等待100ms，

#=========================================================================
#如果选了dongle_id，则必须配置optype为manual
def init_global_objects():
    global q68, q68_remote, s68_res_dev ,q68_i2c2 ,q68_I2C2remote,s680
    q68 = M65Q68_A0(dongle_id=b'\x00Q\x00$41Q\x197997',dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual',)  # G9PH DS board, bus_chan=1 for I2C1 (SDA1, SCL1)
    q68_remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00Q\x00$41Q\x197997',dongle='stm32', id=0, bus='i2c', optype='manual')
    q68_i2c2 = M65Q68_A0(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual')  # G9PH DS board, bus_chan=1 for I2C1 (SDA1, SCL1)
    q68_I2C2remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', optype='manual')
    # s680 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link0
    # , bus_chan=1
    s68_res_dev = [0x20, 0x21, 0x22, 0x23]  # s68转译地址

def power_on():
    """电源控制函数"""
    power_q68.TurnOutputsOff()
    power_camera.TurnOutputsOff()
    
    time.sleep(2)
    
    power_q68.SetCurrents(i1=1.2, i2=0.1, i3=0.2)
    power_q68.SetVoltages(VOLTAGE[0], VOLTAGE[1], VOLTAGE[2])
    power_q68.TurnOutputsOn()
    power_camera.TurnOutputsOn()
    
    time.sleep(23)
    
    # 检查link状态
    link0_status = q68.c2m.rd_test_fsm_status1_link0()                        
    link1_status = q68.c2m.rd_test_fsm_status1_link1()                        
    link2_status = q68.c2m.rd_test_fsm_status2_link2()                        
    link3_status = q68.c2m.rd_test_fsm_status2_link3()                        
    print('Link Status:', link0_status, link1_status, link2_status, link3_status)

def setup_q68_s68_communication():
    """设置Q68和S68之间的通信"""
    print("\n设置Q68和S68通信...")
    
    # 设置Q68链路速率
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])
    q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=BCRATE[0], bc_rate1=BCRATE[1], bc_rate2=BCRATE[2], bc_rate3=BCRATE[3])
    
    # 设置链路参数和编码方式
    q68.Q68_C3_6G_Init(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])
    
    for link in KLINK:
        q68.FECcoding(link=link, pcs_set=pcs_set[link], fec_bypass=fec_bypass[link])
    
    # 设置地址转换
    # for link in KLINK:
        # q68_remote.S68_AddrTrans(
        #     link=link, 
        #     q68_iic_addr=q68_iic_addr,
        #     s68_iic_addr=s68_iic_dev[link], 
        #     s68_retrans_addr=s68_res_dev[link], 
        #     # sensor_addr=s68_sensor_dev[link], 
        #     # sensor_retrans_addr=s68_res_sensor_dev[link],
        #     i2c_bus=0
        # )
        #==========================================================

        # AddrTrans_new(s68_retrans_des=s68_res_dev[link], s68_iic_dev=s68_iic_dev[link], i2cid=0, link=link)
        # AddrTrans_new(s68_retrans_des=s68_res_dev[link], s68_iic_dev=s68_iic_dev[link], i2cid=1, link=link)
        # AddrTrans_new(s68_retrans_des=s68_res_dev[link], s68_iic_dev=s68_iic_dev[link], i2cid=2, link=link)

    
    # 设置S68编码方式
    for link in KLINK:
        q68_remote.S68_FECCoding(
            s68_res_dev=s68_res_dev[link], 
            pcs_set=pcs_set[link],
            fec_bypass=fec_bypass[link], 
            RATE_final=RATE_final[link]
        )
    
    # 设置最终链路速率
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])
    q68.Q68_C3_6G_Init(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])
    
    # 检查链路状态
    link0_status = q68.c2m.rd_test_fsm_status1_link0()
    link1_status = q68.c2m.rd_test_fsm_status1_link1()
    link2_status = q68.c2m.rd_test_fsm_status2_link2()
    link3_status = q68.c2m.rd_test_fsm_status2_link3()
    
    print('链路状态:', link0_status, link1_status, link2_status, link3_status)
    print("Q68和S68通信设置完成\n")

def run_gpio_test_cases():
    """运行GPIO测试用例"""
    print("\n开始执行GPIO测试用例")
    
    # 动态导入测试用例
    # 运行时导入，避免循环导入问题
    # from subtest1.conftest import gpio_test_config, gpio_test_cases, should_run_test
    
    # 动态导入测试用例
    # from subtest1.test_gpio_case1 import test_gpio_frame_sync_q68_to_s68
    # from subtest1.test_gpio_case2 import test_gpio_signal_s68_to_q68
    # from subtest1.test_gpio_case3 import test_gpio_multi_frame_sync
    # from subtest1.test_gpio_case4 import test_gpio_duty_cycle
    # from subtest1.test_gpio_case5 import test_gpio_q68_external_to_s68
    
    # # 显示当前测试配置
    # if gpio_test_config == 0:
    #     print("当前配置: 测试所有用例")
    # else:
    #     print(f"当前配置: 仅测试用例 {gpio_test_config}: {gpio_test_cases[gpio_test_config]}")
    
    # 运行测试用例
    # if should_run_test(1):
        # test_gpio_frame_sync_q68_to_s68(q68, q68_remote, s68_res_dev)
        # 配置S68 GPIO接收帧同步信号
    for link in [0, 1, 2, 3]:
        s68_iic_dev = s68_res_dev[link]
        q68_remote.dongle.devAddr = s68_iic_dev
    
        s68_iic_dev = s68_res_dev[3]
        q68_remote.M2CMFNSet(gpio=3, mfn=0)  #  
        q68_remote.M2CGPIORemoteRx(gpio=3, rx_id=11)  # 使能帧同步输出到相机
        
        s68_iic_dev = s68_res_dev[0]   
        q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=11)  # 使能帧同步输出到相机
        
        s68_iic_dev = s68_res_dev[1]
        q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=11)  # 使能帧同步输出到相机
        
        s68_iic_dev = s68_res_dev[2]
        q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=11)  # 使能帧同步输出到相机
        
        # 配置Q68帧同步输出
        q68.FrameSyncOutConifg(
            i=0, 
            per_div=0x0B, 
            duty_cycle=4, 
            period=17361, 
            fs_tx_id=11, 
            auto_fs=1, 
            outen=1
        )  # 配置并使能帧同步，30 Hz
        
        # 检查链路状态
        link0_status = q68.c2m.rd_test_fsm_status1_link0()
        link1_status = q68.c2m.rd_test_fsm_status1_link1()
        link2_status = q68.c2m.rd_test_fsm_status2_link2()
        link3_status = q68.c2m.rd_test_fsm_status2_link3()
        
        print(f'链路状态: {link0_status}, {link1_status}, {link2_status}, {link3_status}')
        print("测试用例1完成")
    
    # if should_run_test(2):
    #     test_gpio_signal_s68_to_q68(q68, q68_remote, s68_res_dev)
    
    # if should_run_test(3):
    #     test_gpio_multi_frame_sync(q68, q68_remote, s68_res_dev)
    
    # if should_run_test(4):
    #     test_gpio_duty_cycle(q68, q68_remote, s68_res_dev)
    
    # if should_run_test(5):
    #     test_gpio_q68_external_to_s68(q68, q68_remote, s68_res_dev)
    
    print("\nGPIO测试用例执行完成")

def TopCase():
    """主测试函数"""
    print("\n开始执行TopCase...")

    init_global_objects()
    
    # 打开电源
    power_on()
    
    # 设置Q68和S68通信
    setup_q68_s68_communication()
    
    # 执行GPIO测试用例
    run_gpio_test_cases()
    
    print("TopCase执行完成")

if __name__ == "__main__":
    # init_global_objects()
    # try:
    #     print("注意事项1、 M65Q68上电时序：1、PWDNB=0；2、Q68乱序上电；3、pwdnb=1；另外，步骤1和2，2和3之间等待时间＞0.2s")
    #     print("注意事项2、 S68R5 MIPI ECC问题: 需要临时手动设置dco_code=36, 已在booten函数中，同时请设置d-term/hs_settle为0")
    #     print("\n开始执行GPIO测试...\n")
        
    TopCase()
    
    print("\nGPIO测试执行完成！")
    # except Exception as e:
    #     print(f"发生错误: {e}")
    #     import traceback
    #     traceback.print_exc()
    # finally:
    #     # 确保关闭所有设备
    #     try:
            # # 关闭q68设备
            # if 'q68' in globals():
            #     q68.dongle.close()
            #     print("已关闭Q68设备")
            
            # # 关闭q68_remote设备
            # if 'q68_remote' in globals():
            #     q68_remote.dongle.close()
            #     print("已关闭Q68_remote设备")
                
            # 关闭电源
            # if 'power_q68' in globals():
            #     power_q68.TurnOutputsOff()
            #     print("已关闭Q68电源")
                
            # if 'power_camera' in globals():
            #     power_camera.TurnOutputsOff()
            #     print("已关闭Camera电源")
        #     print("done")
        # except Exception as close_error:
        #     print(f"关闭设备时发生错误: {close_error}")