#File:SiglentSDS5Xserial.py
#programming guide:https://siglentna.com/wp-content/uploads/dlm_uploads/2017/10/ProgrammingGuide_forSDS-1-1.pdf
#IVdriver: https://www.siglent.com/download/driver/?ProId=4
#Description: The functions is remote control SIGLENT SDS5000 serial digital scope
#Author:xzx
#Copyright:meritech
#Version:0.0
#Date:2024.06.06
#Change: 

import pyvisa as visa
import collections
eyedic = collections.OrderedDict()
import time

class SiglentSDS5034X():
    def __init__(self,porttype='usb',usb_addr="USB0::0xF4EC::0x1008::SDS5XB0Q6R0190::INSTR"):   #USB0::0xF4EC::0x1008::SDS5XB0Q6R0190::INSTR
        
        '''
        description: this function to initi instr
        author:xzx
        input:
            select communicated type,including USB/GPIB/LAN
                usb: "USB0::0xF4EC::0x1008::SDS5XB0D7R1178::INSTR",query it by scope or NIMAX
                LAN: TBD 
        output:
            1. initi scope instr
            2. return scope type by qurey command
        '''
        
        if porttype=='gpib':
            pass
            #self.instr = visa.instrument("GPIB1::%d::INSTR" % int(address))
        if porttype=='lan':
            pass
            #self.instr = visa.instrument("TCPIP0::***********::inst0::INSTR")
        elif porttype == 'usb':
            
            myMgr = visa.ResourceManager()    #qury .dll file
            print(".dll file includes ",myMgr)    
            instr_scope =  myMgr.list_resources()   #query the all instr closed to PC
            print("scope instr type: ",instr_scope)    
            self.instr = myMgr.open_resource(usb_addr,timeout=300000,write_termination = "\n",read_termination = "\n")  

        device_str = self.instr.query("*IDN?")    #his command requry scope instrument
        print ("Queried the scpe: ",device_str)
    
    def Set_Default(self):
        '''
        description: this function cfg default mode.
        input:
            NA
        output:
            reset measure status
            
        '''
        self.instr.write("*RST")
    
    def Set_AutoScale(self):
        '''
        description: The AUTO_SETUP command attempts to
            identify the waveform type and automatically
            adjusts controls to produce a usable display of
            the input signal.
        input:
            NA
        output:
            reset measure status
            
        '''
        self.instr.write(":ASET")
          
    def Clear_Display(self):
        '''
        description: this command clears the display and resets all associated measurements
        input:
            NA
        output:
            reset measure status
        
        '''
        status_stop =self.instr.write(":STOP")
        if status_stop == None:
            time.sleep(0.1)
        else:
            status_clr=self.instr.write(":CDISPlay")
            if status_clr == None:
                time.sleep(0.1)
            else:
                self.instr.write(":RUN")
                time.sleep(10)
     
    def Set_Couplemode(self,channel=1,mode="DC_50ohm"):
        
        '''
        description: The COUPLING command selects the coupling mode of the specified input channel.
        input:
            <channel> : = {C1, C2, C3, C4}
            <coupling> : = {A1M, A50, D1M, D50, GND}
        output:
            NA
        
        '''
        CHANNEL_DICT    =   {1:"C1",2:"C2",3:"C3",4:"C4"}
        MODE_DICT   ={"AC_1Mohm":"A1M","AC_50ohm":"A50","DC_1Mohm":"D1M","DC_50ohm":"D50","short_to_GND":"GND"}
        self.instr.write(":{}:CPL {}".format(CHANNEL_DICT[channel],MODE_DICT[mode]))
        mode_status =  self.instr.query( ":{}:CouPLing?".format(CHANNEL_DICT[channel]))
        return mode_status
    #
    # def Set_TrigMode(self,trig_mode='AUTO'):
    #     '''
    #     description: The TRIG_MODE command specifies the trigger mode.
    #     input:
    #         <mode>: = {AUTO, NORM, SINGLE,STOP}
    #     output:
    #         start measure or it
    #
    #     '''
    #     self.instr.write(":TRig_MoDe {}".format(trig_mode))
    #     return self.instr.query(":TRig_MoDe?")
    # def Set_TrigMode(self, trig_mode='AUTO', timebase_scale=None):
    #
    #
    #
    #     self.instr.write(":TRig_MoDe {}".format(trig_mode))
    #     current_trig_mode = self.instr.query(":TRig_MoDe?").strip()
    #
    #
    #     current_timebase = None
    #     if timebase_scale is not None:
    #         self.instr.write(":TIMebase:SCALe {}".format(timebase_scale))
    #         current_timebase = float(self.instr.query(":TIMebase:SCALe?"))
    #
    #     return current_trig_mode, current_timebase        
    def Set_TrigMode(self, trig_mode='AUTO', timebase_scale=None):
        '''
        Set the trigger mode and time base, and return the current settings.
        
        Args:
            trig_mode (str): Trigger mode. Options are {AUTO, NORM, SINGLE, STOP}.
            timebase_scale (str): Time base value (seconds per division) in a human-readable format, e.g., '1ms', '100us', '200ns'.
        
        Returns:
            tuple: (Current trigger mode, Current time base setting in seconds per division)
        '''
        # Set the trigger mode
        self.instr.write(":TRig_MoDe {}".format(trig_mode))
        current_trig_mode = self.instr.query(":TRig_MoDe?").strip()
        
        # Convert timebase_scale from human-readable format to seconds per division
        current_timebase = None
        if timebase_scale is not None:
            # Parse the timebase_scale string
            value = float(timebase_scale[:-2])  # Extract the numerical part
            unit = timebase_scale[-2:]  # Extract the unit part
            
            # Convert to seconds
            if unit == 'ms':
                timebase_scale_sec = value * 1e-3
            elif unit == 'us':
                timebase_scale_sec = value * 1e-6
            elif unit == 'ns':
                timebase_scale_sec = value * 1e-9
            else:
                raise ValueError("Unsupported time unit. Use 'ms', 'us', or 'ns'.")
            
            # Set the time base
            self.instr.write(":TIMebase:SCALe {}".format(timebase_scale_sec))
            current_timebase = float(self.instr.query(":TIMebase:SCALe?"))
        
        return current_trig_mode, current_timebase     
        

          
    def RUN(self,trig_mode='AUTO'):
        '''
        description: this command starts the oscilloscope running
        
        output:
            NA
        
        '''
        self.instr.write(":TRig_MoDe {}".format(trig_mode))
        return self.instr.query(":TRig_MoDe?")
        
    def STOP(self):
        '''
        description: this command stop the oscilloscope running
        input:
            NA
        output:
            stop the run state of scope
        
        '''
        self.instr.write(":TRig_MoDe NORM")
        
        return self.instr.query("SAST?")
    
    def MeasureMode(self,channel=1,):
        
        '''
        description: The PARAMETER_CUSTOM command controls the parameters that have customizable qualifiers
        input:
            <channel> : = {C1, C2, C3, C4}
            <parameter> : 
                {PKPK, MAX, MIN, AMPL, TOP,
                BASE, CMEAN, MEAN, RMS, CRMS, OVSN,
                FPRE, OVSP, RPRE, PER, FREQ, PWID, NWID,
                RISE,FALL,WID,DUTY,NDUTY, ALL}
        output:
            NA
        
        '''
        CHANNEL_DICT    =   {1:"C1",2:"C2",3:"C3",4:"C4"}
        for item in ("PKPK", "MAX", "MIN", "AMPL", "TOP","BASE", "CMEAN", "MEAN", "RMS", "CRMS", "OVSN","FPRE", "OVSP", "RPRE", "PER", "FREQ", "PWID", "NWID","RISE","FALL","WID","DUTY","NDUTY", "ALL"):
            self.instr.write(":PACU {}, {}".format(item,CHANNEL_DICT[channel]))
            time.sleep(0.1)
    
    def SaveRecall_setup(self,address='U-disk0\\debug_sds5034x\\',file_name='setup11.SET'):
        
        '''
        description: The STORE_PANEL command stores the 
            complete front-panel setup of the instrument, at 
            the time the command is issued, into a file on 
            the specified-DOS path directory in a USB 
            memory device.

        input:
            address
            file_name:A filename -string of up to 8 characters, with the extension ��.SET��. 
        output:
            NA
        '''
        self.instr.write("STore_PaNel DISK,UDSK,FILE,'{}{}'".format(address,file_name))
        
        #.instr.write("RCPN DISK, UDSK, FILE,'U-disk0\\debug_sds5034x\\setup1.SET'")
        
    
    ##########################################################################
        
    def Measure_bitrate(self,chan=4):
        '''
        description: this command query bitrate.. 
        author:xzx
        input:
            chan=1/2/3/4
            filename:
        output:
            return status
        
        '''
        self.instr.write(":ANALyze:SIGNal:DATarate CHANnel{},Bits".format(chan))
        bitrate = self.instr.query(":ANALyze:SIGNal:DATarate? CHANnel{}".format(chan))
        return bitrate
        
    def Setchan_Display(self,chan=1,status=0):
        '''
        description: this command turns on/off dispaly for the specified channel. 
        author:xzx
        input:
            status: on(1) or off(0)
            channel:An integer, 1-4 in a single oscilloscope
        output:
            return status
        
        '''
        self.instr.write(":MTESt:FOLDing 1") #this command enables (ON,1) or disables (OFF,0) the display of the real-time eye
        self.instr.write(":CHANnel{}:DISPlay {}".format(chan, status))
        return(self.instr.query(":CHANnel<{}>:DISPlay?".fromat(chan)))
        
    # following module is acquire:AVERage command, and refer page239 for more details
        
    def Single(self):
        '''
        description: this command Initiates a single acquisition when the next trigger event occurs.
        author:xzx
        input:
            NA
        output:
            single result
        '''
        self.instr.write(":SINGle")
        
    def System_Mode(self,mode):
        '''
        description: this command Sets the system mode.
        author:xzx
        input:
            mode: ETIMe | RTIMe | PDETect | HRESolution | SEGMented | SEGPdetect | SEGHres
        output:
            acquire result and status
        
        '''
        self.instr.write("SYSTem:MODE " + mode)
        
    def Acquire_Average_State(self,status):
        '''
        description: this command Enables or disables averaging.
        author:xzx
        input:
            status: 1(on)/0(off)
        output:
            acquire result and status
        
        ''' 
        self.instr.write(":ACQuire:AVERage %d" % status)
        
        return(self.instr.query(":ACQuire:AVERage?"))
        
    def Acquire_Count(self,number):
        '''
        description: this command Sets the number of averages for the waveforms.
        author:xzx
        input:
            number: An integer, 2 to 65534
        output:
            acquire result and status
        
        '''
        self.instr.write("ACQuire:COUNt %d" % number)
        
        return(self.instr.query(":ACQuire[:AVERage]:COUNt?"))
        
    def Make_Directory(self,directory="C:\\Users\\<USER>\\Documents\\Infiniium"):
        '''
        description: this command Sets the number of averages for the waveforms.
        author:xzx
        input:
            directory is string , and for example "C:\\Users\\<USER>\\Documents\\Infiniium".
        output:
            NA
        note:
            must create path, and then call this func, and an error occurs when the requested directory does not exist. 
        
        '''
        #self.instr.write(":DISK:CDIRectory '" + directory + "' \n")
        self.instr.write(":DISK:CDIRectory "+ "\'" + directory + "\'")
        
    def Set_Digital_Trigger(self, digital_channel=0):
        '''
        description: Set trigger source to digital channel
        author:xzx
        input:
            digital_channel: Digital channel number (0-15 for D0-D15)
        output:
            Set digital channel as trigger source and return current trigger source
        '''
        self.instr.write(":TRIGger:EDGE:SOURce D{}".format(digital_channel))
        return self.instr.query(":TRIGger:EDGE:SOURce?")

    def Set_Trigger_Source(self, source='C1'):
        '''
        description: Set edge trigger source (analog channel, digital channel, or external)
        author:xzx
        input:
            source: Trigger source string
                - 'C1'-'C4': Analog channels
                - 'D0'-'D15': Digital channels
                - 'EX': External trigger
                - 'EX5': External trigger 5V
                - 'LINE': Line trigger
        output:
            Set trigger source and return current trigger source
        example:
            Set_Trigger_Source('C1')   # Analog channel C1
            Set_Trigger_Source('D1')   # Digital channel D1
            Set_Trigger_Source('EX')   # External trigger
        '''
        source = source.upper()

        # 验证输入格式
        if source.startswith('C'):
            try:
                channel = int(source[1:])
                if not (1 <= channel <= 4):
                    raise ValueError("Analog channel must be C1-C4")
            except ValueError:
                raise ValueError("Invalid analog channel format. Use C1-C4")
        elif source.startswith('D'):
            try:
                channel = int(source[1:])
                if not (0 <= channel <= 15):
                    raise ValueError("Digital channel must be D0-D15")
            except ValueError:
                raise ValueError("Invalid digital channel format. Use D0-D15")
        elif source not in ['EX', 'EX5', 'LINE']:
            raise ValueError("Invalid trigger source. Use C1-C4, D0-D15, EX, EX5, or LINE")

        self.instr.write(":TRIGger:EDGE:SOURce {}".format(source))
        return self.instr.query(":TRIGger:EDGE:SOURce?")

    def Get_Trigger_Source(self):
        '''
        description: Query current edge trigger source
        author:xzx
        output:
            Current trigger source (e.g., 'C1', 'D1', 'EX', 'EX5', 'LINE')
        '''
        return self.instr.query(":TRIGger:EDGE:SOURce?")

    def Set_Trigger_Level(self, level=0.0, channel=None):
        '''
        description: Set edge trigger level for analog channels
        author:xzx
        input:
            level: Trigger level in volts (float)
            channel: Analog channel number (1-4). If None, uses current trigger source
        output:
            Set trigger level and return current trigger level
        example:
            # Set trigger level to 1.5V for current trigger source
            Set_Trigger_Level(1.5)

            # Set trigger level to 2.0V for channel C2
            Set_Trigger_Level(2.0, 2)
        '''
        if channel is not None:
            if not (1 <= channel <= 4):
                raise ValueError("Channel must be 1-4 (C1-C4)")
            self.instr.write(":TRIGger:EDGE:LEVel C{},{}".format(channel, level))
        else:
            self.instr.write(":TRIGger:EDGE:LEVel {}".format(level))

        return self.instr.query(":TRIGger:EDGE:LEVel?")

    def Get_Trigger_Level(self, channel=None):
        '''
        description: Query edge trigger level
        author:xzx
        input:
            channel: Analog channel number (1-4). If None, queries current trigger source
        output:
            Current trigger level in volts
        '''
        if channel is not None:
            if not (1 <= channel <= 4):
                raise ValueError("Channel must be 1-4 (C1-C4)")
            return self.instr.query(":TRIGger:EDGE:LEVel? C{}".format(channel))
        else:
            return self.instr.query(":TRIGger:EDGE:LEVel?")

    def Set_Wavegen_Basic(self, waveform='SINE', frequency=1000, amplitude=2.0, offset=0.0, output_state='ON', load=50):
        '''
        description: Set wavegen basic waveform parameters
        author:xzx
        input:
            waveform: Waveform type {SINE|SQUARE|RAMP|PULSE|NOISE|ARB|DC|PRBS|IQ}
            frequency: Frequency in Hz
            amplitude: Amplitude in Vpp
            offset: Offset in V
            output_state: Output state {ON|OFF}
            load: Load impedance {50|HZ} (50 ohm or high impedance)
        output:
            Configure wavegen parameters and output state
        '''
        # Set waveform type
        self.instr.write("C1:BaSic_WaVe WVTP,{}".format(waveform))

        # Set frequency (not applicable for NOISE and DC)
        if waveform not in ['NOISE', 'DC']:
            self.instr.write("C1:BaSic_WaVe FRQ,{}".format(frequency))

        # Set amplitude (not applicable for NOISE and DC)
        if waveform not in ['NOISE', 'DC']:
            self.instr.write("C1:BaSic_WaVe AMP,{}".format(amplitude))

        # Set offset (not applicable for NOISE)
        if waveform != 'NOISE':
            self.instr.write("C1:BaSic_WaVe OFST,{}".format(offset))

        # Set output state and load
        self.instr.write("C1:OUTPut {},LOAD,{}".format(output_state, load))

        # Query current settings
        basic_wave_status = self.instr.query("C1:BaSic_WaVe?")
        output_status = self.instr.query("C1:OUTPut?")

        return basic_wave_status, output_status

    def Set_Wavegen_Advanced(self, duty_cycle=None, symmetry=None, pulse_width=None, noise_stdev=None, noise_mean=None):
        '''
        description: Set advanced wavegen parameters for specific waveform types
        author:xzx
        input:
            duty_cycle: Duty cycle for SQUARE and PULSE (0-100%)
            symmetry: Symmetry for RAMP (0-100%)
            pulse_width: Pulse width for PULSE in seconds
            noise_stdev: Standard deviation for NOISE in V
            noise_mean: Mean value for NOISE in V
        output:
            Configure advanced wavegen parameters
        '''
        if duty_cycle is not None:
            self.instr.write("C1:BaSic_WaVe DUTY,{}".format(duty_cycle))

        if symmetry is not None:
            self.instr.write("C1:BaSic_WaVe SYM,{}".format(symmetry))

        if pulse_width is not None:
            self.instr.write("C1:BaSic_WaVe WIDTH,{}".format(pulse_width))

        if noise_stdev is not None:
            self.instr.write("C1:BaSic_WaVe STDEV,{}".format(noise_stdev))

        if noise_mean is not None:
            self.instr.write("C1:BaSic_WaVe MEAN,{}".format(noise_mean))

        return self.instr.query("C1:BaSic_WaVe?")

    def Set_Wavegen_Arbitrary(self, arb_index=None, arb_name=None):
        '''
        description: Set arbitrary waveform for wavegen
        author:xzx
        input:
            arb_index: Arbitrary waveform index (0-47, see manual for mapping)
            arb_name: Arbitrary waveform name (e.g., 'Sine', 'StairUp', 'Triang', etc.)
        output:
            Set arbitrary waveform and return current setting
        note:
            Use either arb_index OR arb_name, not both
        '''
        if arb_index is not None:
            self.instr.write("C1:ARbWaVe INDEX,{}".format(arb_index))
        elif arb_name is not None:
            self.instr.write("C1:ARbWaVe NAME,{}".format(arb_name))
        else:
            raise ValueError("Either arb_index or arb_name must be provided")

        return self.instr.query("C1:ARbWaVe?")

    def Set_Display_Persistence(self, time='10S'):
        '''
        description: Set or query the persistence display time (余晖模式)
        author:xzx
        input:
            time: Persistence display time
                  Options: {OFF|INFinite|100MS|200MS|500MS|1S|5S|10S|30S}
                  (Available options may vary by model)
        output:
            Set persistence mode and return current setting
        '''
        self.instr.write(":DISPlay:PERSistence {}".format(time))
        return self.instr.query(":DISPlay:PERSistence?")

    def Clear_Display_Waveform(self):
        '''
        description: Clear the oscilloscope sweeps and restart acquisition (equivalent to Clear Sweeps button)
        author:xzx
        input:
            None
        output:
            Clear acquisition sweeps and restart
        '''
        self.instr.write(":ACQuire:CSWeep")

    def Set_Timebase_Scale(self, timebase_scale='2us'):
        '''
        description: Set the timebase scale (time per division)
        author:xzx
        input:
            timebase_scale: Time per division in human-readable format
                           Examples: '2us', '1ms', '100ns', '5ms', etc.
        output:
            Set timebase scale and return current setting
        '''
        # Parse the timebase_scale string
        value = float(timebase_scale[:-2])  # Extract the numerical part
        unit = timebase_scale[-2:]  # Extract the unit part

        # Convert to seconds
        if unit == 'ms':
            timebase_scale_sec = value * 1e-3
        elif unit == 'us':
            timebase_scale_sec = value * 1e-6
        elif unit == 'ns':
            timebase_scale_sec = value * 1e-9
        elif unit == 's':
            timebase_scale_sec = value
        else:
            raise ValueError("Unsupported time unit. Use 'ns', 'us', 'ms', or 's'.")

        # Set the time base
        self.instr.write(":TIMebase:SCALe {}".format(timebase_scale_sec))
        current_timebase = float(self.instr.query(":TIMebase:SCALe?"))

        return current_timebase

    def Set_Wavegen_Output(self, state='ON', load=50):
        '''
        description: Set wavegen output state and load
        author:xzx
        input:
            state: Output state {ON|OFF}
            load: Load impedance {50|HZ} (50 ohm or high impedance)
        output:
            Set output state and return current setting
        '''
        self.instr.write("C1:OUTPut {},LOAD,{}".format(state, load))
        return self.instr.query("C1:OUTPut?")

    def Set_Wavegen_Frequency(self, frequency):
        '''
        description: Set wavegen frequency only (for frequency sweeping)
        author:xzx
        input:
            frequency: Frequency in Hz
        output:
            Set frequency and return current basic wave status
        '''
        self.instr.write("C1:BaSic_WaVe FRQ,{}".format(frequency))
        return self.instr.query("C1:BaSic_WaVe?")

    def Save_Image(self, filepath="U-disk0/SIGLENT/screen.png", image_format="PNG", invert="OFF", menu="MON"):
        '''
        description: Captures an image of the display active window and saves it into a graphics file.
        author:xzx
        input:
            filepath: File path and name (string)
            image_format: Image format {PNG|BMP|JPEG}
            invert: Invert colors {ON|OFF} - ON for invert, OFF for normal
            menu: Menu display {MON|MOF} - MON to show menu, MOF to hide menu
        output:
            Save picture to specified path
        example:
            Save to U-disk with BMP format, invert colors, hide menu:
            :SAVE:IMAGe "U-disk0/SIGLENT/screen.bmp",BMP,ON,MOF
        '''
        command = ':SAVE:IMAGe "{}",{},{},{}'.format(filepath, image_format, invert, menu)
        self.instr.write(command)
        
    def Save_setup(self,filepath="C:\\Users\\<USER>\\Documents\\Infiniium\\Results\\Meritech",filename="steup1"):
        '''
        description: this command saves the current oscilloscope setup to a disk.The file will have a .set extension.. 
        author:xzx
        input:
            filepath: default is "C:\\Users\\<USER>\\Documents\\Infiniium"
            filename:
        output:
            return status
        
        '''
        self.Make_Directory(directory=filepath)
        self.instr.write(":DISK:SAVE:SETup " + "\'" + filename + "\'")
        
        
    def Load_setup(self,mode=None,filepath="C:\\Users\\<USER>\\Documents\\Infiniium\\Results\\Meritech",filename="setup_on_eyeandjitter_20Mpts"):   #setup_on_eye,and filename excluding ".set"
        '''
        description: this command load the current oscilloscope setup to a disk.The file will have a .set extension.. 
        author:xzx
        input:
            filepath: default is "C:\\Users\\<USER>\\Documents\\Infiniium"
            filename:You can load .WFM, .CSV, .TSV, .TXT, .BIN, .H5, .SET, and .OSC file types.
                eye: setup_on_eye
                jitter: setup_on_jitter_20Mpts
                eye and jitter: setup_on_eyeandjitter_20Mpts
            The destination is only used when loading a waveform memory.
            mode:"eye" or "jitter"
        output:
            return status
        
        '''
        
        if mode =="eye":
            self.Make_Directory(directory=filepath)
            self.instr.write(":DISK:LOAD " + "\'" + filename +".SET" + "\'")
        if mode =="jitter":
            self.Make_Directory(directory=filepath)
            self.instr.write(":DISK:LOAD " + "\'" + filename +".SET" + "\'")
        else:
            self.Make_Directory(directory=filepath)
            self.instr.write(":DISK:LOAD " + "\'" + filename +".SET" + "\'")
        
    #following module is channel command, and refer page339 for more details
    def Setchan_Commode(self,chan=1,status=0):
        '''
        description: this command turns on/off common mode for the channel. 
        note:
            Channels 2 and 4 may form a common mode channel;
            Channels 1 and 3 may form a common mode channel.
        author:xzx
        input:
            status: on(1) or off(0)
            channel:An integer, 1-4 in a single oscilloscope
        output:
            return status
        
        '''
        self.instr.write(":CHANnel{}:COMMonmode {}".format(chan, status))
        return(self.instr.query(":CHANnel<{}>:COMMonmode?".fromat(chan)))
    
    def Setchan_Diffmode(self,chan=1,status=0):
        '''
        description: this command turns on/off diff mode for the channel. 
        note:
            Channels 1 and 3 may form a diff mode channel;
            Channels 2 and 4 may form a diff mode channel.
        author:xzx
        input:
            status: on(1) or off(0)
            channel:An integer, 1-4 in a single oscilloscope
        output:
            return status
        
        '''
        self.instr.write(":CHANnel{}:DIFFerential {}".format(chan, status))
        return(self.instr.query(":CHANnel<{}>:DIFFerential?".fromat(chan)))
    
    def Save_Jitter(self,filepath="C:\\Users\\<USER>\\Documents\\Infiniium\\Results\\Meritech",filename="jitter_result1"):
        '''
        description: this command saves the jitter measurements shown in the RJDJ tab at the bottom of the oscilloscope 
            screen along with the RJDJ graph data in a comma separated variables (CSV) file format
        author:xzx
        input:
            filepath: default is "C:\\Users\\<USER>\\Documents\\Infiniium"
            filename:
        output:
            return status
        
        '''
        self.Make_Directory(directory=filepath)
        self.instr.write(":DISK:SAVE:JITTer " + "\'" + filename + "\'")
        
    # def Measure_WaveEyeData(self,chan=4):
    #     '''
    #     description: Works for eye mode only, this command selects which measurement displayed on the oscilloscope you are performing the eye analysis on
    #     author:xzx
    #     input:
    #         chan=1/2/3/4
    #     output:
    #         return all eye parameters, and it is current value,not means/MIN/MAX,etc.
    #     '''
    #     #from collections import OrderedDict
    #     #eyedic = collections.OrderedDict()
    #
    #     try:
    #         # from collections import OrderedDict 
    #         # eyedic = OrderedDict()
    #
    #         self.instr.write(":MEASure:FREQuency CHANnel{}".format(chan))
    #         eyedic["wave_frequency"] =self.instr.query(":MEASure:FREQuency? CHANnel{}".format(chan))  #unit is HZ
    #         self.instr.write(":MEASure:VMAX CHANnel{}".format(chan))
    #         eyedic["wave_VMAX"] = self.instr.query(":MEASure:VMAX? CHANnel{}".format(chan))  #unit is V
    #         self.instr.write(":MEASure:VMIN CHANnel{}".format(chan))
    #         eyedic["wave_VMIN"] =self.instr.query(":MEASure:VMIN? CHANnel{}".format(chan)) #unit is V
    #         self.instr.write(":MEASure:VPP CHANnel{}".format(chan))
    #         eyedic["wave_Vpp"] = self.instr.query(":MEASure:VPP? CHANnel{}".format(chan))    #unit is V
    #         self.instr.write(":MEASure:DUTYcycle CHANnel{}".format(chan))
    #         eyedic["wave_dcdcycle"] = self.instr.query(":MEASure:DUTYcycle? CHANnel{},RISing".format(chan))
    #         self.instr.write(":MEASure:NOISe CHANnel{},VOLT,BOTH".format(chan))
    #         eyedic["wave_noise_vol"] = self.instr.query(":MEASure:NOISe? CHANnel{}, VOLT,BOTH".format(chan))    ##unit is voltage
    #         self.instr.write(":MEASure:NOISe CHANnel{},UNIT,BOTH".format(chan))
    #         eyedic["wave_noise_UAmp"] = self.instr.query(":MEASure:NOISe? CHANnel{}, UNIT,BOTH".format(chan)) ##unit is UA,equal to UNIT AMPI
    #
    #         self.instr.write(":MEASure:CGRade:EHEight")
    #         eyedic["eye_height"] = self.instr.query(":MEASure:CGRade:EHEight?")   #max value,and unit is V
    #         self.instr.write(":MEASure:CGRade:EWIDth")
    #         eyedic["eye_width"] = self.instr.query(":MEASure:CGRade:EWIDth?")   #max value,and unit is s
    #         self.instr.write(":MEASure:CGRade:CROSsing")
    #         eyedic["eye_crossing"] = self.instr.query(":MEASure:CGRade:CROSsing?")   #max value,and unit is %
    #         self.instr.write(":MEASure:CGRade:JITTer PP")
    #         eyedic["eye_jitterPP"] = self.instr.query(":MEASure:CGRade:JITTer? PP") #max value,and unit is s
    #         self.instr.write(":MEASure:CGRade:JITTer RMS")
    #         eyedic["eye_jitterRMS"] = self.instr.query(":MEASure:CGRade:JITTer? RMS")    #max value,and unit is s
    #
    #         eyedic["eye_onelevel"] = self.instr.query(":MEASure:CGRade:OLEVel? ")    #max value,and unit is V
    #         eyedic["eye_zerolevel"] = self.instr.query(":MEASure:CGRade:ZLEVel? ")   #max value,and unit is V
    #
    #         self.instr.write(":MEASure:VAMPlitude CHAN{}".format(chan))
    #         eyedic["eye_amp"] = self.instr.query(":MEASure:VAMPlitude? CHANnel{}".format(chan)) #unit is V
    #
    #         self.instr.write(":MEASure:DATarate CHANnel{}".format(chan))
    #         eyedic["data_rate"] = self.instr.query(":MEASure:DATarate? CHANnel{}".format(chan))  #unit is Hz
    #
    #         self.instr.write(":MEASure:FALLtime CHANnel{}".format(chan))
    #         eyedic["eye_falltime"] = self.instr.query(":MEASure:FALLtime? CHANnel{}".format(chan))
    #
    #         self.instr.write(":MEASure:RISEtime CHANnel{}".format(chan))
    #         eyedic["eye_risetime"] = self.instr.query(":MEASure:RISetime? CHANnel{}".format(chan))
    #
    #         wave_eyedic=[eyedic["wave_frequency"],eyedic["wave_VMAX"],eyedic["wave_VMIN"],eyedic["wave_Vpp"],eyedic["wave_dcdcycle"],eyedic["wave_noise_vol"],eyedic["wave_noise_UAmp"],eyedic["eye_height"],eyedic["eye_width"],eyedic["eye_crossing"],eyedic["eye_jitterPP"],eyedic["eye_jitterRMS"],eyedic["eye_onelevel"],eyedic["eye_zerolevel"],eyedic["eye_amp"],eyedic["data_rate"],eyedic["eye_falltime"],eyedic["eye_risetime"]]
    #
    #     except:
    #
    #         eyedic["wave_frequency"] = 0
    #         eyedic["wave_VMAX"] = 0
    #         eyedic["wave_VMIN"] = 0
    #         eyedic["wave_Vpp"] = 0
    #         eyedic["wave_dcdcycle"] = 0
    #         eyedic["wave_noise_vol"] = 0
    #         eyedic["wave_noise_UAmp"] = 0
    #
    #         eyedic["eye_height"] = 0
    #         eyedic["eye_width"] = 0
    #         eyedic["eye_crossing"] = 0
    #         eyedic["eye_jitterPP"] = 0
    #         eyedic["eye_jitterRMS"] = 0
    #         eyedic["eye_onelevel"] = 0
    #         eyedic["eye_zerolevel"] = 0
    #         eyedic["eye_amp"] = 0
    #         eyedic["data_rate"] = 0
    #         eyedic["eye_falltime"] = 0
    #         eyedic["eye_risetime"] = 0
    #
    #         wave_eyedic=[eyedic["wave_frequency"],eyedic["wave_VMAX"],eyedic["wave_VMIN"],eyedic["wave_Vpp"],eyedic["wave_dcdcycle"],eyedic["wave_noise_vol"],eyedic["wave_noise_UAmp"],eyedic["eye_height"],eyedic["eye_width"],eyedic["eye_crossing"],eyedic["eye_jitterPP"],eyedic["eye_jitterRMS"],eyedic["eye_onelevel"],eyedic["eye_zerolevel"],eyedic["eye_amp"],eyedic["data_rate"],eyedic["eye_falltime"],eyedic["eye_risetime"]]
    #
    #     return [float(x) for x in wave_eyedic]
    
    def Measure_WaveEyeData(self,chan=4):
        '''
        description: Works for eye mode only, this command selects which measurement displayed on the oscilloscope you are performing the eye analysis on
        author:xzx
        input:
            chan=1/2/3/4
        output:
            return all eye parameters, and it is current value,not means/MIN/MAX,etc.
        '''
            
        self.instr.write(":MEASure:FREQuency CHANnel{}".format(chan))
        wave_frequency =self.instr.query(":MEASure:FREQuency? CHANnel{}".format(chan))  #unit is HZ
        self.instr.write(":MEASure:VMAX CHANnel{}".format(chan))
        wave_VMAX = self.instr.query(":MEASure:VMAX? CHANnel{}".format(chan))  #unit is V
        self.instr.write(":MEASure:VMIN CHANnel{}".format(chan))
        wave_VMIN =self.instr.query(":MEASure:VMIN? CHANnel{}".format(chan)) #unit is V
        self.instr.write(":MEASure:VPP CHANnel{}".format(chan))
        wave_Vpp = self.instr.query(":MEASure:VPP? CHANnel{}".format(chan))    #unit is V
        self.instr.write(":MEASure:DUTYcycle CHANnel{}".format(chan))
        wave_dcdcycle = self.instr.query(":MEASure:DUTYcycle? CHANnel{},RISing".format(chan))
        self.instr.write(":MEASure:NOISe CHANnel{},VOLT,BOTH".format(chan))
        wave_noise_vol = self.instr.query(":MEASure:NOISe? CHANnel{}, VOLT,BOTH".format(chan))    ##unit is voltage
        self.instr.write(":MEASure:NOISe CHANnel{},UNIT,BOTH".format(chan))
        wave_noise_UAmp = self.instr.query(":MEASure:NOISe? CHANnel{}, UNIT,BOTH".format(chan)) ##unit is UA,equal to UNIT AMPI
         
        self.instr.write(":MEASure:CGRade:EHEight")
        eye_height = self.instr.query(":MEASure:CGRade:EHEight?")   #max value,and unit is V
        self.instr.write(":MEASure:CGRade:EWIDth")
        eye_width = self.instr.query(":MEASure:CGRade:EWIDth?")   #max value,and unit is s
        self.instr.write(":MEASure:CGRade:CROSsing")
        eye_crossing = self.instr.query(":MEASure:CGRade:CROSsing?")   #max value,and unit is %
        self.instr.write(":MEASure:CGRade:JITTer PP")
        eye_jitterPP = self.instr.query(":MEASure:CGRade:JITTer? PP") #max value,and unit is s
        self.instr.write(":MEASure:CGRade:JITTer RMS")
        eye_jitterRMS= self.instr.query(":MEASure:CGRade:JITTer? RMS")    #max value,and unit is s
         
        eye_onelevel= self.instr.query(":MEASure:CGRade:OLEVel? ")    #max value,and unit is V
        eye_zerolevel = self.instr.query(":MEASure:CGRade:ZLEVel? ")   #max value,and unit is V
         
        self.instr.write(":MEASure:VAMPlitude CHAN{}".format(chan))
        eye_amp = self.instr.query(":MEASure:VAMPlitude? CHANnel{}".format(chan)) #unit is V
        
        self.instr.write(":MEASure:DATarate CHANnel{}".format(chan))
        data_rate = self.instr.query(":MEASure:DATarate? CHANnel{}".format(chan))  #unit is Hz
        
        self.instr.write(":MEASure:FALLtime CHANnel{}".format(chan))
        eye_falltime = self.instr.query(":MEASure:FALLtime? CHANnel{}".format(chan))
        
        self.instr.write(":MEASure:RISEtime CHANnel{}".format(chan))
        eye_risetime = self.instr.query(":MEASure:RISetime? CHANnel{}".format(chan))
        
        wave_eyedic=[wave_frequency,wave_VMAX,wave_VMIN,wave_Vpp,wave_dcdcycle,wave_noise_vol,wave_noise_UAmp,eye_height,eye_width,eye_crossing,eye_jitterPP,eye_jitterRMS,eye_onelevel,eye_zerolevel,eye_amp,data_rate,eye_falltime,eye_risetime]
             
    
        return [float(x) for x in wave_eyedic]
    
    def Measure_JitterModeData(self,tmode="data",chan=4):
        '''
        Works for Jitter mode only, this command selects which measurement displayed on the oscilloscope you are performing the jitter analysis on
        RJ method must select "Tail-fit"
        '''
        
        
        #seq1=self.instr.write(":MEASure:JITTer:MEASurement MEASurement{}".format(seq))
        jitterdic=[]
        
        if tmode =="data":
        
            Len_temp=self.instr.query(':MEASure:RJDJ:APLength?')
            Len_temp1=Len_temp.replace('\n', '')
            #JITTER_patternlength = int(self.instr.query(":MEASure:RJDJ:APLength?").replace('\n', ''))
            JITTER_patternlength=Len_temp1
            
        self.instr.write(":MEASure:CDRRate CHANnel{}".format(chan))
        JITTER_bitrate = self.instr.query(":MEASure:CDRRate? CHANnel{}".format(chan))
    
        TJRJDJ= (self.instr.query(":MEASure:RJDJ:ALL?")).split(",")
        time.sleep(0.5)
        JITTER_TJ_1Eneg12 = TJRJDJ[1]
        JITTER_RJrms = TJRJDJ[4]
        JITTER_DJdd = TJRJDJ[7]
        JITTER_PJrms = TJRJDJ[10]
        JITTER_BUJdd = TJRJDJ[13]
        JITTER_DDJpp = TJRJDJ[16]
        JITTER_DCD = TJRJDJ[19]
        JITTER_ISIpp = TJRJDJ[22]
        JITTER_Transitions = TJRJDJ[25]
        JITTER_DDPWS = TJRJDJ[28]
        JITTER_Fdiv2 = TJRJDJ[31]
        JITTER_ABUJrms = TJRJDJ[34]
            
        jitterdic=[JITTER_patternlength,JITTER_bitrate,JITTER_TJ_1Eneg12,JITTER_RJrms,JITTER_DJdd,JITTER_PJrms,JITTER_BUJdd,JITTER_DDJpp,JITTER_DCD,JITTER_ISIpp,JITTER_Transitions,JITTER_DDPWS,JITTER_Fdiv2,JITTER_ABUJrms]
        
        return jitterdic
    

# m= SiglentSDS5034X(porttype='usb',usb_addr="USB0::0xF4EC::0x1008::SDS5XB0Q6R0190::INSTR")
# m.Set_Default()
