# -*- coding: utf-8 -*-
"""
重构后的GPIO工具模块
提供统一的GPIO配置、延迟补偿和错误处理功能
"""

import time
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Tuple, Any
from enum import Enum

# =============================================================================
# 配置类定义
# =============================================================================

class GPIOType(Enum):
    """GPIO类型枚举"""
    STANDARD = "standard"
    GPIO9_10 = "gpio9_10"
    GPIO15_16 = "gpio15_16"
    GPIO13 = "gpio13"

@dataclass
class GPIOConfig:
    """GPIO配置"""
    gpio_pin: int
    gpio_type: GPIOType
    i2c_bus: int = 0
    special_config_needed: bool = False
    config_message: str = ""

@dataclass
class DelayCompensationConfig:
    """延迟补偿配置"""
    fwd_dly_range: List[int] = field(default_factory=lambda: [0, 5, 10, 15, 20])
    rvs_dly_range: List[int] = field(default_factory=lambda: [0, 5, 10, 15, 20])
    dly_comp_en: int = 1
    frequency: int = 30
    observation_time: float = 1.0

# =============================================================================
# GPIO配置工具
# =============================================================================

class GPIOConfigManager:
    """GPIO配置管理器"""
    
    @staticmethod
    def analyze_gpio_type(gpio_pin: int) -> GPIOConfig:
        """分析GPIO类型并返回配置"""
        if gpio_pin in [9, 10]:
            return GPIOConfig(
                gpio_pin=gpio_pin,
                gpio_type=GPIOType.GPIO9_10,
                i2c_bus=0,
                special_config_needed=True,
                config_message=f"检测到GPIO{gpio_pin} (I2C0引脚)，需要特殊配置"
            )
        elif gpio_pin in [15, 16]:
            return GPIOConfig(
                gpio_pin=gpio_pin,
                gpio_type=GPIOType.GPIO15_16,
                i2c_bus=1,
                special_config_needed=True,
                config_message=f"检测到GPIO{gpio_pin} (I2C1引脚)，需要连接GPIO 11/12"
            )
        elif gpio_pin == 13:
            return GPIOConfig(
                gpio_pin=gpio_pin,
                gpio_type=GPIOType.GPIO13,
                i2c_bus=0,
                special_config_needed=True,
                config_message=f"检测到GPIO{gpio_pin}，需要特殊寄存器配置"
            )
        else:
            return GPIOConfig(
                gpio_pin=gpio_pin,
                gpio_type=GPIOType.STANDARD,
                i2c_bus=0,
                special_config_needed=False,
                config_message=f"标准GPIO{gpio_pin}配置"
            )
    
    @staticmethod
    def configure_special_gpio(q68, gpio_config: GPIOConfig) -> bool:
        """配置特殊GPIO"""
        try:
            if gpio_config.gpio_type == GPIOType.GPIO13:
                # GPIO13特殊配置
                q68.c2m.wr_pinmux_pin_ctrl_fields(i=13, sel=0)  # 禁用I2C功能
                q68.c2m.wr_reg(0x4479, 0x00)  # 禁用i2c sel
                q68.c2m.wr_reg(0x440d, 0xff)  # 设置特殊寄存器
                print(f"GPIO13特殊配置完成")
                
            elif gpio_config.gpio_type == GPIOType.GPIO9_10:
                # GPIO9/10特殊配置
                q68.c2m.wr_pinmux_pin_ctrl_fields(i=gpio_config.gpio_pin, sel=0)
                print(f"GPIO{gpio_config.gpio_pin}特殊配置完成")
                
            elif gpio_config.gpio_type == GPIOType.GPIO15_16:
                # GPIO15/16特殊配置
                print(f"⚠️  检测到Q68 GPIO{gpio_config.gpio_pin} (I2C1引脚)")
                print("🔌 硬件连接提示: 请确保已连接 Q68 GPIO 11/12 (SDA1/SCL1)")
                
            return True
            
        except Exception as e:
            print(f"特殊GPIO配置失败: {e}")
            return False

# =============================================================================
# 延迟补偿工具
# =============================================================================

class DelayCompensationManager:
    """延迟补偿管理器"""
    
    def __init__(self, config: Optional[DelayCompensationConfig] = None):
        self.config = config or DelayCompensationConfig()
    
    def test_delay_compensation(self, q68, q68_gpio: int, signal_id: int) -> Dict[str, Any]:
        """测试延迟补偿"""
        print(f"\n🔧 开始延迟补偿测试 - Q68 GPIO{q68_gpio}")
        
        results = {}
        total_tests = len(self.config.fwd_dly_range) * len(self.config.rvs_dly_range)
        current_test = 0
        
        for fwd_dly in self.config.fwd_dly_range:
            for rvs_dly in self.config.rvs_dly_range:
                current_test += 1
                test_key = f"fwd{fwd_dly}_rvs{rvs_dly}"
                
                print(f"  测试 {current_test}/{total_tests}: fwd_dly={fwd_dly}, rvs_dly={rvs_dly}")
                
                try:
                    # 配置延迟补偿参数
                    success = self._configure_delay_compensation(
                        q68, q68_gpio, fwd_dly, rvs_dly, signal_id
                    )
                    
                    if success:
                        # 等待观察
                        time.sleep(self.config.observation_time)
                        results[test_key] = "success"
                        print(f"    ✅ 配置成功")
                    else:
                        results[test_key] = "failed"
                        print(f"    ❌ 配置失败")
                        
                except Exception as e:
                    results[test_key] = f"error: {e}"
                    print(f"    💥 异常: {e}")
        
        # 统计结果
        success_count = len([r for r in results.values() if r == "success"])
        print(f"\n📊 延迟补偿测试完成: {success_count}/{total_tests} 成功")
        
        return {
            'results': results,
            'summary': {
                'total': total_tests,
                'success': success_count,
                'failed': total_tests - success_count
            }
        }
    
    def _configure_delay_compensation(self, q68, q68_gpio: int, fwd_dly: int, rvs_dly: int, signal_id: int) -> bool:
        """配置延迟补偿参数"""
        try:
            # 配置延迟参数
            if q68_gpio <= 7:
                # GPIO 0-7使用ctrl0
                q68.c2m.wr_gpios_ctrl0_fields(
                    i=q68_gpio,
                    fwd_dly=fwd_dly,
                    rvs_dly=rvs_dly,
                    dly_comp_en=self.config.dly_comp_en
                )
            else:
                # GPIO 8-14使用ctrl1
                q68.c2m.wr_gpios_ctrl1_fields(
                    i=q68_gpio-8,
                    fwd_dly=fwd_dly,
                    rvs_dly=rvs_dly,
                    dly_comp_en=self.config.dly_comp_en
                )
            
            # 配置GPIO远程传输
            q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id)
            
            return True
            
        except Exception as e:
            print(f"延迟补偿配置失败: {e}")
            return False

# =============================================================================
# I2C错误检测工具
# =============================================================================

class I2CErrorDetector:
    """I2C错误检测器"""
    
    I2C_ERROR_KEYWORDS = [
        "send_cmd_i2c_get cmd ret error",
        "send_cmd_i2c_set cmd ret error",
        "send_cmd_i2c_get respond error",
        "send_cmd_i2c_set respond error",
        "send_cmd_i2c_get usb_send_buff error",
        "send_cmd_i2c_set usb_send_buff error"
    ]
    
    @classmethod
    def is_i2c_error(cls, error_message: str) -> bool:
        """检查是否为I2C通信错误"""
        error_str = str(error_message).lower()
        return any(keyword.lower() in error_str for keyword in cls.I2C_ERROR_KEYWORDS)
    
    @classmethod
    def get_error_suggestion(cls, error_message: str) -> str:
        """获取错误建议"""
        if cls.is_i2c_error(error_message):
            return "建议检查地址转换设置是否正确，确保在GPIO配置前调用setup_address_translation()"
        else:
            return "请检查硬件连接和设备状态"

# =============================================================================
# GPIO测试助手
# =============================================================================

class GPIOTestHelper:
    """GPIO测试助手类"""
    
    def __init__(self, hardware_manager):
        self.hardware_manager = hardware_manager
        self.devices = hardware_manager.get_devices()
        self.gpio_config_manager = GPIOConfigManager()
        self.delay_compensation_manager = DelayCompensationManager()
        self.i2c_error_detector = I2CErrorDetector()
    
    def configure_q68_gpio_with_validation(self, q68_gpio: int, signal_id: int) -> bool:
        """配置Q68 GPIO并进行验证"""
        try:
            # 分析GPIO类型
            gpio_config = self.gpio_config_manager.analyze_gpio_type(q68_gpio)
            print(f"GPIO配置分析: {gpio_config.config_message}")
            
            # 特殊GPIO配置
            if gpio_config.special_config_needed:
                success = self.gpio_config_manager.configure_special_gpio(
                    self.devices['q68'], gpio_config
                )
                if not success:
                    return False
            
            # 标准GPIO配置
            q68 = self.devices['q68']
            q68.MFNSet(gpio=q68_gpio, mfn=0)
            q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id)
            
            print(f"✅ Q68 GPIO{q68_gpio} 配置完成")
            return True
            
        except Exception as e:
            error_suggestion = self.i2c_error_detector.get_error_suggestion(str(e))
            print(f"❌ Q68 GPIO{q68_gpio} 配置失败: {e}")
            print(f"💡 建议: {error_suggestion}")
            return False
    
    def configure_s68_gpio_with_validation(self, link: int, s68_gpio: int, signal_id: int) -> bool:
        """配置S68 GPIO并进行验证"""
        try:
            q68_remote = self.devices['q68_remote']
            s68_res_dev = self.devices['s68_res_dev']
            
            # 设置设备地址
            target_addr = s68_res_dev[link]
            q68_remote.dongle.devAddr = target_addr
            time.sleep(0.1)
            
            # 配置S68 GPIO
            q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
            q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
            
            print(f"✅ S68 Link{link} GPIO{s68_gpio} 配置完成")
            return True
            
        except Exception as e:
            if self.i2c_error_detector.is_i2c_error(str(e)):
                print(f"🔴 S68 GPIO{s68_gpio} I2C通信错误: {e}")
                print(f"💡 提示: 检查地址转换设置是否正确")
            else:
                print(f"❌ S68 GPIO{s68_gpio} 其他错误: {e}")
            return False
    
    def test_gpio_path_with_delay_compensation(self, q68_gpio: int, s68_gpio: int, link: int, signal_id: int) -> Dict[str, Any]:
        """测试GPIO路径并进行延迟补偿"""
        print(f"\n🚀 开始GPIO路径测试: Q68 GPIO{q68_gpio} ← Link{link} S68 GPIO{s68_gpio}")
        
        # 配置S68 GPIO
        s68_success = self.configure_s68_gpio_with_validation(link, s68_gpio, signal_id)
        if not s68_success:
            return {'success': False, 'error': 'S68 GPIO配置失败'}
        
        # 配置Q68 GPIO
        q68_success = self.configure_q68_gpio_with_validation(q68_gpio, signal_id)
        if not q68_success:
            return {'success': False, 'error': 'Q68 GPIO配置失败'}
        
        # 延迟补偿测试
        delay_results = self.delay_compensation_manager.test_delay_compensation(
            self.devices['q68'], q68_gpio, signal_id
        )
        
        return {
            'success': True,
            'q68_gpio': q68_gpio,
            's68_gpio': s68_gpio,
            'link': link,
            'delay_compensation': delay_results
        }

# =============================================================================
# 便捷函数
# =============================================================================

def create_gpio_test_helper(devices) -> GPIOTestHelper:
    """创建GPIO测试助手"""
    hardware_manager = devices['hardware_manager']
    return GPIOTestHelper(hardware_manager)

def quick_gpio_config_check(gpio_pin: int) -> GPIOConfig:
    """快速GPIO配置检查"""
    return GPIOConfigManager.analyze_gpio_type(gpio_pin)

def is_i2c_communication_error(error_message: str) -> bool:
    """快速I2C错误检查"""
    return I2CErrorDetector.is_i2c_error(error_message)
