# import wxP.1ytho.1nxFF
import sys
import csv
import os
import time
import datetime
# import wx

from Common.M65Q68_Common_Fuction_R5 import *
from Common_var.M66S68_Common_Fuction_R5 import *
# from HWInterface import HWImpl
# from instr_drv.<PERSON>ley2230_py3 import *
# from instr_drv.IT6332B import *
# from instr_drv.oven_haituo import *
# from instr_drv.DC5508U_TempMeter import *
# from instr_drv.gpp_4323_py3 import *
# oven=OVEN(port='com21')
#
# "Q68: TT85, s68_0: tt, s68_1: tt, s68_2: tt, s68_3: tt"
# Gpp433 = GPP_4323('com22')
# Keithley2230G1 = Keithley2230(porttype='usb',usb_addr="USB0::0x05E6::0x2230::9211115::INSTR")

#messgebox to input part id & chip id
from tkinter import *
BOARDID = 0 #define global parameter to set boardid
CHIPID = 0 #define global parameter to set chipid
root=Tk()
root.geometry('300x300')

l1=Label(root,text='BOARD ID')
l1.pack()
boardid_text = StringVar()
boardid_set = Entry(root,textvariable=boardid_text)
boardid_text.set("")
boardid_set.pack()

l2=Label(root,text='CHIP ID')
l2.pack()
chipid_text = StringVar()
chipid_set = Entry(root,textvariable=chipid_text)
chipid_text.set("")
chipid_set.pack()

def on_click():
    global BOARDID
    global CHIPID
    BOARDID = boardid_text.get()
    CHIPID = chipid_text.get()
    string=str("sheet is:%s"%(BOARDID))
    string1=str("chipid is:%s"%(CHIPID))
    print(string)
    print(string1)
    root.destroy()
    
Button(root,text='press',command=on_click).pack()
root.mainloop()

SKEW                = ('R5_TT5',)
STEMP                = (85,-40,25,85,)
VOLTAGE     =   [(1.14,1.71,1.71),(1.2,1.8,1.8),(1.08,1.62,1.62),]
VOLTYP              = ('Keithley2230', ) # ('LDO','Keithley2230',) # power supply type, on board LDO or 4 sense power supply
PLLINTERNALLDO      = (6, ) # PLL internal LDO to provide PLL power including aldo_V & dldo_V, 0:0.85V, 1:0.9V, 2:0.95V, 3:1V
PLLINTERNALLDO_DICT = {0:0.7, 1:0.9, 2:0.95, 3:1, 4:1}
ANAMUX              = (2, ) # Routed out PLL analog test signal, 0:vdddig09, 1:vddana09, 2:cp_vtrack, 3:vctrl
ANAMUX_DICT         = {0:'vcm_ahp',1:'vcm_ctle', 2:'vcm_driver', 3:'vcm_rxbuffer', 4:'vcm_t0', 5:'vcm_vga', 6:'vcm_afe_out', 7:'avdd_afe' }
DRIFT               = (25,-40,85,)
Drift_type          = {-40:(-40,25,85), 25:(25,-40,85),85:(85,25,-40)}

q68_iic_add = 0x73 #0x31#0x73
s680_iic_dev = 0x40
s682_iic_dev = 0x40

q68 = M65Q68_R5(dongle='stm32', id=0, bus='i2c', dongle_id=b'\x00=\x00&41Q\x024590', bus_chan=2, optype='auto', chip='M65Q68')# G9PH DS board   
q68_remote = M65Q68_R5_Remote_M66S68(dongle='stm32', id=0, bus='i2c', dongle_id=b'\x00=\x00&41Q\x024590',bus_chan=2, optype='auto')             
# s68_0 = M66S68_R5(dongle='stm32', id=1, bus='i2c',acc='L')
# s68_1 = M66S68_R5(dongle='stm32', id=2, bus='i2c',acc='L')

time.sleep(0.1) 
"turn off bctx output"
# q68.c2m.wr_tx_link0_phy_ana_ctrl0_fields(reset_async_txbc=1)
# q68.c2m.wr_tx_link1_phy_ana_ctrl0_fields(reset_async_txbc=1)
# q68.c2m.wr_tx_link2_phy_ana_ctrl0_fields(reset_async_txbc=1)




# q68.c2m.wr_tx_link3_phy_ana_ctrl0_fields(reset_async_txbc=1)

data_address = "D:\\Project\\m65q68_r5\\Raw_Data\\01_test\\temp_drift\\TT85\\"+str(BOARDID)+"_"+str(CHIPID)+"_"+"camera_temp_shock"+"_"+datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')+".csv"     
     
PHY_TEST = 0
link_en = 1
KLINK=[0,]

def Q68_C3_6G_Init():
    q68.dongle.setBusSpeed(1, 100)
    q68.c2m.wr_test_glb_ctrl0_fields(key=0x5C)   
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel0_fields(adp_delay_xt=0)  
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel84_fields(afe_ahp_csel=1)       
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel117_fields(cdr_kf_rshift=3, cdr_kp_rshift=0)                                                                                                                                                                    
    print ('rd_ana_top_d2a_ch0_regnamedel0_adp_delay_xt is: ',q68.c2m.rd_ana_top_d2a_ch2_regnamedel0_adp_delay_xt())                                                                                                                                                                                               
    # c2m.wr_ana_top_d2a_ch0_regnamedel12_fields(adp_khp_min=1, adp_khp_max=15)                                                                           
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel13_fields(adp_en_override_khp_async=1, adp_khp_override = 15)                                                       
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel16_fields(adp_gearshift_period_khp=7)                                                                              
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel14_fields(adp_gearshift_maxstep_khp=192) # khp调节速率， default100， 过快                                                 
    print ('rd_ana_top_d2a_ch0_regnamedel14_adp_gearshift_maxstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch0_regnamedel14_adp_gearshift_maxstep_khp())           
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel15_fields(adp_gearshift_minstep_khp=64)                                                                            
    print ('rd_ana_top_d2a_ch0_regnamedel15_adp_gearshift_minstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch0_regnamedel15_adp_gearshift_minstep_khp())
    q68.c2m.wr_tx_link0_phy_ana_ctrl4_fields(maxmag_auto_txbc=90)                                                                                                                                                                
    q68.C3BCTx0_MINMAX()
                                                                                                                                           
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel0_fields(adp_delay_xt=0)
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel84_fields(afe_ahp_csel=1)       
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel117_fields(cdr_kf_rshift=1, cdr_kp_rshift=0)                                                                                                                                                                
    print ('rd_ana_top_d2a_ch1_regnamedel0_adp_delay_xt is: ',q68.c2m.rd_ana_top_d2a_ch2_regnamedel0_adp_delay_xt())                                         
    # c2m.wr_ana_top_d2a_ch0_regnamedel12_fields(adp_khp_min=0, adp_khp_max=15)                                                                           
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel13_fields(adp_en_override_khp_async=1, adp_khp_override = 15)                                                       
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel16_fields(adp_gearshift_period_khp=7)                                                                              
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel14_fields(adp_gearshift_maxstep_khp=192) # khp调节速率， default100， 过快                                                 
    print ('rd_ana_top_d2a_ch1_regnamedel14_adp_gearshift_maxstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch1_regnamedel14_adp_gearshift_maxstep_khp())           
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel15_fields(adp_gearshift_minstep_khp=64)                                                                            
    print ('rd_ana_top_d2a_ch1_regnamedel15_adp_gearshift_minstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch1_regnamedel15_adp_gearshift_minstep_khp())             
    # q68.C3DFEEnable(en=1, ch=0)  # set C3 DFE update disable
    # q68.C3DFEEnable(en=1, ch=1)  # set C3 DFE update disable
    q68.c2m.wr_tx_link1_phy_ana_ctrl4_fields(maxmag_auto_txbc=90) 
    q68.C3BCTx1_MINMAX()  
    
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel0_fields(adp_delay_xt=0)                                                                                  
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel84_fields(afe_ahp_csel=1)                                                                                 
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel117_fields(cdr_kf_rshift=3, cdr_kp_rshift=0)                                                                                                                                                
    print ('rd_ana_top_d2a_ch2_regnamedel0_adp_delay_xt is: ',q68.c2m.rd_ana_top_d2a_ch2_regnamedel0_adp_delay_xt())                                  
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel13_fields(adp_en_override_khp_async=1, adp_khp_override = 15)                                              
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel16_fields(adp_gearshift_period_khp=7)                                                                     
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel14_fields(adp_gearshift_maxstep_khp=192) # khp调节速率， default100， 过快                                        
    print ('rd_ana_top_d2a_ch2_regnamedel14_adp_gearshift_maxstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch2_regnamedel14_adp_gearshift_maxstep_khp())  
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel15_fields(adp_gearshift_minstep_khp=64)                                                                   
    print ('rd_ana_top_d2a_ch2_regnamedel15_adp_gearshift_minstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch2_regnamedel15_adp_gearshift_minstep_khp())  
    q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel = 0x0C)  
    print('rx_polar_sel', q68.c2m.rd_test_rx_link_data_inv_rx_polar_sel())                                                                                                                                            
    q68.C3BCTx2_MINMAX()  
    q68.c2m.wr_tx_link2_phy_ana_ctrl4_fields(maxmag_auto_txbc=90)
     
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel0_fields(adp_delay_xt=0)                                                                                 
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel84_fields(afe_ahp_csel=1)                                                                                
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel117_fields(cdr_kf_rshift=1, cdr_kp_rshift=0)                                                                                                                                         
    print ('rd_ana_top_d2a_ch3_regnamedel0_adp_delay_xt is: ',q68.c2m.rd_ana_top_d2a_ch2_regnamedel0_adp_delay_xt())                                
    # c2m.wr_ana_top_d2a_ch0_regnamedel12_fields(adp_khp_min=0, adp_khp_max=15)                                                               
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel13_fields(adp_en_override_khp_async=1, adp_khp_override = 15)                                             
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel16_fields(adp_gearshift_period_khp=7)                                                                    
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel14_fields(adp_gearshift_maxstep_khp=192) # khp调节速率， default100， 过快                                       
    print ('rd_ana_top_d2a_ch3_regnamedel14_adp_gearshift_maxstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch2_regnamedel14_adp_gearshift_maxstep_khp()) 
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel15_fields(adp_gearshift_minstep_khp=64)                                                                  
    print ('rd_ana_top_d2a_ch3_regnamedel15_adp_gearshift_minstep_khp is: ', q68.c2m.rd_ana_top_d2a_ch2_regnamedel15_adp_gearshift_minstep_khp()) 
    q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel = 0x0C)  
    print('rx_polar_sel', q68.c2m.rd_test_rx_link_data_inv_rx_polar_sel())                                                                                                                                         
    q68.C3BCTx3_MINMAX() 
    q68.c2m.wr_tx_link3_phy_ana_ctrl4_fields(maxmag_auto_txbc=90) 
    
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel4_fields(adp_h0_min=20)   
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel4_fields(adp_h0_min=20)   
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel4_fields(adp_h0_min=20)   
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel4_fields(adp_h0_min=20) 
      
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel121_fields(cdr_picode_step_gray=2)     
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel121_fields(cdr_picode_step_gray=2)     
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel121_fields(cdr_picode_step_gray=2)     
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel121_fields(cdr_picode_step_gray=2)
    
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel85_fields(afe_vga_bias=0xa)          
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel85_fields(afe_vga_bias=0xa)          
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel85_fields(afe_vga_bias=0xa)          
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel85_fields(afe_vga_bias=0xa)          
    
    q68.RXPHYLDOGroupSet(termldo=7, afeldo=3, desldo=4, ckldo=5, bctxldo=4) 
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel87_fields(afe_buffer_bias=0xd)  
    
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel87_fields(afe_buffer_bias=0xd) 
    
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel87_fields(afe_buffer_bias=0xd)  
    
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel87_fields(afe_buffer_bias=0xd)        
    q68.PHYAllC3Reset()  # 替换为reset PLL + link reset
    
                                    
    print('link0 status is: ', q68.c2m.rd_test_fsm_status1_link0()) 
    print('link1 status is: ', q68.c2m.rd_test_fsm_status1_link1()) 
    print('link2 status is: ', q68.c2m.rd_test_fsm_status2_link2()) 
    print('link3 status is: ', q68.c2m.rd_test_fsm_status2_link3())
    
def Q68_C3_3G_Init():    
    q68.c2m.wr_test_glb_ctrl0_fields(key=0x5C)   
    
    q68.c2m.wr_tx_link0_phy_ana_ctrl4_fields(maxmag_auto_txbc=127)  # d2a_chx_afe_reserve4<1>对应minmag<6>,d2a_chx_afe_reserve4<2>对应maxmag<6>     
    # q68.c2m.wr_ana_top_d2a_ch0_regnamedel112_fields(txbc_timestep_auto_bus1=0xff)                                                               
    # q68.c2m.wr_ana_top_d2a_ch0_regnamedel111_fields(txbc_timestep_auto_bus0=0xff)   
                                                              
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel0_fields(adp_delay_xt=0)
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel84_fields(afe_ahp_csel=1)       
                                                                                                                                                        
    q68.C3BCTx0_MINMAX()  
    
    q68.c2m.wr_tx_link1_phy_ana_ctrl4_fields(maxmag_auto_txbc=127)  # d2a_chx_afe_reserve4<1>对应minmag<6>,d2a_chx_afe_reserve4<2>对应maxmag<6>                                                                                                          
    # q68.c2m.wr_ana_top_d2a_ch1_regnamedel112_fields(txbc_timestep_auto_bus1=0xff)                                                                 
    # q68.c2m.wr_ana_top_d2a_ch1_regnamedel111_fields(txbc_timestep_auto_bus0=0xff)                                                                                                                                    
    
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel0_fields(adp_delay_xt=0)
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel84_fields(afe_ahp_csel=1)       
    
    q68.C3BCTx1_MINMAX()  
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel0_fields(adp_delay_xt=0)                                                                                  
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel84_fields(afe_ahp_csel=1)                                                                                 
    
    q68.C3BCTx2_MINMAX()  
    
    q68.c2m.wr_tx_link2_phy_ana_ctrl4_fields(maxmag_auto_txbc=127)  # d2a_chx_afe_reserve4<1>对应minmag<6>,d2a_chx_afe_reserve4<2>对应maxmag<6>  
    
    # q68.c2m.wr_ana_top_d2a_ch2_regnamedel112_fields(txbc_timestep_auto_bus1=0xff)                                                                                                
    # q68.c2m.wr_ana_top_d2a_ch2_regnamedel111_fields(txbc_timestep_auto_bus0=0xff) 
    
    q68.c2m.wr_tx_link3_phy_ana_ctrl4_fields(maxmag_auto_txbc=127)  # d2a_chx_afe_reserve4<1>对应minmag<6>,d2a_chx_afe_reserve4<2>对应maxmag<6>                                                                                                          
    # q68.c2m.wr_ana_top_d2a_ch3_regnamedel112_fields(txbc_timestep_auto_bus1=0xff)                                                                 
    # q68.c2m.wr_ana_top_d2a_ch3_regnamedel111_fields(txbc_timestep_auto_bus0=0xff) 
    
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel0_fields(adp_delay_xt=0)                                                                                 
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel84_fields(afe_ahp_csel=1)       
    
    q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel = 0x0C)  
    print('rx_polar_sel', q68.c2m.rd_test_rx_link_data_inv_rx_polar_sel())                                                                                                                                         
    q68.C3BCTx3_MINMAX()  
    
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel4_fields(adp_h0_min=20)   
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel4_fields(adp_h0_min=20)   
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel4_fields(adp_h0_min=20)   
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel4_fields(adp_h0_min=20) 

    q68.c2m.wr_ana_top_d2a_ch0_regnamedel121_fields(cdr_picode_step_gray=2)     
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel121_fields(cdr_picode_step_gray=2)     
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel121_fields(cdr_picode_step_gray=2)     
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel121_fields(cdr_picode_step_gray=2)
    
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel85_fields(afe_vga_bias=0xa)          
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel85_fields(afe_vga_bias=0xa)          
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel85_fields(afe_vga_bias=0xa)          
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel85_fields(afe_vga_bias=0xa)          
    
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch0_regnamedel87_fields(afe_buffer_bias=0xd)  
    
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch1_regnamedel87_fields(afe_buffer_bias=0xd) 
    
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch2_regnamedel87_fields(afe_buffer_bias=0xd)  
    
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel88_fields(afe_buffer_vcm = 2)        
    q68.c2m.wr_ana_top_d2a_ch3_regnamedel87_fields(afe_buffer_bias=0xd) 
    
    q68.RXPHYLDOGroupSet(termldo=7, afeldo=3, desldo=4, ckldo=5, bctxldo=4)                                                                                                                                                                                                                                                       
    q68.PHYAllC3Reset()
    print('link0 status is: ', q68.c2m.rd_test_fsm_status1_link0()) 
    print('link1 status is: ', q68.c2m.rd_test_fsm_status1_link1()) 
    print('link2 status is: ', q68.c2m.rd_test_fsm_status2_link2()) 
    print('link3 status is: ', q68.c2m.rd_test_fsm_status2_link3()) 

def S68Init():
    (forwardtxrate, reverserxrate) = s68_0.PHYLinkRate(forwardtxrate=2, reverserxrate=3)
    time.sleep(0.1) 
    s68_0.m2c.wr_i2c0_ctrl6_fields(rem_acc_en=1)
    time.sleep(0.1) 
    print('rem_acc_en is: ', s68_0.m2c.rd_i2c0_ctrl6_rem_acc_en())
    s68_0.m2c.wr_ana_d2a_bcrx_regnamedel1_fields(lpfc_dacctr=195)
    s68_0.m2c.wr_rcc_rst_link_fields(auto_en=0)  
    time.sleep(0.5)                            
    s68_0.m2c.wr_rcc_rst_link_fields(auto_en=1)
    
    (forwardtxrate, reverserxrate) = s68_1.PHYLinkRate(forwardtxrate=2, reverserxrate=3)
    time.sleep(0.1)
    s68_1.m2c.wr_i2c0_ctrl6_fields(rem_acc_en=1)
    time.sleep(0.1) 
    print('rem_acc_en is: ', s68_1.m2c.rd_i2c0_ctrl6_rem_acc_en())
    # s68_1.m2c.wr_ana_d2a_bcrx_regnamedel1_fields(lpfc_dacctr=174)
    s68_1.m2c.wr_rcc_rst_link_fields(auto_en=0)  
    time.sleep(0.5)                            
    s68_1.m2c.wr_rcc_rst_link_fields(auto_en=1)
    
    # (forwardtxrate, reverserxrate) = s68_2.PHYLinkRate(forwardtxrate=2, reverserxrate=3)
    # time.sleep(0.1) 
    # s68_2.m2c.wr_i2c0_ctrl6_fields(rem_acc_en=1)
    # time.sleep(0.1) 
    # print('rem_acc_en is: ', s68_2.m2c.rd_i2c0_ctrl6_rem_acc_en())
    # s68_2.m2c.wr_ana_d2a_bcrx_regnamedel1_fields(lpfc_dacctr=178)
    # s68_2.m2c.wr_rcc_rst_link_fields(auto_en=0)  
    # time.sleep(0.5)                            
    # s68_2.m2c.wr_rcc_rst_link_fields(auto_en=1)
    #
    # (forwardtxrate, reverserxrate) = s68_3.PHYLinkRate(forwardtxrate=2, reverserxrate=3)
    # time.sleep(0.1) 
    # s68_3.m2c.wr_i2c0_ctrl6_fields(rem_acc_en=1)
    # time.sleep(0.1) 
    # print('rem_acc_en is: ', s68_3.m2c.rd_i2c0_ctrl6_rem_acc_en())
    # s68_3.m2c.wr_ana_d2a_bcrx_regnamedel1_fields(lpfc_dacctr=178)
    # s68_3.m2c.wr_rcc_rst_link_fields(auto_en=0)  
    # time.sleep(0.5)                            
    # s68_3.m2c.wr_rcc_rst_link_fields(auto_en=1)                  
# def PowerMeause(powertype='keithley2230G',power_instr=Gpp433):
#     if powertype=='keithley2230G':
#         curr_vdd10 = power_instr.GetCurrent1()
#         time.sleep(0.2)
#         curr_vddio = power_instr.GetCurrent2()
#         time.sleep(0.2)
#         curr_vdd18 = power_instr.GetCurrent3()
#         time.sleep(0.2)
#         vol_measure_vdd10 = power_instr.GetVoltage1()
#         time.sleep(0.2)
#         vol_measure_vddio = power_instr.GetVoltage2()
#         time.sleep(0.2)
#         vol_measure_vdd18 = power_instr.GetVoltage3()
#         power_total =  curr_vdd10*vol_measure_vdd10+curr_vddio*vol_measure_vddio+curr_vdd18*vol_measure_vdd18
#         time.sleep(0.2)
#         print('measured channel1 voltage is ',vol_measure_vdd10,', and measured channel1 current is',curr_vdd10)
#         print('measured channel2 voltage is ',vol_measure_vddio,', and measured channel2 current is',curr_vddio)
#         print('measured channel3 voltage is ',vol_measure_vdd18,', and measured channel3 current is',curr_vdd18)
#         print('measured total power consumption is ',power_total)
#     elif powertype=='IT6322B':
#         (vol_measure_vdd10, curr_vdd10) = power_instr.Meas_OneChannel(channel=1)
#         time.sleep(0.2)
#         (vol_measure_vddio, curr_vddio) = power_instr.Meas_OneChannel(channel=2)
#         time.sleep(0.2)
#         (vol_measure_vdd18, curr_vdd18) = power_instr.Meas_OneChannel(channel=3)
#         time.sleep(0.2)
#         power_total =  curr_vdd10*vol_measure_vdd10+curr_vddio*vol_measure_vddio+curr_vdd18*vol_measure_vdd18
#         time.sleep(0.2)
#         print('measured total power consumption is ',power_total)
#     else:
#         raise('power type error')
#
#
#     return(curr_vdd10,curr_vddio,curr_vdd18,vol_measure_vdd10,vol_measure_vddio,vol_measure_vdd18,power_total)
    
    
def PRBSCheck():
    with open(data_address, "a", encoding="utf-8", newline="") as f:
        csv_writer = csv.writer(f)
        name=['time','BOARDID','CHIPID','loop','loop1','starttemp','temp_read','tempdrift','current_measure_gpp433','curr_vdd10_q68','curr_vddio_q68','curr_vdd18_q68','vol_measure_vdd10_q68','vol_measure_vddio_q68','vol_measure_vdd18_q68','power_total_q68',\
            'q68_plllock','q68_vco_band_cali_done','q68_vcoband','frame_count0_st','frame_count1_st','frame_count2_st','frame_count3_st','frame_count0', 'frame_count1','frame_count2','frame_count3','line_count0_st','line_count1_st','line_count2_st','line_count3_st','line_count0','line_count1','line_count2','line_count3',\
            'link0_status','link1_status','link2_status','link3_status','video_lock','mdi_status0', 'mdi_status1','mdi_status2','mdi_status3',\
            'line_status0','line_status1','line_status2','line_status3','line_status4','line_status5','line_status6','line_status7','link0_tx_status0','link1_tx_status0','link2_tx_status0','link3_tx_status0','link0_tx_status1','link1_tx_status1','link2_tx_status1','link3_tx_status1',\
            'link0_tx_status2','link1_tx_status2','link2_tx_status2','link3_tx_status2','link0_rx_status0','link1_rx_status0','link2_rx_status0','link3_rx_status0','link0_rx_status2','link1_rx_status2','link2_rx_status2','link3_rx_status2','link0_rx_status7','link1_rx_status7','link2_rx_status7','link3_rx_status7',\
            'rd_bmag_txbc_ch0','adp_khp_ch0','adp_h0_ch0','adp_a0_ch0','sel_afe_offsetcali_ch0','dcali_sb_afe_ch0','slicer0_forecali_ch0','slicer0_dcali_sb_ch0','slicer90_dcali_sb_ch0','slicer180_dcali_ch0','slicer270_dcali_sb_ch0','slicererr0_dcali_sb_ch0','slicererr180_dcali_sb_ch0','adp_b1_ch0','adp_b2_ch0',\
            'adp_b3_ch0','adp_t0_ch0','adp_t1_ch0','adp_t2_ch0','adp_t3_ch0','adp_t4_ch0','adp_t5_ch0','adp_t6_ch0','adp_t7_ch0','adp_t8_ch0','adp_t9_ch0','cdr_picode_a_ch0','cdr_picode_b_ch0','cdr_picode_c_ch0',\
            'rd_bmag_txbc_ch1','adp_khp_ch1','adp_h0_ch1','adp_a0_ch1','sel_afe_offsetcali_ch1','dcali_sb_afe_ch1','slicer0_forecali_ch1','slicer0_dcali_sb_ch1','slicer90_dcali_sb_ch1','slicer180_dcali_ch1','slicer270_dcali_sb_ch1','slicererr0_dcali_sb_ch1','slicererr180_dcali_sb_ch1',\
            'adp_b1_ch1','adp_b2_ch1','adp_b3_ch1','adp_t0_ch1','adp_t1_ch1','adp_t2_ch1','adp_t3_ch1','adp_t4_ch1','adp_t5_ch1','adp_t6_ch1','adp_t7_ch1','adp_t8_ch1','adp_t9_ch1','cdr_picode_a_ch1','cdr_picode_b_ch1','cdr_picode_c_ch1',\
            'rd_bmag_txbc_ch2','adp_khp_ch2','adp_h0_ch2','adp_a0_ch2','sel_afe_offsetcali_ch2','dcali_sb_afe_ch2','slicer0_forecali_ch2','slicer0_dcali_sb_ch2','slicer90_dcali_sb_ch2','slicer180_dcali_ch2','slicer270_dcali_sb_ch2',\
            'slicererr0_dcali_sb_ch2','slicererr180_dcali_sb_ch2','adp_b1_ch2','adp_b2_ch2','adp_b3_ch2','adp_t0_ch2','adp_t1_ch2','adp_t2_ch2','adp_t3_ch2','adp_t4_ch2','adp_t5_ch2','adp_t6_ch2','adp_t7_ch2','adp_t8_ch2','adp_t9_ch2','cdr_picode_a_ch2','cdr_picode_b_ch2','cdr_picode_c_ch2',\
            'rd_bmag_txbc_ch3','adp_khp_ch3','adp_h0_ch3','adp_a0_ch3','sel_afe_offsetcali_ch3','dcali_sb_afe_ch3','slicer0_forecali_ch3','slicer0_dcali_sb_ch3','slicer90_dcali_sb_ch3','slicer180_dcali_ch3','slicer270_dcali_sb_ch3',\
            'slicererr0_dcali_sb_ch3','slicererr180_dcali_sb_ch3','adp_b1_ch3','adp_b2_ch3','adp_b3_ch3','adp_t0_ch3','adp_t1_ch3','adp_t2_ch3','adp_t3_ch3','adp_t4_ch3','adp_t5_ch3','adp_t6_ch3','adp_t7_ch3','adp_t8_ch3','adp_t9_ch3','cdr_picode_a_ch3','cdr_picode_b_ch3','cdr_picode_c_ch3',\
            'rate0','rate1','rate2','rate3', 'bcrxrate0','bcrxrate1','bcrxrate2','bcrxrate3',\
            'link0_rx_status0_remote_tx_fail','link0_rx_status0_local_rx_fail','link0_rx_status0_pkt_timeout','link0_rx_status0_video_sn_err','link0_rx_status0_intf_fifo_uv','link0_rx_status0_intf_fifo_ov',\
            'link0_rx_status1_pkt_crc_err','link0_rx_status2_i2c_cfg_err','link0_rx_status2_sop_kchar_dup_8b10b','link0_rx_status2_sop_kchar_err_8b10b','link0_rx_status2_pkt_type_illegal','link0_rx_status2_pp_fifo_full',\
            'link0_rx_status2_dec_fault','link0_rx_status2_sync_fault','link0_rx_status3_pkt_len_err','link0_rx_status4_pkt_crc_fifo_full','link0_rx_status5_pkt_data_fifo_full','link0_rx_status6_pkt_ack_crc_err',\
            'link0_rd_efh_tx_status0','link0_rd_efh_tx_status1','link0_rd_efh_tx_status2','link0_rd_efh_tx_status3','link0_rd_efh_mipi_rx_status0',\
            'link1_rx_status0_remote_tx_fail','link1_rx_status0_local_rx_fail','link1_rx_status0_pkt_timeout','link1_rx_status0_video_sn_err','link1_rx_status0_intf_fifo_uv','link1_rx_status0_intf_fifo_ov',\
            'link1_rx_status1_pkt_crc_err','link1_rx_status2_i2c_cfg_err','link1_rx_status2_sop_kchar_dup_8b10b','link1_rx_status2_sop_kchar_err_8b10b','link1_rx_status2_pkt_type_illegal','link1_rx_status2_pp_fifo_full',\
            'link1_rx_status2_dec_fault','link1_rx_status2_sync_fault','link1_rx_status3_pkt_len_err','link1_rx_status4_pkt_crc_fifo_full','link1_rx_status5_pkt_data_fifo_full','link1_rx_status6_pkt_ack_crc_err',\
            'link1_rd_efh_tx_status0','link1_rd_efh_tx_status1','link1_rd_efh_tx_status2','link1_rd_efh_tx_status3','link1_rd_efh_mipi_rx_status0',\
            'link2_rx_status0_remote_tx_fail','link2_rx_status0_local_rx_fail','link2_rx_status0_pkt_timeout','link2_rx_status0_video_sn_err','link2_rx_status0_intf_fifo_uv','link2_rx_status0_intf_fifo_ov',\
            'link2_rx_status1_pkt_crc_err','link2_rx_status2_i2c_cfg_err','link2_rx_status2_sop_kchar_dup_8b10b','link2_rx_status2_sop_kchar_err_8b10b','link2_rx_status2_pkt_type_illegal','link2_rx_status2_pp_fifo_full',\
            'link2_rx_status2_dec_fault','link2_rx_status2_sync_fault','link2_rx_status3_pkt_len_err','link2_rx_status4_pkt_crc_fifo_full','link2_rx_status5_pkt_data_fifo_full','link2_rx_status6_pkt_ack_crc_err',\
            'link2_rd_efh_tx_status0','link2_rd_efh_tx_status1','link2_rd_efh_tx_status2','link2_rd_efh_tx_status3','link2_rd_efh_mipi_rx_status0',\
            'link3_rx_status0_remote_tx_fail','link3_rx_status0_local_rx_fail','link3_rx_status0_pkt_timeout','link3_rx_status0_video_sn_err','link3_rx_status0_intf_fifo_uv','link3_rx_status0_intf_fifo_ov',\
            'link3_rx_status1_pkt_crc_err','link3_rx_status2_i2c_cfg_err','link3_rx_status2_sop_kchar_dup_8b10b','link3_rx_status2_sop_kchar_err_8b10b','link3_rx_status2_pkt_type_illegal','link3_rx_status2_pp_fifo_full',\
            'link3_rx_status2_dec_fault','link3_rx_status2_sync_fault','link3_rx_status3_pkt_len_err','link3_rx_status4_pkt_crc_fifo_full','link3_rx_status5_pkt_data_fifo_full','link3_rx_status6_pkt_ack_crc_err',\
            'link3_rd_efh_tx_status0','link3_rd_efh_tx_status1','link3_rd_efh_tx_status2','link3_rd_efh_tx_status3','link3_rd_efh_mipi_rx_status0',\
            'vcid_s680','data_type_s680','word_count_s680','err_lane0_s680','err_lane1_s680','err_lane2_s680','err_lane3_s680','err_lane4_s680','err_ecc_s680', 'err_crc_s680','vcid_s681','data_type_s681','word_count_s681','err_lane0_s681','err_lane1_s681','err_lane2_s681','err_lane3_s681','err_lane4_s681','err_ecc_s681', 'err_crc_s681',\
            'vcid_s682','data_type_s682','word_count_s682','err_lane0_s682','err_lane1_s682','err_lane2_s682','err_lane3_s682','err_lane4_s682','err_ecc_s682', 'err_crc_s682','vcid_s683','data_type_s683','word_count_s683','err_lane0_s683','err_lane1_s683','err_lane2_s683','err_lane3_s683','err_lane4_s683','err_ecc_s683', 'err_crc_s683',\
            's68_link0_header_ecc_error','s68_link0_header_ecc_check_en','s68_link0_error_correct0','s68_link0_error_correct1','s68_link0_error_correct2','s68_link0_error_correct3',\
            's68_link1_header_ecc_error','s68_link1_header_ecc_check_en','s68_link1_error_correct0','s68_link1_error_correct1','s68_link1_error_correct2','s68_link1_error_correct3',\
            's68_link2_header_ecc_error','s68_link2_header_ecc_check_en','s68_link2_error_correct0','s68_link2_error_correct1','s68_link2_error_correct2','s68_link2_error_correct3',\
            's68_link3_header_ecc_error','s68_link3_header_ecc_check_en','s68_link3_error_correct0','s68_link3_error_correct1','s68_link3_error_correct2','s68_link3_error_correct3',\
            's680_dlysetting', 's680_hs_settle_time', 's680_ls_settle_time','s681_dlysetting', 's681_hs_settle_time', 's681_ls_settle_time',\
            's682_dlysetting', 's682_hs_settle_time', 's682_ls_settle_time','s683_dlysetting', 's683_hs_settle_time', 's683_ls_settle_time',\
            's680_lpf','s681_lpf','s682_lpf','s683_lpf']
        
        csv_writer.writerow(name)
        for skew in SKEW:
            for vdd10,vddio,vdd18 in VOLTAGE:

                for starttemp in STEMP:
                    
                    # Gpp433.PowerOn(channel=4, power='OFF')
                    # time.sleep(0.2)
                    # Keithley2230G1.TurnOutputsOff()                    
                    # time.sleep(0.2)
                    # oven.settemp(starttemp)
                    # # oven.start_stop()
                    # temp_measure=oven.tempread()
                    # while ((abs((temp_measure-starttemp)))>2):
                    #     time.sleep(30)
                    #     temp_measure=oven.tempread()
                    # time.sleep(10)                                                   
                    print("vdd10 = ",vdd10) 
                    print("vdd18 = ",vdd18)
                    print("vddio = ",vddio)
                    print("voltyp = ",'sense_supply') 
                    
                    
                    # Gpp433.VoltageSet(channel=4, voltage=12)
                    # time.sleep(2)
                    
                    # Keithley2230G1.TurnOutputsOff()                    
                    # time.sleep(3)
                    # Gpp433.PowerOn(channel=4, power='ON')
                    #
                    # Keithley2230G1.TurnOutputsOn()
                    # time.sleep(0.2)
                    # Keithley2230G1.SetVoltages(v1=vdd10, v2=vddio, v3=vdd18)  # provide supply
                    # Keithley2230G1.SetCurrents(i1=1.5, i2=0.5, i3=0.5) 
                    # time.sleep(0.1)
                    # q68.CommonInit() 
                    # time.sleep(0.1)   
                    # q68.c2m.wr_link_init_ctrl12_fields(reset_async_vcobandcali_rxphypll = 1)
                    # time.sleep(0.1)
                    # q68.c2m.wr_link_init_ctrl12_fields(reset_async_vcobandcali_rxphypll = 0)
                    time.sleep(1)
                    q68.dongle.setBusSpeed(1, 100)
                    q68.CommonInit() 
                    
                    q68.PHYLinkRateSet(forwardrxrate=2, reversetxrate=0)
                    # q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=1)
                    # q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=1)
                    q68.PHYPLLStatus()
                    # Q68_C3_6G_Init() #for 6G init
                    # S68Init()
                    Q68_C3_6G_Init() #for 3G init
                    
                    time.sleep(1)
                    q68.PHYPLLStatus()

                    for pllinternaldo in PLLINTERNALLDO:
                    
                        q68.c2m.wr_test_glb_ctrl0_fields(key=0x5C) 
                        
                        print('link0 status is: ', q68.c2m.rd_test_fsm_status1_link0())
                        print('link1 status is: ', q68.c2m.rd_test_fsm_status1_link1())
                        print('link2 status is: ', q68.c2m.rd_test_fsm_status2_link2())
                        print('link3 status is: ', q68.c2m.rd_test_fsm_status2_link3())
                        print('gpio link ctrl is: ', q68.c2m.rd_sys_cfg_gpio_link_ctrl_gpio_link_enable())
                        print('sysreg_hf_clk_sel is: ', q68.c2m.rd_rcc_clock_ctrl3_sysreg_hf_clk_sel())
                        q68.c2m.wr_rcc_clock_ctrl8_fields(i=0,uart_clk_sel = 2)   
                        q68.c2m.wr_rcc_clock_ctrl8_fields(i=1,uart_clk_sel = 2)   
                        q68.c2m.wr_rcc_clock_ctrl8_fields(i=2,uart_clk_sel = 2) 
                        
                        q68_remote.UARTRemoteConfig(devAddr=q68_iic_add, linkid=2) 
                        q68_remote.dongle.devAddr=0x40                                
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        # q68_remote.m2c.wr_gpios_ctrl0_fields(fwd_dly = 4) 
                        q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly = 0)
                        q68_remote.M2CMFNSet(gpio=0, mfn=0)
                        q68_remote.M2CGPIORemoteRx(0)
                        
                        q68_remote.M2CMFNSet(gpio=3, mfn=0)
                        q68_remote.M2CGPIORemoteRx(3)
                        
                        q68_remote.M2CMFNSet(gpio=4, mfn=0)
                        q68_remote.M2CGPIORemoteRx(4)
                        
                        q68_remote.M2CMFNSet(gpio=5, mfn=0)
                        q68_remote.M2CGPIORemoteRx(5)
                        
                        q68_remote.M2CMFNSet(gpio=6, mfn=0)
                        q68_remote.M2CGPIORemoteRx(6)
                        
                        q68_remote.M2CMFNSet(gpio=7, mfn=0)
                        q68_remote.M2CGPIORemoteRx(7)
                        
                        q68_remote.M2CMFNSet(gpio=8, mfn=0)
                        q68_remote.M2CGPIORemoteRx(8)
                        
                        q68.c2m.wr_rcc_clock_ctrl4_fields(sysreg_div_cfg = 1)
                        # q68.c2m.wr_gpios_gpio_c_fields(i=10, man_trans =0)
                        # q68.c2m.wr_gpios_gpio_c_fields(i=10, man_trans =1)
                        # q68.c2m.wr_gpios_gpio_c_fields(i=10, man_trans =0)
                        q68.MFNSet(gpio=14, mfn=0)
                        # q68.c2m.wr_gpios_ctrl0_fields(fwd_dly = 63) 
                        # q68.c2m.wr_gpios_ctrl1_fields(rvs_dly = 63)
                        q68.MFNSet(gpio=2, mfn=1)
                        q68.FrameSyncOutConifg(per_div=0x0b, period=17361, fs_tx_id=11, auto_fs=1)  # q68 frame sync enable
                        # q68.GPIORemoteTx(gpio=14, tx_id=11, link_id=2, dly_comp_en=0)
                        
                         
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=3, link1=0, link2=0, link3=0) 
                        q68_remote.dongle.devAddr = s680_iic_dev
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()    
                        q68_remote.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  
                        q68_remote.m2c.wr_ana_d2a_bcrx_regnamedel11_fields(lpf_csel_txn_lpf = 0,lpf_csel_txp_lpf=0)
                        q68_remote.M2CPLLBandCalStatus()
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel15_fields(dldo_vtune=6)
                        print('dldo_vtune is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel15_dldo_vtune()) 
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel19_fields(vco_var=0x3)
                        q68_remote.m2c.wr_ana_d2a_tx_regnamedel4_fields(idriver = 7)
                        print('vco_var is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_var())
                        q68_remote.M2CMFNSet(gpio=0, mfn=0) #set gpio0 to gpio mode
                        q68_remote.M2CGPIORemoteTx(gpio=0, tx_id=1) #set gpio0 tx_id to 1
                        
                        
                        
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                        # time.sleep(1)                                                             
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0) 
                        
                        # q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel16_fields(cp_vset = 9)
                        # print('cp_vset is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_vset())
                        # time.sleep(0.1)
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=1)  
                        # time.sleep(0.3) 
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=0) 
                        q68_remote.M2CPLLBandCalStatus()
                        
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        print('pcs_sel is: ', q68_remote.m2c.rd_tx_link_phy_phy_ctrl0_pcs_sel())
                        
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        
                        # q68_remote.M2CPHYLinkRate(forwardtxrate=1, reverserxrate=3)
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=0)  
                        # time.sleep(0.5)                            
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=1)
                        # q68_remote.M2CPHYLinkRate(forwardtxrate=2, reverserxrate=3)
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=0)  
                        # time.sleep(0.5)                            
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=1)
                        
                        
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=3, link2=0, link3=0) 
                        q68_remote.dongle.devAddr = 0x40
                        time.sleep(0.1)
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        q68_remote.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  
                        q68_remote.m2c.wr_ana_d2a_bcrx_regnamedel11_fields(lpf_csel_txn_lpf = 0,lpf_csel_txp_lpf=0)
                        q68_remote.M2CPLLBandCalStatus()
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel15_fields(dldo_vtune=6)
                        print('dldo_vtune is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel15_dldo_vtune()) 
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel19_fields(vco_var=0x3)
                        q68_remote.m2c.wr_ana_d2a_tx_regnamedel4_fields(idriver = 7)
                        print('vco_var is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_var())
                        q68_remote.M2CMFNSet(gpio=0, mfn=0) #set gpio0 as gpio pin
                        q68_remote.M2CGPIORemoteTx(gpio=0, tx_id=2) #set gpio0 tx_id to 2
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                        # time.sleep(1)                                                             
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0) 
                        # q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel16_fields(cp_vset = 9)
                        # print('cp_vset is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_vset())
                        # time.sleep(0.1)
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=1)  
                        # time.sleep(0.3) 
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=0) 
                        q68_remote.M2CPLLBandCalStatus()
                         
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        #q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel15_fields(dldo_vtune=6)
                        # q68_remote.M2CPHYLinkRate(forwardtxrate=1, reverserxrate=3)
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=0)  
                        # time.sleep(0.5)                            
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=1)
                        
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0, link2=3, link3=0) 
                        q68_remote.dongle.devAddr = s682_iic_dev 
                        time.sleep(0.1)
                        q68_remote.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53) 
                        q68_remote.M2CPLLBandCalStatus()
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel15_fields(dldo_vtune=6)
                        print('dldo_vtune is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel15_dldo_vtune()) 
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel19_fields(vco_var=0x3)
                        q68_remote.m2c.wr_ana_d2a_tx_regnamedel4_fields(idriver = 7)
                        print('vco_var is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_var())
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                        # time.sleep(1)                                                             
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0) 
                        q68_remote.m2c.wr_ana_d2a_bcrx_regnamedel11_fields(lpf_csel_txn_lpf = 0,lpf_csel_txp_lpf=0)
                        # print('cp_vset is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_vset())
                        # time.sleep(0.1)
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=1)  
                        # time.sleep(0.3) 
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=0) 
                        q68_remote.M2CPLLBandCalStatus()
                        q68_remote.M2CMFNSet(gpio=3, mfn=0) #set gpio3 as gpio pin
                        q68_remote.M2CGPIORemoteTx(gpio=3, tx_id=3) #set gpio3 tx_id to 3
                        # q68_remote.M2CPHYLinkRate(forwardtxrate=1, reverserxrate=3)
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=0)  
                        # time.sleep(0.5)                            
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=1)
                        # q68_remote.dongle.devAddr = q68_iic_add 
                        # q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=1)
                        
                        
                        # q68_remote.M2CPHYLinkRate(forwardtxrate=1, reverserxrate=3)
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=0)  
                        # time.sleep(0.5)                            
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=1)
                        
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0, link2=0, link3=3) 
                        q68_remote.dongle.devAddr = 0x44 
                        time.sleep(0.1)
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        q68_remote.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  
                        q68_remote.m2c.wr_ana_d2a_bcrx_regnamedel11_fields(lpf_csel_txn_lpf = 0,lpf_csel_txp_lpf=0)
                        q68_remote.M2CPLLBandCalStatus()
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel15_fields(dldo_vtune=6)
                        print('dldo_vtune is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel15_dldo_vtune())
                        q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel19_fields(vco_var=0x3)
                        q68_remote.m2c.wr_ana_d2a_tx_regnamedel4_fields(idriver = 7)
                        print('vco_var is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_var()) 
                        q68_remote.M2CMFNSet(gpio=3, mfn=0) #set gpio3 as gpio pin
                        q68_remote.M2CGPIORemoteTx(gpio=3, tx_id=4) #set gpio3 tx_id to 4
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                        # time.sleep(1)                                                             
                        # q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0) 
                        # q68_remote.m2c.wr_ana_d2a_linkpll_regnamedel16_fields(cp_vset = 9)
                        # print('cp_vset is: ', q68_remote.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_vset())
                        # time.sl
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=1)  
                        # time.sleep(0.3) 
                        # q68_remote.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=0) 
                        q68_remote.M2CPLLBandCalStatus()
                        
                        print('pcs_sel is: ', q68_remote.m2c.rd_tx_link_phy_phy_ctrl0_pcs_sel())
                    
                        print('rx_link_pp_gpio_ctrl0_crc_en is: ', q68_remote.m2c.rd_rx_link_pp_gpio_ctrl0_crc_en())
                        print('rx_link_pp_gpio_ctrl0_arq_en is: ', q68_remote.m2c.rd_rx_link_pp_gpio_ctrl0_arq_en())
                        
                        # q68_remote.M2CPHYLinkRate(forwardtxrate=1, reverserxrate=3)
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=0)  
                        # time.sleep(0.5)                            
                        # q68_remote.m2c.wr_rcc_rst_link_fields(auto_en=1)
                        
                        '''c2m c3 init'''
                        q68_remote.dongle.devAddr = q68_iic_add 
                        # q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=2, rate2=2, rate1=2, rate0=2)
                        rate0 = q68.c2m.rd_sys_cfg_link_ctrl1_rate0()
                        print('link0 forward rx data rate setting: ', rate0)
                        # (rate0,rate1,rate2,rate3, bcrxrate0,bcrxrate1,bcrxrate2,bcrxrate3) = q68.PHYLinkRateSet(forwardrxrate=1, reversetxrate=0)
                        #Q68_C3_3G_Init()
                        q68.c2m.wr_rcc_reset_ctrl3_fields(link_soft_rstn = 0)       
                        time.sleep(0.5)                                         
                        q68.c2m.wr_rcc_reset_ctrl3_fields(link_soft_rstn = 0xf)  
                        time.sleep(0.2)  
                        print('link0 status is: ', q68.c2m.rd_test_fsm_status1_link0())
                        print('link1 status is: ', q68.c2m.rd_test_fsm_status1_link1())
                        print('link2 status is: ', q68.c2m.rd_test_fsm_status2_link2())
                        print('link3 status is: ', q68.c2m.rd_test_fsm_status2_link3())
                        # Reset LINK related logic and auto release Active low. Self cleared.
                        '''c2m router config'''
                        q68.c2m.wr_fh_clr_ctrl0(value=0xF)
                        q68.c2m.wr_fh_clr_ctrl0(value=0x0)
                        
                        q68.MFNSet(gpio=5, mfn=0) #set gpio5 as gpio mode
                        q68.c2m.wr_pinmux_regmap_gpio_soft_ctrl4_fields(i=5, gpio_oen = 0, gpio_pull_sel = 0, gpio_drv_type = 1) #set gpio5 to output mode
                        q68.GPIORemoteLogicOut(logic_fun=0, gpio_out=5) #set logic function 0 output to gpio5
                        
                        q68.GPIORemoteRx(gpio=19, rx_id = 1) #set gpio19 rx id to 1 to get receive data from s680 gpio0
                        q68.GPIORemoteRx(gpio=20, rx_id = 2) #set gpio20 rx id to 1 to get receive data from s681 gpio0
                        
                        
                        
                        q68.GPIORemoteLogic(gpio=19, logic_gpio=0, logic_fun=0, logic_mode=0, c2mlogicinv=0) #set gpio19 map to logic function0 to logic0
                        q68.GPIORemoteLogic(gpio=20, logic_gpio=1, logic_fun=0, logic_mode=0, c2mlogicinv=0) #set gpio20 map to logic function0 to logic1
                        q68.GPIORemoteLogic(gpio=20, logic_gpio=2, logic_fun=0, logic_mode=3, c2mlogicinv=0) #logic function0 to logic2 map to 1
                        q68.GPIORemoteLogic(gpio=20, logic_gpio=3, logic_fun=0, logic_mode=3, c2mlogicinv=0) #logic function0 to logic3 map to 1
                        
                        # q68.GPIORemoteLogic(gpio=19, logic_gpio=0, logic_fun=0, logic_mode=0, c2mlogicinv=0)
                        # q68.GPIORemoteLogic(gpio=20, logic_gpio=1, logic_fun=0, logic_mode=0, c2mlogicinv=0)
                        
                        
                        
                        q68.EfhStatusClr()
                        q68.EfhTRxStatusCheck(i=0)
                        q68.EfhTRxStatusCheck(i=1)
                        
                        for i in range(8):
                            q68.EfhRouterStatusCheck(i)
                    
                        q68.devAddr = q68_iic_add
                        q68.RouterClkSel(sel = 0)
                    
                        q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl35_fields(fatal_err_mask=1)
                        for i in range(4):
                            q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl18_fields(i=i, videoparser_data_pkt_fatal_err_en=0)  
                        # q68.c2m.wr_rx_link0_pp_video_ctrl0_fields(csi_dt_pass_en= 1)
                        # q68.c2m.wr_rx_link1_pp_video_ctrl0_fields(csi_dt_pass_en= 1)
                        # q68.c2m.wr_rx_link2_pp_video_ctrl0_fields(csi_dt_pass_en= 1)
                        # q68.c2m.wr_rx_link3_pp_video_ctrl0_fields(csi_dt_pass_en= 1)
                        # q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl10_fields(i=7, pipex_buf_ae_th_low8=0)  # aggregation, >1 line, trans discontinuous, change to: = 2line, begin trans
                        # q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl11_fields(i=7, pipex_buf_ae_th_high7=0x8)
                    
                        print('router_soft_rstn is:', q68.c2m.rd_rcc_reset_ctrl5_router_soft_rstn())
                        if 0 in KLINK:        
                            q68.RouterClkSel(sel = 0)
                            q68.Router_stream_to_pipe(stream=3, pipe=3)     #map stream0~3 to pipe0~7
                            q68.Router_stream_to_pipe(stream=2, pipe=2)
                            q68.Router_stream_to_pipe(stream=0, pipe=0)
                            q68.Router_stream_to_pipe(stream=1, pipe=1)
                            
                            
                            q68.Router_pipe_to_mdi(pipe=0, mdi=0)       #map pipe0~7 to mdi0~3
                            q68.Router_pipe_to_mdi(pipe=1, mdi=1)
                            q68.Router_pipe_to_mdi(pipe=2, mdi=1)
                            q68.Router_pipe_to_mdi(pipe=3, mdi=1)
                    
                            q68.Router_mdi_to_csi(mdi=0, csi=0)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=1, csi=1)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=2, csi=2)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=3, csi=3)       #map mdi0~3 to csi0~3
                            
                            q68.Pipe_enable(pipe=0, enable=1)    #enable pipe
                            q68.Pipe_enable(pipe=1, enable=0)
                            q68.Pipe_enable(pipe=2, enable=0)
                            q68.Pipe_enable(pipe=3, enable=0)
                            q68.Pipe_enable(pipe=4, enable=0) 
                            q68.Pipe_enable(pipe=5, enable=0) 
                            q68.Pipe_enable(pipe=6, enable=0) 
                            q68.Pipe_enable(pipe=7, enable=0) 
                        if 1 in KLINK: 
                            q68.RouterClkSel(sel = 1)
                            q68.Router_stream_to_pipe(stream=3, pipe=1)     #map stream0~3 to pipe0~7
                            q68.Router_stream_to_pipe(stream=2, pipe=0)
                            q68.Router_stream_to_pipe(stream=0, pipe=3)
                            q68.Router_stream_to_pipe(stream=1, pipe=7)
                            
                            
                            q68.Router_pipe_to_mdi(pipe=3, mdi=1)       #map pipe0~7 to mdi0~3
                            q68.Router_pipe_to_mdi(pipe=0, mdi=1)
                            q68.Router_pipe_to_mdi(pipe=7, mdi=0)
                            q68.Router_pipe_to_mdi(pipe=1, mdi=1)
                    
                            q68.Router_mdi_to_csi(mdi=0, csi=0)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=1, csi=1)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=2, csi=2)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=3, csi=3)       #map mdi0~3 to csi0~3
                            
                            q68.Pipe_enable(pipe=0, enable=0)    #enable pipe
                            q68.Pipe_enable(pipe=1, enable=0)
                            q68.Pipe_enable(pipe=2, enable=0)
                            q68.Pipe_enable(pipe=3, enable=0)
                            q68.Pipe_enable(pipe=4, enable=0) 
                            q68.Pipe_enable(pipe=5, enable=0) 
                            q68.Pipe_enable(pipe=6, enable=0) 
                            q68.Pipe_enable(pipe=7, enable=1) 
                            
                        if 2 in KLINK: 
                            q68.RouterClkSel(sel = 2)
                            q68.Router_stream_to_pipe(stream=3, pipe=1)     #map stream0~3 to pipe0~7
                            q68.Router_stream_to_pipe(stream=2, pipe=0)
                            q68.Router_stream_to_pipe(stream=0, pipe=3)
                            q68.Router_stream_to_pipe(stream=1, pipe=7)
                            
                            
                            q68.Router_pipe_to_mdi(pipe=3, mdi=1)       #map pipe0~7 to mdi0~3
                            q68.Router_pipe_to_mdi(pipe=0, mdi=0)
                            q68.Router_pipe_to_mdi(pipe=7, mdi=1)
                            q68.Router_pipe_to_mdi(pipe=1, mdi=1)
                    
                            q68.Router_mdi_to_csi(mdi=0, csi=0)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=1, csi=1)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=2, csi=2)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=3, csi=3)       #map mdi0~3 to csi0~3
                            
                            q68.Pipe_enable(pipe=0, enable=1)    #enable pipe
                            q68.Pipe_enable(pipe=1, enable=0)
                            q68.Pipe_enable(pipe=2, enable=0)
                            q68.Pipe_enable(pipe=3, enable=0)
                            q68.Pipe_enable(pipe=4, enable=0) 
                            q68.Pipe_enable(pipe=5, enable=0) 
                            q68.Pipe_enable(pipe=6, enable=0) 
                            q68.Pipe_enable(pipe=7, enable=0) 
                        
                        if 3 in KLINK:
                            q68.RouterClkSel(sel = 3)
                            q68.Router_stream_to_pipe(stream=3, pipe=1)     #map stream0~3 to pipe0~7
                            q68.Router_stream_to_pipe(stream=2, pipe=0)
                            q68.Router_stream_to_pipe(stream=0, pipe=3)
                            q68.Router_stream_to_pipe(stream=1, pipe=7)
                            
                            
                            q68.Router_pipe_to_mdi(pipe=3, mdi=1)       #map pipe0~7 to mdi0~3
                            q68.Router_pipe_to_mdi(pipe=0, mdi=1)
                            q68.Router_pipe_to_mdi(pipe=7, mdi=1)
                            q68.Router_pipe_to_mdi(pipe=1, mdi=0)
                    
                            q68.Router_mdi_to_csi(mdi=0, csi=0)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=1, csi=1)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=2, csi=2)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=3, csi=3)       #map mdi0~3 to csi0~3
                            
                            q68.Pipe_enable(pipe=0, enable=0)    #enable pipe
                            q68.Pipe_enable(pipe=1, enable=1)
                            q68.Pipe_enable(pipe=2, enable=0)
                            q68.Pipe_enable(pipe=3, enable=0)
                            q68.Pipe_enable(pipe=4, enable=0) 
                            q68.Pipe_enable(pipe=5, enable=0) 
                            q68.Pipe_enable(pipe=6, enable=0) 
                            q68.Pipe_enable(pipe=7, enable=0) 
                        
                        if ((0 in KLINK) & (1 in KLINK)):               
                            q68.Router_stream_to_pipe(stream=0, pipe=0)     #map stream0~3 to pipe0~7 
                            q68.Router_stream_to_pipe(stream=1, pipe=1)                               
                            q68.Router_stream_to_pipe(stream=2, pipe=2)                               
                            q68.Router_stream_to_pipe(stream=3, pipe=3)                               
                                                                                                      
                                                                                                      
                            q68.Router_pipe_to_mdi(pipe=0, mdi=0)       #map pipe0~7 to mdi0~3        
                            q68.Router_pipe_to_mdi(pipe=1, mdi=1)                                     
                            q68.Router_pipe_to_mdi(pipe=2, mdi=2)                                     
                            q68.Router_pipe_to_mdi(pipe=3, mdi=3)                                     
                                                                                                      
                            q68.Router_mdi_to_csi(mdi=0, csi=1)       #map mdi0~3 to csi0~3           
                            q68.Router_mdi_to_csi(mdi=1, csi=0)       #map mdi0~3 to csi0~3           
                            q68.Router_mdi_to_csi(mdi=2, csi=2)       #map mdi0~3 to csi0~3           
                            q68.Router_mdi_to_csi(mdi=3, csi=3)       #map mdi0~3 to csi0~3           
                                                                                                      
                            q68.Pipe_enable(pipe=0, enable=1)    #enable pipe                         
                            q68.Pipe_enable(pipe=1, enable=1)                                         
                            q68.Pipe_enable(pipe=2, enable=0)                                         
                            q68.Pipe_enable(pipe=3, enable=0)                                         
                            q68.Pipe_enable(pipe=4, enable=0)                                         
                            q68.Pipe_enable(pipe=5, enable=0)                                         
                            q68.Pipe_enable(pipe=6, enable=0)                                         
                            q68.Pipe_enable(pipe=7, enable=0)                                         
                        
                        if ((0 in KLINK) & (1 in KLINK) & (2 in KLINK) & (3 in KLINK)):
                            q68.Router_stream_to_pipe(stream=0, pipe=0)     #map stream0~3 to pipe0~7
                            q68.Router_stream_to_pipe(stream=1, pipe=1)
                            q68.Router_stream_to_pipe(stream=2, pipe=2)
                            q68.Router_stream_to_pipe(stream=3, pipe=3)
                            
                            
                            q68.Router_pipe_to_mdi(pipe=0, mdi=0)       #map pipe0~7 to mdi0~3
                            q68.Router_pipe_to_mdi(pipe=1, mdi=0)
                            q68.Router_pipe_to_mdi(pipe=2, mdi=1)
                            q68.Router_pipe_to_mdi(pipe=3, mdi=1)
                    
                            q68.Router_mdi_to_csi(mdi=0, csi=0)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=1, csi=1)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=2, csi=2)       #map mdi0~3 to csi0~3
                            q68.Router_mdi_to_csi(mdi=3, csi=3)       #map mdi0~3 to csi0~3
                            
                            q68.Pipe_enable(pipe=0, enable=1)    #enable pipe                         
                            q68.Pipe_enable(pipe=1, enable=1)                                         
                            q68.Pipe_enable(pipe=2, enable=1)                                         
                            q68.Pipe_enable(pipe=3, enable=1)    
                            q68.Pipe_enable(pipe=4, enable=0) 
                            q68.Pipe_enable(pipe=5, enable=0) 
                            q68.Pipe_enable(pipe=6, enable=0) 
                            q68.Pipe_enable(pipe=7, enable=0) 
                            
                        q68.RouterClkSel(sel = 2)         
                        q68.Router_mdi_aggregation_mode(mdi=0, mode=0)  #aggregation
                        q68.Router_mdi_aggregation_mode(mdi=1, mode=0)  #aggregation
                        vcid0 = 0
                        vcid1 = 1
                        vcid2 = 2
                        vcid3 = 3
                        q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl19_fields(0, stream_vcid0_map = vcid0)
                        print('stream0 vc id is: ', q68.c2m.rd_rx_router_link_ctrl_pipe_ctrl19_stream_vcid0_map(0))
                        
                        q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl19_fields(1, stream_vcid0_map = vcid1)
                        print('stream1 vc id is: ', q68.c2m.rd_rx_router_link_ctrl_pipe_ctrl19_stream_vcid0_map(1))
                         
                        q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl19_fields(2, stream_vcid0_map = vcid2)
                        print('stream1 vc id is: ', q68.c2m.rd_rx_router_link_ctrl_pipe_ctrl19_stream_vcid0_map(2))
                        
                        q68.c2m.wr_rx_router_link_ctrl_pipe_ctrl19_fields(3, stream_vcid0_map = vcid3)
                        print('stream1 vc id is: ', q68.c2m.rd_rx_router_link_ctrl_pipe_ctrl19_stream_vcid0_map(3))
                    
                        mipi_bias_en = q68.MIPITxRefEn(en=1)
                        mipipllen= q68.MIPIPLLSSCEnAll(sscen=0, sdmen=0)   #disable ssc/sdm
                    
                        for lane in range(12):   #CTS 2.5G
                            # print('lane=',lane)
                            q68.MIPITimeLPx(lane, value=14)    #Tlpx 10
                            q68.MIPITimeHs_prepare(lane, value=4)    #hs_prepare 4
                            q68.MIPITimeHs_zero(lane, value=40) #hs_0   80
                            q68.MIPITimeHs_sync(lane, value=0)    #hs_sync
                            q68.MIPITimeHs_deskew_sync(lane, value=1)  #hs_deskew_sync
                            q68.MIPITimeDlane_dly(lane, value=0)  #lane_dly  80
                            q68.MIPITimeHs_trail(lane, value=12)    #hs_trail   12  
                            q68.MIPITimeHs_exit(lane, value=2) #hs_exit  25
                            q68.MIPITimeClk_post(lane, value=0x50)  #clk_post 0x50
                            q68.MIPITimeClk_Prepare(lane, value=50)#210
                        
                        #q68.MIPITimeoutHs_en(lane=0, en=0)          #timeout msk=0
                        mipipllpha = q68.MIPIPLLPHYPha_adj_nlanes1(clklane=[0,6,])   #when logiclane is clock, set phase=0
                        # mipipllpha = q68.MIPIPLLPHYPha_adj_forVG(clklan=0) 
                        # #CSI output Lane Cofig
                        # crossbarphy0 = q68.MIPICrossbar(type=1, csi=0, logiclane=0, phylane=4)    #phylane0(QFN-PIN) connect to CSI0's lane0(nonsense), used as clock(lanetype=1)
                        # crossbarphy1 = q68.MIPICrossbar(type=0, csi=0, logiclane=0, phylane=0)    #phylane1(QFN-PIN) connect to CSI0's lane0(lanelogic, start from '0'), used as data(lanetype=0)            
                        # crossbarphy2 = q68.MIPICrossbar(type=0, csi=0, logiclane=1, phylane=2) 
                        # crossbarphy3 = q68.MIPICrossbar(type=0, csi=0, logiclane=2, phylane=3) 
                        # crossbarphy4 = q68.MIPICrossbar(type=0, csi=0, logiclane=3, phylane=5)  #1lane<2.15Gbps(orin, 20240323)
                        
                        crossbarphy0 = q68.MIPICrossbar(type=1, csi=0, logiclane=0, phylane=0) 
                        crossbarphy1 = q68.MIPICrossbar(type=0, csi=0, logiclane=0, phylane=1) 
                        crossbarphy2 = q68.MIPICrossbar(type=0, csi=0, logiclane=1, phylane=2) 
                        crossbarphy3 = q68.MIPICrossbar(type=0, csi=0, logiclane=2, phylane=3) 
                        crossbarphy4 = q68.MIPICrossbar(type=0, csi=0, logiclane=3, phylane=4) 
                        
                        crossbarphy5 = q68.MIPICrossbar(type=1, csi=1, logiclane=0, phylane=6) 
                        crossbarphy6 = q68.MIPICrossbar(type=0, csi=1, logiclane=0, phylane=7) 
                        crossbarphy7 = q68.MIPICrossbar(type=0, csi=1, logiclane=1, phylane=8) 
                        crossbarphy8 = q68.MIPICrossbar(type=0, csi=1, logiclane=2, phylane=9) 
                        crossbarphy9 = q68.MIPICrossbar(type=0, csi=1, logiclane=3, phylane=10) 
                        # crossbarphy5 = q68.MIPICrossbar(type=0, csi=0, logiclane=3, phylane=5)
                        
                        q68.MIPILaneENNUM(csi=0, n_lanes=3)         # csi: 0~3; n_lanes: number of data lanes(0 means 1lane,etc..)
                        q68.MIPILaneENNUM(csi=1, n_lanes=3)  
                        
                        for i in range(6):
                            q68.MIPILaneHstxDivEn(bank='ns',lane=i, en=1)     #hstx_div_en_sel
                            q68.MIPILaneHstxDivEn(bank='ew',lane=i, en=1)
                     
                        # CPHY/DPHY
                        q68.MIPICDPHYSel(bank= 'ns', sel = 'dphy')
                        q68.MIPICDPHYSel(bank= 'ew', sel = 'dphy')                                    
                        # q68.MIPISkewEnable(csi=0, en= 0)  
                        # skew
                        q68.MIPISkewEnable(csi=0, en= 1, width = 1)         #disable initial skew
                        q68.MIPIPerSkewEnable(csi=0, en= 0, width = 3)       #0:disable per_skew
                        
                        q68.MIPISkewEnable(csi=1, en= 1, width = 1)         #disable initial skew
                        q68.MIPIPerSkewEnable(csi=1, en= 0, width = 3)       #0:disable per_skew
                        
                        mipiclken = q68.MIPIPllClkLaneEnable(csi=0, en=1)   #1:enables D-PHY Clock Lane Module
                        mipiclken = q68.MIPIPllClkLaneEnable(csi=1, en=1)   #1:enables D-PHY Clock Lane Module
                        
                        q68.MIPIClockMode(csi=0, mode=1)  #1：continue mode clock, 0: non-continuous clock  
                        q68.MIPIClockMode(csi=1, mode=1)  #1：continue mode clock, 0: non-continuous clock  
                                                
                        # MIPI PLL frequency                                    
                        q68.MIPIPLLSet(pll_sel='ns', pll=0, mdivint=25, mdivfraction=0, post_div=0) #divint:12~25,  post_div:0~4/5 
                        q68.MIPIPLLSet(pll_sel='ns', pll=1, mdivint=12, mdivfraction=0, post_div=0)
                        q68.MIPIPLLSet(pll_sel='ew', pll=0, mdivint=12, mdivfraction=0, post_div=0) #to vg ,low speed
                        q68.MIPIPLLSet(pll_sel='ew', pll=1, mdivint=12, mdivfraction=0, post_div=0) #to csi
                        
                        q68.PipeFrameDiscard(pipe=0, disc_num =15)       #discard frame at POR
                        q68.PipeFrameDiscard(pipe=1, disc_num =15)       #discard frame at POR
                        q68.PipeFrameDiscard(pipe=2, disc_num =15)       #discard frame at POR
                        q68.PipeFrameDiscard(pipe=3, disc_num =15)       #discard frame at POR
                        q68.PipeFrameDiscard(pipe=4, disc_num =15)       #discard frame at POR
                        q68.PipeFrameDiscard(pipe=5, disc_num =15)       #discard frame at POR
                        q68.PipeFrameDiscard(pipe=6, disc_num =15)       #discard frame at POR
                        q68.PipeFrameDiscard(pipe=7, disc_num =15)       #discard frame at POR
                        
                    
                        for i in range(6):
                            q68.MIPILaneClkCfg(bank='ns',lane=i, clk_sel=0)     #lane0~5 select clock from mipi-pllx
                        
                        for i in range(6):
                            q68.MIPILaneClkCfg(bank='ew',lane=i, clk_sel=0)     #lane0~5 select clock from mipi-pllx
                        # MIPI Boot
                        boot_en = q68.MIPIBootEN(en1 = 1, en0 = 1)  #Boot state machine enable
                        time.sleep(0.5)
                        
                        (state_r_boot50, state_r_boot51, dco_cal_error0, dco_cal_error1, res_cal_error0, res_cal_error1, boot_done0, boot_done1)\
                         = q68.MIPIBootStatus()
                         
                    
                        q68.PllStatus()
                    
                        
                        for i in range(8):
                            q68.PipeLineEn(pipe=i, line_en=0)    #Line Start/End enable，lane0~7
                        
                        q68.CSIReset(csi=0)     #do reset of CSI-2 tx controller one ti 
                        q68.CSIReset(csi=1)     #do reset of CSI-2 tx controller one ti 
                        q68.MIPIPowerDownUnusedLane(phylane_unused= [5,11])
                        #lpf_status = q68.MIPI_LPF_stat(lane=[0,1,2,3,4,6,7,8])
                        lpf_status = q68.MIPILPFStat(lane=[0,1,2,3,4,5,6,7,8,9,10,11])
                        print('LPF status: ', lpf_status)
                        #hs_status = q68.MIPI_HS_stat(lane=[0,1,2,3,4,6,7,8])
                        hs_status = q68.MIPIHSStat(lane=[0,1,2,3,4,5,6,7,8,9,10,11])
                        print('HS status: ', hs_status)
                    
                        if 0 in KLINK: 
                        # configure s68_1
                            q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=3, link1=0, link2=0, link3=0) 
                            # m2c
                            #MIPI vref & LDO enable, only need on May
                            q68_remote.dongle.devAddr = s680_iic_dev 
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            
                            # q68_remote.m2c.wr_tx_link_video_ctrl2_fields(dt_pass_en=1)  # CSI-2 packet generator data type pass enable
                            
                            q68_remote.EfhStatusClr()
                            q68_remote.EfhStatusRead()
                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                            time.sleep(1)                                                             
                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0)
                            time.sleep(1)    
                            (s680_dlysetting, s680_hs_settle_time, s680_ls_settle_time) = q68_remote.M2CMIPIInit(dlysetting=18,num_lanes=3,hs_settle_time=2,ls_settle_time=10,mode=1)
                            (boot_status, dco_cal_err, res_cal_err, mipi_boot_done, offcal_err0,offcal_err1,offcal_err2,offcal_err3,offcal_err4) = q68_remote.M2CMIPIBootStatus()
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            (lp_status0, lp_status1, lp_status2, lp_status3, lp_status4) = q68_remote.M2CMIPILPStatus()
                            (hs_status0, hs_status1, hs_status2, hs_status3, hs_status4) = q68_remote.M2CMIPIHSStatus()
                    
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            q68_remote.M2CGPIORemoteRx(8)
                                                             
                        # q68.FrameSyncOutConifg(per_div=0x0b, period=17361, fs_tx_id=12, auto_fs=0)            
                        if 1 in KLINK: 
                        # configure s68_2
                            q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=3, link2=0, link3=0)   
                            
                            q68_remote.dongle.devAddr = 0x44 
                            
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            # q68_remote.m2c.wr_tx_link_video_ctrl2_fields(dt_pass_en=1)  # CSI-2 packet generator data type pass enable
                            
                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                            time.sleep(1)                                                             
                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0)
                            time.sleep(1)    
                            q68_remote.EfhStatusClr()
                            q68_remote.EfhStatusRead()
                            
                            (s681_dlysetting, s681_hs_settle_time, s681_ls_settle_time) = q68_remote.M2CMIPIInit(dlysetting=5,num_lanes=3,hs_settle_time=2,ls_settle_time=10,mode=1)
                            print ('dco_fword_reg is: ', q68_remote.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal4_dco_fword_r())
                            (boot_status, dco_cal_err, res_cal_err, mipi_boot_done, offcal_err0,offcal_err1,offcal_err2,offcal_err3,offcal_err4) = q68_remote.M2CMIPIBootStatus()
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            (lp_status0, lp_status1, lp_status2, lp_status3, lp_status4) = q68_remote.M2CMIPILPStatus()
                            (hs_status0, hs_status1, hs_status2, hs_status3, hs_status4) = q68_remote.M2CMIPIHSStatus()
                    
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                    
                            q68_remote.M2CGPIORemoteRx(8)
                                                
                        if 2 in KLINK: 
                            
                            q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0, link2=3, link3=0)   
                            
                            q68_remote.dongle.devAddr = s682_iic_dev
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            # q68_remote.m2c.wr_tx_link_video_ctrl2_fields(dt_pass_en=1)  # CSI-2 packet generator data type pass enable
                            
                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                            time.sleep(1)                                                             
                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0) 
                            time.sleep(1)   
                            q68_remote.EfhStatusClr()
                            q68_remote.EfhStatusRead()

                            (s682_dlysetting, s682_hs_settle_time, s682_ls_settle_time) = q68_remote.M2CMIPIInit(dlysetting=18,num_lanes=3,hs_settle_time=2,ls_settle_time=10,mode=1)
                            
                            (boot_status, dco_cal_err, res_cal_err, mipi_boot_done, offcal_err0,offcal_err1,offcal_err2,offcal_err3,offcal_err4) = q68_remote.M2CMIPIBootStatus()
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            (lp_status0, lp_status1, lp_status2, lp_status3, lp_status4) = q68_remote.M2CMIPILPStatus()
                            (hs_status0, hs_status1, hs_status2, hs_status3, hs_status4) = q68_remote.M2CMIPIHSStatus()
                    
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                    
                            q68_remote.M2CGPIORemoteRx(8)
                                           
                        if 3 in KLINK: 
                            
                            q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0, link2=0, link3=3)   
                            
                            q68_remote.dongle.devAddr = 0x44 
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            # q68_remote.m2c.wr_tx_link_video_ctrl2_fields(dt_pass_en=1)  # CSI-2 packet generator data type pass enable

                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0) 
                            time.sleep(1)
                            q68_remote.m2c.wr_gpios_gpio_a_7_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0) 
                            time.sleep(1)  
                            q68_remote.EfhStatusClr()
                            q68_remote.EfhStatusRead()
                          
                            (s683_dlysetting, s683_hs_settle_time, s683_ls_settle_time) = q68_remote.M2CMIPIInit(dlysetting=5,num_lanes=3,hs_settle_time=2,ls_settle_time=10,mode=1)
                            (boot_status, dco_cal_err, res_cal_err, mipi_boot_done, offcal_err0,offcal_err1,offcal_err2,offcal_err3,offcal_err4) = q68_remote.M2CMIPIBootStatus()
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                            (lp_status0, lp_status1, lp_status2, lp_status3, lp_status4) = q68_remote.M2CMIPILPStatus()
                            (hs_status0, hs_status1, hs_status2, hs_status3, hs_status4) = q68_remote.M2CMIPIHSStatus()
                    
                            q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                    
                            q68_remote.M2CGPIORemoteRx(8)  # mapping frame sync to GPIO8
                    
                        # "LINK0 for X031"
                        # q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=3, link1=0,link2=0,link3=0)              
                        # q68_remote.dongle.devAddr = 0x44   
                        # q68_remote.M2CSPI0Debug(ss_dly_clks=53) 
                        # q68_remote.EfhStatusClr()
                        # q68_remote.EfhStatusRead()   
                        
                        "LINK0 for X8B"
                        
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=3, link1=0,link2=0,link3=0)   
                        q68_remote.dongle.devAddr = s680_iic_dev          
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        q68_remote.EfhStatusClr()
                        q68_remote.EfhStatusRead()
                        # q68_remote.m2c.wr_gpios_gpio_a_0_fields(out = 0, rx_en = 0, tx_en= 0, out_dis = 0)  
                        # time.sleep(1)                                                             
                        # q68_remote.m2c.wr_gpios_gpio_a_0_fields(out = 1, rx_en = 0, tx_en= 0, out_dis = 0)
                        # time.sleep(1) 
                        # q68_remote.M2CRefPLLSet(mdivint=24, mdivfraction=0, postdiv=0, postcnt=25, poststrg2=1)
                        # time.sleep(1)
                        # q68_remote.M2CMFNSet(gpio=4, mfn=4)
                        # time.sleep(1)
                        # q68_remote.Camera_init_X8B_raw10(pcidevAddr=0x36)
                        # q68_remote.Camera_init_X031_RGGB10()
                        q68_remote.EfhStatusRead()
                        # q68.Pipe_enable(pipe=0, enable=1)  
                        "LINK1 for X8B"                       
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=3,link2=0,link3=0)   
                        q68_remote.dongle.devAddr = 0x44          
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        q68_remote.EfhStatusClr()
                        q68_remote.EfhStatusRead()
                        q68_remote.M2CRefPLLSet(mdivint=24, mdivfraction=0, postdiv=0, postcnt=25, poststrg2=1)
                        time.sleep(1)
                        q68_remote.M2CMFNSet(gpio=4, mfn=4)
                        time.sleep(1)
                        q68_remote.Camera_init_X8B_raw10(pcidevAddr=0x36)
                        q68_remote.EfhStatusRead()
                        
                        "LINK2 for X8B"
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0,link2=3,link3=0)   
                        q68_remote.dongle.devAddr = s682_iic_dev         
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        q68_remote.EfhStatusClr()
                        q68_remote.EfhStatusRead()
                        # q68_remote.M2CRefPLLSet(mdivint=24, mdivfraction=0, postdiv=0, postcnt=25, poststrg2=1)
                        # time.sleep(1)
                        # q68_remote.M2CMFNSet(gpio=4, mfn=4)
                        # time.sleep(1)
                        # q68_remote.Camera_init_X8B_raw10(pcidevAddr=0x36)
                        q68_remote.EfhStatusRead()
                        
                        "LINK3 for X8B"
                        q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0,link2=0,link3=3)   
                        q68_remote.dongle.devAddr = 0x44          
                        q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                        q68_remote.EfhStatusClr()
                        q68_remote.EfhStatusRead()
                        q68_remote.M2CRefPLLSet(mdivint=24, mdivfraction=0, postdiv=0, postcnt=25, poststrg2=1)
                        time.sleep(1)
                        q68_remote.M2CMFNSet(gpio=4, mfn=4)
                        time.sleep(1)
                        q68_remote.Camera_init_X8B_raw10(pcidevAddr=0x36)
                        q68_remote.EfhStatusRead() 
                                               
                        q68.dongle.devAddr = q68_iic_add 
                        q68.EfhStatusClr()
                        q68.EfhTRxStatusCheck(i=0)
                        q68.EfhTRxStatusCheck(i=1)
                        q68.EfhTRxStatusCheck(i=2)
                        q68.EfhTRxStatusCheck(i=3)
                        q68.EfhRouterStatusCheck(i=0)
                        q68.EfhRouterStatusCheck(i=1)
                        q68.EfhRouterStatusCheck(i=2)
                        q68.EfhRouterStatusCheck(i=3)
                        time.sleep(1)
                        i=1
                        print('line status0:', hex(q68.c2m.rd_rx_router_status_line_status0(i=i)    ))            
                        print('line status1:', hex(q68.c2m.rd_rx_router_status_line_status1(i=i)    )) 
                        i=2
                        print('line status0:', hex(q68.c2m.rd_rx_router_status_line_status0(i=i)    ))            
                        print('line status1:', hex(q68.c2m.rd_rx_router_status_line_status1(i=i)    )) 
                        q68.MFNSet(gpio=2, mfn=1)
                        q68.FrameSyncOutConifg(per_div=0x0b, period=17361, fs_tx_id=11, auto_fs=1)  # q68 frame sync enable
                        # q68.GPIORemoteTx(gpio=14, tx_id = 11) 
                        
                        time.sleep(1)
                        # q68.Pipe_enable(pipe=3, enable=1)    #enable pipe
                        # q68.Pipe_enable(pipe=2, enable=1)
                        # q68.Pipe_enable(pipe=0, enable=1)
                        # q68.Pipe_enable(pipe=1, enable=1)
                        # q68_remote.IICRemoteConfig(q68Addr=0x31, link0=0, link1=0, link2=0, link3=3)   
                        # QRegisterAccess.q68Addr=0x44 
                        # q68_remote.M2CSPI0Debug(ss_dly_clks=53) 
                        # m2c.wr_gpios_gpio_a_fields(8, out = 0, rx_en = 0, tx_en= 0, out_dis = 0) 
                        # time.sleep(1)
                        # m2c.wr_gpios_gpio_a_fields(8, out = 1, rx_en = 0, tx_en= 0, out_dis = 0) 
                        
                        q68.devAddr = q68_iic_add  
                        q68.EfhTRxStatusCheck(i=0)
                        q68.EfhTRxStatusCheck(i=1)
                        q68.EfhTRxStatusCheck(i=2)
                        q68.EfhTRxStatusCheck(i=3)
                        q68.EfhRouterStatusCheck(i=0)
                        q68.EfhRouterStatusCheck(i=1)
                        q68.EfhRouterStatusCheck(i=2)
                        q68.EfhRouterStatusCheck(i=3)
                        
                        i=0
                        print('line status0:', hex(q68.c2m.rd_rx_router_status_line_status0(i=i)    ))            
                        print('line status1:', hex(q68.c2m.rd_rx_router_status_line_status1(i=i)    )) 
                        i=1
                        print('line status0:', hex(q68.c2m.rd_rx_router_status_line_status0(i=i)    ))            
                        print('line status1:', hex(q68.c2m.rd_rx_router_status_line_status1(i=i)    ))
                        
                        i=2
                        print('line status0:', hex(q68.c2m.rd_rx_router_status_line_status0(i=i)    ))            
                        print('line status1:', hex(q68.c2m.rd_rx_router_status_line_status1(i=i)    )) 
                        i=3
                        print('line status0:', hex(q68.c2m.rd_rx_router_status_line_status0(i=i)    ))            
                        print('line status1:', hex(q68.c2m.rd_rx_router_status_line_status1(i=i)    ))
                        
                        q68.devAddr = q68_iic_add  
                        i= 0# link 
                                                     
                        print('video[7:4]/link[3:0]_lock Status:',q68.c2m.rd_sys_cfg_status0_video_lock())        
                        print('mdi_data_fifo:', hex(q68.c2m.rd_rx_router_status_mdi_status0(i=i)        ))            
                        print('header fifo,buffer:', hex(q68.c2m.rd_rx_router_status_line_status2(i=i)  ))       
                        print('line status0:', hex(q68.c2m.rd_rx_router_status_line_status0(i=i)        ))            
                        print('line status1:', hex(q68.c2m.rd_rx_router_status_line_status1(i=i)        ))            
                        print('frame count_low:', hex(q68.c2m.rd_rx_router_status_frame_cnt_low8(i=i)   ))  #跟着VC走        
                        print('frame count_high:', hex(q68.c2m.rd_rx_router_status_frame_cnt_high8(i=i) ))      
                                                                                                               
                        #for DEBUG                                                                            
                        print('csi0 fifo overflow:', hex(q68.c2m.rd_csi2_tx0_int_mdi_st_src_fifo_overflow()) )    
                        print('mipi_tx_status:', hex(q68.c2m.rd_fh_l0_status2_mipi_tx_status())  )                
                        print('csi/mipi/lane err(bit0~2):', hex(q68.c2m.rd_fh_mipi_tx_status0()) ) 
                        print(hex(q68.c2m.rd_rx_router_status_line_cnt_low8(i=0)))  # VCID
                        print(hex(q68.c2m.rd_rx_router_status_line_cnt_high8(i=0)))     
                                                                                                             
                        print(hex(q68.c2m.rd_csi2_tx0_int_mdi_st_src()))
                        print(hex(q68.c2m.rd_csi2_tx0_int_mdi_errl_src_vc(i=0)))
                        print(hex(q68.c2m.rd_csi2_tx0_int_mdi_errf_src_vc(i=0)))
                        
                        q68.c2m.wr_fh_rx_en_ctrl0(i=1, value=0xFF) 
                        q68.c2m.wr_fh_rx_en_ctrl1(i=1, value=0xFF) 
                        q68.c2m.wr_fh_rx_en_ctrl7(i=1, value=0xFF)  # i: link
                        print(hex(q68.c2m.rd_fh_rx_status0(i=1)))
                        print(hex(q68.c2m.rd_fh_rx_status1(i=1)))
                        print(hex(q68.c2m.rd_fh_rx_status7(i=1)))
                        
                        print(hex(q68.c2m.rd_rx_link1_pp_video_status0()))
                        print(hex(q68.c2m.rd_rx_link1_pp_video_status1()))
                        print(hex(q68.c2m.rd_rx_link1_pp_video_status2()))
                        print(hex(q68.c2m.rd_rx_link1_pp_video_status3()))
                        print(hex(q68.c2m.rd_rx_link1_pp_video_status4()))
                        TEMP = Drift_type[starttemp]
                        print (TEMP)
                        loop=0
                        loop1=0
                        frame_count0_st = q68.FrameCount(csi = 0, vc=vcid0)
                        frame_count1_st = q68.FrameCount(csi = 0, vc=vcid1)
                        frame_count2_st = q68.FrameCount(csi = 1, vc=vcid2)
                        frame_count3_st = q68.FrameCount(csi = 1, vc=vcid3)
                        line_count0_st = q68.c2m.rd_rx_router_status_line_cnt_high8(i=0)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=0)
                        line_count1_st = q68.c2m.rd_rx_router_status_line_cnt_high8(i=1)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=1)
                        line_count2_st = q68.c2m.rd_rx_router_status_line_cnt_high8(i=10)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=10)
                        line_count3_st = q68.c2m.rd_rx_router_status_line_cnt_high8(i=11)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=11)
                        for tempdrif in TEMP:
                        
                            oven.settemp(tempdrif) #set & wait temp to stable  
                            temp_read=oven.tempread() 
                            
                            while ((abs((temp_read-tempdrif)))>2):
                                time.sleep(0.5)
                                temp_read=oven.tempread()
                                print('temp_measure is: ', temp_read)                                          
                                current_measure_gpp433=Gpp433.MeasureCurrent(channel=4)
                                time.sleep(0.5)
                                (curr_vdd10_q68,curr_vddio_q68,curr_vdd18_q68,vol_measure_vdd10_q68,vol_measure_vddio_q68,vol_measure_vdd18_q68,power_total_q68) = PowerMeause(powertype='keithley2230G',power_instr=Keithley2230G1) 
                                time.sleep(0.2)
                                q68.dongle.setBusSpeed(1, 100)
                                link0_status = q68.c2m.rd_test_fsm_status1_link0()
                                link1_status = q68.c2m.rd_test_fsm_status1_link1()
                                link2_status = q68.c2m.rd_test_fsm_status2_link2()
                                link3_status = q68.c2m.rd_test_fsm_status2_link3()
                                mdi_status0 = q68.c2m.rd_rx_router_status_mdi_status0(i=0) 
                                mdi_status1 = q68.c2m.rd_rx_router_status_mdi_status0(i=1)
                                mdi_status2 = q68.c2m.rd_rx_router_status_mdi_status0(i=2)
                                mdi_status3 = q68.c2m.rd_rx_router_status_mdi_status0(i=3)
                                line_status0 = hex(q68.c2m.rd_rx_router_status_line_status0(i=0))
                                line_status1 = hex(q68.c2m.rd_rx_router_status_line_status0(i=1))
                                line_status2 = hex(q68.c2m.rd_rx_router_status_line_status0(i=2))
                                line_status3 = hex(q68.c2m.rd_rx_router_status_line_status0(i=3))
                                line_status4 = hex(q68.c2m.rd_rx_router_status_line_status0(i=4))
                                line_status5 = hex(q68.c2m.rd_rx_router_status_line_status0(i=5))
                                line_status6 = hex(q68.c2m.rd_rx_router_status_line_status0(i=6))
                                line_status7 = hex(q68.c2m.rd_rx_router_status_line_status0(i=7))
                                link0_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=0))
                                link1_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=1))
                                link2_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=2))
                                link3_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=3))
                                link0_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=0))
                                link1_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=1))
                                link2_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=2))
                                link3_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=3))
                                link0_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=0))
                                link1_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=1))
                                link2_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=2))
                                link3_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=3))
                                link0_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=0))
                                link1_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=1))
                                link2_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=2))
                                link3_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=3))
                                link0_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=0))
                                link1_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=1))
                                link2_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=2))
                                link3_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=3))
                                link0_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=0))
                                link1_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=1))
                                link2_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=2))
                                link3_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=3))
                                video_lock = q68.c2m.rd_sys_cfg_status0_video_lock()  
                                # log_err = HWImpl.hw_get_tracelog_status()   #1=error during start-stop hit(all vedio)
                                # print("log error = ",log_err)
                                rate0 = q68.c2m.rd_sys_cfg_link_ctrl1_rate0()
                                rate1 = q68.c2m.rd_sys_cfg_link_ctrl1_rate1()
                                rate2 = q68.c2m.rd_sys_cfg_link_ctrl1_rate2()
                                rate3 = q68.c2m.rd_sys_cfg_link_ctrl1_rate3()
                                bcrxrate0 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate0()
                                bcrxrate1 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate1()
                                bcrxrate2 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate2()
                                bcrxrate3 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate3()
                                                                                                                                                                                                          
                                (q68_plllock, q68_vco_band_cali_done, q68_vcoband) = q68.PHYPLLStatus()                                                                             
                                                                                                                                                                                                                                                                                                           
                                
                                                              
                                frame_count0 =  q68.FrameCount(csi = 0, vc=vcid0)-frame_count0_st
                                frame_count1 =  q68.FrameCount(csi = 0, vc=vcid1)-frame_count1_st
                                frame_count2 =  q68.FrameCount(csi = 1, vc=vcid2)-frame_count2_st
                                frame_count3 =  q68.FrameCount(csi = 1, vc=vcid3)-frame_count3_st
                                
                                line_count0 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=0)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=0)
                                line_count1 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=1)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=1)
                                line_count2 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=10)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=10)
                                line_count3 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=11)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=11)
                                (rd_bmag_txbc_ch0,adp_khp_ch0,adp_h0_ch0,adp_a0_ch0,sel_afe_offsetcali_ch0,dcali_sb_afe_ch0,slicer0_forecali_ch0,slicer0_dcali_sb_ch0,slicer90_dcali_sb_ch0,slicer180_dcali_ch0,slicer270_dcali_sb_ch0,slicererr0_dcali_sb_ch0,slicererr180_dcali_sb_ch0,adp_b1_ch0,adp_b2_ch0,adp_b3_ch0,adp_t0_ch0,adp_t1_ch0,adp_t2_ch0,adp_t3_ch0,adp_t4_ch0,adp_t5_ch0,adp_t6_ch0,adp_t7_ch0,adp_t8_ch0,adp_t9_ch0,cdr_picode_a_ch0,cdr_picode_b_ch0,cdr_picode_c_ch0)=q68.C3ParasrecallCh0()
                                (rd_bmag_txbc_ch1,adp_khp_ch1,adp_h0_ch1,adp_a0_ch1,sel_afe_offsetcali_ch1,dcali_sb_afe_ch1,slicer0_forecali_ch1,slicer0_dcali_sb_ch1,slicer90_dcali_sb_ch1,slicer180_dcali_ch1,slicer270_dcali_sb_ch1,slicererr0_dcali_sb_ch1,slicererr180_dcali_sb_ch1,adp_b1_ch1,adp_b2_ch1,adp_b3_ch1,adp_t0_ch1,adp_t1_ch1,adp_t2_ch1,adp_t3_ch1,adp_t4_ch1,adp_t5_ch1,adp_t6_ch1,adp_t7_ch1,adp_t8_ch1,adp_t9_ch1,cdr_picode_a_ch1,cdr_picode_b_ch1,cdr_picode_c_ch1)=q68.C3ParasrecallCh1()
                                (rd_bmag_txbc_ch2,adp_khp_ch2,adp_h0_ch2,adp_a0_ch2,sel_afe_offsetcali_ch2,dcali_sb_afe_ch2,slicer0_forecali_ch2,slicer0_dcali_sb_ch2,slicer90_dcali_sb_ch2,slicer180_dcali_ch2,slicer270_dcali_sb_ch2,slicererr0_dcali_sb_ch2,slicererr180_dcali_sb_ch2,adp_b1_ch2,adp_b2_ch2,adp_b3_ch2,adp_t0_ch2,adp_t1_ch2,adp_t2_ch2,adp_t3_ch2,adp_t4_ch2,adp_t5_ch2,adp_t6_ch2,adp_t7_ch2,adp_t8_ch2,adp_t9_ch2,cdr_picode_a_ch2,cdr_picode_b_ch2,cdr_picode_c_ch2)=q68.C3ParasrecallCh2()
                                (rd_bmag_txbc_ch3,adp_khp_ch3,adp_h0_ch3,adp_a0_ch3,sel_afe_offsetcali_ch3,dcali_sb_afe_ch3,slicer0_forecali_ch3,slicer0_dcali_sb_ch3,slicer90_dcali_sb_ch3,slicer180_dcali_ch3,slicer270_dcali_sb_ch3,slicererr0_dcali_sb_ch3,slicererr180_dcali_sb_ch3,adp_b1_ch3,adp_b2_ch3,adp_b3_ch3,adp_t0_ch3,adp_t1_ch3,adp_t2_ch3,adp_t3_ch3,adp_t4_ch3,adp_t5_ch3,adp_t6_ch3,adp_t7_ch3,adp_t8_ch3,adp_t9_ch3,cdr_picode_a_ch3,cdr_picode_b_ch3,cdr_picode_c_ch3)=q68.C3ParasrecallCh3()
                                # q68_remote.dongle.devAddr = 0x44 
                                # time.sleep(0.1)  
                                # q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                # time.sleep(0.1)
                                q68_remote.dongle.setBusSpeed(1, 100)
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=3, link1=0,link2=0,link3=0)   
                                q68_remote.dongle.devAddr = s680_iic_dev          
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link0_rx_status0_remote_tx_fail,link0_rx_status0_local_rx_fail,link0_rx_status0_pkt_timeout,link0_rx_status0_video_sn_err,link0_rx_status0_intf_fifo_uv,link0_rx_status0_intf_fifo_ov,\
                                link0_rx_status1_pkt_crc_err,link0_rx_status2_i2c_cfg_err,link0_rx_status2_sop_kchar_dup_8b10b,link0_rx_status2_sop_kchar_err_8b10b,link0_rx_status2_pkt_type_illegal,link0_rx_status2_pp_fifo_full,\
                                link0_rx_status2_dec_fault,link0_rx_status2_sync_fault,link0_rx_status3_pkt_len_err,link0_rx_status4_pkt_crc_fifo_full,link0_rx_status5_pkt_data_fifo_full,link0_rx_status6_pkt_ack_crc_err,\
                                link0_rd_efh_tx_status0,link0_rd_efh_tx_status1,link0_rd_efh_tx_status2,link0_rd_efh_tx_status3,link0_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link0_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link0_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link0_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link0_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link0_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link0_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3()
                                (vcid_s680,data_type_s680,word_count_s680)=q68_remote.M2CMIPIDataInfo()
                                
                                err_lane0_s680 = q68_remote.M2CMIPILaneErrCheck(0)
                                err_lane1_s680 = q68_remote.M2CMIPILaneErrCheck(1)  
                                err_lane2_s680 = q68_remote.M2CMIPILaneErrCheck(2)  
                                err_lane3_s680 = q68_remote.M2CMIPILaneErrCheck(3) 
                                err_lane4_s680 = q68_remote.M2CMIPILaneErrCheck(4)  
                                             
                                (err_ecc_s680, err_crc_s680) =q68_remote.M2CMIPIDataErrCheck(vcid =0)
                                s680_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=3,link2=0,link3=0)   
                                q68_remote.dongle.devAddr = 0x44          
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link1_rx_status0_remote_tx_fail,link1_rx_status0_local_rx_fail,link1_rx_status0_pkt_timeout,link1_rx_status0_video_sn_err,link1_rx_status0_intf_fifo_uv,link1_rx_status0_intf_fifo_ov,\
                                link1_rx_status1_pkt_crc_err,link1_rx_status2_i2c_cfg_err,link1_rx_status2_sop_kchar_dup_8b10b,link1_rx_status2_sop_kchar_err_8b10b,link1_rx_status2_pkt_type_illegal,link1_rx_status2_pp_fifo_full,\
                                link1_rx_status2_dec_fault,link1_rx_status2_sync_fault,link1_rx_status3_pkt_len_err,link1_rx_status4_pkt_crc_fifo_full,link1_rx_status5_pkt_data_fifo_full,link1_rx_status6_pkt_ack_crc_err,\
                                link1_rd_efh_tx_status0,link1_rd_efh_tx_status1,link1_rd_efh_tx_status2,link1_rd_efh_tx_status3,link1_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link1_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link1_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link1_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link1_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link1_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link1_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3()
                                s681_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                (vcid_s681,data_type_s681,word_count_s681)=q68_remote.M2CMIPIDataInfo()
                                err_lane0_s681 = q68_remote.M2CMIPILaneErrCheck(0)
                                err_lane1_s681 = q68_remote.M2CMIPILaneErrCheck(1)  
                                err_lane2_s681 = q68_remote.M2CMIPILaneErrCheck(2)  
                                err_lane3_s681 = q68_remote.M2CMIPILaneErrCheck(3) 
                                err_lane4_s681 = q68_remote.M2CMIPILaneErrCheck(4) 
                                (err_ecc_s681, err_crc_s681) =q68_remote.M2CMIPIDataErrCheck(vcid =1)
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0,link2=3,link3=0)   
                                q68_remote.dongle.devAddr = s682_iic_dev         
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link2_rx_status0_remote_tx_fail,link2_rx_status0_local_rx_fail,link2_rx_status0_pkt_timeout,link2_rx_status0_video_sn_err,link2_rx_status0_intf_fifo_uv,link2_rx_status0_intf_fifo_ov,\
                                link2_rx_status1_pkt_crc_err,link2_rx_status2_i2c_cfg_err,link2_rx_status2_sop_kchar_dup_8b10b,link2_rx_status2_sop_kchar_err_8b10b,link2_rx_status2_pkt_type_illegal,link2_rx_status2_pp_fifo_full,\
                                link2_rx_status2_dec_fault,link2_rx_status2_sync_fault,link2_rx_status3_pkt_len_err,link2_rx_status4_pkt_crc_fifo_full,link2_rx_status5_pkt_data_fifo_full,link2_rx_status6_pkt_ack_crc_err,\
                                link2_rd_efh_tx_status0,link2_rd_efh_tx_status1,link2_rd_efh_tx_status2,link2_rd_efh_tx_status3,link2_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link2_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link2_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link2_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link2_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link2_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link2_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3()
                                s682_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                (vcid_s682,data_type_s682,word_count_s682)=q68_remote.M2CMIPIDataInfo()
                                err_lane0_s682 = q68_remote.M2CMIPILaneErrCheck(0)
                                err_lane1_s682 = q68_remote.M2CMIPILaneErrCheck(1)  
                                err_lane2_s682 = q68_remote.M2CMIPILaneErrCheck(2)  
                                err_lane3_s682 = q68_remote.M2CMIPILaneErrCheck(3) 
                                err_lane4_s682 = q68_remote.M2CMIPILaneErrCheck(4) 
                                (err_ecc_s682, err_crc_s682) =q68_remote.M2CMIPIDataErrCheck(vcid =2)
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0,link2=0,link3=3)   
                                q68_remote.dongle.devAddr = 0x44          
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link3_rx_status0_remote_tx_fail,link3_rx_status0_local_rx_fail,link3_rx_status0_pkt_timeout,link3_rx_status0_video_sn_err,link3_rx_status0_intf_fifo_uv,link3_rx_status0_intf_fifo_ov,\
                                link3_rx_status1_pkt_crc_err,link3_rx_status2_i2c_cfg_err,link3_rx_status2_sop_kchar_dup_8b10b,link3_rx_status2_sop_kchar_err_8b10b,link3_rx_status2_pkt_type_illegal,link3_rx_status2_pp_fifo_full,\
                                link3_rx_status2_dec_fault,link3_rx_status2_sync_fault,link3_rx_status3_pkt_len_err,link3_rx_status4_pkt_crc_fifo_full,link3_rx_status5_pkt_data_fifo_full,link3_rx_status6_pkt_ack_crc_err,\
                                link3_rd_efh_tx_status0,link3_rd_efh_tx_status1,link3_rd_efh_tx_status2,link3_rd_efh_tx_status3,link3_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link3_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link3_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link3_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link3_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link3_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link3_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3()
                                s683_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                (vcid_s683,data_type_s683,word_count_s683)=q68_remote.M2CMIPIDataInfo()
                                err_lane0_s683 = q68_remote.M2CMIPILaneErrCheck(0)    
                                err_lane1_s683 = q68_remote.M2CMIPILaneErrCheck(1)    
                                err_lane2_s683 = q68_remote.M2CMIPILaneErrCheck(2)    
                                err_lane3_s683 = q68_remote.M2CMIPILaneErrCheck(3)    
                                err_lane4_s683 = q68_remote.M2CMIPILaneErrCheck(4)    
                                (err_ecc_s683, err_crc_s683) =q68_remote.M2CMIPIDataErrCheck(vcid =3)
                                # q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                print('frame count0 is',frame_count0)
                                print('frame count1 is',frame_count1)
                                print('frame count2 is',frame_count2)
                                print('frame count3 is',frame_count3)
                                row=[datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'),str(BOARDID),str(CHIPID),loop,loop1,starttemp,temp_read,tempdrif,current_measure_gpp433,curr_vdd10_q68,curr_vddio_q68,curr_vdd18_q68,vol_measure_vdd10_q68,vol_measure_vddio_q68,vol_measure_vdd18_q68,power_total_q68,\
                                    q68_plllock,q68_vco_band_cali_done,q68_vcoband,frame_count0_st,frame_count1_st,frame_count2_st,frame_count3_st,frame_count0, frame_count1,frame_count2,frame_count3,line_count0_st,line_count1_st,line_count2_st,line_count3_st,line_count0,line_count1,line_count2,line_count3,\
                                    link0_status,link1_status,link2_status,link3_status,video_lock,mdi_status0, mdi_status1,mdi_status2,mdi_status3,\
                                    line_status0,line_status1,line_status2,line_status3,line_status4,line_status5,line_status6,line_status7,link0_tx_status0,link1_tx_status0,link2_tx_status0,link3_tx_status0,link0_tx_status1,link1_tx_status1,link2_tx_status1,link3_tx_status1,\
                                    link0_tx_status2,link1_tx_status2,link2_tx_status2,link3_tx_status2,link0_rx_status0,link1_rx_status0,link2_rx_status0,link3_rx_status0,link0_rx_status2,link1_rx_status2,link2_rx_status2,link3_rx_status2,link0_rx_status7,link1_rx_status7,link2_rx_status7,link3_rx_status7,\
                                    rd_bmag_txbc_ch0,adp_khp_ch0,adp_h0_ch0,adp_a0_ch0,sel_afe_offsetcali_ch0,dcali_sb_afe_ch0,slicer0_forecali_ch0,slicer0_dcali_sb_ch0,slicer90_dcali_sb_ch0,slicer180_dcali_ch0,slicer270_dcali_sb_ch0,slicererr0_dcali_sb_ch0,slicererr180_dcali_sb_ch0,adp_b1_ch0,adp_b2_ch0,\
                                    adp_b3_ch0,adp_t0_ch0,adp_t1_ch0,adp_t2_ch0,adp_t3_ch0,adp_t4_ch0,adp_t5_ch0,adp_t6_ch0,adp_t7_ch0,adp_t8_ch0,adp_t9_ch0,cdr_picode_a_ch0,cdr_picode_b_ch0,cdr_picode_c_ch0,\
                                    rd_bmag_txbc_ch1,adp_khp_ch1,adp_h0_ch1,adp_a0_ch1,sel_afe_offsetcali_ch1,dcali_sb_afe_ch1,slicer0_forecali_ch1,slicer0_dcali_sb_ch1,slicer90_dcali_sb_ch1,slicer180_dcali_ch1,slicer270_dcali_sb_ch1,slicererr0_dcali_sb_ch1,slicererr180_dcali_sb_ch1,\
                                    adp_b1_ch1,adp_b2_ch1,adp_b3_ch1,adp_t0_ch1,adp_t1_ch1,adp_t2_ch1,adp_t3_ch1,adp_t4_ch1,adp_t5_ch1,adp_t6_ch1,adp_t7_ch1,adp_t8_ch1,adp_t9_ch1,cdr_picode_a_ch1,cdr_picode_b_ch1,cdr_picode_c_ch1,\
                                    rd_bmag_txbc_ch2,adp_khp_ch2,adp_h0_ch2,adp_a0_ch2,sel_afe_offsetcali_ch2,dcali_sb_afe_ch2,slicer0_forecali_ch2,slicer0_dcali_sb_ch2,slicer90_dcali_sb_ch2,slicer180_dcali_ch2,slicer270_dcali_sb_ch2,\
                                    slicererr0_dcali_sb_ch2,slicererr180_dcali_sb_ch2,adp_b1_ch2,adp_b2_ch2,adp_b3_ch2,adp_t0_ch2,adp_t1_ch2,adp_t2_ch2,adp_t3_ch2,adp_t4_ch2,adp_t5_ch2,adp_t6_ch2,adp_t7_ch2,adp_t8_ch2,adp_t9_ch2,cdr_picode_a_ch2,cdr_picode_b_ch2,cdr_picode_c_ch2,\
                                    rd_bmag_txbc_ch3,adp_khp_ch3,adp_h0_ch3,adp_a0_ch3,sel_afe_offsetcali_ch3,dcali_sb_afe_ch3,slicer0_forecali_ch3,slicer0_dcali_sb_ch3,slicer90_dcali_sb_ch3,slicer180_dcali_ch3,slicer270_dcali_sb_ch3,\
                                    slicererr0_dcali_sb_ch3,slicererr180_dcali_sb_ch3,adp_b1_ch3,adp_b2_ch3,adp_b3_ch3,adp_t0_ch3,adp_t1_ch3,adp_t2_ch3,adp_t3_ch3,adp_t4_ch3,adp_t5_ch3,adp_t6_ch3,adp_t7_ch3,adp_t8_ch3,adp_t9_ch3,cdr_picode_a_ch3,cdr_picode_b_ch3,cdr_picode_c_ch3,\
                                    rate0,rate1,rate2,rate3, bcrxrate0,bcrxrate1,bcrxrate2,bcrxrate3,\
                                    link0_rx_status0_remote_tx_fail,link0_rx_status0_local_rx_fail,link0_rx_status0_pkt_timeout,link0_rx_status0_video_sn_err,link0_rx_status0_intf_fifo_uv,link0_rx_status0_intf_fifo_ov,\
                                    link0_rx_status1_pkt_crc_err,link0_rx_status2_i2c_cfg_err,link0_rx_status2_sop_kchar_dup_8b10b,link0_rx_status2_sop_kchar_err_8b10b,link0_rx_status2_pkt_type_illegal,link0_rx_status2_pp_fifo_full,\
                                    link0_rx_status2_dec_fault,link0_rx_status2_sync_fault,link0_rx_status3_pkt_len_err,link0_rx_status4_pkt_crc_fifo_full,link0_rx_status5_pkt_data_fifo_full,link0_rx_status6_pkt_ack_crc_err,\
                                    link0_rd_efh_tx_status0,link0_rd_efh_tx_status1,link0_rd_efh_tx_status2,link0_rd_efh_tx_status3,link0_rd_efh_mipi_rx_status0,\
                                    link1_rx_status0_remote_tx_fail,link1_rx_status0_local_rx_fail,link1_rx_status0_pkt_timeout,link1_rx_status0_video_sn_err,link1_rx_status0_intf_fifo_uv,link1_rx_status0_intf_fifo_ov,\
                                    link1_rx_status1_pkt_crc_err,link1_rx_status2_i2c_cfg_err,link1_rx_status2_sop_kchar_dup_8b10b,link1_rx_status2_sop_kchar_err_8b10b,link1_rx_status2_pkt_type_illegal,link1_rx_status2_pp_fifo_full,\
                                    link1_rx_status2_dec_fault,link1_rx_status2_sync_fault,link1_rx_status3_pkt_len_err,link1_rx_status4_pkt_crc_fifo_full,link1_rx_status5_pkt_data_fifo_full,link1_rx_status6_pkt_ack_crc_err,\
                                    link1_rd_efh_tx_status0,link1_rd_efh_tx_status1,link1_rd_efh_tx_status2,link1_rd_efh_tx_status3,link1_rd_efh_mipi_rx_status0,\
                                    link2_rx_status0_remote_tx_fail,link2_rx_status0_local_rx_fail,link2_rx_status0_pkt_timeout,link2_rx_status0_video_sn_err,link2_rx_status0_intf_fifo_uv,link2_rx_status0_intf_fifo_ov,\
                                    link2_rx_status1_pkt_crc_err,link2_rx_status2_i2c_cfg_err,link2_rx_status2_sop_kchar_dup_8b10b,link2_rx_status2_sop_kchar_err_8b10b,link2_rx_status2_pkt_type_illegal,link2_rx_status2_pp_fifo_full,\
                                    link2_rx_status2_dec_fault,link2_rx_status2_sync_fault,link2_rx_status3_pkt_len_err,link2_rx_status4_pkt_crc_fifo_full,link2_rx_status5_pkt_data_fifo_full,link2_rx_status6_pkt_ack_crc_err,\
                                    link2_rd_efh_tx_status0,link2_rd_efh_tx_status1,link2_rd_efh_tx_status2,link2_rd_efh_tx_status3,link2_rd_efh_mipi_rx_status0,\
                                    link3_rx_status0_remote_tx_fail,link3_rx_status0_local_rx_fail,link3_rx_status0_pkt_timeout,link3_rx_status0_video_sn_err,link3_rx_status0_intf_fifo_uv,link3_rx_status0_intf_fifo_ov,\
                                    link3_rx_status1_pkt_crc_err,link3_rx_status2_i2c_cfg_err,link3_rx_status2_sop_kchar_dup_8b10b,link3_rx_status2_sop_kchar_err_8b10b,link3_rx_status2_pkt_type_illegal,link3_rx_status2_pp_fifo_full,\
                                    link3_rx_status2_dec_fault,link3_rx_status2_sync_fault,link3_rx_status3_pkt_len_err,link3_rx_status4_pkt_crc_fifo_full,link3_rx_status5_pkt_data_fifo_full,link3_rx_status6_pkt_ack_crc_err,\
                                    link3_rd_efh_tx_status0,link3_rd_efh_tx_status1,link3_rd_efh_tx_status2,link3_rd_efh_tx_status3,link3_rd_efh_mipi_rx_status0,\
                                    vcid_s680,data_type_s680,word_count_s680,err_lane0_s680,err_lane1_s680,err_lane2_s680,err_lane3_s680,err_lane4_s680,err_ecc_s680, err_crc_s680,vcid_s681,data_type_s681,word_count_s681,err_lane0_s681,err_lane1_s681,err_lane2_s681,err_lane3_s681,err_lane4_s681,err_ecc_s681, err_crc_s681,\
                                    vcid_s682,data_type_s682,word_count_s682,err_lane0_s682,err_lane1_s682,err_lane2_s682,err_lane3_s682,err_lane4_s682,err_ecc_s682, err_crc_s682,vcid_s683,data_type_s683,word_count_s683,err_lane0_s683,err_lane1_s683,err_lane2_s683,err_lane3_s683,err_lane4_s683,err_ecc_s683, err_crc_s683,\
                                    s68_link0_header_ecc_error,s68_link0_header_ecc_check_en,s68_link0_error_correct0,s68_link0_error_correct1,s68_link0_error_correct2,s68_link0_error_correct3,\
                                    s68_link1_header_ecc_error,s68_link1_header_ecc_check_en,s68_link1_error_correct0,s68_link1_error_correct1,s68_link1_error_correct2,s68_link1_error_correct3,\
                                    s68_link2_header_ecc_error,s68_link2_header_ecc_check_en,s68_link2_error_correct0,s68_link2_error_correct1,s68_link2_error_correct2,s68_link2_error_correct3,\
                                    s68_link3_header_ecc_error,s68_link3_header_ecc_check_en,s68_link3_error_correct0,s68_link3_error_correct1,s68_link3_error_correct2,s68_link3_error_correct3,\
                                    s680_dlysetting, s680_hs_settle_time, s680_ls_settle_time,s681_dlysetting, s681_hs_settle_time, s681_ls_settle_time,\
                                    s682_dlysetting, s682_hs_settle_time, s682_ls_settle_time,s683_dlysetting, s683_hs_settle_time, s683_ls_settle_time,\
                                    s680_lpf,s681_lpf,s682_lpf,s683_lpf]
                                csv_writer.writerow(row)
                                print (row)
                                print ('\n')                                                               
                                print("vdd10 = ",vdd10) 
                                print("vdd18 = ",vdd18)
                                print("vddio = ",vddio)
                                print("voltyp = ",'sense_supply') 
                                time.sleep(1)
                    
                            for loop1 in range(200):
                                current_measure_gpp433=Gpp433.MeasureCurrent(channel=4)
                                time.sleep(0.5)
                                (curr_vdd10_q68,curr_vddio_q68,curr_vdd18_q68,vol_measure_vdd10_q68,vol_measure_vddio_q68,vol_measure_vdd18_q68,power_total_q68) = PowerMeause(powertype='keithley2230G',power_instr=Keithley2230G1) 
                                time.sleep(0.2)
                                temp_read=oven.tempread()           
                                q68.dongle.setBusSpeed(1, 100)
                                link0_status = q68.c2m.rd_test_fsm_status1_link0()
                                link1_status = q68.c2m.rd_test_fsm_status1_link1()
                                link2_status = q68.c2m.rd_test_fsm_status2_link2()
                                link3_status = q68.c2m.rd_test_fsm_status2_link3()
                                mdi_status0 = q68.c2m.rd_rx_router_status_mdi_status0(i=0) 
                                mdi_status1 = q68.c2m.rd_rx_router_status_mdi_status0(i=1)
                                mdi_status2 = q68.c2m.rd_rx_router_status_mdi_status0(i=2)
                                mdi_status3 = q68.c2m.rd_rx_router_status_mdi_status0(i=3)
                                line_status0 = hex(q68.c2m.rd_rx_router_status_line_status0(i=0))
                                line_status1 = hex(q68.c2m.rd_rx_router_status_line_status0(i=1))
                                line_status2 = hex(q68.c2m.rd_rx_router_status_line_status0(i=2))
                                line_status3 = hex(q68.c2m.rd_rx_router_status_line_status0(i=3))
                                line_status4 = hex(q68.c2m.rd_rx_router_status_line_status0(i=4))
                                line_status5 = hex(q68.c2m.rd_rx_router_status_line_status0(i=5))
                                line_status6 = hex(q68.c2m.rd_rx_router_status_line_status0(i=6))
                                line_status7 = hex(q68.c2m.rd_rx_router_status_line_status0(i=7))
                                link0_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=0))
                                link1_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=1))
                                link2_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=2))
                                link3_tx_status0 = hex(q68.c2m.rd_fh_tx_status0(i=3))
                                link0_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=0))
                                link1_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=1))
                                link2_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=2))
                                link3_tx_status1 = hex(q68.c2m.rd_fh_tx_status1(i=3))
                                link0_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=0))
                                link1_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=1))
                                link2_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=2))
                                link3_tx_status2 = hex(q68.c2m.rd_fh_tx_status2(i=3))
                                link0_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=0))
                                link1_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=1))
                                link2_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=2))
                                link3_rx_status0 = hex(q68.c2m.rd_fh_rx_status0(i=3))
                                link0_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=0))
                                link1_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=1))
                                link2_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=2))
                                link3_rx_status2 = hex(q68.c2m.rd_fh_rx_status2(i=3))
                                link0_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=0))
                                link1_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=1))
                                link2_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=2))
                                link3_rx_status7 = hex(q68.c2m.rd_fh_rx_status7(i=3))
                                video_lock = q68.c2m.rd_sys_cfg_status0_video_lock()  
                                # log_err = HWImpl.hw_get_tracelog_status()   #1=error during start-stop hit(all vedio)
                                # print("log error = ",log_err)
                                rate0 = q68.c2m.rd_sys_cfg_link_ctrl1_rate0()
                                rate1 = q68.c2m.rd_sys_cfg_link_ctrl1_rate1()
                                rate2 = q68.c2m.rd_sys_cfg_link_ctrl1_rate2()
                                rate3 = q68.c2m.rd_sys_cfg_link_ctrl1_rate3()
                                bcrxrate0 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate0()
                                bcrxrate1 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate1()
                                bcrxrate2 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate2()
                                bcrxrate3 = q68.c2m.rd_sys_cfg_link_ctrl3_bc_rate3()                                                                                                                                                                               
                                (q68_plllock, q68_vco_band_cali_done, q68_vcoband) = q68.PHYPLLStatus() 
                                                                                                                                                                                                                                                                                                                                                                                 
                                frame_count0 = q68.FrameCount(csi = 0, vc=vcid0)-frame_count0_st
                                frame_count1 = q68.FrameCount(csi = 0, vc=vcid1)-frame_count1_st
                                frame_count2 = q68.FrameCount(csi = 1, vc=vcid2)-frame_count2_st
                                frame_count3 = q68.FrameCount(csi = 1, vc=vcid3)-frame_count3_st
                                
                                line_count0 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=0)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=0)
                                line_count1 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=1)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=1)
                                line_count2 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=10)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=10)
                                line_count3 = q68.c2m.rd_rx_router_status_line_cnt_high8(i=11)*256+q68.c2m.rd_rx_router_status_line_cnt_low8(i=11)
                                (rd_bmag_txbc_ch0,adp_khp_ch0,adp_h0_ch0,adp_a0_ch0,sel_afe_offsetcali_ch0,dcali_sb_afe_ch0,slicer0_forecali_ch0,slicer0_dcali_sb_ch0,slicer90_dcali_sb_ch0,slicer180_dcali_ch0,slicer270_dcali_sb_ch0,slicererr0_dcali_sb_ch0,slicererr180_dcali_sb_ch0,adp_b1_ch0,adp_b2_ch0,adp_b3_ch0,adp_t0_ch0,adp_t1_ch0,adp_t2_ch0,adp_t3_ch0,adp_t4_ch0,adp_t5_ch0,adp_t6_ch0,adp_t7_ch0,adp_t8_ch0,adp_t9_ch0,cdr_picode_a_ch0,cdr_picode_b_ch0,cdr_picode_c_ch0)=q68.C3ParasrecallCh0()
                                (rd_bmag_txbc_ch1,adp_khp_ch1,adp_h0_ch1,adp_a0_ch1,sel_afe_offsetcali_ch1,dcali_sb_afe_ch1,slicer0_forecali_ch1,slicer0_dcali_sb_ch1,slicer90_dcali_sb_ch1,slicer180_dcali_ch1,slicer270_dcali_sb_ch1,slicererr0_dcali_sb_ch1,slicererr180_dcali_sb_ch1,adp_b1_ch1,adp_b2_ch1,adp_b3_ch1,adp_t0_ch1,adp_t1_ch1,adp_t2_ch1,adp_t3_ch1,adp_t4_ch1,adp_t5_ch1,adp_t6_ch1,adp_t7_ch1,adp_t8_ch1,adp_t9_ch1,cdr_picode_a_ch1,cdr_picode_b_ch1,cdr_picode_c_ch1)=q68.C3ParasrecallCh1()
                                (rd_bmag_txbc_ch2,adp_khp_ch2,adp_h0_ch2,adp_a0_ch2,sel_afe_offsetcali_ch2,dcali_sb_afe_ch2,slicer0_forecali_ch2,slicer0_dcali_sb_ch2,slicer90_dcali_sb_ch2,slicer180_dcali_ch2,slicer270_dcali_sb_ch2,slicererr0_dcali_sb_ch2,slicererr180_dcali_sb_ch2,adp_b1_ch2,adp_b2_ch2,adp_b3_ch2,adp_t0_ch2,adp_t1_ch2,adp_t2_ch2,adp_t3_ch2,adp_t4_ch2,adp_t5_ch2,adp_t6_ch2,adp_t7_ch2,adp_t8_ch2,adp_t9_ch2,cdr_picode_a_ch2,cdr_picode_b_ch2,cdr_picode_c_ch2)=q68.C3ParasrecallCh2()
                                (rd_bmag_txbc_ch3,adp_khp_ch3,adp_h0_ch3,adp_a0_ch3,sel_afe_offsetcali_ch3,dcali_sb_afe_ch3,slicer0_forecali_ch3,slicer0_dcali_sb_ch3,slicer90_dcali_sb_ch3,slicer180_dcali_ch3,slicer270_dcali_sb_ch3,slicererr0_dcali_sb_ch3,slicererr180_dcali_sb_ch3,adp_b1_ch3,adp_b2_ch3,adp_b3_ch3,adp_t0_ch3,adp_t1_ch3,adp_t2_ch3,adp_t3_ch3,adp_t4_ch3,adp_t5_ch3,adp_t6_ch3,adp_t7_ch3,adp_t8_ch3,adp_t9_ch3,cdr_picode_a_ch3,cdr_picode_b_ch3,cdr_picode_c_ch3)=q68.C3ParasrecallCh3()
                                # q68_remote.dongle.devAddr = 0x44 
                                # time.sleep(0.1)  
                                # q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                # time.sleep(0.1)
                                q68_remote.dongle.setBusSpeed(1, 100)
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=3, link1=0,link2=0,link3=0)   
                                q68_remote.dongle.devAddr = s680_iic_dev          
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link0_rx_status0_remote_tx_fail,link0_rx_status0_local_rx_fail,link0_rx_status0_pkt_timeout,link0_rx_status0_video_sn_err,link0_rx_status0_intf_fifo_uv,link0_rx_status0_intf_fifo_ov,\
                                link0_rx_status1_pkt_crc_err,link0_rx_status2_i2c_cfg_err,link0_rx_status2_sop_kchar_dup_8b10b,link0_rx_status2_sop_kchar_err_8b10b,link0_rx_status2_pkt_type_illegal,link0_rx_status2_pp_fifo_full,\
                                link0_rx_status2_dec_fault,link0_rx_status2_sync_fault,link0_rx_status3_pkt_len_err,link0_rx_status4_pkt_crc_fifo_full,link0_rx_status5_pkt_data_fifo_full,link0_rx_status6_pkt_ack_crc_err,\
                                link0_rd_efh_tx_status0,link0_rd_efh_tx_status1,link0_rd_efh_tx_status2,link0_rd_efh_tx_status3,link0_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link0_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link0_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link0_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link0_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link0_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link0_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3()    
                                s680_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()               
                                (vcid_s680,data_type_s680,word_count_s680)=q68_remote.M2CMIPIDataInfo()
                                err_lane0_s680 = q68_remote.M2CMIPILaneErrCheck(0)
                                err_lane1_s680 = q68_remote.M2CMIPILaneErrCheck(1)  
                                err_lane2_s680 = q68_remote.M2CMIPILaneErrCheck(2)  
                                err_lane3_s680 = q68_remote.M2CMIPILaneErrCheck(3) 
                                err_lane4_s680 = q68_remote.M2CMIPILaneErrCheck(4)
                                (err_ecc_s680, err_crc_s680) =q68_remote.M2CMIPIDataErrCheck(vcid =0)
                                # q68_remote.m2c.wr_mipi_rx_mipi_dig_rx_int_clear_fields(mipi_int=1)
                                # q68_remote.m2c.wr_mipi_rx_mipi_dig_rx_int_clear_fields(mipi_int=0)
                                
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=3,link2=0,link3=0)   
                                q68_remote.dongle.devAddr = 0x44          
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link1_rx_status0_remote_tx_fail,link1_rx_status0_local_rx_fail,link1_rx_status0_pkt_timeout,link1_rx_status0_video_sn_err,link1_rx_status0_intf_fifo_uv,link1_rx_status0_intf_fifo_ov,\
                                link1_rx_status1_pkt_crc_err,link1_rx_status2_i2c_cfg_err,link1_rx_status2_sop_kchar_dup_8b10b,link1_rx_status2_sop_kchar_err_8b10b,link1_rx_status2_pkt_type_illegal,link1_rx_status2_pp_fifo_full,\
                                link1_rx_status2_dec_fault,link1_rx_status2_sync_fault,link1_rx_status3_pkt_len_err,link1_rx_status4_pkt_crc_fifo_full,link1_rx_status5_pkt_data_fifo_full,link1_rx_status6_pkt_ack_crc_err,\
                                link1_rd_efh_tx_status0,link1_rd_efh_tx_status1,link1_rd_efh_tx_status2,link1_rd_efh_tx_status3,link1_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link1_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link1_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link1_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link1_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link1_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link1_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3() 
                                s681_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                (vcid_s681,data_type_s681,word_count_s681)=q68_remote.M2CMIPIDataInfo()
                                err_lane0_s681 = q68_remote.M2CMIPILaneErrCheck(0)
                                err_lane1_s681 = q68_remote.M2CMIPILaneErrCheck(1)  
                                err_lane2_s681 = q68_remote.M2CMIPILaneErrCheck(2)  
                                err_lane3_s681 = q68_remote.M2CMIPILaneErrCheck(3) 
                                err_lane4_s681 = q68_remote.M2CMIPILaneErrCheck(4)
                                (err_ecc_s681, err_crc_s681) =q68_remote.M2CMIPIDataErrCheck(vcid =1)
                                # q68_remote.m2c.wr_mipi_rx_mipi_dig_rx_int_clear_fields(mipi_int=1)  # clear ecc/crc error
                                # q68_remote.m2c.wr_mipi_rx_mipi_dig_rx_int_clear_fields(mipi_int=0)
                                
                                
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0,link2=3,link3=0)   
                                q68_remote.dongle.devAddr = s682_iic_dev          
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link2_rx_status0_remote_tx_fail,link2_rx_status0_local_rx_fail,link2_rx_status0_pkt_timeout,link2_rx_status0_video_sn_err,link2_rx_status0_intf_fifo_uv,link2_rx_status0_intf_fifo_ov,\
                                link2_rx_status1_pkt_crc_err,link2_rx_status2_i2c_cfg_err,link2_rx_status2_sop_kchar_dup_8b10b,link2_rx_status2_sop_kchar_err_8b10b,link2_rx_status2_pkt_type_illegal,link2_rx_status2_pp_fifo_full,\
                                link2_rx_status2_dec_fault,link2_rx_status2_sync_fault,link2_rx_status3_pkt_len_err,link2_rx_status4_pkt_crc_fifo_full,link2_rx_status5_pkt_data_fifo_full,link2_rx_status6_pkt_ack_crc_err,\
                                link2_rd_efh_tx_status0,link2_rd_efh_tx_status1,link2_rd_efh_tx_status2,link2_rd_efh_tx_status3,link2_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link2_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link2_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link2_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link2_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link2_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link2_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3() 
                                s682_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                (vcid_s682,data_type_s682,word_count_s682)=q68_remote.M2CMIPIDataInfo()
                                err_lane0_s682 = q68_remote.M2CMIPILaneErrCheck(0)
                                err_lane1_s682 = q68_remote.M2CMIPILaneErrCheck(1)  
                                err_lane2_s682 = q68_remote.M2CMIPILaneErrCheck(2)  
                                err_lane3_s682 = q68_remote.M2CMIPILaneErrCheck(3) 
                                err_lane4_s682 = q68_remote.M2CMIPILaneErrCheck(4)
                                (err_ecc_s682, err_crc_s682) =q68_remote.M2CMIPIDataErrCheck(vcid =2)
                                q68_remote.IICRemoteConfig(devAddr=q68_iic_add, link0=0, link1=0,link2=0,link3=3)   
                                q68_remote.dongle.devAddr = 0x44          
                                q68_remote.M2CSPI0Debug(ss_dly_clks=53)
                                (link3_rx_status0_remote_tx_fail,link3_rx_status0_local_rx_fail,link3_rx_status0_pkt_timeout,link3_rx_status0_video_sn_err,link3_rx_status0_intf_fifo_uv,link3_rx_status0_intf_fifo_ov,\
                                link3_rx_status1_pkt_crc_err,link3_rx_status2_i2c_cfg_err,link3_rx_status2_sop_kchar_dup_8b10b,link3_rx_status2_sop_kchar_err_8b10b,link3_rx_status2_pkt_type_illegal,link3_rx_status2_pp_fifo_full,\
                                link3_rx_status2_dec_fault,link3_rx_status2_sync_fault,link3_rx_status3_pkt_len_err,link3_rx_status4_pkt_crc_fifo_full,link3_rx_status5_pkt_data_fifo_full,link3_rx_status6_pkt_ack_crc_err,\
                                link3_rd_efh_tx_status0,link3_rd_efh_tx_status1,link3_rd_efh_tx_status2,link3_rd_efh_tx_status3,link3_rd_efh_mipi_rx_status0,)=q68_remote.EfhStatusReadOnly()
                                
                                s68_link3_header_ecc_error = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()      
                                s68_link3_header_ecc_check_en = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_en()               
                                s68_link3_error_correct0 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_0()                   
                                s68_link3_error_correct1 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_1()                   
                                s68_link3_error_correct2 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_2()                   
                                s68_link3_error_correct3 = q68_remote.m2c.rd_csi2_rx_csi_rx_int_hdr_err_correct_3() 
                                s683_lpf = q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                (vcid_s683,data_type_s683,word_count_s683)=q68_remote.M2CMIPIDataInfo()
                                err_lane0_s683 = q68_remote.M2CMIPILaneErrCheck(0)
                                err_lane1_s683 = q68_remote.M2CMIPILaneErrCheck(1)  
                                err_lane2_s683 = q68_remote.M2CMIPILaneErrCheck(2)  
                                err_lane3_s683 = q68_remote.M2CMIPILaneErrCheck(3) 
                                err_lane4_s683 = q68_remote.M2CMIPILaneErrCheck(4)
                                (err_ecc_s683, err_crc_s683) =q68_remote.M2CMIPIDataErrCheck(vcid =3)
                                
                                # q68_remote.m2c.rd_test_bcrx_lpfc_dacctr()
                                print('frame count0 is',frame_count0)
                                print('frame count1 is',frame_count1)
                                print('frame count2 is',frame_count2)
                                print('frame count3 is',frame_count3)
                                row=[datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'),str(BOARDID),str(CHIPID),loop,loop1,starttemp,temp_read,tempdrif,current_measure_gpp433,curr_vdd10_q68,curr_vddio_q68,curr_vdd18_q68,vol_measure_vdd10_q68,vol_measure_vddio_q68,vol_measure_vdd18_q68,power_total_q68,\
                                    q68_plllock,q68_vco_band_cali_done,q68_vcoband,frame_count0_st,frame_count1_st,frame_count2_st,frame_count3_st,frame_count0, frame_count1,frame_count2,frame_count3,line_count0_st,line_count1_st,line_count2_st,line_count3_st,line_count0,line_count1,line_count2,line_count3,\
                                    link0_status,link1_status,link2_status,link3_status,video_lock,mdi_status0, mdi_status1,mdi_status2,mdi_status3,\
                                    line_status0,line_status1,line_status2,line_status3,line_status4,line_status5,line_status6,line_status7,link0_tx_status0,link1_tx_status0,link2_tx_status0,link3_tx_status0,link0_tx_status1,link1_tx_status1,link2_tx_status1,link3_tx_status1,\
                                    link0_tx_status2,link1_tx_status2,link2_tx_status2,link3_tx_status2,link0_rx_status0,link1_rx_status0,link2_rx_status0,link3_rx_status0,link0_rx_status2,link1_rx_status2,link2_rx_status2,link3_rx_status2,link0_rx_status7,link1_rx_status7,link2_rx_status7,link3_rx_status7,\
                                    rd_bmag_txbc_ch0,adp_khp_ch0,adp_h0_ch0,adp_a0_ch0,sel_afe_offsetcali_ch0,dcali_sb_afe_ch0,slicer0_forecali_ch0,slicer0_dcali_sb_ch0,slicer90_dcali_sb_ch0,slicer180_dcali_ch0,slicer270_dcali_sb_ch0,slicererr0_dcali_sb_ch0,slicererr180_dcali_sb_ch0,adp_b1_ch0,adp_b2_ch0,\
                                    adp_b3_ch0,adp_t0_ch0,adp_t1_ch0,adp_t2_ch0,adp_t3_ch0,adp_t4_ch0,adp_t5_ch0,adp_t6_ch0,adp_t7_ch0,adp_t8_ch0,adp_t9_ch0,cdr_picode_a_ch0,cdr_picode_b_ch0,cdr_picode_c_ch0,\
                                    rd_bmag_txbc_ch1,adp_khp_ch1,adp_h0_ch1,adp_a0_ch1,sel_afe_offsetcali_ch1,dcali_sb_afe_ch1,slicer0_forecali_ch1,slicer0_dcali_sb_ch1,slicer90_dcali_sb_ch1,slicer180_dcali_ch1,slicer270_dcali_sb_ch1,slicererr0_dcali_sb_ch1,slicererr180_dcali_sb_ch1,\
                                    adp_b1_ch1,adp_b2_ch1,adp_b3_ch1,adp_t0_ch1,adp_t1_ch1,adp_t2_ch1,adp_t3_ch1,adp_t4_ch1,adp_t5_ch1,adp_t6_ch1,adp_t7_ch1,adp_t8_ch1,adp_t9_ch1,cdr_picode_a_ch1,cdr_picode_b_ch1,cdr_picode_c_ch1,\
                                    rd_bmag_txbc_ch2,adp_khp_ch2,adp_h0_ch2,adp_a0_ch2,sel_afe_offsetcali_ch2,dcali_sb_afe_ch2,slicer0_forecali_ch2,slicer0_dcali_sb_ch2,slicer90_dcali_sb_ch2,slicer180_dcali_ch2,slicer270_dcali_sb_ch2,\
                                    slicererr0_dcali_sb_ch2,slicererr180_dcali_sb_ch2,adp_b1_ch2,adp_b2_ch2,adp_b3_ch2,adp_t0_ch2,adp_t1_ch2,adp_t2_ch2,adp_t3_ch2,adp_t4_ch2,adp_t5_ch2,adp_t6_ch2,adp_t7_ch2,adp_t8_ch2,adp_t9_ch2,cdr_picode_a_ch2,cdr_picode_b_ch2,cdr_picode_c_ch2,\
                                    rd_bmag_txbc_ch3,adp_khp_ch3,adp_h0_ch3,adp_a0_ch3,sel_afe_offsetcali_ch3,dcali_sb_afe_ch3,slicer0_forecali_ch3,slicer0_dcali_sb_ch3,slicer90_dcali_sb_ch3,slicer180_dcali_ch3,slicer270_dcali_sb_ch3,\
                                    slicererr0_dcali_sb_ch3,slicererr180_dcali_sb_ch3,adp_b1_ch3,adp_b2_ch3,adp_b3_ch3,adp_t0_ch3,adp_t1_ch3,adp_t2_ch3,adp_t3_ch3,adp_t4_ch3,adp_t5_ch3,adp_t6_ch3,adp_t7_ch3,adp_t8_ch3,adp_t9_ch3,cdr_picode_a_ch3,cdr_picode_b_ch3,cdr_picode_c_ch3,\
                                    rate0,rate1,rate2,rate3, bcrxrate0,bcrxrate1,bcrxrate2,bcrxrate3,\
                                    link0_rx_status0_remote_tx_fail,link0_rx_status0_local_rx_fail,link0_rx_status0_pkt_timeout,link0_rx_status0_video_sn_err,link0_rx_status0_intf_fifo_uv,link0_rx_status0_intf_fifo_ov,\
                                    link0_rx_status1_pkt_crc_err,link0_rx_status2_i2c_cfg_err,link0_rx_status2_sop_kchar_dup_8b10b,link0_rx_status2_sop_kchar_err_8b10b,link0_rx_status2_pkt_type_illegal,link0_rx_status2_pp_fifo_full,\
                                    link0_rx_status2_dec_fault,link0_rx_status2_sync_fault,link0_rx_status3_pkt_len_err,link0_rx_status4_pkt_crc_fifo_full,link0_rx_status5_pkt_data_fifo_full,link0_rx_status6_pkt_ack_crc_err,\
                                    link0_rd_efh_tx_status0,link0_rd_efh_tx_status1,link0_rd_efh_tx_status2,link0_rd_efh_tx_status3,link0_rd_efh_mipi_rx_status0,\
                                    link1_rx_status0_remote_tx_fail,link1_rx_status0_local_rx_fail,link1_rx_status0_pkt_timeout,link1_rx_status0_video_sn_err,link1_rx_status0_intf_fifo_uv,link1_rx_status0_intf_fifo_ov,\
                                    link1_rx_status1_pkt_crc_err,link1_rx_status2_i2c_cfg_err,link1_rx_status2_sop_kchar_dup_8b10b,link1_rx_status2_sop_kchar_err_8b10b,link1_rx_status2_pkt_type_illegal,link1_rx_status2_pp_fifo_full,\
                                    link1_rx_status2_dec_fault,link1_rx_status2_sync_fault,link1_rx_status3_pkt_len_err,link1_rx_status4_pkt_crc_fifo_full,link1_rx_status5_pkt_data_fifo_full,link1_rx_status6_pkt_ack_crc_err,\
                                    link1_rd_efh_tx_status0,link1_rd_efh_tx_status1,link1_rd_efh_tx_status2,link1_rd_efh_tx_status3,link1_rd_efh_mipi_rx_status0,\
                                    link2_rx_status0_remote_tx_fail,link2_rx_status0_local_rx_fail,link2_rx_status0_pkt_timeout,link2_rx_status0_video_sn_err,link2_rx_status0_intf_fifo_uv,link2_rx_status0_intf_fifo_ov,\
                                    link2_rx_status1_pkt_crc_err,link2_rx_status2_i2c_cfg_err,link2_rx_status2_sop_kchar_dup_8b10b,link2_rx_status2_sop_kchar_err_8b10b,link2_rx_status2_pkt_type_illegal,link2_rx_status2_pp_fifo_full,\
                                    link2_rx_status2_dec_fault,link2_rx_status2_sync_fault,link2_rx_status3_pkt_len_err,link2_rx_status4_pkt_crc_fifo_full,link2_rx_status5_pkt_data_fifo_full,link2_rx_status6_pkt_ack_crc_err,\
                                    link2_rd_efh_tx_status0,link2_rd_efh_tx_status1,link2_rd_efh_tx_status2,link2_rd_efh_tx_status3,link2_rd_efh_mipi_rx_status0,\
                                    link3_rx_status0_remote_tx_fail,link3_rx_status0_local_rx_fail,link3_rx_status0_pkt_timeout,link3_rx_status0_video_sn_err,link3_rx_status0_intf_fifo_uv,link3_rx_status0_intf_fifo_ov,\
                                    link3_rx_status1_pkt_crc_err,link3_rx_status2_i2c_cfg_err,link3_rx_status2_sop_kchar_dup_8b10b,link3_rx_status2_sop_kchar_err_8b10b,link3_rx_status2_pkt_type_illegal,link3_rx_status2_pp_fifo_full,\
                                    link3_rx_status2_dec_fault,link3_rx_status2_sync_fault,link3_rx_status3_pkt_len_err,link3_rx_status4_pkt_crc_fifo_full,link3_rx_status5_pkt_data_fifo_full,link3_rx_status6_pkt_ack_crc_err,\
                                    link3_rd_efh_tx_status0,link3_rd_efh_tx_status1,link3_rd_efh_tx_status2,link3_rd_efh_tx_status3,link3_rd_efh_mipi_rx_status0,\
                                    vcid_s680,data_type_s680,word_count_s680,err_lane0_s680,err_lane1_s680,err_lane2_s680,err_lane3_s680,err_lane4_s680,err_ecc_s680, err_crc_s680,vcid_s681,data_type_s681,word_count_s681,err_lane0_s681,err_lane1_s681,err_lane2_s681,err_lane3_s681,err_lane4_s681,err_ecc_s681, err_crc_s681,\
                                    vcid_s682,data_type_s682,word_count_s682,err_lane0_s682,err_lane1_s682,err_lane2_s682,err_lane3_s682,err_lane4_s682,err_ecc_s682, err_crc_s682,vcid_s683,data_type_s683,word_count_s683,err_lane0_s683,err_lane1_s683,err_lane2_s683,err_lane3_s683,err_lane4_s683,err_ecc_s683, err_crc_s683,\
                                    s68_link0_header_ecc_error,s68_link0_header_ecc_check_en,s68_link0_error_correct0,s68_link0_error_correct1,s68_link0_error_correct2,s68_link0_error_correct3,\
                                    s68_link1_header_ecc_error,s68_link1_header_ecc_check_en,s68_link1_error_correct0,s68_link1_error_correct1,s68_link1_error_correct2,s68_link1_error_correct3,\
                                    s68_link2_header_ecc_error,s68_link2_header_ecc_check_en,s68_link2_error_correct0,s68_link2_error_correct1,s68_link2_error_correct2,s68_link2_error_correct3,\
                                    s68_link3_header_ecc_error,s68_link3_header_ecc_check_en,s68_link3_error_correct0,s68_link3_error_correct1,s68_link3_error_correct2,s68_link3_error_correct3,\
                                    s680_dlysetting, s680_hs_settle_time, s680_ls_settle_time,s681_dlysetting, s681_hs_settle_time, s681_ls_settle_time,\
                                    s682_dlysetting, s682_hs_settle_time, s682_ls_settle_time,s683_dlysetting, s683_hs_settle_time, s683_ls_settle_time,\
                                    s680_lpf,s681_lpf,s682_lpf,s683_lpf]
                                csv_writer.writerow(row)
                                print (row)
                                print ('\n')

                                            
                             
                                     


#
# def holdequip():
#     voltageSetup(volset=False,turnoff=True) #turn off power supply




#==========================================
# Create file to save the results
#==========================================
# app = wx.PySimpleApp()
# localtime = time.strftime('%Y-%m-%d_%X').replace(':', '-')
# filedialog = wx.FileDialog(None, "Select File to save to", "", "", "*.csv")
# filedialog.ShowModal()
# file = filedialog.GetFilename()[:-4]+'_'+localtime+'.csv'
# dir = filedialog.GetDirectory()
#
# path = os.path.join(dir, file)
#
# # if user hits cancel then path='' so don't do anything
# if(path):
#     f = open(path, 'a')  # 'w' for write or 'a' for append
# else:
#     sys.exit()
#
# f = open(path, 'wb', buffering=0)
# writer = csv.writer(f, delimiter=',')
# writer.writerow(('skew','temp','voltyp','vddio','vdd18','vdd12','vdd10','PLLInternalLDO_dict[pllinternaldo]','cfgdldo', 'crossbarphy0', 'crossbarphy1', 'crossbarphy2','mdivint_read', 'postdiv_read', 'mdivfraction_read', 'boot_en',\
#               'state_r_boot50', 'state_r_boot51', 'dco_cal_error0', 'dco_cal_error1', 'res_cal_error0', 'res_cal_error1', 'boot_done0', 'boot_done1',\
#               'restb_mipipll_reg0','restb_mipipll_reg1','en_mipipll_reg0','en_mipipll_reg1','en_mipipll_aldo_reg0','en_mipipll_aldo_reg1','en_mipipll_dldo_reg0','en_mipipll_dldo_reg1',\
#               'prbs_mode', 'prbs_en','vdd33a_current','vdd33_current','vdda_current','vdd_current'))
#
#
if __name__ == "__main__":

    PRBSCheck()