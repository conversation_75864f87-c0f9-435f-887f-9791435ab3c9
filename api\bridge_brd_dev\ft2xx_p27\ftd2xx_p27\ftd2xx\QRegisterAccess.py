# packages
import ftd2xx

# devices
ftd2xxSpiDev=ftd2xx.FTD2XX
ftd2xxI2cDev=ftd2xx.FTD2XX

def findMeritechI2cChan():
    global ftd2xxI2cDev
    devNum = ftd2xx.createDeviceInfoList()
    #print (devNum)
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        print info
        if "Meritech Bridge A" in info['description'].decode():
            ftd2xxI2cDev = ftd2xx.open(i)
            ftd2xxI2cDev.i2cInit(1000e3, 0x01)
            return True
    return False

def findMeritechSpiChan():
    global ftd2xxSpiDev
    devNum = ftd2xx.createDeviceInfoList()
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        if "Meritech Bridge B" in info['description'].decode():
            ftd2xxSpiDev = ftd2xx.open(i, False)
            ftd2xxSpiDev.spiInit(10e6, 0, 3, 0, 0)
            return True
    return False

def findMeritechFt4232GpioChan():
    ftd2xxGpioDev = None
    devNum = ftd2xx.createDeviceInfoList()
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        if "Meritech Bridge A" in info['description'].decode():
            ftd2xxGpioDev = ftd2xx.open(i)
            ftd2xxGpioDev.gpioInit()
            break
    return ftd2xxGpioDev

def find1860SpiChan():
    global ftd2xxSpiDev
    devNum = ftd2xx.createDeviceInfoList()
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        if "LARK" in info['description'].decode():
            ftd2xxSpiDev = ftd2xx.open(i + 1, False)
            ftd2xxSpiDev.spiInit(10e6, 0, 3, 0, 0)
            return True
    return False

def spiRead1860Reg(addr):
    sentbuffer = (0x01, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff, 0x00)
    ftd2xxSpiDev.spiWrite(sentbuffer, 0x2)
    rdBuf = ftd2xxSpiDev.spiRead(1, 0x6)
    value = rdBuf[0]
    return ord(value)

def spiWrite1860Reg(addr, value):
    sentbuffer = (0x00, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff, 0x00, value)
    ftd2xxSpiDev.spiWrite(sentbuffer, 0x6)
    return True

def spiReadADDAReg(addr):
    addrac=(2<<14) + addr
    sentbuffer = ((addrac >> 8) & 0xff, addrac & 0xff)
    ftd2xxSpiDev.spiWrite(sentbuffer, 0x2)
    rdBuf = ftd2xxSpiDev.spiRead(4, 0x6)
    value = rdBuf[0]
    return ord(value)

def spiWriteADDAReg(addr, value):
    sentbuffer = ((addr >> 8) & 0xff, addr & 0xff, 0x00, (value>>24)&0xFF, (value>>16)&0xFF,(value>>8)&0xFF,value&0xFF)
    ftd2xxSpiDev.spiWrite(sentbuffer, 0x6)
    return True

def i2cReadAxiMasterReg(addr):
    wrBuf = ((addr >> 8) & 0xff, addr & 0xff)
    ftd2xxI2cDev.i2cWrite(0x70, wrBuf, 0x1)
    rdBuf = ftd2xxI2cDev.i2cRead(0x70, 4, 0x3)
    value = ord(rdBuf[0]) + ord(rdBuf[1] << 8) + ord(rdBuf[2] << 16) + ord(rdBuf[3] << 24)
    return value

def i2cWriteAxiMasterReg(addr, value):
    wrBuf = ((addr >> 8) & 0xff, addr & 0xff, value & 0xff, (value >> 8) & 0xff, (value >> 16) & 0xff, (value >> 24) & 0xff)
    ftd2xxI2cDev.i2cWrite(0x70, wrBuf, 0x3)
    return True

def i2cReadC3TxTestReg(devAddr, regAddr):
    wrBuf = (regAddr,)
    ftd2xxI2cDev.i2cWrite(devAddr, wrBuf, 0x1)
    rdBuf = ftd2xxI2cDev.i2cRead(devAddr, 1, 0x3)
    value = ord(rdBuf[0])
    return value

def i2cWriteC3TxTestReg(devAddr, regAddr, value):
    wrBuf = (regAddr, value)
    ftd2xxI2cDev.i2cWrite(devAddr, wrBuf, 0x3)
    return True

def M66S68_Write_Bitfield(devi_addr,regi_addr, bitnum, startbit,value):
    '''
    Inputs:
          devi_addr: Int - I2C communication adress
          regi_addr: Int - register address you want to write
          bitnum: how many bits will to write from lowest bit you want to write
          startbit: lowest bit you want to write
          value: data to write to register
    Returns:
          N/A
    '''
    bitsToSave = 2 ** 8 - 2 ** (startbit + bitnum) + 2 ** startbit - 1
    reg_data = i2cReadC3TxTestReg(devi_addr, regi_addr) & bitsToSave
    data1 = reg_data + (value << startbit)
    i2cWriteC3TxTestReg(devi_addr, regi_addr, data1) 

def M66S68_Read_Bitfield(devi_addr ,regi_addr, bitnum, startbit):
    '''
    Inputs:
          devi_addr: Int - I2C communication adress
          regi_addr: Int - register address you want to write
          bitnum: how many bits will to read from lowest bit you want to write
          startbit: lowest bit you want to read
    Returns:
          value: register readback value 
    '''
    bitsToGet = 0xff-(2**8-2**(startbit+bitnum)+2**startbit-1) 
    value =  (i2cReadC3TxTestReg(devi_addr, regi_addr) & bitsToGet) >> startbit
    return value
    
def M66S68_Write_Register(devi_addr,regi_addr, value):
    '''
    Inputs:
          devi_addr: Int - I2C communication adress
          regi_addr: Int - register address you want to write
          value: data to write to register
    Returns:
          N/A
    '''
    i2cWriteC3TxTestReg(devi_addr, regi_addr, value) 
       
def M66S68_Read_Register(devi_addr,regi_addr):
    '''
    Inputs:
          devi_addr: Int - I2C communication adress
          regi_addr: Int - register address you want to write
    Returns:
          value: register readback value 
    '''
    value =  i2cReadC3TxTestReg(devi_addr, regi_addr)
    return value

def spiReadC3(addr, size):
    sentbuffer = (0x01, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff)
    ftd2xxSpiDev.spiWrite(sentbuffer, 0x2)
    rdBuf = ftd2xxSpiDev.spiRead(size, 0x6)
    value = ord(rdBuf[0])
    return value

def axiI2cMasterEnable():
    i2cWriteAxiMasterReg(0x0100, 0x02) # CR, reset tx fifo
    i2cWriteAxiMasterReg(0x0100, 0x01) # CR, enable axi-iic
    return

def axiI2cMasterWrite(devAddr, regAddr, regValue):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:            
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddr << 1)) # tx fifo, start + dev addr
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x200 | regValue)       # tx fifo, reg value + stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    return True

def axiI2cMasterRead(devAddr, regAddr):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddr << 1)) # tx fifo, start + dev addr (w)
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x101 | (devAddr << 1)) # tx fifo, start + dev addr (r)
    i2cWriteAxiMasterReg(0x0108, 0x200)                  # tx fifo, stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    value = i2cReadAxiMasterReg( 0x010c) # rx fifo
    return value

def readReg(moduleName, addr):
    return i2cReadAxiMasterReg(moduleName, addr)

def writeReg(moduleName, addr, value):
    return i2cWriteAxiMasterReg(moduleName, addr, value)

def readSingleData(pollingName):
    data  = []
    return data

def readBulkData(pollingName, dataLen):
    data  = []
    return data
