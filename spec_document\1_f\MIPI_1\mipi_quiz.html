<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MIPI接收模块面试问答 - 记忆助手</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .question-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        
        .question-number {
            background: #007bff;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .chapter-tag {
            background: #6c757d;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .question-text {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .answer-section {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .answer-text {
            color: #495057;
            line-height: 1.8;
            white-space: pre-wrap;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 MIPI接收模块面试问答</h1>
        <p>记忆助手 - 点击显示答案，测试你的掌握程度</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div class="stat-number" id="totalQuestions">0</div>
            <div class="stat-label">总题数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="answeredQuestions">0</div>
            <div class="stat-label">已练习</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="correctRate">0%</div>
            <div class="stat-label">掌握率</div>
        </div>
    </div>
    
    <div class="progress">
        <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
    </div>
    
    <div class="controls">
        <button class="btn btn-primary" onclick="loadQuestions()">📚 加载题目</button>
        <button class="btn btn-success" onclick="shuffleQuestions()">🎲 随机顺序</button>
        <button class="btn btn-warning" onclick="showAllAnswers()">👁️ 显示所有答案</button>
        <button class="btn btn-danger" onclick="hideAllAnswers()">🙈 隐藏所有答案</button>
    </div>
    
    <div id="questionsContainer"></div>
    
    <script>
        let questions = [];
        let answeredCount = 0;
        let correctCount = 0;
        
        // 示例问题数据 - 实际使用时需要从markdown文件解析
        const sampleQuestions = [
            {
                id: 1,
                chapter: "整体架构问答",
                question: "MIPI CSI-2接收链路的整体架构是怎样的？",
                answer: "本设计采用了一个以时序简化为核心的单时钟域架构，通过并行处理来匹配高速MIPI输入与后端视频时序，从而避免了复杂的跨时钟域问题。数据流：MIPI PHY→Lane Aligner→Packet Decoder→Raw Depacker→Debayer Filter→RGB888时序生成→HDMI输出。所有处理都在mipi_byte_clock域完成。"
            },
            {
                id: 2,
                chapter: "MIPI PHY模块问答",
                question: "什么是Gear比？为什么重要？",
                answer: "Gear比是串并转换比例，8x表示8:1串并转换。影响字节时钟频率：mipi_byte_clock = line_rate / gear_ratio。"
            }
            // 更多问题需要从实际markdown文件解析
        ];
        
        function loadQuestions() {
            questions = [...sampleQuestions];
            displayQuestions();
            updateStats();
            alert('📚 题目已加载！\n\n💡 提示：实际使用时需要解析markdown文件获取完整题目。\n当前显示的是示例数据。');
        }
        
        function displayQuestions() {
            const container = document.getElementById('questionsContainer');
            container.innerHTML = '';
            
            questions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-card';
                questionDiv.innerHTML = `
                    <div class="question-header">
                        <span class="question-number">第 ${index + 1} 题</span>
                        <span class="chapter-tag">${q.chapter}</span>
                    </div>
                    <div class="question-text">${q.question}</div>
                    <button class="btn btn-primary" onclick="toggleAnswer(${index})">
                        <span id="btn-text-${index}">显示答案</span>
                    </button>
                    <div class="answer-section" id="answer-${index}">
                        <div class="answer-text">${q.answer}</div>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-success" onclick="markCorrect(${index})">✅ 掌握</button>
                            <button class="btn btn-warning" onclick="markDifficult(${index})">🤔 一般</button>
                            <button class="btn btn-danger" onclick="markWrong(${index})">❌ 困难</button>
                        </div>
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }
        
        function toggleAnswer(index) {
            const answerDiv = document.getElementById(`answer-${index}`);
            const btnText = document.getElementById(`btn-text-${index}`);
            
            if (answerDiv.style.display === 'none' || answerDiv.style.display === '') {
                answerDiv.style.display = 'block';
                btnText.textContent = '隐藏答案';
            } else {
                answerDiv.style.display = 'none';
                btnText.textContent = '显示答案';
            }
        }
        
        function markCorrect(index) {
            if (!questions[index].answered) {
                questions[index].answered = true;
                questions[index].correct = true;
                answeredCount++;
                correctCount++;
                updateStats();
            }
            alert('✅ 标记为掌握！');
        }
        
        function markDifficult(index) {
            if (!questions[index].answered) {
                questions[index].answered = true;
                questions[index].correct = false;
                answeredCount++;
                updateStats();
            }
            alert('🤔 标记为一般，需要继续练习！');
        }
        
        function markWrong(index) {
            if (!questions[index].answered) {
                questions[index].answered = true;
                questions[index].correct = false;
                answeredCount++;
                updateStats();
            }
            alert('❌ 标记为困难，建议重点复习！');
        }
        
        function shuffleQuestions() {
            if (questions.length === 0) {
                alert('请先加载题目！');
                return;
            }
            
            for (let i = questions.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [questions[i], questions[j]] = [questions[j], questions[i]];
            }
            displayQuestions();
            alert('🎲 题目顺序已随机打乱！');
        }
        
        function showAllAnswers() {
            questions.forEach((_, index) => {
                const answerDiv = document.getElementById(`answer-${index}`);
                const btnText = document.getElementById(`btn-text-${index}`);
                if (answerDiv) {
                    answerDiv.style.display = 'block';
                    btnText.textContent = '隐藏答案';
                }
            });
        }
        
        function hideAllAnswers() {
            questions.forEach((_, index) => {
                const answerDiv = document.getElementById(`answer-${index}`);
                const btnText = document.getElementById(`btn-text-${index}`);
                if (answerDiv) {
                    answerDiv.style.display = 'none';
                    btnText.textContent = '显示答案';
                }
            });
        }
        
        function updateStats() {
            document.getElementById('totalQuestions').textContent = questions.length;
            document.getElementById('answeredQuestions').textContent = answeredCount;
            
            const rate = answeredCount > 0 ? Math.round((correctCount / answeredCount) * 100) : 0;
            document.getElementById('correctRate').textContent = rate + '%';
            
            const progress = questions.length > 0 ? Math.round((answeredCount / questions.length) * 100) : 0;
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = progress + '%';
            progressBar.textContent = progress + '%';
        }
        
        // 页面加载时的提示
        window.onload = function() {
            alert('🎯 欢迎使用MIPI面试问答记忆助手！\n\n📝 使用说明：\n1. 点击"加载题目"开始\n2. 点击"显示答案"查看答案\n3. 根据掌握程度标记题目\n4. 使用"随机顺序"打乱题目\n\n💡 当前为演示版本，实际使用需要解析完整的markdown文件。');
        };
    </script>
</body>
</html>
