#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import USBDataDeal
from HWInterface import TCPDataDeal
from HWInterface import SendD<PERSON><PERSON><PERSON>
from HWInterface import RecvDealCenter
import platform


# 获取连接的设备列表
def hw_serial_scan():
    return USBDataDeal.ser_ee_dev_scan()


# 开启指定的连接设备
def hw_open(port_name=None, ip_addr=None):
    if platform.system() == 'Linux':
        if port_name is not None:
            SendDealCenter.comm_open("/dev/"+port_name, ip_addr)
        else:
            SendDealCenter.comm_open(None, ip_addr)
    else:
        SendDealCenter.comm_open(port_name, ip_addr)


# 关闭指令的连接设备
def hw_close():
    SendDealCenter.comm_close()
#################################################################################################
# 发送指令

# ID:0x0000: 获取设备SN号
def hw_get_sn():
    return SendDealCenter.send_cmd_get_sn()


# ID:0x0001: 设置指定输出引脚的值
def hw_set_gpio(pin_num: int, pin_val: int):
    return SendDealCenter.send_cmd_set_gpio(pin_num, pin_val)


# ID:0x0002: 获取指定输入引脚的值
def hw_get_gpio(pin_num: int):
    return SendDealCenter.send_cmd_get_gpio(pin_num)


# ID:0x0003:设置I2C寄存器的值
# mode:0:r8v8 1:r8v16
def hw_i2c_set(mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):
    return SendDealCenter.send_cmd_i2c_set(mode, bus, dev_addr, reg_addr, reg_val)


# ID:0x0004:获取I2C寄存器的值
# mode 0:r8v8 1:r16v8
# return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
def hw_i2c_get(mode: int, bus: int, dev_addr: int, reg_addr: int):
    return SendDealCenter.send_cmd_i2c_get(mode, bus, dev_addr, reg_addr)


# ID:0x0005:设置数据的模式
# mode 0:真实数据(默认) 1:虚拟数据
def hw_set_mode(mode:int):
    return SendDealCenter.send_cmd_set_mode(mode)


# ID:0x0100:设置指定CAM通道宽高及格式
# channel:255是所有通道，其它是单独通道
def hw_set_video_cam(cam_w: int, cam_h: int, cam_fmt: int, channel: int = 0xFF):
    return SendDealCenter.send_cmd_set_video_cam(cam_w, cam_h, cam_fmt, channel)


# ID:0x0101: 重启设备
def hw_reboot():
    return SendDealCenter.send_cmd_reboot()


# ID:0x0102:设置指定WIN通道宽高
# channel:255是所有通道，其它是单独通道
def hw_set_video_win(win_w: int, win_h: int, win_x: int, win_y: int, channel: int = 0xFF):
    return SendDealCenter.send_cmd_set_video_win(win_w, win_h, win_x, win_y, channel)


# ID:0x0103: 打开指定video
def hw_open_video(index: int = 0):
    return SendDealCenter.send_cmd_open_video(index)


# ID:0x0104: 关闭指定video
def hw_close_video(index: int = 0):
    return SendDealCenter.send_cmd_close_video(index)


# ID:0x0105: 选择系统的配置文件
def hw_select_dtb(index: int = 0):
    return SendDealCenter.send_cmd_select_dtb(index)


# ID:0x0106: 保存指定通道的一帧数据
def hw_save_one_videoframe(index: int = 0):
    return SendDealCenter.send_cmd_save_one_videoframe(index)


# ID:0x0107: 切换设备上显示程序的页面
def hw_show_form_index(index: int = 0):
    return SendDealCenter.send_cmd_show_form_index(index)

# ID:0x0300: 设置MIPI: csiindex -> csiport phy_mode bus_width discontinuous_clk pix_clk_hz serdes_pix_clk_hz mclk_multiplier
# 设置 csi控制器 对应的引脚，及lane/trio数和时钟参数
# phy_mode: 0:DPHY 1:CPHY
# discontinuous_clk: 0:discontinuous 1:continuous
# mclk_multiplier:可以使用默认的3.01
def hw_set_csi_param(csiindex:int = 0, csiport:int = 0, phy_mode:int = 0, bus_width:int = 1, discontinuous_clk:int = 0, pix_clk_hz:int = 74250000, serdes_pix_clk_hz:int = 156250000, mclk_multiplier:float = 3.01):
    return SendDealCenter.send_cmd_set_csi_param(csiindex, csiport, phy_mode, bus_width, discontinuous_clk, pix_clk_hz, serdes_pix_clk_hz, mclk_multiplier)

# ID:0x0301: 设置MIPI: csiindex channel(start-end) -> vcid active_w active_h line_length data_fmt
# 每一个 csi控制器 预分配了四个channel, 所以channel(start-end)在0~3.
# 0:UYVY 1:YUYV (10:RAW8 暂不支持) 11:RAW10 12:RAW12 20:RGB888
def hw_set_csi_channel_param(csiindex:int = 0, channel:int = 0, vcid:int = 0, active_w:int = 1280, active_h:int = 720, line_length:int = 2560, data_fmt:int = 0):
    return SendDealCenter.send_cmd_set_csi_channel_param(csiindex, channel, vcid, active_w, active_h, line_length, data_fmt)


# ID:0x0302: 保存MIPI配置
def hw_save_mipi_param():
    return SendDealCenter.send_cmd_save_mipi_param()

# ID:0x0303: 启动日志保存
def hw_start_tracelog():
    return SendDealCenter.send_cmd_start_tracelog()

# ID:0x0304: 关闭日志保存
def hw_stop_tracelog():
    return SendDealCenter.send_cmd_stop_tracelog()

# ID:0x0305: 清除缓存的日志内容和解析状态，从当前时刻开始解析
def hw_clear_tracelog_status():
    return SendDealCenter.send_cmd_clear_tracelog_status()

# ID:0x0306: 获取到目前位置，mipilog的解析结果
def hw_get_tracelog_status():
    return SendDealCenter.send_cmd_get_tracelog_status()

# ID:0x0307: 获取对应通道帧率
def hw_get_video_info(channel: int = 0):
    return SendDealCenter.send_cmd_get_video_info(channel)

# ID:0x0308: 设置tracelog 单个文件大小和总日志大小的限制。单位MByte
def hw_set_tracelog_info(total_size:int = 10240, file_size:int = 2):
    return SendDealCenter.send_cmd_set_tracelog_info(total_size, file_size)

# 发送指令_END
#################################################################################################


#################################################################################################
# 接收状态


# 等待设备
def hw_waithw_ok(timeout:int):
    return RecvDealCenter.wait_hw_ok(timeout)

# 接收状态_END
#################################################################################################
