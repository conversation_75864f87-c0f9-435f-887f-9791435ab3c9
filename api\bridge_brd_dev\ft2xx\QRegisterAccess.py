# packages
import ftd2xx

# devices
ftd2xxSpiDev:ftd2xx.FTD2XX
ftd2xxI2cDev:ftd2xx.FTD2XX

def findMeritechI2cChan():
    global ftd2xxI2cDev
    devNum = ftd2xx.createDeviceInfoList()
    print (devNum)
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        print(info)
        if "Meritech Bridge A" in info['description'].decode():
            ftd2xxI2cDev = ftd2xx.open(i)
            ftd2xxI2cDev.i2cInit(1000e3, 0x01)
            return True
    return False

def findMeritechSpiChan():
    global ftd2xxSpiDev
    devNum = ftd2xx.createDeviceInfoList()
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        if "Meritech Bridge B" in info['description'].decode():
            ftd2xxSpiDev = ftd2xx.open(i, False)
            ftd2xxSpiDev.spiInit(10e6, 0, 3, 0, 0)
            return True
    return False

def findMeritechFt4232GpioChan(channel:int):
    ftd2xxGpioDev = None
    devNum = ftd2xx.createDeviceInfoList()
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        if "Meritech" in info['description'].decode():
            ftd2xxGpioDev = ftd2xx.open(i + channel)
            ftd2xxGpioDev.gpioInit()
            break
    return ftd2xxGpioDev

def find1860SpiChan():
    global ftd2xxSpiDev
    devNum = ftd2xx.createDeviceInfoList()
    for i in range(devNum):
        info = ftd2xx.getDeviceInfoDetail(i, False)
        if "LARK" in info['description'].decode():
            ftd2xxSpiDev = ftd2xx.open(i + 1, False)
            ftd2xxSpiDev.spiInit(10e6, 0, 3, 0, 0)
            return True
    return False

def spiRead1860Reg(addr : int) -> int:
    sentbuffer = (0x01, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff, 0x00)
    ftd2xxSpiDev.spiWrite(bytes(sentbuffer), 0x2)
    rdBuf = ftd2xxSpiDev.spiRead(1, 0x6)
    value = rdBuf[0]
    return value

def spiWrite1860Reg(addr : int, value : int) -> int:
    sentbuffer = (0x00, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff, 0x00, value)
    ftd2xxSpiDev.spiWrite(bytes(sentbuffer), 0x6)
    return True

def i2cReadAxiMasterReg(addr : int) -> int:
    wrBuf = ((addr >> 8) & 0xff, addr & 0xff)
    ftd2xxI2cDev.i2cWrite(0x70, bytes(wrBuf), 0x1)
    rdBuf = ftd2xxI2cDev.i2cRead(0x70, 4, 0x3)
    value = rdBuf[0] + (rdBuf[1] << 8) + (rdBuf[2] << 16) + (rdBuf[3] << 24)
    return value

def i2cWriteAxiMasterReg(addr : int, value : int) -> int:
    wrBuf = ((addr >> 8) & 0xff, addr & 0xff, value & 0xff, (value >> 8) & 0xff, (value >> 16) & 0xff, (value >> 24) & 0xff)
    ftd2xxI2cDev.i2cWrite(0x70, bytes(wrBuf), 0x3)
    return True

def i2cReadC3TxTestReg(devAddr : int, regAddr:int) -> int:
    wrBuf = (regAddr,)
    ftd2xxI2cDev.i2cWrite(devAddr, bytes(wrBuf), 0x1)
    rdBuf = ftd2xxI2cDev.i2cRead(devAddr, 1, 0x3)
    value = rdBuf[0]
    return value

def i2cWriteC3TxTestReg(devAddr : int, regAddr:int, value : int) -> int:
    wrBuf = (regAddr, value)
    ftd2xxI2cDev.i2cWrite(devAddr, bytes(wrBuf), 0x3)
    return True

def spiReadC3(addr : int, size:int) -> int:
    sentbuffer = (0x01, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff)
    ftd2xxSpiDev.spiWrite(bytes(sentbuffer), 0x2)
    rdBuf = ftd2xxSpiDev.spiRead(size, 0x6)
    value = rdBuf[0]
    return value

def axiI2cMasterEnable():
    i2cWriteAxiMasterReg(0x0100, 0x02) # CR, reset tx fifo
    i2cWriteAxiMasterReg(0x0100, 0x01) # CR, enable axi-iic
    return

def axiI2cMasterWrite(devAddr, regAddr, regValue):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:            
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddr << 1)) # tx fifo, start + dev addr
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x200 | regValue)       # tx fifo, reg value + stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    return True

def axiI2cMasterRead(devAddr, regAddr):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddr << 1)) # tx fifo, start + dev addr (w)
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x101 | (devAddr << 1)) # tx fifo, start + dev addr (r)
    i2cWriteAxiMasterReg(0x0108, 0x200)                  # tx fifo, stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    value = i2cReadAxiMasterReg( 0x010c) # rx fifo
    return value

def readReg(moduleName : str, addr : int) -> int:
    return i2cReadAxiMasterReg(moduleName, addr)

def writeReg(moduleName : str, addr : int, value : int) -> int:
    return i2cWriteAxiMasterReg(moduleName, addr, value)

def readSingleData(pollingName: str) -> list:
    data  = []
    return data

def readBulkData(pollingName: str, dataLen: int) -> list:
    data  = []
    return data
