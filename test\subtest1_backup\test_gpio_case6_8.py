# -*- coding: utf-8 -*-
"""
GPIO Case-6: Signal Transmission Path from S68 to Q68

This test verifies the configuration of a signal transmission path from a
specific S68 GPIO pin to a specific GPIO pin on the Q68. The actual
signal presence should be verified externally using an oscilloscope.
"""
import logging
import time
import pytest

# 测试配置 - 可以手动修改
TEST_CONFIG = {
    'active_links': [0, 1, 2, 3],  # 默认使用所有Link (S68到Q68信号传输需要测试所有Link)
    'signal_id': 11,               # GPIO信号ID
    'observation_time': 5,         # 观察时间(秒)
    'configurations': [
        # (S68 Link, S68 GPIO Pin, Q68 GPIO Pin, Signal ID)
        # 测试所有Link的GPIO0到Q68 GPIO0
        (0, 2, 0, 11),
        (1, 2, 0, 11),
        (2, 2, 0, 11),
        (3, 2, 0, 11),
        (0, 2, 1, 11),
        (0, 2, 2, 11),
        (0, 2, 3, 11),
        (0, 2, 4, 11),
        (0, 2, 5, 11),
        (0, 2, 6, 11),
        (0, 2, 7, 11),
        (0, 2, 8, 11),
        (0, 2, 9, 11),
        (0, 2, 10, 11),
        (0, 2, 11, 11),
        (0, 2, 12, 11),
        (0, 2, 13, 11),
        (0, 2, 14, 11),
        # (0, 3, 15, 11),
        # (0, 3, 16, 11),
    ]
}

def test_gpio_s68_external_to_q68(devices):
    """
    Configures multiple signal paths from S68 GPIOs to Q68 GPIOs in a single run.

    Test Steps:
    0.  Configure specific Links for this test
    1.  Define a list of all S68-to-Q68 GPIO configurations.
    2.  Iterate through each configuration:
        a. Select the target S68 device based on the link.
        b. Configure the S68 GPIO as a remote transmitter (Tx).
        c. Configure the corresponding Q68 GPIO as a remote receiver (Rx).
    3.  After configuring all paths, wait for observation.
    4.  Check and assert that all communication links remain stable.
    """
    print(f"\n[CASE-6] 开始执行: S68 -> Q68 信号路径配置")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")

    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']

    # ---------------------------------------------------------------------
    # 0. Configure specific Links for this test
    # ---------------------------------------------------------------------
    print(f"\n步骤0: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")

    # 设置地址转换 (关键步骤！)
    print(f"\n步骤0.5: 设置激活Links的地址转换...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            # 设置地址转换：转译地址 -> 实际设备地址
            q68_remote.S68_AddrTrans(
                link=link,
                q68_iic_addr=0x73,                    # Q68地址
                s68_iic_addr=0x40,                    # S68实际地址
                s68_retrans_addr=s68_res_dev[link],   # S68转译地址 (0x20, 0x21, 0x22, 0x23)
                sensor_addr=0x24,                     # sensor地址 (如果需要)
                sensor_retrans_addr=0x24 + link      # sensor转译地址
            )
            print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40")
    print("  - 地址转换设置完成")

    # 过滤配置，只保留激活Links的配置
    active_configurations = [
        config for config in TEST_CONFIG['configurations']
        if config[0] in TEST_CONFIG['active_links']
    ]

    print(f"\n有效配置数量: {len(active_configurations)} (过滤后只包含激活Links)")

    # ---------------------------------------------------------------------
    # 1. & 2. S68 and Q68 Side Configuration (Transmitter & Receiver)
    # ---------------------------------------------------------------------
    print("\n步骤1&2: 循环配置激活Links的 S68 和 Q68 GPIO...")

    for link_to_test, s68_gpio_pin, q68_gpio_pin, signal_id in active_configurations:
        print(f"  - 配置: S68 (Link-{link_to_test}, GPIO-{s68_gpio_pin}) -> Q68 (GPIO-{q68_gpio_pin}), Signal ID: {signal_id}")

        # q68.M65Q68I2CLocalWrite(regAddr=0x440d,value=0x40)
        # print(q68.M65Q68I2CLocalRead(regAddr=0x440d, CRC=False))

        # q68.M65Q68I2CLocalWrite(regAddr=0x4420,value=0x01)
        # print(q68.M65Q68I2CLocalRead(regAddr=0x4420, CRC=False))

        # q68.M65Q68I2CLocalWrite(regAddr=0x4420,value=0x41)
        # print(q68.M65Q68I2CLocalRead(regAddr=0x4420, CRC=False))

        # 只配置激活Links中的配置
        if link_to_test < len(s68_res_dev):
            # S68 Side Configuration (Transmitter)
            target_s68_addr = s68_res_dev[link_to_test]
            q68_remote.dongle.devAddr = target_s68_addr

            q68_remote.M2CMFNSet(gpio=s68_gpio_pin, mfn=0)
            q68_remote.M2CGPIORemoteTx(gpio=s68_gpio_pin, tx_id=signal_id)

            # Q68 Side Configuration (Receiver)
            q68.MFNSet(gpio=q68_gpio_pin, mfn=0)
            q68.GPIORemoteRx(gpio=q68_gpio_pin, rx_id=signal_id)

            # 可选的寄存器读取 (调试用)
            # print(q68.M65Q68I2CLocalRead(regAddr=0x440d, CRC=False))
        else:
            print(f"    - 警告: Link{link_to_test} 超出s68_res_dev范围，跳过")

    print("    - 激活Links的GPIO配置完成。")

    # ---------------------------------------------------------------------
    # 3. Observation and Verification
    # ---------------------------------------------------------------------
    print(f"\n步骤3: 等待{TEST_CONFIG['observation_time']}秒以便用示波器捕获波形...")
    time.sleep(TEST_CONFIG['observation_time'])

    # 只检查激活Links的状态
    print(f"\n步骤4: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    active_statuses = {}
    for link_id in TEST_CONFIG['active_links']:
        if 0 <= link_id <= 3:
            status = link_status_funcs[link_id]()
            active_statuses[f'link{link_id}'] = status
            print(f"    - Link{link_id} 状态: {status}")

    # 验证激活Links的状态
    failed_links = [link for link, status in active_statuses.items() if status != 5]
    assert len(failed_links) == 0, \
        f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"

    print(f"\n==> [CASE-6] 测试通过: S68 -> Q68 信号路径已配置到Links {TEST_CONFIG['active_links']}，链路状态正常。")


@pytest.mark.parametrize("link_combination", [
    [0],           # 只测试Link0
    [1],           # 只测试Link1
    [2],           # 只测试Link2
    [3],           # 只测试Link3
    [0, 1],        # 测试Link0和Link1
    [0, 1, 2, 3],  # 测试所有Link (默认)
])
def test_gpio_case6_parametrized(devices, link_combination):
    """参数化测试 - 可以测试不同的Link组合"""
    # 临时修改配置
    original_links = TEST_CONFIG['active_links']
    TEST_CONFIG['active_links'] = link_combination

    try:
        test_gpio_s68_external_to_q68(devices)
        print(f"✅ Link组合 {link_combination} 测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['active_links'] = original_links


def test_gpio_case6_single_link_focus(devices):
    """专门测试单个Link的详细配置"""
    # 简化配置，只测试Link0的基本功能
    simplified_config = [
        (0, 0, 0, 11),  # S68 Link0 GPIO0 -> Q68 GPIO0
        (0, 0, 1, 11),  # S68 Link0 GPIO0 -> Q68 GPIO1
        (0, 0, 2, 11),  # S68 Link0 GPIO0 -> Q68 GPIO2
    ]

    # 临时修改配置
    original_configs = TEST_CONFIG['configurations']
    original_links = TEST_CONFIG['active_links']

    TEST_CONFIG['configurations'] = simplified_config
    TEST_CONFIG['active_links'] = [0]

    try:
        test_gpio_s68_external_to_q68(devices)
        print("✅ 单Link详细测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['configurations'] = original_configs
        TEST_CONFIG['active_links'] = original_links


if __name__ == "__main__":
    """
    使用说明:

    1. 测试默认配置 (所有Link):
       pytest test_gpio_case6_8.py::test_gpio_s68_external_to_q68 -v -s

    2. 参数化测试特定Link组合:
       pytest test_gpio_case6_8.py::test_gpio_case6_parametrized[link_combination0] -v -s

    3. 测试所有Link组合:
       pytest test_gpio_case6_8.py::test_gpio_case6_parametrized -v -s

    4. 单Link详细测试:
       pytest test_gpio_case6_8.py::test_gpio_case6_single_link_focus -v -s

    5. 手动修改TEST_CONFIG中的active_links来测试特定组合
    """
    print("GPIO Case-6 灵活Link测试")
    print("请使用pytest运行测试")