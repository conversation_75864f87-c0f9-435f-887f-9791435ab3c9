from _ast import Break, Continue
import time
# from numpy.typing.tests.data import pass
# from future.backports.test.pystone import TRUE
version = 'c_20240222'

if version == 'c_20240222':
    #import m2c_r0p5_a_20240715 as m2c
    import Common_var.m2c_r0a0_a_20250210 as m2c
    # import Common.c2m_r0p7_a_20241017 as c2m

import QRegisterAccessV2
    
class M66S68_A0():
    def __init__(self, cfg0_setting=82, cfg1_setting=250, ldo_setting=0x4d,i2c_sel=1, dongle='stm32', id=1, bus='i2c', acc='L', dongle_id=None, bus_chan=2, optype='auto'):  #cfg0: 82, 76 , 115--0x44 4d-3.3V,0x5d-2.7V; 0x8D-1.8V;
        
        '''print important notice !!!'''
        
        print('\n')
        print('不要修改S68默认用bus_chan=2!!!')
        print('注意事项1、 M66S68上电时序：1、PWDNB=0；2、S68乱序上电；3、pwdnb=1；另外，步骤1和2，2和3之间等待时间＞0.2s')
        # print('\n')
        print('注意事项2、 S68R5 efuse请用函数 dev.EfusePgm_S68R5_Verify，且其变量efuse_lock_out=0x38将S68 efuse永久熔断，不可逆！！！ efuse_lock_out=0x0，掉电后可逆 ！')
        print('注意事项3、 S68R5 MIPI ECC问题: 需要临时手动设置dco_code=36, 已在booten函数中，同时请设置d-term/hs_settle为0')
        print('\n')
        # if findMeritechI2cChan():
        #     print ('Bridge board i2c is found!')
        # else:
        #     print ('Can not find bridge i2c!') 
        #     raise
        '''
        description: 
            value: 
        '''
        # if dongle=='ft4222':
        #     import ft4222_py3.QRegisterAccess as QRegisterAccess
        #
        #     import ft4222    
        #
        #     location0=801  #801#12817
        #     location1=802  #802#12818
        #
        #     devNum = ft4222.createDeviceInfoList()
        #     add = [0 for i in range(devNum)]
        #     for i in range(devNum):
        #         add[i]=ft4222.getDeviceInfoDetail(i, False)
        #         print(ft4222.getDeviceInfoDetail(i, False))
        #
        # elif dongle == 'stm32':
        #     import QRegisterAccessV2
        #     from stm32 import USBImpl
        #     # import SMT32.QRegisterAccess as QRegisterAccess
        #     stm32_inst_list = []
        #     dev = USBImpl.USBImpl()
        #     devlist = dev.usbdev_scan()
        #     #stm32_dev = None
        #     print("Find %d STM32 Devices:"%(len(devlist)))
        #     for i in range(len(devlist)):
        #         #dev = USBImpl.USBImpl()
        #         if id==0:
        #             dev.usbdev_open(devlist[i].name)
        #             dev.usbdev_i2c_setbusspeed(1, 400)
        #             dev.usbdev_i2c_setbusspeed(2, 400)
        #             dev.usbdev_i2c_setbusspeed(3, 400)
        #         (ret, sn) = dev.usbdev_get_sn()
        #         stm32_inst_list.append((dev, sn))
        #         print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
        # else:
        #     raise('none mcu type')
        #

        if dongle=='ft4222':
            self.dongle = QRegisterAccessV2.QRegisterAccess(dongle=dongle, product='m2c', id=id)  # search Q68 connection
            #self.c2m = c2m.c2m()
            self.m2c = m2c.m2c(dongle=self.dongle)
            
            self.m2c.QRegisterAccess = self.dongle
            self.m2c.dongle = self.dongle
            self.m2c.name = 'm2c'
            data = self.m2c.rd_i2c0_ctrl1()
            print ('read back value on reg_0x0101: ', hex(data), ', expect value: 0x55 ')
            # for useadd in range(int(devNum/2)):
            #     location0 = add[2*useadd]['location']
            #     location1 = add[2*useadd+1]['location']
            #     QRegisterAccess.ft4222DevA=ft4222.openByLocation(location0)
            #     QRegisterAccess.ft4222DevB=ft4222.openByLocation(location1)
            #     QRegisterAccess.ft4222DevA.setClock(ft4222.SysClock.CLK_80)
            #     # if MCU=='ft4222':
            #     #     from QRegisterAccess import *
            #     # elif MCU=='ftd2xx':
            #     #     from ftd2xx.QRegisterAccess import *
            #
            #     if QRegisterAccess.findMeritechI2cChan():
            #         print ('Bridge board i2c is found!')
            #     else:
            #         print ('Can not find bridge i2c!') 
            #         raise 
            #
            #     for devAddr in [0x40,0x44]:
            #
            #         QRegisterAccess.devAddr = devAddr
            #         print('devAddr is: ', hex(QRegisterAccess.devAddr))
            #
            #         reg_ana_0x0101 = self.M66S68I2CLocalRead(regAddr=0x0101,CRC=False)
            #         print ('read back value on reg_0x0101: ', hex(reg_ana_0x0101), ', expect value: 0x55 ')
            #
            #
            #         if reg_ana_0x0101 == 0x55:
            #             break
            #     if reg_ana_0x0101 == 0x55:
            #         break
        elif dongle == 'stm32':
            if acc == 'L':  # local control
                
                #for inst in stm32_inst_list:
                    self.dongle = QRegisterAccessV2.QRegisterAccess(dongle=dongle, product='m2c', id=id, dongle_id=dongle_id, bus_chan=bus_chan,optype=optype)  # search Q68 connection
                    #self.c2m = c2m.c2m()
                    self.m2c = m2c.m2c(dongle=self.dongle)
                    
                    self.m2c.QRegisterAccess = self.dongle
                    self.m2c.dongle = self.dongle
                    self.m2c.name = 'm2c'
                    self.QRegisterAccess = self.dongle
                    # self.dongle.stm32_dev = inst[0]
                    # self.dongle.stm32_dev_sn = inst[1]
                    self.dongle.stm32_bus = bus
                    self.m2c.dongle = self.dongle
                    self.m2c.name = 'm2c'
                    # find q68 automatically if i2c address is not specified
                    if bus == 'i2c':
                        if id<=1:  # means 1 s68 only
                            for devAddr in [0x40,0x44]:
                                self.dongle.devAddr = devAddr
                                self.dongle.stm32_bus_chan = bus_chan
                                data = self.m2c.rd_i2c0_ctrl1()
                                print ('read back value on reg_0x0101: ', hex(data), ', expect value: 0x55 ')
                    
                                if data == 0x55:
                                    print('find s68 device 0x%x'%(devAddr))
                                    break
                        elif id>=2: # means at least 2 
                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                self.dongle.devAddr = devAddr
                    
                                self.dongle.stm32_bus_chan = 1
                                data = self.c2m.rd_vc2_config1()
                    
                                if data == 0x5:  # 1# S68
                                    if id==2 & len(devlist)==1:
                                        self.dongle.stm32_bus_chan = 2  # 1# S68
                                        for devAddr in [0x40,0x44]:
                                            self.dongle.devAddr = devAddr
                                            data = self.m2c.rd_i2c0_ctrl1()
                                            print ('read back value on reg_0x0101: ', hex(data), ', expect value: 0x55 ')
                    
                                            if reg_ana_0x0101 == 0x55:
                                                print('find s68 device 0x%x'%(devAddr))
                                                break
                                    else:
                    
                                        self.dongle.stm32_bus_chan = id-2  # 1# S68
                                        for devAddr in [0x40,0x44]:
                                            self.dongle.devAddr = devAddr
                    data = self.m2c.rd_i2c0_ctrl1()
                    print ('read back value on reg_0x0101: ', hex(data), ', expect value: 0x55 ')
                    #
                    #                         if reg_ana_0x0101 == 0x55:
                    #                             print('find s68 device 0x%x'%(devAddr))
                    #                             break
                    #
                    #                 # self.dongle.devAddr = i2c_addr
                    #                 print('find q68 device 0x%x'%(devAddr))
                    #                 break
                    #     elif id>2:
                    #         pass
                    
                    
                    
                    # self.dongle = QRegisterAccessV2.QRegisterAccess(dongle=dongle)  # search Q68 connection
                    # self.m2c = m2c.m2c()
                    #
                    # self.m2c.QRegisterAccess = self.dongle
                    #
                    # # find q68 automatically if i2c address is not specified
                    # if bus == 'i2c':
                    #
                    #     for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                    #         self.dongle.stm32_dev = inst[0]
                    #         self.dongle.stm32_dev_sn = inst[1]
                    #         self.dongle.devAddr = devAddr
                    #         self.dongle.stm32_bus = bus
                    #         self.dongle.stm32_bus_chan = 1
                    #         data = self.c2m.rd_vc2_config1()
                    #         if data == 0x5:
                    #             i2c_addr = devAddr
                    #             # self.dongle.devAddr = i2c_addr
                    #             print('find q68 device 0x%x'%(devAddr))
                    #             break
        elif dongle == 'sim':
            self.dongle = QRegisterAccessV2.QRegisterAccess(dongle=dongle, product='m2c', id=id, dongle_id=dongle_id, bus_chan=bus_chan,optype=optype)  # search Q68 connection
            #self.c2m = c2m.c2m()
            self.m2c = m2c.m2c(dongle=self.dongle)
            
            self.m2c.QRegisterAccess = self.dongle
            self.m2c.dongle = self.dongle
            self.m2c.name = 'm2c'
            self.QRegisterAccess = self.dongle     
            self.dongle.devAddr = 0x40       
        else:
            raise('none mcu type')
        # self.devAddr=0x40
        # print('cfg0: ', hex(self.devAddr))
        # #self.devAddr=0x6B
        # QRegisterAccess.devAddr = self.devAddr
        # iicAddInit(self.devAddr)
        
        # if 0<((cfg1_5395/256.0)*100)<=11.7:
        #     print ('current mode is: STP, 24Gbps')
        # elif 16.9<((cfg1_5395/256.0)*100)<=23.6: # 51:20
        #     print ('current mode is: STP, 12Gbps')
        # elif 28.8<((cfg1_5395/256.0)*100)<=35.5:  
        #     print ('current mode is: STP, 3Gbps')
        # elif 40.7<((cfg1_5395/256.0)*100)<=47.4: 
        #     print ('current mode is: STP, 6Gbps')
        # elif 52.6<((cfg1_5395/256.0)*100)<=59.3:
        #     print ('current mode is: COAX, 24Gbps')
        # elif 64.5<((cfg1_5395/256.0)*100)<=71.2:
        #     print ('current mode is: COAX, 12Gbps')
        # elif 76.4<((cfg1_5395/256.0)*100)<=83.1:
        #     print ('current mode is: COAX, 3Gbps')
        # elif 88.3<((cfg1_5395/256.0)*100)<=100:
        #     print ('current mode is: COAX, 6Gbps')
        # else:
        #     # raise('cfg1 setting is not in recommendation region')
        #     pass
        # #eval_board reset ,and replace key
        # initGpio23(0,0)
        #
        # setGpio(2,0)
        # time.sleep(0.2)
        # setGpio(2,1)
        # time.sleep(0.2)
        # reg_ana_0x1001 = self.M66S68I2CLocalRead(regAddr=0x0101,CRC=False)
        # print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')
        
        # while (reg_ana_0x1001!=5):
        #
        #     setGpio(2,0)
        #     time.sleep(0.2)
        #     setGpio(2,1)
        #     time.sleep(0.2)
        #     reg_ana_0x1001 = self.M66S68I2CLocalRead(regAddr=0x1001,CRC=False)
        #     print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')
        #     if reg_ana_0x1001==5:
        #         break
        
    def M66S68I2CLocalWrite(self, regAddr, value, CRC=False):
        
        if CRC==False:
            self.dongle.M66S68I2CWrite_normal(regAddr, value)
        else:
            self.dongle.M66S68I2CWrite_CRC(regAddr, value)
    
    def M66S68I2CLocalRead(self,regAddr, CRC=False):
        
        if CRC==False:
            value=self.dongle.M66S68I2CRead_normal(regAddr)
        else:
            value=self.dongle.M66S68I2CRead_CRC(regAddr)
        return value  
      
    def M66S68I2CLocalWriteBitfiled(self, regAddr, bitnum, startbit,value, CRC=False):
        
        bitsToSave = 2 ** 8 - 2 ** (startbit + bitnum) + 2 ** startbit - 1
        reg_data = self.M66S68I2CLocalRead(regAddr, CRC) & bitsToSave
        data1 = reg_data + (value << startbit)
        self.M66S68I2CLocalWrite(regAddr, data1, CRC)
        # if CRC==False:
        #     M66S68I2CWrite_normal(regAddr, data1)
        # else:
        #     M66S68I2CWrite_CRC(regAddr, data1)
            
    def M66S68I2CLocalReadBitfiled(self,regAddr,bitnum, startbit, CRC=False):
        
        bitsToGet = 0xff-(2**8-2**(startbit+bitnum)+2**startbit-1) 
        
        value = (self.M66S68I2CLocalRead(regAddr, CRC) & bitsToGet) >> startbit
        # if CRC==False:
        #     value = (M66S68I2CRead_normal(regAddr) & bitsToGet) >> startbit
        # else:
        #     value =(M66S68I2CRead_CRC(regAddr) & bitsToGet) >> startbit
        return value
    
    def Common_init(self):
        '''
        To config the common registers to ensure silicon up correctly
        '''
        self.m2c.wr_ana_d2a_linkpll_regnamedel0_fields(ssc_sdm_en=0, ssc_en=0)
        print('rd_ana_d2a_linkpll_regnamedel0_ssc_en is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel0_ssc_en())
        print('rd_ana_d2a_linkpll_regnamedel0_ssc_sdm_en is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel0_ssc_sdm_en())
        
        # setting the order of the sigma-delta modulator
        # <3:2> ssc order;
        # 00: order is 0
        # 01: order is 1
        # 10: order is 2
        # 11: order is 3
        self.m2c.wr_ana_pll_cfg_sdm_order_fields(bf0=3)
        
        # <7:4>cfg xtal capa(used to change cap value and adjust the starting time of the oscillator)
        # bit<7:4>,0000:8p(default);0100:4p(min);1011:19p(max)
        self.m2c.wr_ana_xtal_reg_xtal_capa_09_fields(bf0=0x0B)  
        
        # <3:0>cfg xtal capb(used to change cap value and adjust the starting time of the oscillator)
        # bit<3:0>,0000:8p(default);0100:4p(min);1011:19p(max)
        self.m2c.wr_ana_xtal_reg_xtal_capb_09_fields(bf0=0x00)
        
        # <5:4> cfg xtal gm(used to change the gm of amp by adding the current)
        # bit<5:4> 00:1.2mA;01:1.8mA;10:0mA;11:0.6mA
        self.m2c.wr_ana_xtal_reg_xtal_gm_09_fields(bf0=1)
    
    def MIPIRx_RCLKOUT(self, mipirx_en, rclkout24M_en, rclkoutpin):
        '''
        Description: set vddcore_cap
        Input: 
            mipirx_en:     1->enable mipi all lanes, 0->don't enable mipi lanes
            rclkout24M_en: 1->enable RCLKOUT to send out 24M clk, 0->don't send out RCLK
            rclkoutpin: select which pin to send out 24M clk; 
                2->gpio2, 4->gpio4
        Output:
            NA
        '''
        if mipirx_en:
            
            self.MIPIPHY0Crossbar(lanetype=0, lanelogic=0)
            self.MIPIPHY1Crossbar(lanetype=0, lanelogic=1)
            self.MIPIPHY2Crossbar(lanetype=1, lanelogic=0)
            self.MIPIPHY3Crossbar(lanetype=0, lanelogic=2)
            self.MIPIPHY4Crossbar(lanetype=0, lanelogic=3)
            self.MIPIRxLaneEN(num_lane=3)
            self.MIPIBootEN()
            self.MIPIDefaultSetting()
        else:
            self.MIPIBootEN()
        
        if rclkout24M_en:
            self.self.m2c.efPLLSet(mdivint=24, mdivfraction=0, postdiv=0, postcnt=25, poststrg2=1)
            if rclkoutpin==2:
                self.self.m2c.FNSet(gpio=2, mfn=1)
            elif rclkoutpin==4:
                self.self.m2c.FNSet(gpio=4, mfn=4)
            else:
                raise('setting over range')
        else:
            self.m2c.wr_ana_d2a_refpll_regnamedel10_fields(en = 0, rst = 1)
                
               
    def PHYPLLInit(self):
        pass
    
 
    def PHYPLLInternalLDOSet(self, aldosetting=0, dldosetting=0, dldo2=0):
        '''
        This function is PLL internal LDO enable
        RegisterDescription: 
            aldo/dldo_setting: 00 0.85, 01 0.9, 10 0.95, 11 1.0. default 0.9V (4)
            dldo2: default 0, bit2 inv
        ''' 
        self.m2c.wr_ana_d2a_linkpll_regnamedel11_fields(aldo_vtune=aldosetting)  # aldo vtune, 0.7~1.0, step=50mV/25mV, default 4
        cfgaldo =  self.m2c.rd_ana_d2a_linkpll_regnamedel11_aldo_vtune()
        print('rd_ana_d2a_linkpll_regnamedel11_aldo_vtune is: ', cfgaldo)
        self.m2c.wr_ana_d2a_linkpll_regnamedel15_fields(dldo_en=1, dldo_vtune=dldosetting, aldo_en=1)  # dldo vtune, 0.7~1.0V, 50mV/25mV
        aldo_state = self.m2c.rd_ana_d2a_linkpll_regnamedel15_aldo_en()
        print('rd_ana_d2a_linkpll_regnamedel15_aldo_en is: ', aldo_state)
        cfgdldo = self.m2c.rd_ana_d2a_linkpll_regnamedel15_dldo_vtune()
        print('rd_ana_d2a_linkpll_regnamedel15_dldo_vtune is: ', cfgdldo)
        dldo_state = self.m2c.rd_ana_d2a_linkpll_regnamedel15_dldo_en()
        print('rd_ana_d2a_linkpll_regnamedel15_dldo_en is: ', dldo_state)
        
        self.m2c.wr_ana_d2a_linkpll_regnamedel22_fields(dldo_mmd_vtune=dldo2)
        dldo2 = self.m2c.rd_ana_d2a_linkpll_regnamedel22_dldo_mmd_vtune()
        print('rd_ana_d2a_rsvd_regnamedel0_plldldo2 is: ', dldo2)
        
        return (aldo_state, cfgaldo, dldo_state, cfgdldo, dldo2)
    
    def DigLDOSet(self, dldoset=1):
        '''
        Description: set vddcore_cap
        Input: 
            dldoset: 0~7
                d2a_digldo_vset<2:0>   vddcore_cap
                            100                                    700mv
                            101                                    750mv 
                            110                                    800mv
                            111                                    850mv
                            000                                    900mv
                            001                                    950mv
                            010                                    975mv
                            011                                    1v
        Output:
            NA
        '''
        self.m2c.wr_ana_d2a_digldo_regnamedel1_fields(vset=dldoset)  # 3bit, default 0, bit2 is reverse, means 4, 0.9V. ldo output setting, 0.7V~1.0V, step=50mV, for last three stage, step=25mV
        dldo = self.m2c.rd_ana_d2a_digldo_regnamedel1_vset()
        print('VDDcore_cap is: ', dldo)
        return dldo
    
    
    def PADTest(self,ldo_test_en):
        
        
        self.m2c.wr_ana_d2a_padtest_regnamedel1_fields(ldosel = ldo_test_en)
        return self.m2c.rd_ana_d2a_padtest_regnamedel1_ldosel()
    
        
                # Description: enable ldo test
        # Input:
        #     en=0,1,2,4,8,16
        #        0: 关闭测试
        #        1: gpio0 bctx_vcm, gpio4 vdd_bctx
        #        2: gpio0 vddtx_driver, gpio4 vddtx_ser
        #        4: gpio0 vddtx_ck, gpio4 vdd09_mipi
        #        8: gpio0 vdddpll, gpio4 dphy1.2V
        #        16: gpio0 vddcorecap, gpio4 vbg12
        #
        # '''
        # self.m2c.wr_ana_d2a_padtest_regnamedel1_fields(ldosel = ldo_test_en)
        # return self.m2c.rd_ana_d2a_padtest_regnamedel1_ldosel()
    def PHYLDOSet(self, en=1, serldo=4, driverldo=4, ldockgen_vout=4, bcrxaldo=4):
        '''
        Description: set serializer ldo
        Input:
            en: 0 or 1
            vset: 0~7, default 4
        Output:
            NA
        '''
        self.m2c.wr_ana_d2a_tx_regnamedel0_fields(serldo_en=en)  # ser ldo enable signal; 1: enable
        serldo_en = self.m2c.rd_ana_d2a_tx_regnamedel0_serldo_en()
        print('rd_ana_d2a_tx_regnamedel0_serldo_en is: ', serldo_en)
        self.m2c.wr_ana_d2a_tx_regnamedel2_fields(serldo_vset=serldo)  # 设置ser ldo电压； range 0.7v~1.0v step=50mv last two step=25mv;
        serldo_vset = self.m2c.rd_ana_d2a_tx_regnamedel2_serldo_vset()
        print('rd_ana_d2a_tx_regnamedel2_serldo_vset is: ', serldo_vset)
        
        # ldodriver_out - 设置driver ldo电压； range 0.7v~1.0v step=50mv last two step=25mv;
        # ldockgen_vout - set ckgen ldo voltage; ldo output range: 0.7v~1v step=50mv last two step=25mv;
        self.m2c.wr_ana_d2a_tx_regnamedel7_fields(ckgenldo_en=en, driverldo_en=en, ldodriver_vout=driverldo, ldockgen_vout=ldockgen_vout)
        ckgenldo_en = self.m2c.rd_ana_d2a_tx_regnamedel7_ckgenldo_en()
        print('rd_ana_d2a_tx_regnamedel7_ckgenldo_en is: ', ckgenldo_en)
        ldockgen_vout = self.m2c.rd_ana_d2a_tx_regnamedel7_ldockgen_vout()
        print('rd_ana_d2a_tx_regnamedel7_ldockgen_vout is: ', ldockgen_vout)
        
        driverldo_en = self.m2c.rd_ana_d2a_tx_regnamedel7_driverldo_en()
        print('rd_ana_d2a_tx_regnamedel7_driverldo_en is: ', driverldo_en)
        ldodriver_vout = self.m2c.rd_ana_d2a_tx_regnamedel7_ldodriver_vout()
        print('rd_ana_d2a_tx_regnamedel7_ldodriver_vout is: ', ldodriver_vout)
        
        # 设置bcrx ldo输出电压幅度；range 0.7v1.0v step=50mv last two step=25mv;
        self.m2c.wr_ana_d2a_bcrx_regnamedel5_fields(aldo_vset=bcrxaldo)
        bcrxaldo = self.m2c.rd_ana_d2a_bcrx_regnamedel5_aldo_vset()
        print('rd_ana_d2a_bcrx_regnamedel5_aldo_vset is: ', bcrxaldo)
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel9_fields(aldo_en=en)
        bcrxaldoen = self.m2c.rd_ana_d2a_bcrx_regnamedel9_aldo_en()
        print('rd_ana_d2a_bcrx_regnamedel9_aldo_en is: ', bcrxaldoen)
        
        return(serldo_en, serldo_vset, ckgenldo_en, ldockgen_vout, driverldo_en, ldodriver_vout, bcrxaldo, bcrxaldoen)
        
    #function6:   
    def PLLEn(self,state=0):
        '''
        Description: This function is PLL Power up,,and keep the same state for analog/digital LDO
        Input:
            state: if power up pll
                1 - power up PLL
                0 - disable PLL(default)
        Output:
            PLL power up status
        
        ''' 
        self.m2c.wr_ana_d2a_linkpll_regnamedel22_fields(en=state)
        return self.m2c.rd_ana_d2a_linkpll_regnamedel22_en()
    
    def PLLReset(self, state=0):
        '''
        Description: This function is reset PLL
        Input:
            state  
                1: rest PLL (default)
                0: normal work
        Output:
            PLL reset status        
        ''' 
        self.m2c.wr_ana_d2a_linkpll_regnamedel22_fields(rst=1)
        self.m2c.wr_ana_d2a_linkpll_regnamedel22_fields(rst=state)  
        return  self.m2c.rd_ana_d2a_linkpll_regnamedel22_rst() 
    def BandcaliReset(self):
        '''
        Description: This function is reset PLL
        Input:
            state  
                1: bandcali reset
                0: normal work
        Output:
            bandcali result, pll lock status & band         
        ''' 
        self.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali = 1)
        time.sleep(0.1)
        self.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali = 0)
        print('reset_async_vcobandcali is: ',self.m2c.rd_tx_link_phy_ana_ctrl9_reset_async_vcobandcali())
        (bandcalstatus, band) = self.PLLBandCalStatus()
        return (bandcalstatus, band)    
     
    def LinkPLLAnaTestMux (self,en=0, atest_sel=0):
        '''
        Description: This function is PLL analog test mux
        Input: 
            atest_sel: 0/1/2/3
                0-> 00:vdddig0.9,
                1-> 01:vddana0.9:
                2-> 10:cp vtrack, 
                3-> 11:vctrl
            en: 0--not enable atest, 1--enable atets
        Output:
            NA        
        '''  
        self.m2c.wr_ana_d2a_linkpll_regnamedel21_fields(atest_sel=atest_sel, atest_en=en)
        en = self.m2c.rd_ana_d2a_linkpll_regnamedel21_atest_en()
        atest_sel = self.m2c.rd_ana_d2a_linkpll_regnamedel21_atest_sel()
        return (en, atest_sel)
    
    #function9   

    def LinkPLLDigTestMux(self,en=0, dtest_sel=0):
        '''
        Description: This function is PLL digital test mux
        Input:
            dtest_sel: 0/1/2/3
                0 -> 00:ckfb_det, 
                1 -> 01:ckref_det, 
                2 -> 10:vdd09, 
                3 -> 11:pll lock rb
            en: 0--not enable atest, 1--enable atets
        Output:
            NA 
        '''  
        self.m2c.wr_ana_d2a_linkpll_regnamedel21_fields(dtest_sel=dtest_sel, dtest_en=en)
        en =  self.m2c.rd_ana_d2a_linkpll_regnamedel21_dtest_en()
        dtest_sel = self.m2c.rd_ana_d2a_linkpll_regnamedel21_dtest_sel()  #Cfg_dtest_sel
        
        return (en, dtest_sel)
    
    def LinkPLLLockSatus(self):
        '''
        Description: This function is read PLL lock status
        Input:
            NA
        Output:
            lock_state: PLL lock status
                1 -- pll locked
                0 -- pll unlock 
        '''  
        lock_state = self.m2c.rd_rcc_pll_status_linkpll_lock()        
        return lock_state
    
    def LinkPLLBandCalStatus(self):
        '''
        Description: This function is to record PLL band calibration status
        Input:
            NA
        Output: 
            bandcalidone: vco cali done, 1 done, 0 not done
            band: vco band value, settle when cali done        
        ''' 
        bandcalidone = self.m2c.rd_ana_a2d_linkpll_regnamedel0_bandcali_done() 
        print('rd_ana_a2d_linkpll_regnamedel0_bandcali_done is: ', bandcalidone)
        band = self.m2c.rd_ana_a2d_linkpll_regnamedel0_vcoband()
        print('rd_ana_a2d_linkpll_regnamedel0_vcoband is: ', band)
        
        return (bandcalidone, band)  
    
    def LinkPLLBandOverride(self, band=19, bandoverride_en=0, loopbreak=0, vcobandcalireset=0):
        '''
        Description: to override the VCO band and reset vcobandcali
        Input:
            band -- override band value, max value means min freq
            bandoverride_en -- 1: enable override, 0--disable
            loopbreak: 0-- not override break, 1 -- override and enable break
            vcobandcalireset: 1--reset vcobandcali, 0--not reset
        Output:
            NA
        '''
        self.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=1)
        self.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_vcobandcali=vcobandcalireset)
        self.m2c.wr_ana_d2a_linkpll_regnamedel10_fields(bandcali_vcoband_override=band)
        self.m2c.wr_ana_d2a_linkpll_regnamedel11_fields(bandcali_loopbreak_override=loopbreak, bandcali_en_override=bandoverride_en)
    
    def LinkPLLSSC(self,ssc_fdiv_max=0x09C4, ssc_fdiv_step=1, ssc_fdiv_min=0):
        '''
        Description: this function is used to open s68 forward SSC function
        Author: yadan
        Input:
            ssc_fdiv_max: SSC的最大调节深度
            ssc_fdiv_step: SSC的调节步长
            SSC_fdiv_min: SSC的最小调节深度
            ssc frequency = 125M/(ssc_fdiv_max-ssc_fdiv_min)/2/ssc_fdiv_step
            12G frequency deviation = 25M*10*((ssc_fdiv_max-ssc_fdiv_min)/65536)/4
        Output:
            NA
        '''
        self.m2c.wr_ana_d2a_linkpll_regnamedel3_fields(ssc_fdiv_max_bus0=(ssc_fdiv_max&0xFF))                               
        self.m2c.wr_ana_d2a_linkpll_regnamedel4_fields(ssc_fdiv_max_bus1=((ssc_fdiv_max>>8)&0xFF))                               
        self.m2c.wr_ana_d2a_linkpll_regnamedel8_fields(ssc_fdiv_step_bus1=((ssc_fdiv_step>>8)&0xFF))                              
        self.m2c.wr_ana_d2a_linkpll_regnamedel7_fields(ssc_fdiv_step_bus0=(ssc_fdiv_step&0xFF))                              
        self.m2c.wr_ana_d2a_linkpll_regnamedel5_fields(ssc_fdiv_min_bus0 = (ssc_fdiv_min&0xFF))                             
        self.m2c.wr_ana_d2a_linkpll_regnamedel6_fields(ssc_fdiv_min_bus1 = ((ssc_fdiv_min>>8)&0xFF))                             
        self.m2c.wr_ana_d2a_linkpll_regnamedel0_fields(ssc_sdm_en = 1, ssc_en = 1)   
        
        print('ssc_fdiv_max is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel4_ssc_fdiv_max_bus1()*256 + self.m2c.rd_ana_d2a_linkpll_regnamedel3_ssc_fdiv_max_bus0())                        
        print('ssc_fdiv_step is:', self.m2c.rd_ana_d2a_linkpll_regnamedel8_ssc_fdiv_step_bus1()*256 + self.m2c.rd_ana_d2a_linkpll_regnamedel7_ssc_fdiv_step_bus0())           
        print('ssc_fdiv_min is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel6_ssc_fdiv_min_bus1()*256 + self.m2c.rd_ana_d2a_linkpll_regnamedel5_ssc_fdiv_min_bus0())            
        print('ssc_sdm_en is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel0_ssc_sdm_en())                       
        print('ssc_en is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel0_ssc_en())                               
        
    def LinkPLLSSCSDMRest(self,):
        '''
        Description: this function is used to reset ssc&sdm, after enable ssc & sdm then disable, should reset it
        Author: yadan
        Input:
            NA
        Output:
            NA
        '''
        self.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_sscsdm = 1)
        self.m2c.wr_tx_link_phy_ana_ctrl9_fields(reset_async_sscsdm = 0)
    
    ''' the following module apply for SAR adc input div cfg'''
    def SARADCDiv_Setting(self,lane=0,value=0):
        ''' 
        Description: adc input divide, need to config for different voltage for 16channels
        Input:
            chan: 0~15
            value: 0:div/1,1:div/3,2:div/5,3:div/7
        Output:
            NA
        
        '''
        ''' write default for adc input divide before cfg div'''
        self.m2c.wr_ana_d2a_sar_regnamedel0_fields(vandtctrl_bus0=0x0)
        self.m2c.wr_ana_d2a_sar_regnamedel1_fields(vandtctrl_bus1=0x08)
        
        if lane ==0 or lane ==8:
            
            print('adc input divide value on channel0/8 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x03)>>0)
            val_ch0_8   =   (self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0xfc
            self.m2c.wr_ana_d2a_sar_regnamedel0_fields(vandtctrl_bus0 = val_ch0_8 | ((value<<0)&0xff))
            print('adc input divide value on channel0/8 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x03)>>0)
            
        if lane ==1 or lane ==9:
        
            print('adc input divide value on channel1/9 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x0c)>>2)
            val_ch1_9   =   (self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0xf3
            self.m2c.wr_ana_d2a_sar_regnamedel0_fields(vandtctrl_bus0 = val_ch1_9 | ((value<<2)&0xff))
            print('adc input divide value on channel1/9 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x0c)>>2)
            
        if lane ==2 or lane ==10:
        
            print('adc input divide value on channel2/10 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x30)>>4)
            val_ch2_10   =   (self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0xcf
            self.m2c.wr_ana_d2a_sar_regnamedel0_fields(vandtctrl_bus0 = val_ch2_10 | ((value<<4)&0xff))
            print('adc input divide value on channel2/10 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x30)>>4)
        
        if lane ==3 or lane ==11:
        
            print('adc input divide value on channel3/11 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0xc0)>>6)
            val_ch3_11   =   (self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x3f
            self.m2c.wr_ana_d2a_sar_regnamedel0_fields(vandtctrl_bus0 = val_ch3_11 | ((value<<6)&0xff))
            print('adc input divide value on channel3/11 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0xc0)>>6)
            
        if lane ==4 or lane ==12:
            
            print('adc input divide value on channel4/12 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x03)>>0)
            val_ch4_11   =   (self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0xfc
            self.m2c.wr_ana_d2a_sar_regnamedel1_fields(vandtctrl_bus1 = val_ch4_11 | ((value<<0)&0xff))
            print('adc input divide value on channel4/12 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x03)>>0)
            
        if lane ==5 or lane ==13:
        
            print('adc input divide value on channel5/13 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x0c)>>2)
            val_ch5_13   =   (self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0xf3
            self.m2c.wr_ana_d2a_sar_regnamedel1_fields(vandtctrl_bus1 = val_ch5_13 | ((value<<2)&0xff))
            print('adc input divide value on channel5/13 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x0c)>>2)
            
        if lane ==6 or lane ==14:
        
            print('adc input divide value on channel6/14 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x30)>>4)
            val_ch6_14   =   (self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0xcf
            self.m2c.wr_ana_d2a_sar_regnamedel1_fields(vandtctrl_bus1 = val_ch6_14 | ((value<<4)&0xff))
            print('adc input divide value on channel6/14 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x30)>>4)
        
        if lane ==7 or lane ==15:
        
            print('adc input divide value on channel7/15 before setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0xc0)>>6)
            val_ch7_15   =   (self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x3f
            self.m2c.wr_ana_d2a_sar_regnamedel1_fields(vandtctrl_bus1 = val_ch7_15 | ((value<<6)&0xff))
            print('adc input divide value on channel7/15 after setting is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0xc0)>>6)
            
        return True
              
    def SARADCDiv_Read(self,lane=0):
        ''' 
        Description: adc input divide, need to config for different voltage for 16channels
        Input:
            channel: 0~15
            
        Output:
            input divide value: 0/1/2/3
        
        '''
        if lane ==0 or lane ==8:
            
            print('adc input divide value on channel0/8 is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x03)>>0)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x03)>>0
            
        if lane ==1 or lane ==9:
        
            print('adc input divide value on channel1/9 is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x0c)>>2)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x0c)>>2
            
        if lane ==2 or lane ==10:
        
            print('adc input divide value on channel2/10 is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x30)>>4)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0x30)>>4
        
        if lane ==3 or lane ==11:
        
            print('adc input divide value on channel3/11 is ',((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0xc0)>>6)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel0_vandtctrl_bus0()) & 0xc0)>>6
            
        if lane ==4 or lane ==12:
            
            print('adc input divide value on channel4/12 is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x03)>>0)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x03)>>0
            
        if lane ==5 or lane ==13:
        
            print('adc input divide value on channel5/13 is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x0c)>>2)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x0c)>>2
            
        if lane ==6 or lane ==14:
        
            print('adc input divide value on channel6/14 is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x30)>>4)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0x30)>>4
        
        if lane ==7 or lane ==15:
        
            print('adc input divide value on channel7/15 is ',((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0xc0)>>6)
            return ((self.m2c.rd_ana_d2a_sar_regnamedel1_vandtctrl_bus1()) & 0xc0)>>6
        
    def SARADC_EN(self,adc_enable=0):
        ''' 
        Description: adc enable or disable
        Input:
            0/1
            
        Output:
            NA
        
        '''
        print("read back value on adc enable status before setting is : ",self.m2c.rd_adc_ctrl_adc_ctrl0_adc_en())
        self.m2c.wr_adc_ctrl_adc_ctrl0_fields(adc_en=adc_enable)
        print("read back value on adc enable status after setting is : ",self.m2c.rd_adc_ctrl_adc_ctrl0_adc_en())
        
    def SARADC_Setting(self,lane=0,adc_pu_time=0,round_robin_period=0,adc_deci=0,isr_sample=0x01,adc_over_th_0_7=255,adc_over_th_8_10=7,adc_under_th_0_7=0,adc_under_th_8_10=0):
        ''' 
        Description: cfg ADC setting
        Input:
            adc_pu_time: 0:25us(default),1:50us,2:75us,3:100us
            round_robin_period: 0:25us(default),1:50us,2:75us,3:100us
            
        Output:
            NA
        
        '''
        #2. adc power up time and round robin time
        print("read back on adc power up time before setting:",self.m2c.rd_adc_ctrl_adc_ctrl1_adc_power_up_time())
        print("read back value on round_robin_sw_period before setting:",self.m2c.rd_adc_ctrl_adc_ctrl1_round_robin_sw_period())
        self.m2c.wr_adc_ctrl_adc_ctrl1_fields(adc_power_up_time=adc_pu_time, round_robin_sw_period=round_robin_period)
        print("read back on adc power up time after setting:",self.m2c.rd_adc_ctrl_adc_ctrl1_adc_power_up_time())
        print("read back value on round_robin_sw_period after setting:",self.m2c.rd_adc_ctrl_adc_ctrl1_round_robin_sw_period())
        
        #3. adc deci mode
        print("read back value on adc deci mode before setting:",self.m2c.rd_adc_ctrl_adc_ctrl3_adc_deci_mode())
        self.m2c.wr_adc_ctrl_adc_ctrl3_fields(adc_deci_mode=adc_deci)
        print("read back value on adc deci mode before setting:",self.m2c.rd_adc_ctrl_adc_ctrl3_adc_deci_mode())
        
        #4. Isr sample number
        print("read back value on Isr sample number before setting:",self.m2c.rd_adc_ctrl_adc_ctrl4_isr_sample_num())
        self.m2c.wr_adc_ctrl_adc_ctrl4_fields(isr_sample_num=isr_sample)
        print("read back value on Isr sample number before setting:",self.m2c.rd_adc_ctrl_adc_ctrl4_isr_sample_num())
        
        #5. config the threshold of over/under range
        #over range
        print("read back value on adc_over_range_th_0_7 before setting:",self.m2c. rd_adc_ctrl_adc_ctrl5_adc_over_range_th_0_7(i=lane))
        print("read back value on adc_over_range_th_8_10 before setting:",self.m2c.rd_adc_ctrl_adc_ctrl6_adc_over_range_th_8_10(i=lane))
        self.m2c.wr_adc_ctrl_adc_ctrl5_fields(i=lane, adc_over_range_th_0_7=adc_over_th_0_7)
        self.m2c.wr_adc_ctrl_adc_ctrl6_fields(i=lane,adc_over_range_th_8_10=adc_over_th_8_10)
        print("read back value on adc_over_range_th_0_7 before setting:",self.m2c. rd_adc_ctrl_adc_ctrl5_adc_over_range_th_0_7(i=lane))
        print("read back value on adc_over_range_th_8_10 before setting:",self.m2c.rd_adc_ctrl_adc_ctrl6_adc_over_range_th_8_10(i=lane))
        
        #under range
        print("read back value on adc_under_range_th_0_7 before setting:",self.m2c. rd_adc_ctrl_adc_ctrl7_adc_under_range_th_0_7(i=lane))
        print("read back value on adc_under_range_th_8_10 before setting:",self.m2c.rd_adc_ctrl_adc_ctrl8_adc_under_range_th_8_10(i=lane))
        self.m2c.wr_adc_ctrl_adc_ctrl7_fields(i=lane, adc_under_range_th_0_7=adc_under_th_0_7)
        self.m2c.wr_adc_ctrl_adc_ctrl8_fields(i=lane, adc_under_range_th_8_10=adc_under_th_8_10)
        print("read back value on adc_under_range_th_0_7 before setting:",self.m2c. rd_adc_ctrl_adc_ctrl7_adc_under_range_th_0_7(i=lane))
        print("read back value on adc_under_range_th_8_10 before setting:",self.m2c.rd_adc_ctrl_adc_ctrl8_adc_under_range_th_8_10(i=lane))
        
        '''over/under range enable'''     
        if lane<8:
            self.m2c.wr_efh_adc_en_ctrl0_fields( adc_ovr1=(0x01<<lane)&0xff)
            # dev.m2c.wr_adc_ctrl_adc_ctrl2(i=0, value=(0x01<<channel)&0xff)
            print("read back value on reg_0x0a26 low 8channel after setting:",hex(self.m2c.rd_efh_adc_en_ctrl0_adc_ovr1()))
            print("read back value on reg_0x0a27 high 8channels after setting:",hex(self.m2c.rd_efh_adc_en_ctrl1_adc_ovr2()))
            
            self.m2c.wr_efh_adc_en_ctrl2_fields( adc_uvr1=(0x01<<lane)&0xff)
            # dev.m2c.wr_adc_ctrl_adc_ctrl2(i=0, value=(0x01<<channel)&0xff)
            print("read back value on reg_0x0d28 low 8channel after setting:",hex(self.m2c.rd_efh_adc_en_ctrl2_adc_uvr1()))
            print("read back value on reg_0x0d29 high 8channels after setting:",hex(self.m2c.rd_efh_adc_en_ctrl3_adc_uvr2()))
            
        if lane>7:
            self.m2c.wr_efh_adc_en_ctrl1_fields( adc_ovr2=(0x01<<(lane-8))&0xff)
            # dev.m2c.wr_adc_ctrl_adc_ctrl2(i=0, value=(0x01<<channel)&0xff)
            print("read back value on reg_0x0a26 low 8channel after setting:",hex(self.m2c.rd_efh_adc_en_ctrl0_adc_ovr1()))
            print("read back value on reg_0x0a27 high 8channels after setting:",hex(self.m2c.rd_efh_adc_en_ctrl1_adc_ovr2()))
            
            self.m2c.wr_efh_adc_en_ctrl3_fields( adc_uvr2=(0x01<<(lane-8))&0xff)
            # dev.m2c.wr_adc_ctrl_adc_ctrl2(i=0, value=(0x01<<channel)&0xff)
            print("read back value on reg_0x0d28 low 8channel after setting:",hex(self.m2c.rd_efh_adc_en_ctrl2_adc_uvr1()))
            print("read back value on reg_0x0d29 high 8channels after setting:",hex(self.m2c.rd_efh_adc_en_ctrl3_adc_uvr2()))
        
    '''end''' 
        
    ''' the following module apply for ref_video_pll'''
    
    def M66S68_SARADC_offset(self,sdg6032x,READVOL_TIMES,ADC_CHANNEL):
        '''
            This function is find ADC OFFSET for 0.6V
                set GPIO and MFN
                 0:channel=0 ADC0:gpio=3,mfn=4
                 0:channel=1 ADC1:gpio=5,mfn=2
                 0:channel=2 ADC2:gpio=6,mfn=2
            print important notice !!!
        '''
        print('\n')
        print('注意事项1、 选择对应的channel和GPIO外灌电压，确保GPIO路径干净，程序已写好，务必使用SDG6032X，电压更稳定. 必须先从程序外部导入:from SDG6032X import *')
        print('注意事项2、 外灌电压在程序内已使用SDG6032X写好：0.6V:CODE_TARGET=1024,常温误差TT,FF预计在20code内，SS预计在50code内')
        print('采样次数已设置:默认READVOL_TIMES=50,时间较长，注意50次的max-min<20,更改建议10次以上')
        print('调用方法：(delta_code,offset_into_efuse) = dev.M66S68_SARADC_offset(sdg6032x = SDG6032X(porttype="usb",usb_addr="USB0::0xF4EC::0x1101::SDG6XFCD7R0920::INSTR"),READVOL_TIMES=2,ADC_CHANNEL=1,)')
        print('使用方法：EfusePgm_S68R7_Verify(adc_offset =offset_into_efuse,)。其中delta_code:是0.6V时的code误差，offset_into_efuse：是code转化后，直接赋给adc_offset的值')

        offset_dict ={} # get 0.6v ▲code
        # READVOL_TIMES = 50
        # ADC_CHANNEL =   (1,)
        input_div        =   0
        input_div_dict   =   {0:1,1:3,2:5,3:7}
        
        pll_en = self.PLLEn(state=1) # 1 - power up PLL
        print('pll enable status is ',pll_en)
        self.PHYTxEn(txen=1)       # txen: tx 总使能开关; 1: enable
        c3tx_status=self.m2c.rd_ana_d2a_tx_regnamedel0_en()
        print('c3tx status is ',c3tx_status)
    
        pllstatus = self.PLLLockSatus()  # readback pll lock status
        (bandcalstatus, vco_band) = self.PLLBandCalStatus()
        print('PLL lock status: ', pllstatus)
        print('band cal status: ', bandcalstatus,'vco_band: ', vco_band)
            
        self.m2c.wr_ana_d2a_rsvd_regnamedel1_fields(reg1=0x01)  # cfg adc_sample_edge
        print("read back value on adc sample_edge<0> after setting:",self.m2c.rd_ana_d2a_rsvd_regnamedel1_reg1())
        
        channel = ADC_CHANNEL  # select channel
        self.SARADC_EN(adc_enable=0)
        print("read back value on adc enable status after setting:",self.m2c.rd_adc_ctrl_adc_ctrl0_adc_en())
        
        if channel==2:
            self.AnalogPinSet(gpio=6)
            mfn = self.MFNSet(gpio=6,mfn=2)
            print("read back value on MFN value: ",mfn)
        if channel==1:
            self.AnalogPinSet(gpio=5)
            mfn = self.MFNSet(gpio=5,mfn=2)
            print("read back value on MFN value: ",mfn)
        if channel==0:
            self.AnalogPinSet(gpio=3)
            mfn = self.MFNSet(gpio=3,mfn=4)
            print("read back value on MFN value: ",mfn)
        
        for external_input_vol in list(range(200,1100,200)):
        # for external_input_vol in (600,):
            
            print('external_input_vol = ',external_input_vol)
            sdg6032x.TurnOff(channel=1)
            time.sleep(0.5)
            vol_external_input  =  external_input_vol/1000.0
            sdg6032x.Set_DCWaveForm(channel=1, offset=vol_external_input)
            time.sleep(0.5)
            sdg6032x.TurnOn(channel=1)
            time.sleep(2)
            CODE_TARGET =   1024 + (vol_external_input-0.6)*2048/1.6
            print('CODE_TARGET on external_input_vol is', CODE_TARGET)
            
            print("read back value on reg_0x0d02 low 8channels before setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=0)))
            print("read back value on reg_0x0d02 high 8channels before setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=1)))
            self.m2c.wr_adc_ctrl_adc_ctrl2(i=0,value=0)
            self.m2c.wr_adc_ctrl_adc_ctrl2(i=1,value=0)
            print("read back value on reg_0x0d02 low 8channel after setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=0)))
            print("read back value on reg_0x0d02 high 8channels after setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=1)))
            
            
            '''channel enable'''     
            if channel<8:
                self.m2c.wr_adc_ctrl_adc_ctrl2(i=0, value=(0x01<<channel)&0xff)
                print("read back value on reg_0x0d02 low 8channel after setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=0)))
                print("read back value on reg_0x0d02 high 8channels after setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=1)))
            if channel>7:
                self.m2c.wr_adc_ctrl_adc_ctrl2(i=1, value=(0x01<<(channel-8))&0xff)
                print("read back value on reg_0x0d02 low 8channel after setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=0)))
                print("read back value on reg_0x0d02 high 8channels after setting:",hex(self.m2c.rd_adc_ctrl_adc_ctrl2(i=1)))
            
            '''adc input divide, need to config for different voltage for 16channels'''
            print("read back value on reg_0x0ba4[7:0] before setting:",hex(self.m2c.rd_ana_d2a_sar_regnamedel0()))
            print("read back value on reg_0x0ba5[7:0] before setting:",hex(self.m2c.rd_ana_d2a_sar_regnamedel1()))
            self.SARADCDiv_Setting(lane=channel,value=input_div) #value: 0:div/1,1:div/3,2:div/5,3:div/7
            print('the current channel is ',channel,' and its adc input divide = ',self.SARADCDiv_Read(lane=channel))
            print("read back value on reg_0x0ba4[7:0] after setting:",hex(self.m2c.rd_ana_d2a_sar_regnamedel0()))
            print("read back value on reg_0x0ba5[7:0] after setting:",hex(self.m2c.rd_ana_d2a_sar_regnamedel1()))
            
            '''common config: '''
            self.SARADC_Setting(lane=channel,adc_pu_time=3,round_robin_period=0,adc_deci=0,isr_sample=0x01,adc_over_th_0_7=255,adc_over_th_8_10=7,adc_under_th_0_7=0,adc_under_th_8_10=0)
            
            # adc enable,then analog ADC start working
            print("read back value on adc enable status before setting:",self.m2c.rd_adc_ctrl_adc_ctrl0_adc_en())
            self.SARADC_EN(adc_enable=1)
            print("read back value on adc enable status after setting:",self.m2c.rd_adc_ctrl_adc_ctrl0_adc_en())
            
            adc_ready=self.m2c.rd_adc_ctrl_adc_status5_adc_ready()
            print('the adc ready status is ',adc_ready,', expect adc_ready: 1 ')
            
            channel_sel=self.m2c.rd_adc_ctrl_adc_status0_adc_chnl_sel()
            print('after the current selected channel is:',channel_sel,', expect channel_sel: 1 ')
            
            """ read back data from ADC output """
            data_bit0_7 =[0 for x in range(READVOL_TIMES)]
            data_bit8_10 =[0 for x in range(READVOL_TIMES)]
            adc_output_code =[0 for x in range(READVOL_TIMES)]
            vol_tested_syntax2 =[0 for x in range(READVOL_TIMES)]
            adc_ready_test = [0 for x in range(READVOL_TIMES)]
            i2c_test= [0 for x in range(READVOL_TIMES)]
            
            for i in list(range(READVOL_TIMES)):
                
                data_bit0_7[i] = self.m2c.rd_adc_ctrl_adc_ch_data_0_7_adc_channel_data_0_7(i=channel)
                data_bit8_10[i] = self.m2c.rd_adc_ctrl_adc_ch_data_8_10_adc_channel_data_8_10(i=channel)
                adc_output_code[i] = ((data_bit8_10[i])*256)+data_bit0_7[i]
                #syntax:v_adc=[(adcout-1024)*1.6/2048 + 0.6]*div
                vol_tested_syntax2[i] = (((adc_output_code[i])-1024)*1.6/2048+0.6)*input_div_dict[input_div]
                adc_ready_test[i] = self.m2c.rd_adc_ctrl_adc_status5_adc_ready()
                i2c_test[i] = hex(self.M66S68I2CLocalRead(regAddr=0x0101,CRC=False))
                
            data_bit0_7_avg =round(sum(data_bit0_7)/READVOL_TIMES)
            data_bit8_10_avg =round(sum(data_bit8_10)/READVOL_TIMES)
            adc_output_code_avg =round(sum(adc_output_code)/READVOL_TIMES)
            adc_output_code_max = max(adc_output_code)
            adc_output_code_min = min(adc_output_code)
            vol_tested_avg =sum(vol_tested_syntax2)/READVOL_TIMES
            
            print("data_bit0_7: read back value on special channel:reg_0x0d2c + i * 0x0001:",data_bit0_7)
            print("data_bit8_10: read back value on reg_address:0x0d34 + i * 0x0001:",data_bit8_10)
            print("adc_output_code: read back the value on SAR ADC output from channel{} = ".format(channel),adc_output_code)
            print("adc_output_code_avg value: read back the value on SAR ADC output from channel{} = ".format(channel),adc_output_code_avg)
            print("adc_output_code max value: read back the value on SAR ADC output from channel{} = ".format(channel),adc_output_code_max)
            print("adc_output_code min value: read back the value on SAR ADC output from channel{} = ".format(channel),adc_output_code_min)
            print("syntax2_cal_adc voltage: read back the tested voltage form SAR ADC output from channel{} = ".format(channel),vol_tested_syntax2)
            
            '''clear exception status'''
            self.m2c.wr_adc_ctrl_adc_fault_clear_fields(i=channel, adc_under_flow_clear=1, adc_over_flow_clear=1, adc_under_range_clear=1, adc_over_range_clear=1)
            ''' create offset_dict to find ADC_offset'''
            adc_offset_measure= CODE_TARGET-adc_output_code_avg
            print("adc_offset_measure is:",adc_offset_measure)
            offset_dict[vol_external_input]=adc_offset_measure
            print("offset_dict is:",offset_dict)
            
        print("delta_code is:",offset_dict[0.6])
            
        # """ adc bits <7:0> and: (<6:0>binary source code),(<7> 0 for +,1 for -) """       
        # def ADC_int_to_binary(ADC_offset, bits=7): # ADC transform
        num=int(offset_dict[0.6])
        if num>=0:
            original ='0'+bin(num)[2:].zfill(7-1)
        else:
            if num == -127:
                original = '1'*7
            else:
                absolute_num = abs(num)
                original = '1'+bin(absolute_num)[2:].zfill(7-1)
        print('original is:',original)
        offset_into_efuse = int(('0'+'b'+original),2)
        print('offset_into_efuse is:',offset_into_efuse)
        if num>=0:
            complement = original
        else:
            complement = ''.join('1' if x == '0' else '0' for x in original[1:])
            complement = '1'+ complement
        if num>=0:
            twos_complement = original
        else:
            twos_complement = bin(int(complement, 2) + 1)[2:].zfill(7)   
        # return offset, original, complement, twos_complement
        if abs(offset_into_efuse)<50:
            pass
        else:
            offset_into_efuse=0
        
        print('offset_into_efuse finally',offset_into_efuse)
        
        return (offset_dict[0.6],offset_into_efuse)
    
    
        
    def RefVideoPLL_SSCSDM(self,ssc=1,sdm=1):
        '''
        This function is en/disable ssc and sdm
        '''
        #disable SSC,and default is 1
        print("read back value on reg_0x0b60[3] before setting:",self.M66S68I2CLocalReadBitfiled(regAddr=0x0b60, bitnum=1, startbit=3,CRC=False))
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b60, bitnum=1, startbit=3, value=ssc,CRC=False) #default is 1
        print("read back value on reg_0x0b60[3] after setting:",self.M66S68I2CLocalReadBitfiled(regAddr=0x0b60, bitnum=1, startbit=3,CRC=False))
        ssc_set = self.M66S68I2CLocalReadBitfiled(regAddr=0x0b60, bitnum=1, startbit=3,CRC=False)
        
        #disable SDM,and default is 1
        print("read back value on reg_0x0b60[2] before setting:",self.M66S68I2CLocalReadBitfiled(regAddr=0x0b60, bitnum=1, startbit=2,CRC=False))
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b60, bitnum=1, startbit=2, value=sdm,CRC=False) #default is 1
        print("read back value on reg_0x0b60[2] after setting:",self.M66S68I2CLocalReadBitfiled(regAddr=0x0b60, bitnum=1, startbit=2,CRC=False))
        sdm_set = self.M66S68I2CLocalReadBitfiled(regAddr=0x0b60, bitnum=1, startbit=2,CRC=False)
        
        return(sdm_set,ssc_set)
    
    def RefVideoPLLVset(self,ldo_1p2=1,ldo_0p9=0):
        '''
        This function is set ldo_0p9, and ldo_1p2
        <3:2> ldo0p9 vset,default is 0;
            <3:2>ldo0p9 v set; set ldo vref 00:1.15V;01:1.2V;10:1.25V;11:1.3V
        <5:4> ldo1p2 vset,default is 1;
            <5:4> ldo1p2 v set; set ldo vref 00:1.15V;01:1.2V;10:1.25V;11:1.3V
        ''' 
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6c, bitnum=2, startbit=4, value=ldo_1p2,CRC=False)
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6c, bitnum=2, startbit=2, value=ldo_0p9,CRC=False)
        time.sleep(0.1)
        ldo_1p2_set = self.M66S68I2CLocalReadBitfiled(regAddr=0x0b6c, bitnum=2, startbit=4,CRC=False)
        ldo_0p9_set = self.M66S68I2CLocalReadBitfiled(regAddr=0x0b6c, bitnum=2, startbit=2,CRC=False)
        
        return (ldo_1p2_set,ldo_0p9_set)
    
    def RefVideoPLLEn(self,state=0):
        '''
        This function is PLL Power up and enable some ldo
        1 - power up 
        0 - disable (default)
            <1> enable dtesto_videopll,videopll_lk,refpll_lk and enable the lsh of pll logic and enable ldo0p9
            <1> =1 enable dtesto_videopll/refpll
            <1>=1 enable videopll_lk
            <1>=1 enable refpll_lk
            <1>=1 enable the lsh of pll logic
            <1>=1 enable ldo0p9
        
        ''' 
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6c, bitnum=1, startbit=1, value=state,CRC=False)
        
        return self.M66S68I2CLocalReadBitfiled(regAddr=0x0b6c, bitnum=1, startbit=1,CRC=False)
    
    
    def RefVideoPLLLDOEn(self, en=0):
        '''
        This function is enable ldo1p2
        1 - enable 
        0 - disable(default)
        
        ''' 
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6c, bitnum=1, startbit=0, value=en,CRC=False)
        
        return self.M66S68I2CLocalReadBitfiled(regAddr=0x0b6c, bitnum=1, startbit=0,CRC=False)
    
    def RefVideoPLLPostCNT(self, postcnt=0):
        '''
        This function is set <5:0> cfg_postcnt<5:0> of refpll postdiv
        
        ''' 
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6d, bitnum=6, startbit=0, value=postcnt,CRC=False)
        
        return self.M66S68I2CLocalReadBitfiled(regAddr=0x0b6d, bitnum=6, startbit=0,CRC=False)
    
    def RefVideoPLLPostDiv(self, postdiv=0):
        '''
        This function is set postdiv,and [7] is revd
            <7:5> post div;
            <7:5> post div; 000:/2;001:/4;010:/8;011:/16;1xx:/32
            000:625M~1.25G
            001:312.5M~625G
            010:156.25M~312.5G(default)
            011:78.125M~156.25G
            1xx:39.0625M~78.125G
        
        ''' 
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b66, bitnum=2, startbit=5, value=postdiv,CRC=False)
        
        return self.M66S68I2CLocalReadBitfiled(regAddr=0x0b66, bitnum=2, startbit=5,CRC=False)
    
    def RefPLLInit(self):
        '''
        This function is initial ref_pll and vedio_pll, and RCLKOUT is 12.5MHZ
        
        '''
        
        #reg0_ref_pll,and cp cfg=1(2uA default), ssc=sdm=disable
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b60, bitnum=8, startbit=0, value=0x13,CRC=False)
        #reg0_vedio_pll,and cp cfg=1(2uA default), ssc=sdm=disable
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b61, bitnum=8, startbit=0, value=0x13,CRC=False)
        
        #reg1_ref_pll,keep default
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b62, bitnum=8, startbit=0, value=0x05,CRC=False)
        #reg1_vedio_pll,keep default
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b67, bitnum=8, startbit=0, value=0x05,CRC=False)
        
        #reg2_ref_pll, and [5:4]00:vssu;01:ckref_det;10:ckfb_det;11:pll_lk_rb
        #0x39,pll_lk_rb, and it is LOW level(0V)
        #0x19 ckref_det,25MHZ
        #0x29 ckfb_det,and 25MHZ
        #0x39,pll_lk_rb, and it is high level(3.3V)
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b63, bitnum=8, startbit=0, value=0x19,CRC=False)
        #reg2_vedio_pll
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b68, bitnum=8, startbit=0, value=0x19,CRC=False)
        
        #reg3_ref_pll, and cfg fractive, lsb
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b64, bitnum=8, startbit=0, value=0,CRC=False)
        #reg3_vedio_pll
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b69, bitnum=8, startbit=0, value=0,CRC=False)
        
        #reg4_ref_pll, and cfg fractive, msb
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b65, bitnum=8, startbit=0, value=0,CRC=False)
        #reg4_vedio_pll
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6a, bitnum=8, startbit=0, value=0,CRC=False)
        
        #reg5_ref_pll,and set post div and int, 
        '''[6:5] map to post div,and 00/1,01/2,10/4,11/8
            [4:0] map to INT, and range 12~25
        '''
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b66, bitnum=8, startbit=0, value=0x6c,CRC=False)    #0x6c from TJ
        #reg5_vedio_pll
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6b, bitnum=8, startbit=0, value=0x6c,CRC=False)
        
        #reg6_ref_pll,and set cnt,range 0~31,but design advise CNT>=5 
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6d, bitnum=8, startbit=0, value=0x0c,CRC=False) #0x0c from TJ
        
        #regpwr set 0x5b
        self.M66S68I2CLocalWriteBitfiled(regAddr=0x0b6c, bitnum=8, startbit=0, value=0x5b,CRC=False)
        
    def RefPLLSet(self, mdivint=24, mdivfraction=0, postdiv=0, postcnt=25, poststrg2=1):
        ''' 
        S68 generate a Reference Clock for Camera 
        
        Pll Frequecy Settting : 25MHz* (mdivint+mdivfraction/65536)*4/(postdiv*postcnt*poststrg2)
            NOTE!!!  
            
        INPUT: 
            mdivint:         12~25
            mdivfraction:    0~65535
            postdiv:         divider, 0~7,  [0:2, 1:4, 2:8, 3:16, 4:32, 5:32]       
            postcnt:         divider, 3~63,
            poststrg2:       divider2, 0~3, [0:1, 1:2, 2:4, 3:8]
            
        OUTPUT: 
           config
        
        '''
        divider = {0:2, 1:4, 2:8, 3:16, 4:32, 5:32, 6:32, 7:32}
        divider2 = {0:1, 1:2, 2:4, 3:8}
        post_div_val = divider[postdiv]
        post_div2_val = divider2[poststrg2]
        
        Pll_Frequecy_set =  25 * (mdivint+2*mdivfraction/65536)*4/(post_div_val * postcnt * post_div2_val) #MHz
        print('[Ref Clock] frequency is',Pll_Frequecy_set, 'MHz')
        
        '''cfg biasgen'''
        
        '''cfg CNT[7:6] & poststrg2_set[5:0](divide should > 2)''' 
        self.m2c.wr_ana_d2a_vgpll_regnamedel10_fields(d2a_vgpll_aldo_en= 1, d2a_vgpll_dldo_en= 1, en = 1, rst = 0)
        
        self.m2c.wr_ana_d2a_refpll_regnamedel10_fields(d2a_refpll_aldo_en = 1, d2a_refpll_dldo_en= 1,en = 1, rst = 0)

        
        reg11=((poststrg2<<6)+postcnt)&0xFF
        self.m2c.wr_ana_d2a_refpll_regnamedel11_fields(reg10 = reg11)
        postcnt_set   = (self.m2c.rd_ana_d2a_refpll_regnamedel11_reg10()&0x3F)
        poststrg2_set = (self.m2c.rd_ana_d2a_refpll_regnamedel11_reg10()>>6)&0x03
  
        '''cfg postdiv & mdiv(12~25)'''
        reg5 = ((postdiv<<5)+mdivint)&0xFF
        self.m2c.wr_ana_d2a_refpll_regnamedel5_fields(reg5 = reg5)
        postdiv_set = (self.m2c.rd_ana_d2a_refpll_regnamedel5_reg5()>>5)&0x07
        mdiv_set    = (self.m2c.rd_ana_d2a_refpll_regnamedel5_reg5()&0x1F)
        
        '''cfg mdivfraction'''
        mdivfractionLSB=(mdivfraction) & 0xFF
        self.m2c.wr_ana_d2a_refpll_regnamedel3_fields(reg3 = mdivfractionLSB) # fraction part of divider
        mdivfractionMSB=(mdivfraction >> 8) & 0xFF
        self.m2c.wr_ana_d2a_refpll_regnamedel4_fields(reg4 = mdivfractionMSB)
        mdivfraction_LSB_set = self.m2c.rd_ana_d2a_refpll_regnamedel3_reg3()
        mdivfraction_MSB_set = self.m2c.rd_ana_d2a_refpll_regnamedel4_reg4()
        mdivfraction = mdivfraction_LSB_set*256 + mdivfraction_MSB_set
        
        time.sleep(0.01)
        ref_pll_lock_status = self.m2c.rd_rcc_pll_status_refpll_lock()
        print('ref_pll_lock_status is: ', ref_pll_lock_status)
        
        return ref_pll_lock_status
        
    def RefVideoPLLLock(self,):
        '''
        This function is read ref_pll_lock status
        
        ''' 
        ref_pll_lock_status = self.M66S68I2CLocalReadBitfiled(regAddr=0x0b6e, bitnum=1, startbit=1,CRC=False)
        video_pll_lock_status = self.M66S68I2CLocalReadBitfiled(regAddr=0x0b6e, bitnum=1, startbit=0,CRC=False)
        
        return (ref_pll_lock_status,video_pll_lock_status)

    '''end'''
        
    def PHYTxEn(self, txen=1):
        '''
        Description: to enable the whole Tx
        Input:
            txen: tx 总使能开关; 1: enable
        Output:
            txen status
        '''
        self.m2c.wr_ana_d2a_tx_regnamedel0_fields(en=txen)  # 0 - tx off, 1 - tx on
    def PHYPLLCDRSettings(self, vcoen=1,cpen=1,nbias=2,pbias=12,bbias=8,kvco=1,vcoidc=31,lpfr1=3,lpfr3=4,lpfc3=1,lpfc2=0,cpcurrent=3,cpvset=8):
        '''
            Description: to cfg C3 PHY PLL settings
            Input:
                cfg nbias and pbias
                cfg vco_varand bbias
                cfg lpf r1,c2/3
                cfg lpf r3 and vcoidac
                cfg cp current and vset
            
            Output:
                NA
        '''
        '''cfg nbias and pbias'''
        print("read back value on vco_varcap before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel20_vco_refn())
        print("read back value on vco_voltage control band cap before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel20_vco_refp())
        self.m2c.wr_ana_d2a_linkpll_regnamedel20_fields(vco_refn=nbias, vco_refp=pbias)
        print("read back value on vco_varcap after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel20_vco_refn())
        print("read back value on vco_voltage control band cap after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel20_vco_refp())
        
        '''cfg vco_varand bbias'''
        print("read back value on vco_varcap before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_var())
        print("read back value on vco_voltage control band cap before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_refb())
        self.m2c.wr_ana_d2a_linkpll_regnamedel19_fields(vco_var=kvco, vco_refb=bbias)
        print("read back value on vco_varcap after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_var())
        print("read back value on vco_voltage control band cap after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_refb())
        
        '''cfg lpf r1,c2/3'''
        print("read back value on lpf_r1 before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel17_lpf_r1())
        print("read back value on lpf_c3 before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel17_lpf_c3())
        print("read back value on lpf_c2 before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel17_lpf_c2())
        self.m2c.wr_ana_d2a_linkpll_regnamedel17_fields(lpf_r1=lpfr1, lpf_c3=lpfc3, lpf_c2=lpfc2)
        print("read back value on lpf_r1 after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel17_lpf_r1())
        print("read back value on lpf_c3 after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel17_lpf_c3())
        print("read back value on lpf_c2 after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel17_lpf_c2())
        
        '''cfg lpf r3 and vcoidac'''
        print("read back value on lpf_r3 before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel18_lpf_r3())
        print("read back value on vco idc before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel18_vco_idc())
        self.m2c.wr_ana_d2a_linkpll_regnamedel18_fields(vco_idc=vcoidc, lpf_r3=lpfr3)
        print("read back value on lpf_r3 after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel18_lpf_r3())
        print("read back value on vco idc after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel18_vco_idc())
        
        '''cfg cp current and vset'''
        print("read back value on cp current before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_ictrl())
        print("read back value on cp vset before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_vset())
        print("read back value on vco en before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel16_vco_en())
        print("read back value on cp en before setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel15_cp_en())
        self.m2c.wr_ana_d2a_linkpll_regnamedel16_fields(vco_en=vcoen,cp_ictrl=cpcurrent, cp_vset=cpvset)
        self.m2c.wr_ana_d2a_linkpll_regnamedel15_fields(cp_en=cpen)
        print("read back value on cp current after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_ictrl())
        print("read back value on cp vset after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_vset())
        print("read back value on vco en after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel16_vco_en())
        print("read back value on cp en after setting:",self.m2c.rd_ana_d2a_linkpll_regnamedel15_cp_en())
            
    
    
    def PHYLinkRate(self, forwardtxrate=1, reverserxrate=3):
        '''
        Description: configure the C3 link rate
        Input:
            forwardtxrate: 0~3
                Link rate
                00: 1.5Gbps
                01: 2Gbps
                10: 6Gbps
                11: 12Gbps
            reverserxrate: 0~3
                2'b11 500MHz, 250Mbps,          
                2'b10 400MHz, 200Mbps           
                2'b01 375MHz, 187.5Mbps         
                2'b00 300MHz, 150Mbps           
        Output:
            rate selection
        '''
        self.m2c.wr_sys_cfg_link_ctrl_fields(link_rate=forwardtxrate)
        forwardtxrate = self.m2c.rd_sys_cfg_link_ctrl_link_rate()
        print('forward tx data rate setting: ', forwardtxrate)
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel10_fields(cktree_div=reverserxrate)
        reverserxrate = self.m2c.rd_ana_d2a_bcrx_regnamedel10_cktree_div()
        print('reverse rx data rate setting: ', reverserxrate)
        return (forwardtxrate, reverserxrate)
    
    def PHYTxPRBSGen(self,sel=0,mode=1,userpatternen=0, datapattern=0x55):
        '''
        This function is Tx prbs pattern generator
        Cfg_post_div:
        sel:
            0: This is no name enum (TEST_TX_PRBS_CTRL_SEL_PRBS7)
            1: This is no name enum (TEST_TX_PRBS_CTRL_SEL_PRBS9)
            2: This is no name enum (TEST_TX_PRBS_CTRL_SEL_PRBS23)
            3: This is no name enum (TEST_TX_PRBS_CTRL_SEL_PRBS31)
        mode:
            1: Raw PHY (TEST_TX_PRBS_CTRL_MODE_PHY)
            2: FEC (TEST_TX_PRBS_CTRL_MODE_FEC)
            3: 8B10B Encoder (TEST_TX_PRBS_CTRL_MODE_ENC_8B10B)
            5: 64B66B Gearbox (TEST_TX_PRBS_CTRL_MODE_GB_64B66B)
            6: 64B66B Scrambler (TEST_TX_PRBS_CTRL_MODE_SCRAM_64B66B)
            4: 8B10B Scrambler (TEST_TX_PRBS_CTRL_MODE_SCRAM_8B10B)
        userpatternen:
            1: to generate the user pattern, 0: prbs pattern
        ''' 
        # Test register access key When the key equals to 0x5C, the test registers can be written.
        self.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  
        if userpatternen ==0: 
            print( self.m2c.rd_tx_link_phy_phy_ctrl0_pcs_sel())
            self.m2c.wr_test_tx_prbs_ctrl_fields(sel=sel, mode=mode)
            prbs_sel = self.m2c.rd_test_tx_prbs_ctrl_sel()
            prbspath = self.m2c.rd_test_tx_prbs_ctrl_mode()
        else:
            for i in range(10):
                self.m2c.wr_test_tx_usr_patn(i=i, value=datapattern)
        self.m2c.wr_test_tx_usr_ctrl_fields(en=userpatternen)
            
        
        # return (prbs_sel, prbspath)
    def PHYErrorInJect(self, num=0, DIST=0, en=0, flip=0, period=0):
        '''
        This function is used to inject errors afetr inetrface from digital to analog
        Input:
            num: num of error inject events, 0->16, 1->128, 2->1024, 3->Continuous
            DIST: 0->periodic, 1->pseudorandom
            en: enable error injection, 1->enable, 0->disable
            FLIP: how many errors inject at each error inject event
            Period: how long an error event occur
                 0: 1x20bit
                 1: 16x20bit
                 2: 256x20bit
                 3: 1024x20bit
                 4: 4096x20bit
                 5: 65536x20bit
                 6: 524288x20bit
                 7: 1048576x20bit
                 period=0, distribution is inactive
        Output:
            NA
        '''
        self.m2c.wr_tx_link_phy_errinj_ctrl0_fields(num = num, dist = DIST)
        self.m2c.wr_tx_link_phy_errinj_ctrl1_fields(flip = flip, period = period)
        self.m2c.wr_tx_link_phy_errinj_ctrl0_fields(en = en)
            
    def PHYBackChanDebug(self, anamon=0):
        '''
        refer to: C:\project\m66s68_r1\Document\02_design\backchannel\c3_dbg_signal_mapping.xlsx
        value=13:
        ANA_MON0~ANA_MON7 pins:
        0    bc_ck5ui_digital    Back channel RX clock for 5UI
        1    bc_d1_buf[0]    Back channel RX data [0]
        2    bc_d1_buf[1]    Back channel RX data [1]
        3    bc_d1_buf[2]    Back channel RX data [2]
        4    bc_d1_buf[3]    Back channel RX data [3]
        5    bc_d1_buf[4]    Back channel RX data [4]
        6    RSVD    Reversed
        7    RSVD    Reversed
        '''
        self.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  
        self.m2c.wr_test_dbg_ctrl(i=0, value=13)
        self.MFNSet(gpio=anamon, mfn=9)
        
    def DbugPinSet(self,dbug_sel=12, gpio=3, div_sel=0):  
        '''
        INPUT:
            dbug_sel:    ID, see doc 'c3_t3_dbg_signal_mapping.xlsx'
            gpio:        PIN
            div_sel:    Divider ratio selection
                            0x0: DIV2
                            0X1: DIV4
                            0X2: DIV8
                            0X3: DIV16
        '''
          
        self.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  
        self.m2c.wr_test_dbg_ctrl(i=0, value=dbug_sel)
        print('dbg_ctrl_sel is: ', self.m2c.rd_test_dbg_ctrl_sel(0))
        self.m2c.wr_test_dbg_ck_ctrl_fields(div_sel = div_sel)
        
        self.MFNSet(gpio=gpio, mfn=9)
        
        if gpio == 1:
            self.m2c.wr_pinmux_pin_io_ow_en_1(value =0) 
            print('[GPIO1]: enable gpio1 because used as cfg0 default')
        if gpio == 2:
            self.m2c.wr_pinmux_pin_io_ow_en_2(value =0) 
            print('[GPIO2]: enable gpio2 because used as cfg1 default')    
    
    def MFNSet(self,gpio=5,mfn=4): 
        '''
        
        '''
        if gpio==0:
            self.m2c.wr_pinmux_pin_ctrl0_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl0_sel()
        elif gpio==1:
            self.m2c.wr_pinmux_pin_ctrl1_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl1_sel()
            
            self.m2c.wr_pinmux_pin_io_ow_en_1(value =0) 
            print('[GPIO1]: enable gpio1 because used as cfg0 default')
            
        elif gpio==2:
            self.m2c.wr_pinmux_pin_ctrl2_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl2_sel()
            
            self.m2c.wr_pinmux_pin_io_ow_en_2(value =0) 
            print('[GPIO2]: enable gpio1 because used as cfg1 default')
            
        elif gpio==3:
            self.m2c.wr_pinmux_pin_ctrl3_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl3_sel() 
        elif gpio==4:
            self.m2c.wr_pinmux_pin_ctrl4_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl4_sel()   
        elif gpio==5:
            self.m2c.wr_pinmux_pin_ctrl5_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl5_sel() 
        elif gpio==6:
            self.m2c.wr_pinmux_pin_ctrl6_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl6_sel()
        elif gpio==7:
            self.m2c.wr_pinmux_pin_ctrl7_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl7_sel()
        elif gpio==8:
            self.m2c.wr_pinmux_pin_ctrl8_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl8_sel()
        elif gpio==9:
            self.m2c.wr_pinmux_pin_ctrl9_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl9_sel()
        elif gpio==10:
            self.m2c.wr_pinmux_pin_ctrl10_fields(sel=mfn)
            mfnset=self.m2c.rd_pinmux_pin_ctrl10_sel()
        else:
            print('over gpio setting range')
               
        print("gpio:", hex(gpio), ' MFN:', mfnset)
        return  mfnset    
    
    def AnalogPinSet(self,gpio=1):   
        '''
        Mix A pins
        gpio: support 0(gpio0),1(gpio1),2(gpio2),3(gpio3),4(gpio5),5(gpio6)
        analog output setting:ae=1, oen=1, ie=0, pu=0, pd=0
        '''
        if gpio==0:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_0(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_0(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_0()
        elif gpio==1:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_1(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_1(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_1()
        elif gpio==2:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_2(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_2(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_2()   
        elif gpio==3:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_3(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_3(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_3() 
        elif gpio==4:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_4(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_4(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_4()
        elif gpio==5:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_5(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_5(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_5()
        elif gpio==6:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_6(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_6(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_6()
        elif gpio==7:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_7(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_7(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_7()
        elif gpio==8:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_8(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_8(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_8()
        elif gpio==9:
            self.m2c.wr_pinmux_pin_io_ow_ctrl_9(value=0x50)
            self.m2c.wr_pinmux_pin_io_ow_en_9(value=0xFF)
            analogselect=self.m2c.rd_pinmux_pin_io_ow_ctrl_9()
        else:
            print('gpio setting over range')
               
        print('gpio:', hex(gpio), 'MFN:', analogselect)

    def PHYLDOTest(self, atest_en, atest_sel):
        '''
        Description: to configure the PLL parameters
        Input:
            atest_en: 0->disable; 1->enable
            atest_sel: 0->driver ldo; 1->mux2to1 ldo; 2->agnd; 3->driver bias voltage
        Output:
            None
        '''
        self.m2c.wr_ana_d2a_tx_regnamedel8_fields(atest_en = atest_en, atest_sel = atest_sel)
        print('d2a_tx_regnamedel8_atest_en is: ', self.m2c.rd_ana_d2a_tx_regnamedel8_atest_en())
        print('tx_regnamedel8_atest_sel is: ', self.m2c.rd_ana_d2a_tx_regnamedel8_atest_sel())
        
    def PHYPLLConfig(self):
        '''
        Description: to configure the PLL parameters
        Input:
            NA
        Output:
            NA
        '''
        self.m2c.wr_ana_d2a_linkpll_regnamedel16_fields(vco_en=1, cp_ictrl=0)  # cp_ictrl-cp current tune
        print('rd_ana_d2a_linkpll_regnamedel16_vco_en is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel16_vco_en())
        print('rd_ana_d2a_linkpll_regnamedel16_cp_ictrl is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel16_cp_ictrl())
        self.m2c.wr_ana_d2a_linkpll_regnamedel20_fields(vco_refn=0x1, vco_refp=0xe)  # var p voltage vbias, 0.1~0.8, 50mV/step
        print('rd_ana_d2a_linkpll_regnamedel20_vco_refn is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel20_vco_refn())
        print('rd_ana_d2a_linkpll_regnamedel20_vco_refp is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel20_vco_refp())
        self.m2c.wr_ana_d2a_linkpll_regnamedel19_fields(vco_var=0x1, vco_refb=0xc)  # vco_var--var sel, kvco tune.    refb-voltage control for band cap, 0.1~0.8, 50mV/step
        print('rd_ana_d2a_linkpll_regnamedel19_vco_refb is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel19_vco_refb())
        self.m2c.wr_ana_d2a_linkpll_regnamedel0_fields(ssc_sdm_en=0, ssc_en=0)
        print('rd_ana_d2a_linkpll_regnamedel0_ssc_en is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel0_ssc_en())
        print('rd_ana_d2a_linkpll_regnamedel0_ssc_sdm_en is: ', self.m2c.rd_ana_d2a_linkpll_regnamedel0_ssc_sdm_en())
        
    def PHYRAWCapture(self):
        self.m2c.wr_test_rx_raw_ctrl_fields(en = 1)
        print('rx_raw_ctrl_en is:', self.m2c.rd_test_rx_raw_ctrl_en())
        for i in range(10):
            print('raw_data is: ',self.m2c.rd_test_rx_raw_data(i))
    def PHYBCRXCSEL(self, csel_txp=3,csel_txn=8):
        '''
        Description:
            cfg S68R7 bcrx csel with 4bit,and value 0~15
        Input:
            default csel_txp=3,csel_txn=8
        '''
        
        reg0_temp   =   self.m2c.rd_ana_d2a_rsvd_regnamedel0_reg0()
        print('reg0_temp',reg0_temp)
        csel_txp_bit01    =   csel_txp & 0x03
        csel_txp_bit23   =   (csel_txp & 0x0c)>>2
        
        csel_txn_bit01    =   csel_txn & 0x03
        csel_txn_bit23    =   (csel_txn & 0x0c)>>2
        
        csel_txpn_bit23 =   ((csel_txp_bit23<<6)&0xc0) | ((csel_txn_bit23<<4)&0x30) | (reg0_temp&0x0f)
        print('csel_txpn_bit23 = ',hex(csel_txpn_bit23))
        
        print('csel_txp = ',csel_txp,' ,and csel_txp_bit01 = ',csel_txp_bit01,' ,and csel_txp_bit23 = ',csel_txp_bit23)
        print('csel_txn = ',csel_txn,' ,and csel_txn_bit01 = ',csel_txn_bit01,' ,and csel_txn_bit23 = ',csel_txn_bit23)
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel11_fields(lpf_csel_txn_lpf=csel_txn_bit01, lpf_csel_txp_lpf=csel_txp_bit01)
        self.m2c.wr_ana_d2a_rsvd_regnamedel0_fields(reg0=csel_txpn_bit23)
        
        rd_s68r7_csel_txp  =   ((((self.m2c.rd_ana_d2a_rsvd_regnamedel0_reg0())&0xc0)>>4)&0x0C) | ((self.m2c.rd_ana_d2a_bcrx_regnamedel11_lpf_csel_txp_lpf())&0x03)
        rd_s68r7_csel_txn  =   ((((self.m2c.rd_ana_d2a_rsvd_regnamedel0_reg0())&0x30)>>2)&0x0C) | ((self.m2c.rd_ana_d2a_bcrx_regnamedel11_lpf_csel_txn_lpf())&0x03)
        
        print('rd_s68r7_csel_txp = ',rd_s68r7_csel_txp,' ,rd_s68r7_csel_txn',rd_s68r7_csel_txn)
        
        return (rd_s68r7_csel_txp,rd_s68r7_csel_txn)
        
    def PHYBCRXCTLE(self, bcrx_ctle=1):
        '''
        Description:
            cfg S68R7 bcrx ctle with 3bit,and value 0~7, but 0 is not good
        Input:
            dbcrx_ctle=1~7
        '''
        reg4_temp = (self.m2c.rd_ana_d2a_rsvd_regnamedel4_reg4())&0xf1
        
        print(' s68r7 bcrx ctle before setting is ',((self.m2c.rd_ana_d2a_rsvd_regnamedel4_reg4())&0x0e)>>1)
        self.m2c.wr_ana_d2a_rsvd_regnamedel4_fields(reg4=(((bcrx_ctle&0x07)<<1) | reg4_temp)) 
        print(' s68r7 bcrx ctle before setting is ',((self.m2c.rd_ana_d2a_rsvd_regnamedel4_reg4())&0x0e)>>1)
    
    def PHYBCRXHPF(self, hpf=0):
        '''
        Description:
            cfg S68R7 bcrx ctle with 1bit WITHIN RSVD_REG5[],and value 0~7, but 0 is not good
        Input:
            dbcrx_ctle=1~7
        '''
        reg5_temp = (self.m2c.rd_ana_d2a_rsvd_regnamedel5_reg5())&0xe7
        
        print(' s68r7 hpf before setting is ',((self.m2c.rd_ana_d2a_rsvd_regnamedel5_reg5())&0x18)>>3)
        self.m2c.wr_ana_d2a_rsvd_regnamedel5_fields(reg5=(((hpf&0x03)<<3) | reg5_temp)) 
        print(' s68r7 hpf before setting is ',((self.m2c.rd_ana_d2a_rsvd_regnamedel5_reg5())&0x18)>>3)
    
    def PHYBackChanPRBSCheck(self, mode=1, sel=0, period=1, ReturnErrCountBef=0):
        '''
        Description:
            PRBS checker
        Input:
            err_cnt_clr: RX PHY PRBS error counter clear
            sel: RX PHY PRBS type selection
                0: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS7)
                1: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS9)
                2: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS23)
                3: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS31)
            mode: RX PHY PRBS mode
                1: Raw PHY (TEST_RX_PRBS_CTRL_MODE_PHY)
                2: FEC (TEST_RX_PRBS_CTRL_MODE_FEC)
                3: 8B10B Decoder (TEST_RX_PRBS_CTRL_MODE_DEC_8B10B)
                4: 8B10B Descrambler (TEST_RX_PRBS_CTRL_MODE_DESCRAM_8B10B)
                5: 64B66B Gearbox (TEST_RX_PRBS_CTRL_MODE_GB_64B66B)
                6: 64B66B Descrambler (TEST_RX_PRBS_CTRL_MODE_DESCRAM_64B66B)
            period: PRBS checking time
        '''
        # Test register access key When the key equals to 0x5C, the test registers can be written.
        self.m2c.wr_test_glb_ctrl0_fields(key=0x5C)  

        if ReturnErrCountBef != 0:
            errorcount_lsb = self.m2c.rd_test_rx_prbs_status1_err_cnt_lsb()  # RX PHY PRBS error counter [7:0]
            errorcount_msb = self.m2c.rd_test_rx_prbs_status2_err_cnt_msb()  # RX PHY PRBS error counter [15:8]            
            errCountBefClr = errorcount_msb*256 + errorcount_lsb 
    
        self.m2c.wr_test_rx_prbs_ctrl_fields(err_cnt_clr = 1)
        self.m2c.wr_test_rx_prbs_ctrl_fields(sel = sel)
        self.m2c.wr_test_rx_prbs_ctrl_fields(mode = 0)
        #clear error count
        error_count = self.m2c.rd_test_rx_prbs_status1_err_cnt_lsb() + self.m2c.rd_test_rx_prbs_status2_err_cnt_msb()*256
        # print ('S68 error count is: ', error_count)
        
        self.m2c.wr_test_rx_prbs_ctrl_fields(err_cnt_clr = 0)
        self.m2c.wr_test_rx_prbs_ctrl_fields(mode = mode)
        time.sleep(period)
        
        prbs_lock = self.m2c.rd_test_rx_prbs_status0_lock()
        
        error_count = self.m2c.rd_test_rx_prbs_status1_err_cnt_lsb() + self.m2c.rd_test_rx_prbs_status2_err_cnt_msb()*256
        
        # print (prbs_lock, error_count)

        if ReturnErrCountBef == 0:
            return (prbs_lock, error_count)
        else:
            return (prbs_lock, error_count, errCountBefClr)
    
    def PHYBackChanPRBSCheck_only(self, mode=1, sel=0, period=1):
        '''
        Description:
            PRBS checker
        Input:
            err_cnt_clr: RX PHY PRBS error counter clear
            sel: RX PHY PRBS type selection
                0: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS7)
                1: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS9)
                2: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS23)
                3: This is no name enum (TEST_RX_PRBS_CTRL_SEL_PRBS31)
            mode: RX PHY PRBS mode
                1: Raw PHY (TEST_RX_PRBS_CTRL_MODE_PHY)
                2: FEC (TEST_RX_PRBS_CTRL_MODE_FEC)
                3: 8B10B Decoder (TEST_RX_PRBS_CTRL_MODE_DEC_8B10B)
                4: 8B10B Descrambler (TEST_RX_PRBS_CTRL_MODE_DESCRAM_8B10B)
                5: 64B66B Gearbox (TEST_RX_PRBS_CTRL_MODE_GB_64B66B)
                6: 64B66B Descrambler (TEST_RX_PRBS_CTRL_MODE_DESCRAM_64B66B)
            period: PRBS checking time
        '''
        # Test register access key When the key equals to 0x5C, the test registers can be written.
        
        time.sleep(period)
        
        prbs_lock = self.m2c.rd_test_rx_prbs_status0_lock()
        
        error_count = self.m2c.rd_test_rx_prbs_status1_err_cnt_lsb() + self.m2c.rd_test_rx_prbs_status2_err_cnt_msb()*256
        
        print (prbs_lock, error_count)
        
        return (prbs_lock, error_count)
    
    
    
    def PHYBackChanErrCheck(self, wait=0.1):
        time.sleep(wait)
        
        prbs_lock = self.m2c.rd_test_rx_prbs_status0_lock()
        
        error_count = self.m2c.rd_test_rx_prbs_status1_err_cnt_lsb() + self.m2c.rd_test_rx_prbs_status2_err_cnt_msb()*256
        
        print (prbs_lock, error_count)
        
        return (prbs_lock, error_count)
    def PHYBackChanEn(self, encdr=1, analogen=1, clktree_en=1, cktree_pudiv=1):
        '''
        Description: To enable the back channel 
        Input:
            encdr: cdr enable signal; 1: enable
            analogen: bcrx analog part enable signal; 1 is enable
            clktree_en: cktree enable signal; 1:enable
            cktree_pudiv: div5 enable signal; 1: enable
        Output:
            NA
        '''
        self.m2c.wr_ana_d2a_bcrx_regnamedel10_fields(cktree_en=clktree_en, cktree_pudiv=cktree_pudiv)
        clktree_en = self.m2c.rd_ana_d2a_bcrx_regnamedel10_cktree_en()
        print('rd_ana_d2a_bcrx_regnamedel10_cktree_en is: ', clktree_en)
        clktree_pudiv = self.m2c.rd_ana_d2a_bcrx_regnamedel10_cktree_pudiv()
        print('rd_ana_d2a_bcrx_regnamedel10_cktree_pudiv is: ', clktree_pudiv)
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel2_fields(en_cdr=encdr)  # cdr enable signal; 1: enable
        en_cdr = self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_cdr()
        print('rd_ana_d2a_bcrx_regnamedel2_en_cdr is: ', en_cdr)
        
        # en - ana_bcrx_reg_en_bcrx_analog in R1, true-sync-only
        self.m2c.wr_ana_d2a_bcrx_regnamedel0_fields(en=analogen)
        bcrxanlogen = self.m2c.rd_ana_d2a_bcrx_regnamedel0_en()
        print('rd_ana_d2a_bcrx_regnamedel0_en is: ', bcrxanlogen)
        
        return (en_cdr, analogen, clktree_en, cktree_pudiv)
    
    
    def PHYBackChanConfig(self, lpf=0, kp=0, kf=2, input_slewctr=3, output_slewctr=3, mode='8b10b'):
        '''
        _c_20231007 version
        '''
        prin = False  # False-not print
        
        # kp - 设置cdr_rxbc电路工作在线性回路模式的比
        self.m2c.wr_ana_d2a_bcrx_regnamedel5_fields(en_ck5ui_digital_reverse=1, en_ck10ui_digital_reverse=1, en_ck10ui_cdr_reverse=1, kp=kp)  # 设置模拟数字发送接收沿； 1： 反相
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel5_en_ck5ui_digital_reverse is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel5_en_ck5ui_digital_reverse())
            print ('rd_ana_d2a_bcrx_regnamedel5_en_ck10ui_digital_reverse is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel5_en_ck10ui_digital_reverse())        
            print ('rd_ana_d2a_bcrx_regnamedel5_en_ck10ui_cdr_reverse is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel5_en_ck10ui_cdr_reverse())
            print ('rd_ana_d2a_bcrx_regnamedel5_kp is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel5_kp())
                
        # lpf description:, reg_bcrx_lpfc_cvarctr in R1
        '''
            txn lpfc C1 cap select; cap=280f+220f*code
            used to adjust bcrx 3order lpf C1 cap value:
            2'b00:280f;
            2'b01:520f;
            2'b10:760f;
            2'b11:1pF;
            forward tx is prbs10 pattern: set 2'b11;
            forward tx is 64b66b pattern:set 2'b11;
        '''
        if mode=='8b10b':
            self.m2c.wr_ana_d2a_bcrx_reg15_fields(lpf_csel_txn_lpfc=3, lpf_csel_txp_lpfc=3, lpf_csel_txn_lpf=3, lpf_csel_txp_lpf=3)
        else:
            self.m2c.wr_ana_d2a_bcrx_reg15_fields(lpf_csel_txn_lpfc=3, lpf_csel_txp_lpfc=3, lpf_csel_txn_lpf=3, lpf_csel_txp_lpf=1)
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel7_fields(pi_output_slewctr=output_slewctr, pi_input_slewctr=input_slewctr, picode_step=4)
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel7_pi_input_slewctr is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel7_pi_input_slewctr())  # bcrx pi电路输入信号的带宽控制码,pi_input_bw_ctr[1:0]*Cap_unit
            print ('rd_ana_d2a_bcrx_regnamedel7_pi_output_slewctr is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel7_pi_output_slewctr())  # bcrx pi电路输入出信号的带宽控制码,pi_output_bw_ctr[1:0]*Cap_unit
            print ('rd_ana_d2a_bcrx_regnamedel7_picode_step is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel7_picode_step())  # 设置cdr_rxbc电路在正常工作模式中picode更新的step值；
        
        # en - ana_bcrx_reg_en_bcrx_analog in R1, true-sync-only
        self.m2c.wr_ana_d2a_bcrx_regnamedel0_fields(kcycle_reset_cdr=0, en=1)
        print('rd_ana_d2a_bcrx_regnamedel0_kcycle_reset_cdr is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel0_kcycle_reset_cdr())
        print('rd_ana_d2a_bcrx_regnamedel0_en is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel0_en())
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel2_fields(en_picode_override=0, en_picode_offset=0, en_kf=0, en_kp=0, en_hratio=0, reset_async_cdr=0)
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel2_en_kf is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_kf())
            print ('rd_ana_d2a_bcrx_regnamedel2_en_kp is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_kp())
            print ('rd_ana_d2a_bcrx_regnamedel2_en_hratio is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_hratio())
            print ('rd_ana_d2a_bcrx_regnamedel2_en_picode_override is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_picode_override())
            print ('rd_ana_d2a_bcrx_regnamedel2_en_picode_offset is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_picode_offset())
            # print ('rd_ana_d2a_bcrx_regnamedel2_en_cdr is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_cdr())
            print ('rd_ana_d2a_bcrx_regnamedel2_reset_async_cdr is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel2_reset_async_cdr())
            
        self.m2c.wr_ana_d2a_bcrx_regnamedel1_fields(lpfc_dacctr=lpf)  # bcrx dac 电流code，用来抵消tx signal；current=40u+2.5u*code
        print ('rd_ana_d2a_bcrx_regnamedel1_lpfc_dacctr is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel1_lpfc_dacctr())
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel3_fields(hratio=0)
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel3_hratio is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel3_hratio())
        self.m2c.wr_ana_d2a_bcrx_regnamedel4_fields(kf=kf, kmr=0x10)
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel4_kf is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel4_kf())
            print ('rd_ana_d2a_bcrx_regnamedel4_kmr is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel4_kmr())
            
        self.m2c.wr_ana_d2a_bcrx_regnamedel8_fields(picode_a_override=0)  # 设置cdr_rxbc电路的picode_a的手控值( 手控值:override所指定的加载值)
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel8_picode_a_override is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel8_picode_a_override())
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel9_fields(picode_b_override=0)
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel9_picode_b_override is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel9_picode_b_override())
        
        self.m2c.wr_ana_d2a_bcrx_regnamedel6_fields(picode_offset=0)
        if prin:
            print ('rd_ana_d2a_bcrx_regnamedel6_picode_offset is: ', self.m2c.rd_ana_d2a_bcrx_regnamedel6_picode_offset())
        

    def PHYBackChanReset(self, encdr=1):   
        '''
        Description: to reset the CDR
        Input:
            en_cdr: 1-- enable cdr
        Output:
            en_cdr: 1-- enable CDR
        '''
        self.m2c.wr_ana_d2a_bcrx_regnamedel2_fields(en_cdr=0)  # cdr enable signal; 1: enable
        self.m2c.wr_ana_d2a_bcrx_regnamedel2_fields(en_cdr=encdr)  # cdr enable signal; 1: enable
        en_cdr = self.m2c.rd_ana_d2a_bcrx_regnamedel2_en_cdr()
        print('rd_ana_d2a_bcrx_regnamedel2_en_cdr is: ', en_cdr)
        return en_cdr
    
    def PHYSwingSetting(self, swing_setting=0):
        '''
        Description: C3 forward Tx swing setting
        Input:
            Swing: 6bits, Tx forward的输出电压幅度控制码, 该控制码的2进制值越大则Tx forward的输出电压幅度越小
        Output:
            NA
        '''
        self.m2c.wr_ana_d2a_tx_regnamedel3_fields(k_swing_down=swing_setting)
        print ('rd_ana_d2a_tx_regnamedel3_k_swing_down is:', self.m2c.rd_ana_d2a_tx_regnamedel3_k_swing_down())
                
    def PHYEmphasis(self, sign_main=0, sign_pre=0, sign_post=0, k_pre=0, k_post=0):
        '''
        _c_20231007
        '''
        self.m2c.wr_ana_d2a_tx_regnamedel1_fields(sign_pre=sign_pre, sign_post=sign_post, sign_main=sign_main, k_post=k_post)  # Tx forward的FFE均衡器中post-cursor1/tap1系数的绝对值/幅度值的控制码,该控制码的2进制值越大则对应系数的绝对值/幅度值越大
        print ('rd_ana_d2a_tx_regnamedel1_sign_main is: ', self.m2c.rd_ana_d2a_tx_regnamedel1_sign_main())
        print('rd_ana_d2a_tx_regnamedel1_sign_pre is: ', self.m2c.rd_ana_d2a_tx_regnamedel1_sign_pre())
        print('rd_ana_d2a_tx_regnamedel1_sign_post is: ', self.m2c.rd_ana_d2a_tx_regnamedel1_sign_post())
        
        print('rd_ana_d2a_tx_regnamedel1_k_post is: ', self.m2c.rd_ana_d2a_tx_regnamedel1_k_post())
        
        self.m2c.wr_ana_d2a_tx_regnamedel2_fields(k_pre=k_pre)
        print ('k_pre is: ', self.m2c.rd_ana_d2a_tx_regnamedel2_k_pre())
    
    def PHYLinkState(self):
        '''
        Description: to record the C3 link status
        Input:
            NA
        Output:
            link_lock_state:
                Link FSM state
            0x0: LINK_IDLE. It transfers to LINK_INIT_PLL state when the main FSM is in ACTIVE state.
            0x1: LINK_INIT_PLL state. Enable link PLL
            0x2: LINK_INIT_HSTX state. Enable link high speed TX channel
            0x3: LINK_INIT_BCRX state. Enable link back RX channel
            0x4: LINK_RESID_CALIB state. TX residual calibration.
            0x5: in LINK_SETUP state. Wait link lock
            0x6: in LINK_NORMAL state. Link normal
            0x7: in LINK_LOSS state. Link loss
            
            Main FSM state
            0x0: IDLE.
            0x1: INIT_PLL. Enable PLL for Memory/Logic BIST.
            0x2: INIT_MBIST. Memory BIST
            0x3: INIT_LBIST_S. Stuck Logic BIST
            0x4: INIT_LBIST_T. Transition Logic BIST
            0x5: INIT_RESET. Reset the device. Disable PLL for Memory/Logic BIST.
            0x6: INIT_S2. efuse refreshing, CFGIO parsing, SAR ADC BIST.
            0x7: ACTIVE.
            
        '''
        link_lock_state = self.m2c.rd_test_fsm_status0_link()
        print('C3 link lock status is: ', link_lock_state)
        main_state = self.m2c.rd_test_fsm_status0_main()
        print('Main state is: ', main_state)
        return (link_lock_state, main_state)
    
    def MIPIPHYEN(self): 
        
        '''
            enable and set the voltage of mipi DLDO/Vref LDO
        '''
        
        self.m2c.wr_mipi_rx_dphy_top_rx_ref_fields(en=1, vset=4)  

        print ('mipi_ref_vset is: ', self.m2c.rd_mipi_rx_dphy_top_rx_ref_vset())
        
        print ('mipi_ref_en is: ', self.m2c.rd_mipi_rx_dphy_top_rx_ref_en())
        
        self.m2c.wr_mipi_rx_dphy_top_rx_dldo_fields(vset=4)
        print ('mipildo_vset_09 is: ', self.m2c.rd_mipi_rx_dphy_top_rx_dldo_vset())
        
        print ('ana_ldo_reg_mipildo_swmode_09 is: ', self.m2c.rd_mipi_rx_dphy_top_rx_dldo_sw())
        
        # print ('ana_ldo_reg_mipildo_bp_09 is: ', self.m2c.rd_mipi_rx_dphy_top_rx_dldo_bp())
        
        self.m2c.wr_mipi_rx_mipi_dig_rx_mipi_ctrl_fields(en_mipidldo = 1)
        print ('en_mipildo', self.m2c.rd_mipi_rx_mipi_dig_rx_mipi_ctrl_en_mipidldo())
          
    def MIPIPHYEN_LMN(self,mipi_ref_vset=4,mipi_dldo=4): 
        '''only for LMN debug'''
        
        self.m2c.wr_mipi_rx_dphy_top_rx_ref_fields(en=1, vset=mipi_ref_vset)  

        print ('mipi_ref_vset is: ', self.m2c.rd_mipi_rx_dphy_top_rx_ref_vset())
        
        print ('mipi_ref_en is: ', self.m2c.rd_mipi_rx_dphy_top_rx_ref_en())
        
        self.m2c.wr_mipi_rx_dphy_top_rx_dldo_fields(vset=mipi_dldo)
        print ('mipildo_vset_09 is: ', self.m2c.rd_mipi_rx_dphy_top_rx_dldo_vset())
        
        print ('ana_ldo_reg_mipildo_swmode_09 is: ', self.m2c.rd_mipi_rx_dphy_top_rx_dldo_sw())
        
        #print ('ana_ldo_reg_mipildo_bp_09 is: ', self.m2c.rd_mipi_rx_dphy_top_rx_dldo_bp())
        
        self.m2c.wr_mipi_rx_mipi_dig_rx_mipi_ctrl_fields(en_mipidldo = 1)
        print ('en_mipildo', self.m2c.rd_mipi_rx_mipi_dig_rx_mipi_ctrl_en_mipidldo())
        
    def MIPIPHYEN_b_20230828(self):   
        
        self.m2c.wr_ana_misc_reg_mipi_ref_vset_fields(bf0=4)
        print ('ana_misc_reg_mipi_ref_vset is: ', self.m2c.rd_ana_misc_reg_mipi_ref_vset_bf0())
        
        self.m2c.wr_ana_misc_reg_mipi_ref_en_fields(bf0=1)
        print ('ana_misc_reg_mipi_ref_en is: ', self.m2c.rd_ana_misc_reg_mipi_ref_en_bf0())
        
        self.m2c.wr_ana_ldo_reg_mipildo_vset_09_fields(bf0=4)
        print ('ana_ldo_reg_mipildo_vset_09 is: ', self.m2c.rd_ana_ldo_reg_mipildo_vset_09_bf0())
        
        self.m2c.wr_ana_ldo_reg_mipildo_swmode_09_fields(bf0=0)
        print ('ana_ldo_reg_mipildo_swmode_09 is: ', self.m2c.rd_ana_ldo_reg_mipildo_swmode_09_bf0())
        
        self.m2c.wr_ana_ldo_reg_mipildo_bp_09_fields(bf0=0)
        print ('ana_ldo_reg_mipildo_bp_09 is: ', self.m2c.rd_ana_ldo_reg_mipildo_bp_09_bf0())
        
        self.m2c.wr_ana_ldo_reg_en_mipildo_09_fields(bf0=1)
        print ('ana_ldo_reg_en_mipildo_09 is: ', self.m2c.rd_ana_ldo_reg_en_mipildo_09_bf0())
        
        self.m2c.wr_mipi_rx_reg_mipi_dig_rx_mipi_ctrl_fields(en_mipidldo = 1)
        print('en_mipidldo is: ', self.m2c.rd_mipi_rx_reg_mipi_dig_rx_mipi_ctrl_en_mipidldo())   

    def MIPICrossbar(self, type=1, logiclane=0, phylane=0):
        '''
        
        INPUT:
            type:       used as data(0) or clock(1)
            logiclane:  connect to CSIx's lane(0~4), 4 data lane max`
            phylane:    phylane  — PIN(QFN) mapping(R1):  
            polarity:   PN polarity(HS/LP)  0: normal(default), 3: HS+LP revert, only in DPHY
            lsb_first:  msb or lsb first, 0: msb first(default), only in DPHY
                        
                        when D-PHY: 2x5=10pin,  
                                            phylane
                                     lane0  — D0               
                                     lane1  — D1                
                                     lane2  — CLK             
                                     lane3  — D2              
                                     lane4  — D3                              

        '''
        
        return
    
    def MIPIPHY0Crossbar(self, lanetype=0, lanelogic=0): 
        
        '''
        config phylane0 as clock or data, and enable
        INPUT:
            lanetype: 0:data lane, 1:clock lane
            lanelogic: csi logic lane0~3
        '''
        
        lanemap = (lanetype<<2) + lanelogic
        self.m2c.wr_mipi_rx_mipi_dig_rx_ctrl00_fields(lane_map = lanemap)
        print ('mipi_rx_reg_mipi_dig_rx_ctrl00_lane is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_ctrl00_lane_map())
    
    def MIPIPHY1Crossbar(self, lanetype=0, lanelogic=1): 
        
        lanemap = (lanetype<<2) + lanelogic
        self.m2c.wr_mipi_rx_mipi_dig_rx_ctrl01_fields(lane_map = lanemap)
        print ('mipi_rx_reg_mipi_dig_rx_ctrl01_lane is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_ctrl01_lane_map())
    
    def MIPIPHY2Crossbar(self, lanetype=1, lanelogic=0): 
        
        lanemap = (lanetype<<2) + lanelogic
        self.m2c.wr_mipi_rx_mipi_dig_rx_ctrl02_fields(lane_map = lanemap)
        print ('mipi_rx_reg_mipi_dig_rx_ctrl02_lane is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_ctrl02_lane_map())    
    
    def MIPIPHY3Crossbar(self, lanetype=0, lanelogic=2): 
        
        lanemap = (lanetype<<2) + lanelogic
        self.m2c.wr_mipi_rx_mipi_dig_rx_ctrl03_fields(lane_map = lanemap)
        print ('mipi_rx_reg_mipi_dig_rx_ctrl03_lane is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_ctrl03_lane_map())         
    
    def MIPIPHY4Crossbar(self, lanetype=0, lanelogic=3): 
        
        lanemap = (lanetype<<2) + lanelogic
        self.m2c.wr_mipi_rx_mipi_dig_rx_ctrl04_fields(lane_map = lanemap)
        print ('mipi_rx_reg_mipi_dig_rx_ctrl04_lane is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_ctrl04_lane_map())    
        
    def MIPIRxLaneEN(self, num_lane=0):
        
        '''
        nums of data lane
        
        INPUT:
            num_lane:  use (num_lane+1) data lane
        '''
        
        self.m2c.wr_csi2_rx_csi_rx_n_lanes_fields(n_lanes=num_lane)
        print ('csi2_rx_reg_n_lanes_n_lanes is: ', self.m2c.rd_csi2_rx_csi_rx_n_lanes_n_lanes())
        
    
    def MIPIDCOSetting(self, err_window=0, dco_cal_time=3, fword_time=3, dco_clk_en_time=3, dco_en_time=3): 
        
          
        self.m2c.wr_mipi_rx_mipi_dig_rx_dco_cal3_fields(error_window=err_window)  # counter changing precision
        print('error_window is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal3_error_window())
        
        self.m2c.wr_mipi_rx_mipi_dig_rx_dco_cal2_fields(window_time_sel=dco_cal_time, wait_time_for_fword=fword_time, wait_time_for_dco_clk_en=dco_clk_en_time, wait_time_for_dco_en=dco_en_time)
        
        
        print ('mipi_dig_rx_dco_cal2_window_time_sel is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal2_window_time_sel())
        print ('mipi_dig_rx_dco_cal2_wait_time_for_fword is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal2_wait_time_for_fword())
        print ('mipi_dig_rx_dco_cal2_wait_time_for_dco_en is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal2_wait_time_for_dco_en())
        print ('mipi_dig_rx_dco_cal2_wait_time_for_dco_clk_en is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal2_wait_time_for_dco_clk_en())
        
    def MIPIBootEN(self):
        
        self.m2c.wr_ana_d2a_rsvd_regnamedel1(value = 0x00)
        self.m2c.wr_ana_d2a_rsvd_regnamedel1(value = 0x01)  #bit0: pll_200M de-glitch enable, set 0->1 will enable pll_200M to MIPI module
        print('[pll_200M] (de-glitch) set 0->1 will enable pll_200M to MIPI module')
        
        self.m2c.wr_sys_cfg_mipi_ctrl_fields(en = 0)

        self.m2c.wr_mipi_rx_lane_module_rx_ctrl00_fields(num_of_trail = 0) # before R7, crc error when shot hs_trail
        print('[EOT=LP11] and set num of trial=0, because of BUG before R7 versions')
        
        self.m2c.wr_sys_cfg_mipi_ctrl_fields(en = 1)
        time.sleep(0.01)
        print ('mipi boot en is: ', self.m2c.rd_sys_cfg_mipi_ctrl_en())
        
        '''powerdown DCO, R7 state machine need to execute it, after boot up done, power done DCO '''
        self.m2c.wr_mipi_rx_mipi_dig_rx_dco_cal1_fields(dco_clk_en_reg = 0, dco_en_reg = 0, dco_pon_reg = 0)    #powerdown DCO
        print('Disable DCO (R7 Version), PLL 200M is as default')  
        
        self.m2c.wr_mipi_rx_dphy_rx_reg1_fields(cb_spare_in = 0x56) 
        print('set LP-HYST to ~50mV[from R8]')  
        
    def MIPIRxSettle(self, hs_settle_time=0, ls_settle_time=0):  
        
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(0, lane_t_hs_settle = hs_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(1, lane_t_hs_settle = hs_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(2, lane_t_hs_settle = hs_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(3, lane_t_hs_settle = hs_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(4, lane_t_hs_settle = hs_settle_time)
        
        print('lane_t_hs_settle0 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_5_lane_t_hs_settle(0))
        print('lane_t_hs_settle1 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_5_lane_t_hs_settle(1))
        print('lane_t_hs_settle2 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_5_lane_t_hs_settle(2))
        print('lane_t_hs_settle3 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_5_lane_t_hs_settle(3))
        print('lane_t_hs_settle4 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_5_lane_t_hs_settle(4))
        
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(0, ls_settle = ls_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(1, ls_settle = ls_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(2, ls_settle = ls_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(3, ls_settle = ls_settle_time)
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(4, ls_settle = ls_settle_time)
        
        print('ls_settle0 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_lane_ls_settle_ls_settle(0))
        print('ls_settle1 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_lane_ls_settle_ls_settle(1))
        print('ls_settle2 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_lane_ls_settle_ls_settle(2))
        print('ls_settle3 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_lane_ls_settle_ls_settle(3))
        print('ls_settle4 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_lane_ls_settle_ls_settle(4))
                 
    def MIPIBootStatus(self):
        
        print('')
        print('[MIPI Boot Status]:')
        
        boot_status = self.m2c.rd_mipi_rx_mipi_dig_rx_boot4_state_r()
        print('mipi_rx_reg_mipi_dig_rx_boot4_state_r is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_boot4_state_r())
        
        dco_cal_err = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status0_dco_cal_err()
        print ('dco cal err is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status0_dco_cal_err())
        
        res_cal_err = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status0_res_cal_err()
        print ('res_cal_err is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status0_res_cal_err())
        
        mipi_boot_done = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status0_mipi_boot_done()
        print ('mipi_boot_done is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status0_mipi_boot_done())
        
        offcal_err0 = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status1_offcal_err(0)

        offcal_err1 = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status1_offcal_err(1)

        offcal_err2 = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status1_offcal_err(2)

        offcal_err3 = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status1_offcal_err(3)

        offcal_err4 = self.m2c.rd_mipi_rx_mipi_dig_rx_boot_status1_offcal_err(4)
        print('rx_boot_status1_offcal_err0/1/2/3/4:', offcal_err0,offcal_err1,offcal_err2,offcal_err3,offcal_err4)
         
        dco_code = self.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal4_dco_fword_r()
        setr_calib_r_auto = self.m2c.rd_mipi_rx_mipi_dig_rx_res_cal2_setr_calib_r()
        setr_r_auto = self.m2c.rd_mipi_rx_mipi_dig_rx_res_cal2_setr_r()
        
        offset0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal_offcal_prog(i=0)
        offset1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal_offcal_prog(i=1)
        offset2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal_offcal_prog(i=2)
        offset3 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal_offcal_prog(i=3)
        offset4 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal_offcal_prog(i=4)
        
        print('[DCO_CAL] dco_cal_err/dco_code = ', dco_cal_err, dco_code,)
        print('[RES_CAL] res_cal_err/res_code(4bit)/res_code(3bit) = ', res_cal_err ,setr_calib_r_auto,setr_r_auto)
        print('[Offset_CAL] lane0/1/2/3/4 = ', offset0,offset1,offset2,offset3,offset4)
        print('')
        
        return (boot_status, dco_cal_err, res_cal_err, mipi_boot_done, offcal_err0,offcal_err1,offcal_err2,offcal_err3,offcal_err4, dco_code,setr_calib_r_auto,setr_r_auto,offset0,offset1,offset2,offset3,offset4)
        
    def CSI2REST(self): 
        
        '''
        do CSI reset one time.
        
        '''
        
        self.m2c.wr_csi2_rx_csi_rx_csi2_resetn_fields(csi2_resetn=0)   
        time.sleep(0.2)
        self.m2c.wr_csi2_rx_csi_rx_csi2_resetn_fields(csi2_resetn=1)
        
        print('CSI reset one time(200ms).')


    def CSIVcDtRemap(self, remap_func= 1, remap_id =1, vcid_old = 0, vcid_new = 0, dt_old=0, dt_new=0 ):
        '''
        remap or filter spicified (vcid & dt)
        
        INPUT:
            remap_func:       remap or filter spicified (vcid & dt)
                                    2'b00: bypass all the dt and vc
                                    2'b01: remap original dt/vc to new dt/vc
                                    2'b10: filter dt/vc from the stream
                                    
            remap_id:         0~8, have max. 8 (vcid & dt) types of remap/filter
            vcid_old:         0~31, old vcid to be remapped, 5bits
            vcid_new:         0~31, new vcid, 5bits
            dt_old:           0~64, old dt to remapped, 6bits.
            dt_new:           0~64, new vcid, 6bits.

        luowen @20250310
        '''
          
        self.m2c.wr_sys_cfg_remap_func_fields(i=remap_id, sel = remap_func)
        self.m2c.wr_sys_cfg_ori_dt_fields(i=remap_id, config = dt_old)
        self.m2c.wr_sys_cfg_remap_dt_fields(i=remap_id, config = dt_old)
        
        self.m2c.wr_sys_cfg_ori_vc_fields(i=remap_id, config = vcid_new)
        self.m2c.wr_sys_cfg_remap_vc_fields(i=remap_id, config = vcid_new) 
            
        print('[(VCID & DT) remap]','remap_func=', remap_func, 'remap_id=',remap_id, 'vcid_old=',vcid_old, 'vcid_new=',vcid_new,'dt_old=',dt_new)  
 
    
    def M2CHSStateClear(self):
        self.QRegisterAccess.devAddr = 0x44
        self.m2c.wr_rcc_ck_mipi_fields(rx_en = 0)
        self.m2c.wr_rcc_ck_mipi_fields(rx_en = 1)
      
    def TempSensor(self):       
        '''
        on chip temperature Sensor
        ''' 
        self.m2c.wr_adc_ctrl_adc_ctrl0_fields(adc_en=0)
        channel=7
        self.m2c.wr_adc_ctrl_adc_ctrl2(i=0, value=(0x01<<channel)&0xff)
        self.m2c.wr_adc_ctrl_adc_ctrl0_fields(adc_en=1)
        data_bit0_7 = self.m2c.rd_adc_ctrl_adc_ch_data_0_7_adc_channel_data_0_7(i=channel)
        data_bit8_10 = self.m2c.rd_adc_ctrl_adc_ch_data_8_10_adc_channel_data_8_10(i=channel)
        adc_output_code = (data_bit8_10*256)+ data_bit0_7
        vol_tested_syntax2 = (((adc_output_code)-1024)*1.6/2048+0.6) 

        T=(vol_tested_syntax2-0.7786)/(-0.0016)  
        
        return T
           
    def MIPIPRBSCheck(self, prbs_mode='prbs7', wait=1, lsb_first=0, lane=None, userdata=[0x55, 0x55]):
        '''
        INPUT:
            lane:         0~3,  data lane(Note: depends on CIS data lane selected, if only 1 lane used, should ='0' even the connected physical lane is '1' or others)
            prbs_mode:    prbs7, prbs9, prbs11, prbs15, prbs18, prbs23, prbs31, user,catch_4_bytes_data
            wait:         time window for bit error check
            lsb_first:    enable LSB first
            
        OUTPUT:
            error count: 32bis
        
        '''
            
        prbs_dict = {'prbs7':0, 'prbs9':1, 'prbs11':2, 'prbs15':3, 'prbs18':4, 'prbs23':5, 'prbs31':6, 'user':7, 'catch_4_bytes_data':15}

        if lane == None:
            
            ''' 1. capture data to check prbs data is captured '''
            prbs_mode0 = 'catch_4_bytes_data'

            prbs_work_status = []
            prbs_err_count = []
            for lane in range(4):
                
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, prbs_lsb_first = lsb_first)
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_chk_en = 0)
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_gen_mode = prbs_dict[prbs_mode0])
                if prbs_mode=='user':
                    self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk2_lsb_fields(i=lane, user_data = userdata[0])
                    self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk2_msb_fields(i=lane, user_data = userdata[1])
                else:
                    pass        
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_chk_en = 1)
          
                ''' clear error counter'''
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 0)       
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 1)
                
                time.sleep(0.001)    #
      
                ''' refresh error counter'''
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 0)    
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 1)
                            
                ''' read bit error count'''
                error_lsb0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk3_test_err_cnt_7_0(i=lane)
                error_mid0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk4_test_err_cnt_15_8(i=lane)
                error_midu0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk5_test_err_cnt_23_16(i=lane)
                error_msb0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk6_test_err_cnt_31_24(i=lane)
                print('[Capture data0] of lane', lane, ': ', hex(error_lsb0), hex(error_mid0), hex(error_midu0), hex(error_msb0))
            
                time.sleep(0.01)    #time length should less than HS duration 
                
                
                ''' clear2 error counter'''
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 0)       
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 1)                

                time.sleep(0.001)    #
                    
                ''' refresh2 error counter'''
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 0)    
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 1)
                            
                ''' read2 bit error count'''
                error_lsb1 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk3_test_err_cnt_7_0(i=lane)
                error_mid1 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk4_test_err_cnt_15_8(i=lane)
                error_midu1 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk5_test_err_cnt_23_16(i=lane)
                error_msb1 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk6_test_err_cnt_31_24(i=lane)
                print('[Capture data1] of lane', lane, ': ', hex(error_lsb1), hex(error_mid1), hex(error_midu1), hex(error_msb1))
                
                if (error_lsb0 == error_lsb1) & (error_mid0 == error_mid1) & (error_midu0 == error_midu1) &  (error_msb0 == error_msb1):        #判断是否接收到hs数据，因为如果没有启动,也会返回0,存在导致误测          
                    prbs_status = 0
                    prbs_count = 1000000        #BER=1
                    print('Maybe PRBS0 not work! of lane',lane)
                else:
                    prbs_status = 1
                    prbs_count = 0        #BER=1
                    print('Maybe PRBS is work! of lane',lane)  
                        
                prbs_work_status.append(prbs_status) 
                prbs_err_count.append(prbs_count)               
            
            ''' 2. check prbs error '''                    
            for lane in range(4):

                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, prbs_lsb_first = lsb_first)
        
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_chk_en = 0)
                
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_gen_mode = prbs_dict[prbs_mode])
                
                if prbs_mode=='user':
                    self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk2_lsb_fields(i=lane, user_data = userdata[0])
                    self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk2_msb_fields(i=lane, user_data = userdata[1])
                else:
                    pass        
                
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_chk_en = 1)
          
                ''' clear error counter'''
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 0)       
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 1)
                
               
            time.sleep(wait)    #time length should less than HS duration
                  
            error_count = []    
            for lane in range(4):    
                ''' refresh error counter'''
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 0)    
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 1)
                     
                ''' read bit error count'''
                error_lsb0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk3_test_err_cnt_7_0(i=lane)
                error_mid0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk4_test_err_cnt_15_8(i=lane)
                error_midu0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk5_test_err_cnt_23_16(i=lane)
                error_msb0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk6_test_err_cnt_31_24(i=lane)    
                
                err_cnt = (error_msb0<<24) + (error_midu0<<16) +(error_mid0<<8) + error_lsb0 
                
                if prbs_work_status[lane] == 1:
                    error_count.append(err_cnt)     #actual bit err
                 
                elif prbs_work_status[lane] == 0:   
                    error_count.append(prbs_err_count[lane])     #not work, return 1000000
                 
            return  error_count   
        
        else:
    
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, prbs_lsb_first = lsb_first)
    
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_chk_en = 0)
            
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_gen_mode = prbs_dict[prbs_mode])
            
            if prbs_mode=='user':
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk2_lsb_fields(i=lane, user_data = userdata[0])
                self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk2_msb_fields(i=lane, user_data = userdata[1])
            else:
                pass        
            
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk0_fields(i=lane, test_chk_en = 1)
      
            ''' clear error counter'''
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 0)       
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, clear_err = 1)
            
            time.sleep(wait)    #time length should less than HS duration
            
            
            ''' refresh error counter'''
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 0)    
            self.m2c.wr_mipi_rx_mipi_dig_rx_test_chk1_fields(i=lane, update_err = 1)
              
            
            ''' read bit error count'''
            error_lsb0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk3_test_err_cnt_7_0(i=lane)
            error_mid0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk4_test_err_cnt_15_8(i=lane)
            error_midu0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk5_test_err_cnt_23_16(i=lane)
            error_msb0 = self.m2c.rd_mipi_rx_mipi_dig_rx_test_chk6_test_err_cnt_31_24(i=lane)
            
            if prbs_mode != 'catch_4_bytes_data':
                error_count = (error_msb0<<24) + (error_midu0<<16) +(error_mid0<<8) + error_lsb0
                print ('[error_count] of lane', lane,'is:', error_count)
                
            else:
                error_count = (hex(error_msb0),hex(error_midu0),hex(error_mid0),hex(error_lsb0))
                print ('[Data Capture] of lane', lane,'is:', error_count)
            
            return (error_count)
    
        
            
    def MIPILPStatus(self):    
        
        lp_status0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(0)
        lp_status1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(1)
        lp_status2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(2)
        lp_status3 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(3)
        lp_status4 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(4)
        
        
        # print ('lp_status0 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(0)) 
        # print ('lp_status1 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(1))   
        # print ('lp_status2 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(2))   
        # print ('lp_status3 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(3))  
        # print ('lp_status4 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_ls_state(4)) 
        
        print('[LP Status] lane0~4 :',lp_status0, lp_status1, lp_status2, lp_status3, lp_status4)
        
        return (lp_status0, lp_status1, lp_status2, lp_status3, lp_status4)
        
    def MIPIHSStatus(self):  
        
        hs_status0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(0) 
        hs_status1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(1) 
        hs_status2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(2)
        hs_status3 = self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(3)
        hs_status4 = self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(4)
        
        # print ('hs_status0 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(0)) 
        # print ('hs_status1 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(1)) 
        # print ('hs_status2 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(2))
        # print ('hs_status3 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(3))
        # print ('hs_status4 is: ', self.m2c.rd_mipi_rx_lane_module_rx_ro_hs_state_hs_state(4))
        
        print('[HS Status] lane0~4 :',hs_status0,hs_status1,hs_status2,hs_status3,hs_status4)
        
        return (hs_status0, hs_status1, hs_status2, hs_status3, hs_status4)
        

    def MIPILPData(self,lane=0):  
        
        '''
        read LP 0/1 directly
        '''

        lp_level = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_lp_data(i=lane) 
        
        lp_n = (lp_level&0x02)>>1
        lp_p = lp_level&0x01
        
        print('LP  p/n of lane',lane,'=', lp_p, lp_n)
        
        return (lp_level)


    def MIPILPDataAll(self,lane=None):  
        
        '''
        read LP 0/1 directly
        '''

        lp_level0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_lp_data(i=0) 
        lp_level1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_lp_data(i=1) 
        lp_level2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_lp_data(i=2) 
        lp_level3 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_lp_data(i=3) 
        lp_level4 = self.m2c.rd_mipi_rx_lane_module_rx_ro_ls_state_lp_data(i=4)
         
        print('LP of lane0/1/2/3/4 =', lp_level0,lp_level1,lp_level2,lp_level3,lp_level4)
        
        return (lp_level0,lp_level1,lp_level2,lp_level3,lp_level4)

        
    def MIPITermEnDly(self, dlysetting):   
        
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(0, lane_t_d_term_en = dlysetting)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(1, lane_t_d_term_en = dlysetting)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(2, lane_t_d_term_en = dlysetting)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(3, lane_t_d_term_en = dlysetting)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(4, lane_t_d_term_en = dlysetting)
        
        print ('lane_t_d_term_en0 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_4_lane_t_d_term_en(0))
        print ('lane_t_d_term_en1 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_4_lane_t_d_term_en(1))
        print ('lane_t_d_term_en2 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_4_lane_t_d_term_en(2))
        print ('lane_t_d_term_en3 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_4_lane_t_d_term_en(3))
        print ('lane_t_d_term_en4 is: ', self.m2c.rd_mipi_rx_lane_module_rx_r_sw_force_cfg_4_lane_t_d_term_en(4))

    def MIPIRxTiming(self, term_en = 0, hs_settle = 0, ls_settle = 0, dly_rx_term = 0, init_time = 7, hs_settle_dco = 15):
        '''
        MIPI Timing, 
        
        INPUT:
            term_en:       7bits, enable rx term after LP01 [DCO frequency]
            hs_settle_dco: 7bits, base on dco clock, total Hs_Settle = hs_settle_dco +hs_settle, starts at LP01
            hs_settle:     7bits, prepare to receive hs data, base on 8*UI, starts at LP01
            ls_settle:     7bits, hs skip from HS to LP  [DCO frequency]
            dly_rx_term:   7bits, term_dis timing setting
            init_time:     7bits, Slave initial time. (n+1)*10us
        '''
        
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(0, lane_t_d_term_en = term_en)    #enable rx term after LP01
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(1, lane_t_d_term_en = term_en)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(2, lane_t_d_term_en = term_en)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(3, lane_t_d_term_en = term_en)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_4_fields(4, lane_t_d_term_en = term_en)
                 
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(0, lane_t_hs_settle = hs_settle)  # prepare to receive hs data
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(1, lane_t_hs_settle = hs_settle)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(2, lane_t_hs_settle = hs_settle)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(3, lane_t_hs_settle = hs_settle)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_5_fields(4, lane_t_hs_settle = hs_settle)

        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_7_fields(0, lane_t_hs_settle_dco = hs_settle_dco)  # prepare to receive hs data
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_7_fields(1, lane_t_hs_settle_dco = hs_settle_dco)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_7_fields(2, lane_t_hs_settle_dco = hs_settle_dco)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_7_fields(3, lane_t_hs_settle_dco = hs_settle_dco)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_7_fields(4, lane_t_hs_settle_dco = hs_settle_dco)
        
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(0, ls_settle = ls_settle)  # hs skip from HS to LP   
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(1, ls_settle = ls_settle)
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(2, ls_settle = ls_settle)
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(3, ls_settle = ls_settle)
        self.m2c.wr_mipi_rx_lane_module_rx_r_lane_ls_settle_fields(4, ls_settle = ls_settle)
        
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_6_fields(i=0, lane_t_dly_rx_term_en = dly_rx_term) #
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_6_fields(i=1, lane_t_dly_rx_term_en = dly_rx_term)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_6_fields(i=2, lane_t_dly_rx_term_en = dly_rx_term)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_6_fields(i=3, lane_t_dly_rx_term_en = dly_rx_term)
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_6_fields(i=4, lane_t_dly_rx_term_en = dly_rx_term)
        
        self.m2c.wr_mipi_rx_mipi_dig_rx_slv_init_time_fields(slv_init_time = init_time)
        
        print('[MIPI Timing] term_en/hs_settle_dco/hs_settle/dly_rx_term/ls_settle/init_time= ', term_en, hs_settle_dco, hs_settle, dly_rx_term, ls_settle,init_time)

        
    def MIPIDefaultSetting(self):    
        
        '''
        set dphy setting from register
        set DCO = 52
            
            0: all dphy setting are from the state machine. 
            1: all dphy setting are from the register map
        '''
        
        self.m2c.wr_mipi_rx_mipi_dig_rx_dco_cal0_fields(dco_fword_reg = 52)
        print ('dco_fword_reg is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_dco_cal0_dco_fword_reg())
        
        #setting from register
        self.m2c.wr_mipi_rx_mipi_dig_rx_ctrl1_fields(mipi_reg_sel = 1)
        print('mipi_reg_sel is: ', self.m2c.rd_mipi_rx_mipi_dig_rx_ctrl1_mipi_reg_sel())
    
    def MIPICLKModeSet(self, mode): 
        
        self.m2c.wr_mipi_rx_lane_module_rx_r_continous_clk_mode_fields(con_clk_mode = mode) 
    
    def MIPIULPSStatus(self, lane = None):
        
        '''
        Indicates whether the lane is in ULPS mode(if so, =0)
        
        INPUT:
            lane:    mipi lane module, 0~4
           
        '''
        
        if lane != None: 
            if lane == 0:
                lane0_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_0()
                return lane0_sta
            if lane == 1:
                lane1_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_1()
                return lane1_sta
            if lane == 2:
                lane2_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_2()
                return lane2_sta
            if lane == 3:
                lane3_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_3()
                return lane3_sta
        else:
            
            lane0_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_0()
            lane1_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_1()
            lane2_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_2()
            lane3_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_3()
            lane4_sta = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsclknot()  #clock
            
        print ('ULPS mode(0=ULPS)_lane0/1/2/3/clk:', lane0_sta,lane1_sta,lane2_sta,lane3_sta,lane4_sta)
            
        return (lane0_sta,lane1_sta,lane2_sta,lane3_sta,lane4_sta)

    def MIPIDeskewStatus(self, lane = None):
        
        '''
        RO_DESKEW_STATUS
        
        INPUT:
            lane:   phylane, 0~4
           
        '''
        
        if lane != None: 
            seg0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_deskew_status_step_0_flag(i=lane)
            seg1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_deskew_status_step_1_flag(i=lane)
            seg2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_deskew_status_step_2_flag(i=lane)
            
            Status_chk = self.m2c.rd_mipi_rx_lane_module_rx_ro_status_0_rxskewcal(i=lane)
            Status_seg = (seg2,seg1,seg0)
            
            print('[Deskew status_step] step2/1/0 of lane',lane,'is:', Status_seg)
            print('[Deskew status_chk1] of lane',lane,'is:', Status_chk)

            return Status_seg
        else:
            Status_seg = []
            Status_chk = []
            for lane in range(5):          
                seg2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_deskew_status_step_2_flag(i=lane)
                Status_seg.append(seg2)

                seg1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_deskew_status_step_1_flag(i=lane)
                Status_seg.append(seg1)
                
                seg0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_deskew_status_step_0_flag(i=lane)
                Status_seg.append(seg0)
                
                Status_chk1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_status_0_rxskewcal(i=lane)
                Status_chk.append(Status_chk1)
                                
            print('[Deskew status] step2/1/0 of lane_0/1/2/3/4 is:', Status_seg)
            print('[Deskew status_chk1] of lane',lane,'is:', Status_chk)
            return Status_seg

    def MIPIDeskewPatternCheck(self, lane = None):
        
        '''
        check whether captured 16 x '1'
        
        INPUT:
            lane:   phylane, 0~4 (logic data lane)
           
        '''
        
        if lane != None:
            
            Status_chk = self.m2c.rd_mipi_rx_lane_module_rx_ro_status_0_rxskewcal(i=lane)

            print('[Deskew pattern check] of lane',lane,'is:', Status_chk)

            return Status_chk
        
        else:
            Status_chk = []
            for lane in range(5):          
                
                Status_chk1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_status_0_rxskewcal(i=lane)
                Status_chk.append(Status_chk1)

            print('[Deskew pattern check] of lane',lane,'is:', Status_chk)
            
            return Status_chk
    
    def MIPIDeskewError(self,):
        
        '''
        deskew fifo full status(each lane)
        error in De-skew block
        
        INPUT:
            NA
           
        '''
        
        deskew_fifo_full = self.m2c.rd_csi2_rx_csi_rx_int_main_status0_deskew_fifo_full()
        deskew_err = self.m2c.rd_csi2_rx_csi_rx_int_main_status0_de_skew_err()
          
        print('Deskew Error and each lane(x4) fifo is ', deskew_err, bin(deskew_fifo_full))
        
        return (deskew_err, deskew_fifo_full)       
    
    def MIPIDeskewResult(self, lane : int = None, type = 'data'):

        '''
        deskew calibration result
        
        INPUT:
            lane:   phylane, 0~4
            type:   clock or data lane
           
        '''
        if lane == None:
            
            deskew_0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val_hsrx_dat_dly(i=0)   #as data
            deskew_1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val_hsrx_dat_dly(i=1)   #as data
            deskew_2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val_hsrx_dat_dly(i=2)   #as data
            deskew_3 = self.m2c.rd_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val_hsrx_dat_dly(i=3)   #as data
            deskew_4 = self.m2c.rd_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val_hsrx_dat_dly(i=4)   #as data
            
            deskew_rslt = (deskew_0,deskew_1,deskew_2,deskew_3,deskew_4)
                
            print('[Deskew] result(lane0/1/2/3/4) is:', deskew_rslt)
        
            return deskew_rslt
        
        else:       
            if type == 'clock':
                deskew_rslt = hex(self.m2c.rd_mipi_rx_lane_module_rx_ro_dekskew_final_clk_val_hsrx_clk_dly(i=lane))
            elif type == 'data':
                deskew_rslt = hex(self.m2c.rd_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val_hsrx_dat_dly(i=lane))
            
            print('[Deskew] result of lane',lane,'= ', deskew_rslt)
            
            return deskew_rslt
    
    
    def MIPIDeskewSet(self, lane = 0, en = 0, delay = 0xff):
        
        '''
        set deskew force (delay)
        Set hsrx_clk_delay and hsrx_data_delay by the register
        
        INPUT:
            lane:   phylane, 0~4
            en:    1=enable, 0=disable(force)
            delay: data lane number.
           
        '''
        self.m2c.wr_mipi_rx_lane_module_rx_r_deskew_sw_force_cfg_fields(i=lane, deskew_sw_force_en=en)
        
        enable_val = self.m2c.rd_mipi_rx_lane_module_rx_r_deskew_sw_force_cfg_deskew_sw_force_en(i=lane)

        self.m2c.wr_mipi_rx_lane_module_rx_r_deslew_sw_force_dat_val_fields(i=lane, deskew_force_dat_delay = delay)
        
        delay_rd = hex(self.m2c.rd_mipi_rx_lane_module_rx_r_deslew_sw_force_dat_val_deskew_force_dat_delay(i=lane))
   
        # print('set Deskew of lane',lane,'is:', delay_rd)
        
        return enable_val

    def MIPIDeskewInitValue(self, deskew_init = 0xff):
        
        '''
        set init deskew value
        
        INPUT:
            deskew_init:   init deskew value
           
        '''
        self.m2c.wr_mipi_rx_lane_module_rx_r_deslew_dat_dly_init_cfg_fields(deskew_dat_dly_init_val=deskew_init)
        deskew_init = self.m2c.rd_mipi_rx_lane_module_rx_r_deslew_dat_dly_init_cfg()
        
        print('set Init Deskew value = ',deskew_init)
        
        return deskew_init

    def MIPICalibOffset(self, lane = 0, value = 0x7f):
        
        '''
        offset calibration setting
        
        INPUT:
            lane:   phylane, 0~4
            value:    8bits offset value
           
        '''
        self.m2c.wr_mipi_rx_mipi_dig_rx_offset_cal1_fields(i = lane, offcal_prog_reg = value)
        
        offset_val  = self.m2c.rd_mipi_rx_mipi_dig_rx_offset_cal1_offcal_prog_reg(i = lane)

        return offset_val
    
    def MIPICalibOffsetManualEn(self, en = 1):
        
        '''
        offset calibration test, manual mode enable
        
        INPUT:
            lane:   phylane, 0~4
            en:    1 = enable, route out calib AMP out for test
            hsrx_mode_reg:    1 = connect AMP input (mipi input) to internal ground, 0 = to external PAD 
           
        '''       

        self.m2c.wr_mipi_rx_mipi_dig_rx_offset_cal0_fields(i = 0, offcal_obs_en_reg = en, hsrx_mode_reg = 1) 
        self.m2c.wr_mipi_rx_mipi_dig_rx_offset_cal0_fields(i = 1, offcal_obs_en_reg = en, hsrx_mode_reg = 1) 
        self.m2c.wr_mipi_rx_mipi_dig_rx_offset_cal0_fields(i = 2, offcal_obs_en_reg = en, hsrx_mode_reg = 1) 
        self.m2c.wr_mipi_rx_mipi_dig_rx_offset_cal0_fields(i = 3, offcal_obs_en_reg = en, hsrx_mode_reg = 1) 
        self.m2c.wr_mipi_rx_mipi_dig_rx_offset_cal0_fields(i = 4, offcal_obs_en_reg = en, hsrx_mode_reg = 1)   #hsrx_mode_reg should not '01' when scan code


    def MIPICalibOffsetValue(self, lane = None):
        
        '''
        offset calibration test, manual mode enable
        
        INPUT:
            lane:   phylane, 0~4
        Output:
            offset value after calibration, available after R5
           
        '''       
        
        if lane == None:
            offset0 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal(i = 0) 
            offset1 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal(i = 1) 
            offset2 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal(i = 2) 
            offset3 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal(i = 3) 
            offset4 = self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal(i = 4) 
        
            print('offset of lane0~4 is:', offset0,offset1,offset2,offset3,offset4)
            return (offset0,offset1,offset2,offset3,offset4)
        else:
            offset = hex(self.m2c.rd_mipi_rx_lane_module_rx_ro_offset_cal(i = lane))
            
            print('offset of lane', lane,' is:',offset)
            return offset
                         
    def MIPIStateManualCtrl(self, lane = 0, state = 6):
        
        '''
        manual 
            R2/R3/R5, calibration fail, because of wrong setting of sw_set_lane_hsrx_en=0 and sw_set_lane_hsrx_pon=0 during calibration
        
        INPUT:
            sw_set_lane_hsrx_term_en:   enable
            sw_set_lane_hsrx_mode:    2bits
            sw_set_lane_hsrx_pon:     power on
            sw_set_lane_hsrx_s2p_en
            sw_set_lane_hsrx_term_en
            sw_set_lane_lprx_pon    2bits
            sw_set_lane_lprx_ulp_pon    2bits
           
        '''     
        self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_0_fields(i=lane, sw_force_ctrl_en = 0xff) #enable 'force enable'
          
        if state == 6:
            self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_1_fields(i=lane, sw_set_lane_hsrx_term_en=0, sw_set_lane_hsrx_s2p_en = 0, sw_set_lane_hsrx_en=1)
            self.m2c.wr_mipi_rx_lane_module_rx_r_sw_force_cfg_2_fields(i=lane, sw_set_lane_hsrx_pon = 1, sw_set_lane_hsrx_mode = 1, sw_set_lane_lprx_pon=0,sw_set_lane_lprx_ulp_pon=0)    

    def MIPIPHYStatus(self):
        
        '''
        CSI_RX_PHY_RX
        
        OUTPUT:
            clock_active: Indicates that D-PHY clock lane is actively receiving a DDR clock
            clock_IN_ULPS: Active Low. This signal indicates that D-PHY Clock Lane module has entered the Ultra Low Power state
            D3_IN_ULPS:    Lane module 3 has entered the Ultra Low Power mode
            D2_IN_ULPS:    Lane module 2 has entered the Ultra Low Power mode 
            D1_IN_ULPS:    Lane module 1 has entered the Ultra Low Power mode
            D0_IN_ULPS:    Lane module 0 has entered the Ultra Low Power mode
                        
        '''

        clock_active = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxclkactivehs()
        clock_IN_ULPS = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsclknot()
        D3_IN_ULPS = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_3()
        D2_IN_ULPS = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_2()
        D1_IN_ULPS = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_1()
        D0_IN_ULPS = self.m2c.rd_csi2_rx_csi_rx_phy_rx_phy_rxulpsesc_0()
          
        print('[MIPI PHY Status] clock active =',clock_active)
        print('[MIPI PHY Status] clock/D0/D1/D2/D3 in ULPS =',clock_IN_ULPS,D0_IN_ULPS,D1_IN_ULPS,D2_IN_ULPS,D3_IN_ULPS)
        
        return (clock_active,clock_IN_ULPS,D0_IN_ULPS,D1_IN_ULPS,D2_IN_ULPS,D3_IN_ULPS)
 
    def MIPILaneStatus(self):  
        '''
        lane status
        
        OUTPUT:
            Deskew_done:    1=It means that the skew calibration is done
            STOP:    1=It means that ls state is the STOP state
            SYNC_mactch:    1=sync pattern match indicator
        '''
        
        Deskew_done0 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=0) 
        Deskew_done1 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=1) 
        Deskew_done2 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=2) 
        Deskew_done3 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=3) 
        Deskew_done4 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=4) 
        
        STOP0 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=0) 
        STOP1 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=1) 
        STOP2 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=2) 
        STOP3 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=3) 
        STOP4 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=4) 
        
        SYNC_mactch0 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=0) 
        SYNC_mactch1 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=1) 
        SYNC_mactch2 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=2) 
        SYNC_mactch3 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=3) 
        SYNC_mactch4 = self.m2c.rd_mipi_rx_lane_module_rx_r_dbg_ctrl_0_clear_rxskewcal_flag(i=4)         
        
        print('[CSI Lane Status] DeskewDone (0~4) :',Deskew_done0,Deskew_done1,Deskew_done2,Deskew_done3,Deskew_done4)
        print('[CSI Lane Status] inSTOP (0~4) :',STOP0,STOP1,STOP2,STOP3,STOP4)
        print('[CSI Lane Status] SYNC (0~4) :',SYNC_mactch0,SYNC_mactch1,SYNC_mactch2,SYNC_mactch3,SYNC_mactch4)
                
        return (Deskew_done0,Deskew_done1,Deskew_done2,Deskew_done3,Deskew_done4,STOP0,STOP1,STOP2,STOP3,STOP4,SYNC_mactch0,SYNC_mactch1,SYNC_mactch2,SYNC_mactch3,SYNC_mactch4)
   

    def MIPIGetVcid(self):
        
        '''
        read vcid(5bits)
        
        INPUT:
           
        '''

        vcid = self.m2c.rd_csi2_rx_debug_status2_vcid()
          
        print('vcid = ',vcid)
        
        return vcid
    
    def MIPIDataInfo(self):
        
        '''
        read vcid(5bits)
        
        OUTPUT:
            vcid:    5bits
           
        '''

        vcid = self.m2c.rd_csi2_rx_debug_status2_vcid()
        data_type = hex(self.m2c.rd_csi2_rx_debug_status4_data_type())
        wc_l = self.m2c.rd_csi2_rx_debug_status5_word_count(i=0)
        wc_h = self.m2c.rd_csi2_rx_debug_status5_word_count(i=1)
        
        word_count = wc_h*256 + wc_l
          
        print('[Data Info] VCID/DT/WC = ',vcid,data_type,word_count)
        
        return (vcid,data_type,word_count)
                                
    def MIPILaneErrCheck(self, lane = None):
        
        '''
        check lane error on MIPI RX
        
        INPUT:
            lane:   phylane, 0~4
           
        '''
        
        if lane == None:
            for i in range(5):
                err_lane = self.m2c.rd_mipi_rx_lane_module_rx_ro_error_indicator_0(i)
                
                print('[Lane Error] ESC_timeout/EOT_timeout/LS_FalseCtrl/Sync/ESC/EOT/SOT of lane',i,'is:', err_lane)
        else:
            err_lane = self.m2c.rd_mipi_rx_lane_module_rx_ro_error_indicator_0(i = lane)
            
            print('[Lane Error] ESC_timeout/EOT_timeout/LS_FalseCtrl/ESC/EOT/SOT of lane',lane,'is:', err_lane)
        # err_lane = bin(err_lane)[4:].zfill(6)
                
        return err_lane
        

    def MIPIDataErrCheck(self, vcid = None):
        
        '''
        check ECC and CRC error on MIPI DATA
        
        INPUT:
            vcid:    virtual channel id, 0~31
           
        '''
        
        err_ecc = self.m2c.rd_csi2_rx_csi_rx_int_hdr_err_fatal_err_ecc_double()   
        
        if vcid != None:
            if vcid == 0:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc0()
            if vcid == 1:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc1()
            if vcid == 2:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc2()
            if vcid == 3:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc3()
            if vcid == 4:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc4()
            if vcid == 5:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc5()
            if vcid == 6:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc6()
            if vcid == 7:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_err_crc_vc7()
                
            if vcid == 8:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc8()
            if vcid == 9:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc9()
            if vcid == 10:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc10()
            if vcid == 11:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc11()
            if vcid == 12:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc12()          
            if vcid == 13:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc13()
            if vcid == 14:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc14()
            if vcid == 15:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_err_crc_vc15()
                
            if vcid == 16:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc16()
            if vcid == 17:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc17()
            if vcid == 18:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc18()
            if vcid == 19:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc19()
            if vcid == 20:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc20()
            if vcid == 21:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc21()
            if vcid == 22:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc22()
            if vcid == 23:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_err_crc_vc23()
                
            if vcid == 24:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc24()             
            if vcid == 25:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc25()
            if vcid == 26:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc26()
            if vcid == 27:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc27()
            if vcid == 28:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc28()
            if vcid == 29:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc29()
            if vcid == 30:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc30()
            if vcid == 31:
                err_crc = self.m2c.rd_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_err_crc_vc31()
               
            print('[DATA Error] ECC(header) error is:',err_ecc)
            print('[DATA Error] CRC(payload) error of vcid',vcid,'is:', err_crc)
            return (err_ecc, err_crc) 
        
        else:
            print('[DATA Error] ECC(Header):', err_ecc)
            return (err_ecc) 
             
        
        
    def EfuseCRCGen(self, shadow_data):
        import copy
        NUM_WORDS = 12
        crc_gen = [0 for y in range(16)]
        c = [1 for z in range(16)] #'1111111111111111'# [1 for z in range(16)]
        
        wor = ['','','','']
        
        for i in range(0, NUM_WORDS-1):
            
            for j in range(4):
                d = ''
                dd = str(bin(shadow_data[i*4+j])[2:])
                da  = dd.zfill(8)
                for m in range(8):
                    d += da[7-m]
                crc_gen[0]  = int(d[7]) ^ int(d[6]) ^ int(d[3]) ^ int(d[2]) ^ int(d[0] ) ^ int(c[8] ) ^ int(c[10]) ^ int(c[11]) ^ int(c[14]) ^ int(c[15]) 
                crc_gen[1]  = int(d[6]) ^ int(d[4]) ^ int(d[2]) ^ int(d[1]) ^ int(d[0] ) ^ int(c[8] ) ^ int(c[9] ) ^ int(c[10]) ^ int(c[12]) ^ int(c[14]) 
                crc_gen[2]  = int(d[7]) ^ int(d[5]) ^ int(d[3]) ^ int(d[2]) ^ int(d[1] ) ^ int(c[9] ) ^ int(c[10]) ^ int(c[11]) ^ int(c[13]) ^ int(c[15]) 
                crc_gen[3]  = int(d[7]) ^ int(d[4]) ^ int(d[0]) ^ int(c[8]) ^ int(c[12]) ^ int(c[15])            
                crc_gen[4]  = int(d[7]) ^ int(d[6]) ^ int(d[5]) ^ int(d[3]) ^ int(d[2] ) ^ int(d[1] ) ^ int(d[0] ) ^ int(c[8] ) ^ int(c[9] ) ^ int(c[10]) ^ int(c[11]) ^ int(c[13]) ^ int(c[14]) ^ int(c[15])
                crc_gen[5]  = int(d[7]) ^ int(d[6]) ^ int(d[4]) ^ int(d[3]) ^ int(d[2] ) ^ int(d[1] ) ^ int(c[9] ) ^ int(c[10]) ^ int(c[11]) ^ int(c[12]) ^ int(c[14]) ^ int(c[15]) 
                crc_gen[6]  = int(d[6]) ^ int(d[5]) ^ int(d[4]) ^ int(d[0]) ^ int(c[8] ) ^ int(c[12]) ^ int(c[13]) ^ int(c[14])    
                crc_gen[7]  = int(d[7]) ^ int(d[6]) ^ int(d[5]) ^ int(d[1]) ^ int(c[9] ) ^ int(c[13]) ^ int(c[14]) ^ int(c[15])    
                crc_gen[8]  = int(d[3]) ^ int(d[0]) ^ int(c[0]) ^ int(c[8]) ^ int(c[11])                         
                crc_gen[9]  = int(d[4]) ^ int(d[1]) ^ int(c[1]) ^ int(c[9]) ^ int(c[12])                         
                crc_gen[10] = int(d[7]) ^ int(d[6]) ^ int(d[5]) ^ int(d[3]) ^ int(d[0] ) ^ int(c[2] ) ^ int(c[8] ) ^ int(c[11]) ^ int(c[13]) ^ int(c[14]) ^ int(c[15])
                crc_gen[11] = int(d[7]) ^ int(d[6]) ^ int(d[4]) ^ int(d[1]) ^ int(c[3] ) ^ int(c[9] ) ^ int(c[12]) ^ int(c[14]) ^ int(c[15])
                crc_gen[12] = int(d[6]) ^ int(d[5]) ^ int(d[3]) ^ int(d[0]) ^ int(c[4] ) ^ int(c[8] ) ^ int(c[11]) ^ int(c[13]) ^ int(c[14])
                crc_gen[13] = int(d[4]) ^ int(d[3]) ^ int(d[2]) ^ int(d[1]) ^ int(d[0] ) ^ int(c[5] ) ^ int(c[8] ) ^ int(c[9] ) ^ int(c[10]) ^ int(c[11]) ^ int(c[12]) 
                crc_gen[14] = int(d[7]) ^ int(d[6]) ^ int(d[5]) ^ int(d[4]) ^ int(d[1] ) ^ int(d[0] ) ^ int(c[6] ) ^ int(c[8] ) ^ int(c[9] ) ^ int(c[12]) ^ int(c[13]) ^ int(c[14]) ^ int(c[15])
                crc_gen[15] = int(d[7]) ^ int(d[6]) ^ int(d[5]) ^ int(d[2]) ^ int(d[1] ) ^ int(c[7] ) ^ int(c[9] ) ^ int(c[10]) ^ int(c[13]) ^ int(c[14]) ^ int(c[15]) 
                c = copy.copy(crc_gen)
                print('shadow:',i*4+j, 'crc_gen: ', crc_gen)
        for x in range(7,-1,-1):
            wor[0] += str(crc_gen[x])
            wor[1] += str(crc_gen[x+8])
        
        print('wor[0]: ', wor[0], 'wor[1]: ', wor[1])
        print('ecc_gen: ', crc_gen, ', dec value byte0: ', int(wor[0],2), ', dec value byte1: ', int(wor[1],2))
        return (int(wor[0], 2), int(wor[1], 2), 0, 0)
    
    def EfuseCRCGen_int(self, shadow_data):
        NUM_WORDS = 12
        crc_gen = [0 for i in range(16)]
        c = [1 for i in range(16)]
        
        for i in range(0, NUM_WORDS-1):
            for j in range(4):
                d = shadow_data[i][j*8:j*8+8]
                crc_gen[0]  = d[7]^d[6]^d[3]^d[2] ^d[0] ^c[8] ^c[10]^c[11]^c[14]^c[15]
                crc_gen[1]  = d[6]^d[4]^d[2]^d[1] ^d[0] ^c[8] ^c[9] ^c[10]^c[12]^c[14]
                crc_gen[2]  = d[7]^d[5]^d[3]^d[2] ^d[1] ^c[9] ^c[10]^c[11]^c[13]^c[15]
                crc_gen[3]  = d[7]^d[4]^d[0]^c[8] ^c[12]^c[15]
                crc_gen[4]  = d[7]^d[6]^d[5]^d[3] ^d[2] ^d[1] ^d[0] ^c[8] ^c[9] ^c[10]^c[11]^c[13]^c[14]^c[15]
                crc_gen[5]  = d[7]^d[6]^d[4]^d[3] ^d[2] ^d[1] ^c[9] ^c[10]^c[11]^c[12]^c[14]^c[15]
                crc_gen[6]  = d[6]^d[5]^d[4]^d[0] ^c[8] ^c[12]^c[13]^c[14]
                crc_gen[7]  = d[7]^d[6]^d[5]^d[1] ^c[9] ^c[13]^c[14]^c[15]
                crc_gen[8]  = d[3]^d[0]^c[0]^c[8] ^c[11]
                crc_gen[9]  = d[4]^d[1]^c[1]^c[9] ^c[12]
                crc_gen[10] = d[7]^d[6]^d[5]^d[3] ^d[0] ^c[2] ^c[8] ^c[11]^c[13]^c[14]^c[15]
                crc_gen[11] = d[7]^d[6]^d[4]^d[1] ^c[3] ^c[9] ^c[12]^c[14]^c[15]
                crc_gen[12] = d[6]^d[5]^d[3]^d[0] ^c[4] ^c[8] ^c[11]^c[13]^c[14]
                crc_gen[13] = d[4]^d[3]^d[2]^d[1] ^d[0] ^c[5] ^c[8] ^c[9] ^c[10]^c[11]^c[12]
                crc_gen[14] = d[7]^d[6]^d[5]^d[4] ^d[1] ^d[0] ^c[6] ^c[8] ^c[9] ^c[12]^c[13]^c[14]^c[15]
                crc_gen[15] = d[7]^d[6]^d[5]^d[2] ^d[1] ^c[7] ^c[9] ^c[10]^c[13]^c[14]^c[15]
                c = crc_gen
        return c
    
    def EfuseECCGen(self, data):
        d = data
        wor = '0b'
        ecc_gen = [0 for i in range(8)]
        ecc_gen[0] = int(d[0]) ^ int(d[1] ) ^ int(d[2] ) ^ int(d[3] ) ^ int(d[4] ) ^ int(d[5] ) ^ int(d[6] ) ^ int(d[7] ) ^ int(d[14]) ^ int(d[19]) ^ int(d[22]) ^ int(d[24]) ^ int(d[30]) ^ int(d[31]) 
        ecc_gen[1] = int(d[4]) ^ int(d[7] ) ^ int(d[8] ) ^ int(d[9] ) ^ int(d[10]) ^ int(d[11]) ^ int(d[12]) ^ int(d[13]) ^ int(d[14]) ^ int(d[15]) ^ int(d[18]) ^ int(d[21]) ^ int(d[24]) ^ int(d[29]) 
        ecc_gen[2] = int(d[3]) ^ int(d[11]) ^ int(d[16]) ^ int(d[17]) ^ int(d[18]) ^ int(d[19]) ^ int(d[20]) ^ int(d[21]) ^ int(d[22]) ^ int(d[23]) ^ int(d[26]) ^ int(d[27]) ^ int(d[29]) ^ int(d[30]) 
        ecc_gen[3] = int(d[2]) ^ int(d[6] ) ^ int(d[10]) ^ int(d[13]) ^ int(d[15]) ^ int(d[16]) ^ int(d[24]) ^ int(d[25]) ^ int(d[26]) ^ int(d[27]) ^ int(d[28]) ^ int(d[29]) ^ int(d[30]) ^ int(d[31]) 
        ecc_gen[4] = int(d[1]) ^ int(d[2] ) ^ int(d[5] ) ^ int(d[7] ) ^ int(d[9] ) ^ int(d[12]) ^ int(d[15]) ^ int(d[20]) ^ int(d[21]) ^ int(d[22]) ^ int(d[23]) ^ int(d[25]) ^ int(d[26]) ^ int(d[28]) 
        ecc_gen[5] = int(d[0]) ^ int(d[5] ) ^ int(d[6] ) ^ int(d[8] ) ^ int(d[12]) ^ int(d[13]) ^ int(d[14]) ^ int(d[16]) ^ int(d[17]) ^ int(d[18]) ^ int(d[19]) ^ int(d[20]) ^ int(d[28]) 
        ecc_gen[6] = int(d[0]) ^ int(d[1] ) ^ int(d[3] ) ^ int(d[4] ) ^ int(d[8] ) ^ int(d[9] ) ^ int(d[10]) ^ int(d[11]) ^ int(d[17]) ^ int(d[23]) ^ int(d[25]) ^ int(d[27]) ^ int(d[31]) 
        
        for i in range(7,-1, -1):
            wor += str(ecc_gen[i])
        print('ecc_gen: ', ecc_gen, ', dec value: ', int(wor,2))
        return int(wor, 2)
    
    def EfuseECCGen_int(self, data):
        d = data
        wor = '0b'
        ecc_gen = [0 for i in range(8)]
        ecc_gen[0] = d[0]^d[1] ^d[2] ^d[3] ^d[4] ^d[5] ^d[6] ^d[7] ^d[14]^d[19]^d[22]^d[24]^d[30]^d[31]
        ecc_gen[1] = d[4]^d[7] ^d[8] ^d[9] ^d[10]^d[11]^d[12]^d[13]^d[14]^d[15]^d[18]^d[21]^d[24]^d[29]
        ecc_gen[2] = d[3]^d[11]^d[16]^d[17]^d[18]^d[19]^d[20]^d[21]^d[22]^d[23]^d[26]^d[27]^d[29]^d[30]
        ecc_gen[3] = d[2]^d[6] ^d[10]^d[13]^d[15]^d[16]^d[24]^d[25]^d[26]^d[27]^d[28]^d[29]^d[30]^d[31]
        ecc_gen[4] = d[1]^d[2] ^d[5] ^d[7] ^d[9] ^d[12]^d[15]^d[20]^d[21]^d[22]^d[23]^d[25]^d[26]^d[28]
        ecc_gen[5] = d[0]^d[5] ^d[6] ^d[8] ^d[12]^d[13]^d[14]^d[16]^d[17]^d[18]^d[19]^d[20]^d[28]
        ecc_gen[6] = d[0]^d[1] ^d[3] ^d[4] ^d[8] ^d[9] ^d[10]^d[11]^d[17]^d[23]^d[25]^d[27]^d[31]
        for i in range(8):
            wor += ecc_gen[i]
        print('ecc_gen: ', ecc_gen, ', dec value: ', int(wor,2))
        return int(wor, 2)
    
    def EfuseShadowRecord(self):
        for i in list(range(128)):
            shadow = self.m2c.rd_efuse_shadow_data_shadow_data(i)
            print('shadow: ', i, 'value: ', shadow)
            
        return [self.m2c.rd_efuse_shadow_data_shadow_data(x) for x in list(range(128))]
            
    def EfuseTrimPgmUnlockSet(self, d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,dev_id=0,silicon_vid=0,kswing_done_12g=0, bcrx_lpfc_dac_12g=0,kswing_done_6g=0, bcrx_lpfc_dac_6g=0,kswing_done_3g=0, bcrx_lpfc_dac_3g=0,adc_offset=0,tx_bit_order_8b10b=0,rx_bit_order_8b10b=0,bist_bypass=0):
        
        write_shadow_data = [0 for i in range(512)] 
        a0=(d2a_trim_bg_reg + (d2a_trim_bg_ts_vr_sel<<3)+((dev_id&7)<<5))&0xFF
        a1=((dev_id>>3)+((silicon_vid&0x7)<<5))&0xFF
        a2=((silicon_vid>>3)+((kswing_done_12g&0x07)<<5))&0xFF
        a3=((kswing_done_12g>>3)+((bcrx_lpfc_dac_12g&0x1f)<<3))&0xff
        a4=((bcrx_lpfc_dac_12g>>5)+((kswing_done_6g&0x1f)<<3))&0xFF
        a5=((kswing_done_6g>>5)+((bcrx_lpfc_dac_6g&0x7F)<<1))&0xFF
        a6=((bcrx_lpfc_dac_6g>>7)+(kswing_done_3g<<1)+((bcrx_lpfc_dac_3g&0x1)<<7))&0xFF
        a7=((bcrx_lpfc_dac_3g>>1)+((adc_offset&0x1)<<7))&0xFF
        a8=((adc_offset>>1)+(tx_bit_order_8b10b<<6)+(rx_bit_order_8b10b<<7))&0xFF
        a9=bist_bypass
        NUM_WORDS = 12
        print('rd_efuse_int_st is: ', self.m2c.rd_efuse_int_st())
        
        # enable interrupt flag
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1)   
    
        for word in range(11): # max 47 raw words
            ecc_word_raw = ''
            for byte in range(4):  # 4 bytes for each word
                if ((word*4+byte)==0):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a0)  # write raw data
                    write_shadow_data[word*4+byte] = a0
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a0)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a0
                    tran = str(bin(a0)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a0:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==1):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a1)  # write raw data
                    write_shadow_data[word*4+byte] = a1
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a1)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a1
                    tran = str(bin(a1)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a1:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==2):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a2)  # write raw data
                    write_shadow_data[word*4+byte] = a2
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a2)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a2
                    tran = str(bin(a2)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a2:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==3):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a3)  # write raw data
                    write_shadow_data[word*4+byte] = a3
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a3)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a3
                    tran = str(bin(a3)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a3:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==4):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a4)  # write raw data
                    write_shadow_data[word*4+byte] = a4
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a4)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a4
                    tran = str(bin(a4)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a4:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==5):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a5)  # write raw data
                    write_shadow_data[word*4+byte] = a5
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a5)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a5
                    tran = str(bin(a5)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a5:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==6):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a6)  # write raw data
                    write_shadow_data[word*4+byte] = a6
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a6)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a6
                    
                    tran = str(bin(a6)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a6:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==7):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a7)  # write raw data
                    write_shadow_data[word*4+byte] = a7
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a7)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a7
                    tran = str(bin(a7)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a7:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==8):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a8)  # write raw data
                    write_shadow_data[word*4+byte] = a8
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a8)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a8
                    
                    tran = str(bin(a8)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a8:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==9):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a9)  # write raw data
                    write_shadow_data[word*4+byte] = a9
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a9)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a9
                    
                    tran = str(bin(a9)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a9:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                else:
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=word*4+byte)  # write raw data
                    write_shadow_data[word*4+byte] = word*4+byte
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=word*4+byte)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = word*4+byte
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != word*4+byte:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                    tran = str(bin(word*4+byte)[2:])
                    
                cd = tran.zfill(8)
                print('cd: ', cd)
                ne = ''
                for mm in range(8):
                    ne += cd[7-mm]
                ecc_word_raw += ne
            print('ecc_word(str) is: ', ecc_word_raw)
            
            ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
            print('ECC shadow: ', word+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
            
            write_shadow_data[word+8*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
            rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+8*NUM_WORDS)
            print('read raw ecc: shadow: ', word+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
            
            write_shadow_data[word+9*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
            rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+9*NUM_WORDS)
            print('read double ecc: shadow: ', word+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)
            
            if ecc_shadow_expect != rd_raw_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to ecc_shadow_expect')
            elif rd_raw_ecc_shadow_data != rd_double_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to rd_double_ecc_shadow_data')
        
        print('write_shadow_data is: ', write_shadow_data)
        
        crc_shadow_expect = self.EfuseCRCGen(write_shadow_data)
        print('crc_shadow_expect: ', crc_shadow_expect)
        
        # write CRC & calculate ECC for word 47
        ecc_word_raw = ''
        for byte in range(4):
            write_shadow_data[11*4+byte] = crc_shadow_expect[byte]
            self.m2c.wr_efuse_shadow_data_fields(i=11*4+byte, shadow_data=crc_shadow_expect[byte])  # write CRC data
            rd_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(11*4+byte)
            print('read crc: shadow: ', 11*4+byte, ', value: ', rd_crc_shadow_data)
            
            self.m2c.wr_efuse_shadow_data_fields(i=(11+NUM_WORDS)*4+byte, shadow_data=crc_shadow_expect[byte])  # write double data
            write_shadow_data[(11+NUM_WORDS)*4+byte] = crc_shadow_expect[byte]
            
            rd_double_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((11+NUM_WORDS)*4+byte)
            print('read double: shadow: ', (11+NUM_WORDS)*4+byte, ', value: ', rd_double_crc_shadow_data)
            
            if rd_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_crc_shadow_data misalign to ecc_shadow_expect')
            
            if rd_double_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_double_crc_shadow_data misalign to ecc_shadow_expect')
            
            tran = str(bin(crc_shadow_expect[byte])[2:])
            cd = tran.zfill(8)
            print('cd: ', cd)
            ne = ''
            for mm in range(8):
                ne += cd[7-mm]
            ecc_word_raw += ne
        print('ecc_word(str) is: ', ecc_word_raw)
        
        ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
        print('ECC shadow: ', 11+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
        
        write_shadow_data[11+8*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
        rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+8*NUM_WORDS)
        print('read raw ecc: shadow: ', 11+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
        
        write_shadow_data[11+9*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
        rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+9*NUM_WORDS)
        print('read double ecc: shadow: ', 11+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)   
        
        write_shadow_data[31*4] = 0
        self.m2c.wr_efuse_shadow_data_fields(i=31*4, shadow_data=0)  # 0x38 to lock out, whether to lock out
        
        print('write_shadow_data is: ', write_shadow_data)
        
        pgm = True  # enable program?
        if pgm==True:
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage 
            
            # raw fpgm
            self.m2c.wr_efuse_pgm_start(0)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46)  # 46
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            # time.sleep(0.1)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            # double fpgm
            self.m2c.wr_efuse_pgm_start(0+48)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46+48)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # CRC Ppgm
            self.m2c.wr_efuse_partial_addr(0x47)  # Partial read/program address, in word, A[6:0].
            for i in range(4):
                self.m2c.wr_efuse_ppgm_data(i, crc_shadow_expect[i])
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # ECC and remaining section
            self.m2c.wr_efuse_pgm_start(NUM_WORDS*2-1)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(127)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            # lock key efuse
            self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage 
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.
    def EfuseTrimPgmlockSet(self, d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,dev_id=0,silicon_vid=0,kswing_done_12g=0, bcrx_lpfc_dac_12g=0,kswing_done_6g=0, bcrx_lpfc_dac_6g=0,kswing_done_3g=0, bcrx_lpfc_dac_3g=0,adc_offset=0,tx_bit_order_8b10b=0,rx_bit_order_8b10b=0,bist_bypass=0):
        write_shadow_data = [0 for i in range(512)] 
        shadow_data = [0 for i in range(512)] 
        shadow_data[0]=(d2a_trim_bg_reg + (d2a_trim_bg_ts_vr_sel<<3)+((dev_id&7)<<5))&0xFF
        shadow_data[1]=((dev_id>>3)+((silicon_vid&0x7)<<5))&0xFF
        shadow_data[2]=((silicon_vid>>3)+((kswing_done_12g&0x07)<<5))&0xFF
        shadow_data[3]=((kswing_done_12g>>3)+((bcrx_lpfc_dac_12g&0x1f)<<3))&0xff
        shadow_data[4]=((bcrx_lpfc_dac_12g>>5)+((kswing_done_6g&0x1f)<<3))&0xFF
        shadow_data[5]=((kswing_done_6g>>5)+((bcrx_lpfc_dac_6g&0x7F)<<1))&0xFF
        shadow_data[6]=((bcrx_lpfc_dac_6g>>7)+(kswing_done_3g<<1)+((bcrx_lpfc_dac_3g&0x1)<<7))&0xFF
        shadow_data[7]=((bcrx_lpfc_dac_3g>>1)+((adc_offset&0x1)<<7))&0xFF
        shadow_data[8]=((adc_offset>>1)+(tx_bit_order_8b10b<<6)+(rx_bit_order_8b10b<<7))&0xFF
        shadow_data[9]=bist_bypass
        
        for i in range(10,512,1):
            shadow_data[i]=0
        
        NUM_WORDS = 48
        print('rd_efuse_int_st is: ', self.m2c.rd_efuse_int_st())
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1) 
        
        for word in range(47): # max 47 raw words
            ecc_word_raw = ''
            for byte in range(4):  # 4 bytes for each word
                print('write raw expected: shadow: ', word*4+byte, ', value: ', word*4+byte)
                self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=shadow_data[word*4+byte])  # write raw data
                write_shadow_data[word*4+byte] = shadow_data[word*4+byte]
                
                print('write double expected: shadow: ', shadow_data[(word+NUM_WORDS)*4+byte], ', value: ', word*4+byte)
                self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=shadow_data[word*4+byte])  # write double data
                write_shadow_data[(word+NUM_WORDS)*4+byte] = shadow_data[word*4+byte]
                
                rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                if rd_raw_shadow_data != shadow_data[word*4+byte]:
                    raise('rd_raw_shadow_data misalign to write raw data')
                if rd_raw_shadow_data != rd_double_shadow_data:
                    raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                tran = str(bin(word*4+byte)[2:])
                cd = tran.zfill(8)
                print('cd: ', cd)
                ne = ''
                for mm in range(8):
                    ne += cd[7-mm]
                ecc_word_raw += ne
            print('ecc_word(str) is: ', ecc_word_raw)
            
            ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
            print('ECC shadow: ', word+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
            
            write_shadow_data[word+8*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
            rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+8*NUM_WORDS)
            print('read raw ecc: shadow: ', word+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
            
            write_shadow_data[word+9*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
            rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+9*NUM_WORDS)
            print('read double ecc: shadow: ', word+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)
            
            if ecc_shadow_expect != rd_raw_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to ecc_shadow_expect')
            elif rd_raw_ecc_shadow_data != rd_double_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to rd_double_ecc_shadow_data')
        
        print('write_shadow_data is: ', write_shadow_data)
        
        crc_shadow_expect = self.EfuseCRCGen(write_shadow_data)
        print('crc_shadow_expect: ', crc_shadow_expect)
        
        # write CRC & calculate ECC for word 47
        ecc_word_raw = ''
        for byte in range(4):
            write_shadow_data[47*4+byte] = crc_shadow_expect[byte]
            self.m2c.wr_efuse_shadow_data_fields(i=47*4+byte, shadow_data=crc_shadow_expect[byte])  # write CRC data
            rd_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(47*4+byte)
            print('read crc: shadow: ', 47*4+byte, ', value: ', rd_crc_shadow_data)
            
            self.m2c.wr_efuse_shadow_data_fields(i=(47+NUM_WORDS)*4+byte, shadow_data=crc_shadow_expect[byte])  # write double data
            write_shadow_data[(47+NUM_WORDS)*4+byte] = crc_shadow_expect[byte]
            
            rd_double_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((47+NUM_WORDS)*4+byte)
            print('read double: shadow: ', (47+NUM_WORDS)*4+byte, ', value: ', rd_double_crc_shadow_data)
            
            if rd_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_crc_shadow_data misalign to ecc_shadow_expect')
            
            if rd_double_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_double_crc_shadow_data misalign to ecc_shadow_expect')
            
            tran = str(bin(crc_shadow_expect[byte])[2:])
            cd = tran.zfill(8)
            print('cd: ', cd)
            ne = ''
            for mm in range(8):
                ne += cd[7-mm]
            ecc_word_raw += ne
        print('ecc_word(str) is: ', ecc_word_raw)
        
        ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
        print('ECC shadow: ', 47+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
        
        write_shadow_data[47+8*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=47+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
        rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=47+8*NUM_WORDS)
        print('read raw ecc: shadow: ', 47+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
        
        write_shadow_data[47+9*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=47+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
        rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=47+9*NUM_WORDS)
        print('read double ecc: shadow: ', 47+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)   
        
        write_shadow_data[127*4] = 0x38
        self.m2c.wr_efuse_shadow_data_fields(i=127*4, shadow_data=0x38)  # 0x38 to lock out, whether to lock out
        
        print('write_shadow_data is: ', write_shadow_data)
        
        pgm = False  # enable program?
        if pgm==True:
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage 
            
            # raw fpgm
            self.m2c.wr_efuse_pgm_start(0)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46)  # 46
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            # time.sleep(0.1)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            # double fpgm
            self.m2c.wr_efuse_pgm_start(0+48)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46+48)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # CRC Ppgm
            self.m2c.wr_efuse_partial_addr(0x47)  # Partial read/program address, in word, A[6:0].
            for i in range(4):
                self.m2c.wr_efuse_ppgm_data(i, crc_shadow_expect[i])
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # ECC and remaining section
            self.m2c.wr_efuse_pgm_start(NUM_WORDS*2-1)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(127)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            # lock key efuse
            self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage 
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.
    
                        
    def EfusePgmVerify(self,):
        
        write_shadow_data = [0 for i in range(512)] 
        
        NUM_WORDS = 48
        print('rd_efuse_int_st is: ', self.m2c.rd_efuse_int_st())
        
        # enable interrupt flag
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1)   
    
        for word in range(47): # max 47 raw words
            ecc_word_raw = ''
            for byte in range(4):  # 4 bytes for each word
                print('write raw expected: shadow: ', word*4+byte, ', value: ', word*4+byte)
                self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=word*4+byte)  # write raw data
                write_shadow_data[word*4+byte] = word*4+byte
                
                print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', word*4+byte)
                self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=word*4+byte)  # write double data
                write_shadow_data[(word+NUM_WORDS)*4+byte] = word*4+byte
                
                rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                if rd_raw_shadow_data != word*4+byte:
                    raise('rd_raw_shadow_data misalign to write raw data')
                if rd_raw_shadow_data != rd_double_shadow_data:
                    raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                tran = str(bin(word*4+byte)[2:])
                cd = tran.zfill(8)
                print('cd: ', cd)
                ne = ''
                for mm in range(8):
                    ne += cd[7-mm]
                ecc_word_raw += ne
            print('ecc_word(str) is: ', ecc_word_raw)
            
            ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
            print('ECC shadow: ', word+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
            
            write_shadow_data[word+8*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
            rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+8*NUM_WORDS)
            print('read raw ecc: shadow: ', word+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
            
            write_shadow_data[word+9*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
            rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+9*NUM_WORDS)
            print('read double ecc: shadow: ', word+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)
            
            if ecc_shadow_expect != rd_raw_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to ecc_shadow_expect')
            elif rd_raw_ecc_shadow_data != rd_double_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to rd_double_ecc_shadow_data')
        
        print('write_shadow_data is: ', write_shadow_data)
        
        crc_shadow_expect = self.EfuseCRCGen(write_shadow_data)
        print('crc_shadow_expect: ', crc_shadow_expect)
        
        # write CRC & calculate ECC for word 47
        ecc_word_raw = ''
        for byte in range(4):
            write_shadow_data[47*4+byte] = crc_shadow_expect[byte]
            self.m2c.wr_efuse_shadow_data_fields(i=47*4+byte, shadow_data=crc_shadow_expect[byte])  # write CRC data
            rd_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(47*4+byte)
            print('read crc: shadow: ', 47*4+byte, ', value: ', rd_crc_shadow_data)
            
            self.m2c.wr_efuse_shadow_data_fields(i=(47+NUM_WORDS)*4+byte, shadow_data=crc_shadow_expect[byte])  # write double data
            write_shadow_data[(47+NUM_WORDS)*4+byte] = crc_shadow_expect[byte]
            
            rd_double_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((47+NUM_WORDS)*4+byte)
            print('read double: shadow: ', (47+NUM_WORDS)*4+byte, ', value: ', rd_double_crc_shadow_data)
            
            if rd_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_crc_shadow_data misalign to ecc_shadow_expect')
            
            if rd_double_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_double_crc_shadow_data misalign to ecc_shadow_expect')
            
            tran = str(bin(crc_shadow_expect[byte])[2:])
            cd = tran.zfill(8)
            print('cd: ', cd)
            ne = ''
            for mm in range(8):
                ne += cd[7-mm]
            ecc_word_raw += ne
        print('ecc_word(str) is: ', ecc_word_raw)
        
        ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
        print('ECC shadow: ', 47+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
        
        write_shadow_data[47+8*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=47+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
        rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=47+8*NUM_WORDS)
        print('read raw ecc: shadow: ', 47+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
        
        write_shadow_data[47+9*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=47+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
        rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=47+9*NUM_WORDS)
        print('read double ecc: shadow: ', 47+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)    
            
        write_shadow_data[127*4] = 0x38
        self.m2c.wr_efuse_shadow_data_fields(i=127*4, shadow_data=0x38)  # 0x38 to lock out, whether to lock out
        
        print('write_shadow_data is: ', write_shadow_data)
        
        pgm = True  # enable program?
        if pgm==True:
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage 
            
            # raw fpgm
            self.m2c.wr_efuse_pgm_start(0)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46)  # 46
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            # time.sleep(0.1)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            # double fpgm
            self.m2c.wr_efuse_pgm_start(0+48)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46+48)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # CRC Ppgm
            self.m2c.wr_efuse_partial_addr(0x47)  # Partial read/program address, in word, A[6:0].
            for i in range(4):
                self.m2c.wr_efuse_ppgm_data(i, crc_shadow_expect[i])
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # ECC and remaining section
            self.m2c.wr_efuse_pgm_start(NUM_WORDS*2-1)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(127)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            # lock key efuse
            self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage 
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.
    
    def EfusePgmS68R2Verify(self, d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,dev_id=0,silicon_vid=0,kswing_done_12g=0, bcrx_lpfc_dac_12g=0,kswing_done_6g=0, bcrx_lpfc_dac_6g=0,kswing_done_3g=0, bcrx_lpfc_dac_3g=0,adc_offset=0,tx_bit_order_8b10b=0,rx_bit_order_8b10b=0,bist_bypass=0,bypass_resid_calib=0):
        
        write_shadow_data = [0 for i in range(128)] 
        a0=(d2a_trim_bg_reg + (d2a_trim_bg_ts_vr_sel<<3)+((dev_id&7)<<5))&0xFF
        a1=((dev_id>>3)+((silicon_vid&0x7)<<5))&0xFF
        a2=((silicon_vid>>3)+((kswing_done_12g&0x07)<<5))&0xFF
        a3=((kswing_done_12g>>3)+((bcrx_lpfc_dac_12g&0x1f)<<3))&0xff
        a4=((bcrx_lpfc_dac_12g>>5)+((kswing_done_6g&0x1f)<<3))&0xFF
        a5=((kswing_done_6g>>5)+((bcrx_lpfc_dac_6g&0x7F)<<1))&0xFF
        a6=((bcrx_lpfc_dac_6g>>7)+(kswing_done_3g<<1)+((bcrx_lpfc_dac_3g&0x1)<<7))&0xFF
        a7=((bcrx_lpfc_dac_3g>>1)+((adc_offset&0x1)<<7))&0xFF
        a8=((adc_offset>>1)+(tx_bit_order_8b10b<<6)+(rx_bit_order_8b10b<<7))&0xFF
        a9=(bist_bypass+(bypass_resid_calib<<1))&0xFF
        NUM_WORDS = 12
        print('rd_efuse_int_st is: ', self.m2c.rd_efuse_int_st())
        
        # enable interrupt flag
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1)   
    
        for word in range(11): # max 47 raw words
            ecc_word_raw = ''
            for byte in range(4):  # 4 bytes for each word
                if ((word*4+byte)==0):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a0)  # write raw data
                    write_shadow_data[word*4+byte] = a0
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a0)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a0
                    tran = str(bin(a0)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a0:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==1):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a1)  # write raw data
                    write_shadow_data[word*4+byte] = a1
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a1)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a1
                    tran = str(bin(a1)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a1:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==2):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a2)  # write raw data
                    write_shadow_data[word*4+byte] = a2
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a2)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a2
                    tran = str(bin(a2)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a2:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==3):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a3)  # write raw data
                    write_shadow_data[word*4+byte] = a3
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a3)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a3
                    tran = str(bin(a3)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a3:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==4):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a4)  # write raw data
                    write_shadow_data[word*4+byte] = a4
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a4)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a4
                    tran = str(bin(a4)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a4:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==5):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a5)  # write raw data
                    write_shadow_data[word*4+byte] = a5
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a5)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a5
                    tran = str(bin(a5)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a5:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==6):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a6)  # write raw data
                    write_shadow_data[word*4+byte] = a6
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a6)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a6
                    
                    tran = str(bin(a6)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a6:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==7):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a7)  # write raw data
                    write_shadow_data[word*4+byte] = a7
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a7)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a7
                    tran = str(bin(a7)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a7:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==8):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a8)  # write raw data
                    write_shadow_data[word*4+byte] = a8
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a8)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a8
                    
                    tran = str(bin(a8)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a8:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==9):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a9)  # write raw data
                    write_shadow_data[word*4+byte] = a9
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a9)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a9
                    
                    tran = str(bin(a9)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a9:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                else:
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=word*4+byte)  # write raw data
                    write_shadow_data[word*4+byte] = word*4+byte
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=word*4+byte)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = word*4+byte
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != word*4+byte:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                    tran = str(bin(word*4+byte)[2:])
                    
                cd = tran.zfill(8)
                print('cd: ', cd)
                ne = ''
                for mm in range(8):
                    ne += cd[7-mm]
                ecc_word_raw += ne
            print('ecc_word(str) is: ', ecc_word_raw)
            
            ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
            print('ECC shadow: ', word+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
            
            write_shadow_data[word+8*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
            rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+8*NUM_WORDS)
            print('read raw ecc: shadow: ', word+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
            
            write_shadow_data[word+9*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
            rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+9*NUM_WORDS)
            print('read double ecc: shadow: ', word+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)
            
            if ecc_shadow_expect != rd_raw_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to ecc_shadow_expect')
            elif rd_raw_ecc_shadow_data != rd_double_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to rd_double_ecc_shadow_data')
        
        print('write_shadow_data is: ', write_shadow_data)
        
        crc_shadow_expect = self.EfuseCRCGen(write_shadow_data)
        print('crc_shadow_expect: ', crc_shadow_expect)
        
        # write CRC & calculate ECC for word 47
        ecc_word_raw = ''
        for byte in range(4):
            write_shadow_data[11*4+byte] = crc_shadow_expect[byte]
            self.m2c.wr_efuse_shadow_data_fields(i=11*4+byte, shadow_data=crc_shadow_expect[byte])  # write CRC data
            rd_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(11*4+byte)
            print('read crc: shadow: ', 11*4+byte, ', value: ', rd_crc_shadow_data)
            
            self.m2c.wr_efuse_shadow_data_fields(i=(11+NUM_WORDS)*4+byte, shadow_data=crc_shadow_expect[byte])  # write double data
            write_shadow_data[(11+NUM_WORDS)*4+byte] = crc_shadow_expect[byte]
            
            rd_double_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((11+NUM_WORDS)*4+byte)
            print('read double: shadow: ', (11+NUM_WORDS)*4+byte, ', value: ', rd_double_crc_shadow_data)
            
            if rd_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_crc_shadow_data misalign to ecc_shadow_expect')
            
            if rd_double_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_double_crc_shadow_data misalign to ecc_shadow_expect')
            
            tran = str(bin(crc_shadow_expect[byte])[2:])
            cd = tran.zfill(8)
            print('cd: ', cd)
            ne = ''
            for mm in range(8):
                ne += cd[7-mm]
            ecc_word_raw += ne
        print('ecc_word(str) is: ', ecc_word_raw)
        
        ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
        print('ECC shadow: ', 11+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
        
        write_shadow_data[11+8*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
        rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+8*NUM_WORDS)
        print('read raw ecc: shadow: ', 11+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
        
        write_shadow_data[11+9*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
        rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+9*NUM_WORDS)
        print('read double ecc: shadow: ', 11+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)    
            
        write_shadow_data[31*4] = 0x38
        self.m2c.wr_efuse_shadow_data_fields(i=31*4, shadow_data=0x38)  # 0x38 to lock out, whether to lock out
        
        print('write_shadow_data is: ', write_shadow_data)
        
        pgm = True  # enable program?
        if pgm==True:
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage 
            
            # raw fpgm
            self.m2c.wr_efuse_pgm_start(0)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(31)  # 46
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status!=0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            time.sleep(1)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            '''
            # double fpgm
            self.m2c.wr_efuse_pgm_start(0+48)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46+48)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # CRC Ppgm
            self.m2c.wr_efuse_partial_addr(0x47)  # Partial read/program address, in word, A[6:0].
            for i in range(4):
                self.m2c.wr_efuse_ppgm_data(i, crc_shadow_expect[i])
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # ECC and remaining section
            self.m2c.wr_efuse_pgm_start(NUM_WORDS*2-1)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(127)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            '''
            # lock key efuse
            self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status!=0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            time.sleep(1)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage 
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.
    
    def Efuse_partial_test(self, Wafer_ID0=0,Wafer_ID1=0, Wafer_ID2=0,Wafer_ID3=0, Wafer_ID4=0, Wafer_ID5=0,Wafer_ID6=0, Wafer_ID7=0,Wafer_ID8=0,Wafer_ID9=0,die_x_axis=0, die_y_axis=0):
        
        data_a0 = 0 
        data_a1 = ((Wafer_ID0<<1)+((Wafer_ID1&0x01)<<7))&0xFF
        data_a2 = (Wafer_ID1>>1)&0x1F
        data_a3 = (Wafer_ID2<<3)&0xFF
        data_a4 = ((Wafer_ID2>>5)&0x01 + (Wafer_ID3&0x03))&0xFF
        data_a5 = ((Wafer_ID3<<2)&0x0F + (Wafer_ID4&0x07))&0xFF 
        data_a6 = 0x0
        data_a7 = ((Wafer_ID4>>3)&0x07 + ((Wafer_ID5&0x0F)<<3))&0xFF
        data_a8 = 0
        data_a9 = ((((Wafer_ID5>>4)&0x03)<<2) + ((Wafer_ID6&0x0F)<<4))&0xFF
        data_a10 = ((((Wafer_ID6>>4)&0x03)<<3) + ((Wafer_ID7&0x07)<<5))&0xFF
        data_a11 = (((Wafer_ID7>>3)&0x07) + ((Wafer_ID8<<3)&0x1F))&0xFF
        data_a12 = (((Wafer_ID8>>5)&0x01)+(Wafer_ID9<<1))&0xFF
        data_a13 = 0
        data_a14 = 0
        data_a15 = 0
        data_a28 = die_x_axis&0x7F
        data_a29 = 0
        data_a30 = 0
        data_a31 = (die_x_axis>>7)&0x1F
        data_a40 = 0
        data_a41 = (die_y_axis>>8)&0x07
        data_a42 = ((die_y_axis&0x0F)<<4)&0xFF
        data_a43 = ((die_y_axis>>4)&0x0F)<<4
        
            
        shadow_expect=[data_a0,data_a1,data_a2,data_a3,data_a4,data_a5,data_a6,data_a7,data_a8,data_a9,data_a10,data_a11,data_a12,data_a13,data_a14,data_a15,data_a28,data_a29,data_a30,data_a31,data_a40,data_a41,data_a42,data_a43,]
        self.m2c.wr_efuse_partial_addr(0)  # Partial read/program address, in word, A[6:0].
        for i in range(4):
            self.m2c.wr_efuse_ppgm_data(i, shadow_expect[i])
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int() 
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        # status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.EfuseINTClearStatus()
        self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        
        shadow_expect=[15,16,11,25]
        self.m2c.wr_efuse_partial_addr(12)  # Partial read/program address, in word, A[6:0].
        for i in range(4):
            self.m2c.wr_efuse_ppgm_data(i, shadow_expect[i])
        
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.  
          
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.  
    
    def Efuse_partial_CP(self, Wafer_ID0=0,Wafer_ID1=0,Wafer_ID2=0,Wafer_ID3=0, Wafer_ID4=0, Wafer_ID5=0,Wafer_ID6=0, Wafer_ID7=0,Wafer_ID8=0,Wafer_ID9=0,die_x_axis=0, die_y_axis=0):
        
        Wafer_map={'0':0, '1':1, '2':2, '3':3, '4':4,'5':5,'6':6,'7':7,'8':8,'9':9,'A':10,'B':11,'C':12,'D':13,'E':14,'F':15,'G':16,'H':17,'I':18,\
                   'J':19,'K':20,'L':21,'M':22,'N':23,'O':24,'P':25,'Q':26,'R':27,'S':28,'T':29,'U':30,'V':31,'W':32,'X':33,'Y':34,'Z':35}
        
        data_a0 = 0 
        data_a1 = ((Wafer_map[Wafer_ID0]<<1)+((Wafer_map[Wafer_ID1]&0x01)<<7))&0xFF
        data_a2 = (Wafer_map[Wafer_ID1]>>1)&0x1F
        data_a3 = (Wafer_map[Wafer_ID2]<<3)&0xFF
        data_a4 = (((Wafer_map[Wafer_ID2]>>5)&0x01) + ((Wafer_map[Wafer_ID3]&0x03)<<1))&0xFF
        data_a5 = ((((Wafer_map[Wafer_ID3]>>2)&0x0F)<<1) + ((Wafer_map[Wafer_ID4]&0x07)<<5))&0xFF 
        data_a6 = 0x0
        data_a7 = (((Wafer_map[Wafer_ID4]>>3)&0x07) + ((Wafer_map[Wafer_ID5]&0x0F)<<3))&0xFF
        data_a8 = 0
        data_a9 = ((((Wafer_map[Wafer_ID5]>>4)&0x03)<<2) + ((Wafer_map[Wafer_ID6]&0x0F)<<4))&0xFF
        data_a10 = ((((Wafer_map[Wafer_ID6]>>4)&0x03)<<3) + ((Wafer_map[Wafer_ID7]&0x07)<<5))&0xFF
        data_a11 = (((Wafer_map[Wafer_ID7]>>3)&0x07) + ((Wafer_map[Wafer_ID8]&0x1F)<<3))&0xFF
        data_a12 = (((Wafer_map[Wafer_ID8]>>5)&0x01)+(Wafer_map[Wafer_ID9]<<1))&0xFF
        data_a13 = 0
        data_a14 = 0
        data_a15 = 0
        data_a28 = die_x_axis&0x7F
        data_a29 = 0
        data_a30 = 0
        data_a31 = (die_x_axis>>7)&0x1F
        data_a40 = 0
        data_a41 = (die_y_axis>>8)&0x07
        data_a42 = ((die_y_axis&0x0F)<<4)&0xFF
        data_a43 = ((die_y_axis>>4)&0x0F)<<4
        
            
        shadow_expect=[data_a0,data_a1,data_a2,data_a3,data_a4,data_a5,data_a6,data_a7,data_a8,data_a9,data_a10,data_a11,data_a12,data_a13,data_a14,data_a15,data_a28,data_a29,data_a30,data_a31,data_a40,data_a41,data_a42,data_a43,]
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int() 
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        # status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        
        # self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        
        for i in range(4):
            self.m2c.wr_efuse_partial_addr(i)  # Partial read/program address, in word, A[6:0].
            for j in range(4):
                self.m2c.wr_efuse_ppgm_data(j, shadow_expect[4*i+j])
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            while(status==0):
                status=self.m2c.rd_efuse_int_st_efuse_done_int()
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
        
        self.m2c.wr_efuse_partial_addr(7)  # Partial read/program address, in word, A[6:0].
        for j in range(4):
            self.m2c.wr_efuse_ppgm_data(j, shadow_expect[16+j])
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        self.m2c.wr_efuse_partial_addr(10)  # Partial read/program address, in word, A[6:0].
        for j in range(4):
            self.m2c.wr_efuse_ppgm_data(j, shadow_expect[20+j])
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
            
        for i in range(4):
            self.m2c.wr_efuse_partial_addr(12+i)  # Partial read/program address, in word, A[6:0].
            for j in range(4):
                self.m2c.wr_efuse_ppgm_data(j, shadow_expect[4*i+j])
            
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            while(status==0):
                status=self.m2c.rd_efuse_int_st_efuse_done_int()
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            
        self.m2c.wr_efuse_partial_addr(19)  # Partial read/program address, in word, A[6:0].
        for j in range(4):
            self.m2c.wr_efuse_ppgm_data(j, shadow_expect[16+j])
        
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        self.m2c.wr_efuse_partial_addr(22)  # Partial read/program address, in word, A[6:0].
        for j in range(4):
            self.m2c.wr_efuse_ppgm_data(j, shadow_expect[20+j])
        
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.  
          
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.  
        
    def Efuse_partial_FT(self, d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,dev_id=0,\
                         kswing_done_12g=0, kswing_done_6g=0,kswing_done_3g=6,adc_offset=0,tx_bit_order_8b10b=0,\
                         rx_bit_order_8b10b=0,bist_bypass=0,bypass_resid_calib=0,resid_dac_override=0,bcrx_picode_step=6,bcrx_comp_bw=0,\
                         bcrx_csel_txp=3,bcrx_csel_txn=3,bcrx_comp_vcm=3,rsvd_reg0=0x20,rsvd_reg1=1,rsvd_reg4=6,\
                         bcrx_aldo_vset=4,txldockgen=4,serldo_vset=4,tx_ldodriver=31,tx_idriver=8,\
                         tx_dac_bias_en=0,tx_bias_en=1,tx_normal_bias_en=1,\
                         efuse_ana_reg_en=9,\
                         tx_link_polar=0,rx_link_polar=0,lpf_csel_txp_coax=0,\
                         lpf_csel_txn_coax=0,rsvd_reg0_coax=1,tx_k_pre_3g=0,tx_k_pre_6g=0,tx_k_pre_12g=0,\
                         tx_k_post_3g=0,tx_k_post_6g=0,tx_k_post_12g=0,tx_sign_pre=0,tx_sign_main=0,tx_sign_post=0,\
                         tx_backcali_en=1,tx_override_en=0,bcrx_cktree_div=0,tx_driver_bias_en=1,\
                         bcrx_en_kp=1,bcrx_en_kf=1,bcrx_en_picode_offset=0x0,\
                         bcrx_kp=0,bcrx_kf=0,rx_fec_en=0,tx_fec_cwm_period_def_sel=0,tx_safe_mode=0,tx_dummy_en=0,\
                         rsvd_reg7_3g_bit0to3=0,\
                         bcrx_pi_input_slewctr=3,bcrx_pi_output_slewctr=3,xtal_gm=0,digldo_vset=0,\
                         linkpll_aldo_vtune=4,linkpll_dldo_vtune=4,linkpll_pfd_dly=1,linkpll_cp_ictrl=0,\
                         linkpll_cp_vset=0,linkpll_lpf_c2=0,linkpll_lpf_c3=1,linkpll_lpf_r1=3,\
                         linkpll_lpf_r3=4,linkpll_vco_idc=8,linkpll_vco_refb=8,linkpll_vco_refn=3,\
                         linkpll_vco_refp=11,linkpll_vco_var=3,linkpll_dldo_mmd_vtune=4,rsvd_reg5=0,linkpll_cp_vset_bit3=1,\
                         efuse_lock_out=0x38, Wafer_ID0='0',Wafer_ID1='0',Wafer_ID2='0',\
                         Wafer_ID3='0', Wafer_ID4='0', Wafer_ID5='0', Wafer_ID6='0', Wafer_ID7='0',Wafer_ID8='0',Wafer_ID9='0',die_x_axis=0, die_y_axis=0):
        
        '''
        dev_id: 
            M66S68：0
            M66S66：1
            M66S63：2
        '''
        Wafer_map       = {'0':0, '1':1, '2':2, '3':3, '4':4,'5':5,'6':6,'7':7,'8':8,'9':9,'A':10,'B':11,'C':12,'D':13,'E':14,'F':15,'G':16,'H':17,'I':18,\
                   'J':19,'K':20,'L':21,'M':22,'N':23,'O':24,'P':25,'Q':26,'R':27,'S':28,'T':29,'U':30,'V':31,'W':32,'X':33,'Y':34,'Z':35}
        
        self.EfuseFullRefresh()      # should refresh when CP pre-efused
        
        #================================check CP ID===================================
        Wafer_ID0_r  = (self.m2c.rd_efuse_shadow_data_shadow_data(1) & 0x7E) >>1
        Wafer_ID1_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(1) & 0x80) >>7) +  ((self.m2c.rd_efuse_shadow_data_shadow_data(2) & 0x1f)<<1      )
        Wafer_ID2_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(3) & 0xF8) >>3) +  ((self.m2c.rd_efuse_shadow_data_shadow_data(4) & 0x1)<<5       )
        Wafer_ID3_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(4) & 0x06) >>1) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(5) & 0x1e)>>1)<<2 )
        Wafer_ID4_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(5) & 0xe0) >>5) +  ((self.m2c.rd_efuse_shadow_data_shadow_data(7) & 0x7)<<3       )
        Wafer_ID5_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(7) & 0x78) >>3) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(9) & 0x0c)>>2)<<4 )
        Wafer_ID6_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(9) & 0xf0) >>4) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(10) & 0x18)>>3)<<4)
        Wafer_ID7_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(10) & 0xe0) >>5) + (((self.m2c.rd_efuse_shadow_data_shadow_data(11) & 0x07))<<3   )
        Wafer_ID8_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(11) & 0xf8) >>3) + (((self.m2c.rd_efuse_shadow_data_shadow_data(12) & 0x1))<<5    )
        Wafer_ID9_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(12) & 0x7e) >>1)
        die_x_axis_r = ((self.m2c.rd_efuse_shadow_data_shadow_data(28) & 0x7f)) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(31) & 0x1f))<<7)
        die_y_axis_r = ((self.m2c.rd_efuse_shadow_data_shadow_data(42) & 0xf0) >>4) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(43) & 0xf0)>>4)<<4)  +  (((self.m2c.rd_efuse_shadow_data_shadow_data(41) & 0x07))<<8)

        if (Wafer_ID0_r | Wafer_ID1_r |Wafer_ID2_r | Wafer_ID3_r | Wafer_ID4_r | Wafer_ID5_r | Wafer_ID6_r | Wafer_ID7_r | Wafer_ID8_r | Wafer_ID9_r | die_x_axis_r | die_y_axis_r):
            Wafer_ID0 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID0_r )]
            Wafer_ID1 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID1_r )]
            Wafer_ID2 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID2_r )]  
            Wafer_ID3 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID3_r )]  
            Wafer_ID4 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID4_r )] 
            Wafer_ID5 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID5_r )] 
            Wafer_ID6 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID6_r )] 
            Wafer_ID7 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID7_r )] 
            Wafer_ID8 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID8_r )] 
            Wafer_ID9 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID9_r )] 
            die_x_axis= die_x_axis_r
            die_y_axis= die_y_axis_r
        
        write_shadow_data = [0 for i in range(128)] 
        data_a0 = (d2a_trim_bg_reg + (d2a_trim_bg_ts_vr_sel<<3)+((dev_id&7)<<5))&0xFF 
        data_a1 = ((dev_id>>3)+(Wafer_map[Wafer_ID0]<<1)+((Wafer_map[Wafer_ID1]&0x01)<<7))&0xFF
        data_a2 = (((Wafer_map[Wafer_ID1]>>1)&0x1F) + ((kswing_done_12g&0x07)<<5))&0xFF
        data_a3 = ((kswing_done_12g>>3)+(Wafer_map[Wafer_ID2]<<3))&0xFF
        data_a4 = (((kswing_done_6g&0x1f)<<3)+((Wafer_map[Wafer_ID2]>>5)&0x01)+((Wafer_map[Wafer_ID3]&0x03)<<1))&0xFF
        data_a5 = ((((Wafer_map[Wafer_ID3]>>2)&0x0F)<<1) + ((Wafer_map[Wafer_ID4]&0x07)<<5) + (kswing_done_6g>>5))&0xFF 
        data_a6 = (kswing_done_3g<<1)&0xFF
        data_a7 = (((adc_offset&0x1)<<7)+((Wafer_map[Wafer_ID4]>>3)&0x07) + ((Wafer_map[Wafer_ID5]&0x0F)<<3))&0xFF
        data_a8 = ((adc_offset>>1)+(tx_bit_order_8b10b<<6)+(rx_bit_order_8b10b<<7))&0xFF
        data_a9 = (bist_bypass+(bypass_resid_calib<<1)+(((Wafer_map[Wafer_ID5]>>4)&0x03)<<2) + ((Wafer_map[Wafer_ID6]&0x0F)<<4))&0xFF
        data_a10 = ((((Wafer_map[Wafer_ID6]>>4)&0x03)<<3) + ((Wafer_map[Wafer_ID7]&0x07)<<5)+(resid_dac_override<<2))&0xFF
        data_a11 = (((Wafer_map[Wafer_ID7]>>3)&0x07) + ((Wafer_map[Wafer_ID8]&0x1F)<<3))&0xFF
        data_a12 = (((Wafer_map[Wafer_ID8]>>5)&0x01)+(Wafer_map[Wafer_ID9]<<1))&0xFF
        data_a13 = (((bcrx_picode_step&0x0f)<<3)+(bcrx_comp_bw<<7))&0xff
        data_a14 = (bcrx_csel_txp +(bcrx_csel_txn<<2)+(bcrx_comp_vcm<<4)+((rsvd_reg0&0x01)<<7))&0xff
        data_a15 = ((rsvd_reg0>>1)+((rsvd_reg1&0x01)<<7))&0xff
        data_a16 = ((rsvd_reg4&0x01)<<7)&0xff
        data_a17 = ((rsvd_reg4>>1)+((bcrx_aldo_vset&0x01)<<7))&0xff
        data_a18 = ((bcrx_aldo_vset>>1)+(txldockgen<<2)+(serldo_vset<<5))&0xff
        data_a19 = (tx_ldodriver+((tx_idriver&0x07)<<5))&0xff
        data_a20 = ((tx_idriver>>3)+(tx_dac_bias_en<<5)+(tx_bias_en<<6)+(tx_normal_bias_en<<7))&0xff
        data_a21 = (efuse_ana_reg_en + (tx_link_polar<<4) + (rx_link_polar<<5) + (lpf_csel_txp_coax<<6)) & 0xff   #efuse shadow value transfer into analog
        data_a22 = (lpf_csel_txn_coax+((rsvd_reg0_coax&0x3f)<<2))&0xff
        data_a23 = (((rsvd_reg0_coax&0xc0)>>6)+(tx_k_pre_3g<<2)+((tx_k_pre_6g&0x1)<<7))&0xff
        data_a24 = (((tx_k_pre_6g&0x1e)>1)+((tx_k_pre_12g&0x0f)<<4))&0xff
        data_a25 = (((tx_k_pre_12g&0x10)>>4)+(tx_k_post_3g<<1)+((tx_k_post_6g&0x3)<<6))&0xff
        data_a26 = (((tx_k_post_6g&0x1c)>>2)+(tx_k_post_12g<<3))&0xff
        data_a27 = (tx_sign_pre+(tx_sign_main<<1)+(tx_sign_post<<2)+(tx_backcali_en<<3)+(tx_override_en<<4)+(bcrx_cktree_div<<5))&0xff 
        data_a28 = die_x_axis&0x7F
        data_a29 = ((tx_driver_bias_en<<2)+(bcrx_en_kp<<3)+(bcrx_en_kf<<4)+(bcrx_en_picode_offset<<5)+((bcrx_kp&0x1)<<7))&0xff
        data_a30 = (((bcrx_kp&0x2)>>1)+(bcrx_kf<<1)+(rx_fec_en<<4)+(tx_fec_cwm_period_def_sel<<5)+(tx_safe_mode<<6)+(tx_dummy_en<<7))&0xff
        data_a31 = (die_x_axis>>7)&0x1F
        data_a32 = (rsvd_reg7_3g_bit0to3+((bcrx_pi_input_slewctr&0x1)<<7))&0xff
        data_a33 = (((bcrx_pi_input_slewctr&0x2)>>1)+(bcrx_pi_output_slewctr<<1)+(xtal_gm<<3)+(digldo_vset<<5))&0xff
        data_a34 = (linkpll_aldo_vtune+(linkpll_dldo_vtune<<3)+(linkpll_pfd_dly<<6))&0xff
        data_a35 = (linkpll_cp_ictrl+(linkpll_cp_vset<<3)+(linkpll_lpf_c2<<6))&0xff
        data_a36 = (linkpll_lpf_c3+(linkpll_lpf_r1<<2)+(linkpll_lpf_r3<<5))&0xff
        data_a37 = (linkpll_vco_idc+((linkpll_vco_refb&0x7)<<5))&0xff
        data_a38 = (((linkpll_vco_refb&0x8)>>3)+(linkpll_vco_refn<<1)+((linkpll_vco_refp&0x7)<<5))&0xff
        data_a39 = (((linkpll_vco_refp&0x8)>>3)+(linkpll_vco_var<<1)+(linkpll_dldo_mmd_vtune<<3)+((rsvd_reg5&0x3)<<6))&0xff
        data_a40 = (((rsvd_reg5&0xfc)>>2))&0xff
        data_a41 = (((die_y_axis>>8)&0x07) + (linkpll_cp_vset_bit3<<6))&0xFF
        data_a42 = ((die_y_axis&0x0F)<<4)&0xFF
        data_a43 = ((die_y_axis>>4)&0x0F)<<4
        
            
        shadow_expect=[data_a0,data_a1,data_a2,data_a3,data_a4,data_a5,data_a6,data_a7,data_a8,data_a9,data_a10,data_a11,data_a12,data_a13,data_a14,data_a15,\
                       data_a16,data_a17,data_a18,data_a19,data_a20,data_a21,data_a22,data_a23,data_a24,data_a25,data_a26,data_a27,\
                       data_a28,data_a29,data_a30,data_a31,data_a32,data_a33,data_a34,data_a35,data_a36,data_a37,data_a38,data_a39,data_a40,data_a41,data_a42,data_a43,]
        
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1)  
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int() 
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        # status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.EfuseINTClearStatus()
        self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        # self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        NUM_WORDS=12
        for word in range(11):  #efuse shadow
            ecc_word_raw = ''
            self.m2c.wr_efuse_partial_addr(word)  # Partial read/program address, in word, A[6:0].
            for byte in range(4):
                self.m2c.wr_efuse_ppgm_data(byte, shadow_expect[4*word+byte])
                write_shadow_data[word*4+byte] = shadow_expect[4*word+byte]
                    
                tran = str(bin(shadow_expect[4*word+byte])[2:])
            
                cd = tran.zfill(8)
                print('cd: ', cd)
                ne = ''
                for mm in range(8):
                    ne += cd[7-mm]
                ecc_word_raw += ne
            print('ecc_word(str) is: ', ecc_word_raw)
            
            ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
            print('ECC shadow: ', word+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
            write_shadow_data[word+8*NUM_WORDS] = ecc_shadow_expect #ecc
            write_shadow_data[word+9*NUM_WORDS] = ecc_shadow_expect #double ecc
            
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status==0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            
        for word in range(11): #efuse double shadow
            self.m2c.wr_efuse_partial_addr(NUM_WORDS+word)  # Partial read/program address, in word, A[6:0].
            for byte in range(4):
                self.m2c.wr_efuse_ppgm_data(byte, shadow_expect[4*word+byte])
                write_shadow_data[(word+NUM_WORDS)*4+byte] = shadow_expect[4*word+byte]
            
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status==0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            
        crc_shadow_expect = self.EfuseCRCGen(write_shadow_data)
        print('crc_shadow_expect: ', crc_shadow_expect)   
         
        # write CRC & calculate ECC for word 47
        ecc_word_raw = ''
        for byte in range(4):
            write_shadow_data[11*4+byte] = crc_shadow_expect[byte]
            write_shadow_data[(11+NUM_WORDS)*4+byte] = crc_shadow_expect[byte]
            tran = str(bin(crc_shadow_expect[byte])[2:])
            cd = tran.zfill(8)
            print('cd: ', cd)
            ne = ''
            for mm in range(8):
                ne += cd[7-mm]
            ecc_word_raw += ne
        print('ecc_word(str) is: ', ecc_word_raw)
        
        ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
        print('ECC shadow: ', 11+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
        write_shadow_data[11+8*NUM_WORDS] = ecc_shadow_expect
        write_shadow_data[11+9*NUM_WORDS] = ecc_shadow_expect
        
        for i in range(6): #efuse ecc
            self.m2c.wr_efuse_partial_addr(24+i)  # Partial read/program address, in word, A[6:0].                        #efuse ecc
            for byte in range(4):
                self.m2c.wr_efuse_ppgm_data(byte, write_shadow_data[4*i+byte+8*NUM_WORDS])
          
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status==0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
       
        #efuse crc
        self.m2c.wr_efuse_partial_addr(11)  # Partial read/program address, in word, A[6:0]. 
        for byte in range(4):
            self.m2c.wr_efuse_ppgm_data(byte, write_shadow_data[11*4+byte]) 
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        # while(status==0):
        #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        #efuse double crc       
        self.m2c.wr_efuse_partial_addr(23)  # Partial read/program address, in word, A[6:0]. 
        for byte in range(4):
            self.m2c.wr_efuse_ppgm_data(byte, write_shadow_data[23*4+byte])
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        # while(status==0):
        #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        #efuse lock key 
        write_shadow_data[31*4] = efuse_lock_out   
        self.m2c.wr_efuse_partial_addr(31)  # Partial read/program address, in word, A[6:0]. 
        for byte in range(4):
            self.m2c.wr_efuse_ppgm_data(byte, write_shadow_data[31*4+byte])
        self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        # while(status==0):
        #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        print('write_shadow_data is: ', write_shadow_data)
        
        self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.  
          
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        # while(status==0):
        #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro. 
        
        return [0]

    def ChipInfo(self):
        '''
        return chip serial number, pre-efused during CP, and burned during FT.
        '''
        
        Wafer_map       = {'0':0, '1':1, '2':2, '3':3, '4':4,'5':5,'6':6,'7':7,'8':8,'9':9,'A':10,'B':11,'C':12,'D':13,'E':14,'F':15,'G':16,'H':17,'I':18,\
                   'J':19,'K':20,'L':21,'M':22,'N':23,'O':24,'P':25,'Q':26,'R':27,'S':28,'T':29,'U':30,'V':31,'W':32,'X':33,'Y':34,'Z':35}
               
        Wafer_ID0_r  = (self.m2c.rd_efuse_shadow_data_shadow_data(1) & 0x7E) >>1
        Wafer_ID1_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(1) & 0x80) >>7) +  ((self.m2c.rd_efuse_shadow_data_shadow_data(2) & 0x1f)<<1      )
        Wafer_ID2_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(3) & 0xF8) >>3) +  ((self.m2c.rd_efuse_shadow_data_shadow_data(4) & 0x1)<<5       )
        Wafer_ID3_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(4) & 0x06) >>1) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(5) & 0x1e)>>1)<<2 )
        Wafer_ID4_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(5) & 0xe0) >>5) +  ((self.m2c.rd_efuse_shadow_data_shadow_data(7) & 0x7)<<3       )
        Wafer_ID5_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(7) & 0x78) >>3) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(9) & 0x0c)>>2)<<4 )
        Wafer_ID6_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(9) & 0xf0) >>4) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(10) & 0x18)>>3)<<4)
        Wafer_ID7_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(10) & 0xe0) >>5) + (((self.m2c.rd_efuse_shadow_data_shadow_data(11) & 0x07))<<3   )
        Wafer_ID8_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(11) & 0xf8) >>3) + (((self.m2c.rd_efuse_shadow_data_shadow_data(12) & 0x1))<<5    )
        Wafer_ID9_r  = ((self.m2c.rd_efuse_shadow_data_shadow_data(12) & 0x7e) >>1)
        die_x_axis_r = ((self.m2c.rd_efuse_shadow_data_shadow_data(28) & 0x7f)) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(31) & 0x1f))<<7)
        die_y_axis_r = ((self.m2c.rd_efuse_shadow_data_shadow_data(42) & 0xf0) >>4) +  (((self.m2c.rd_efuse_shadow_data_shadow_data(43) & 0xf0)>>4)<<4)  +  (((self.m2c.rd_efuse_shadow_data_shadow_data(41) & 0x07))<<8)
        
        Wafer_ID0 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID0_r )]
        Wafer_ID1 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID1_r )]
        Wafer_ID2 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID2_r )]  
        Wafer_ID3 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID3_r )]  
        Wafer_ID4 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID4_r )] 
        Wafer_ID5 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID5_r )] 
        Wafer_ID6 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID6_r )] 
        Wafer_ID7 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID7_r )] 
        Wafer_ID8 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID8_r )] 
        Wafer_ID9 = list(Wafer_map.keys()) [list(Wafer_map.values()).index(Wafer_ID9_r )] 
        die_x_axis= die_x_axis_r
        die_y_axis= die_y_axis_r
            
        chip_id = str(Wafer_ID0)+ '_' + str(Wafer_ID1)+ '_'+ str(Wafer_ID2)+ '_'+ str(Wafer_ID3)+ '_'+ str(Wafer_ID4)+ '_'+ str(Wafer_ID5)+ '_'+ str(Wafer_ID6)+ '_'+ str(Wafer_ID7)+ '_'+ str(Wafer_ID8)+ '_'+ str(Wafer_ID9)+ '_'+\
                  str(die_x_axis)+ '_' + str(die_y_axis)
                  
        print('chip_id = ', chip_id)
        return chip_id

    def EfuseFullRead(self):
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        self.m2c.wr_efuse_efuse_key(0x8B)  # this key full read. (EFUSE_FREAD_KEY)
        status=self.m2c.rd_efuse_int_st_efuse_done_int()
        while(status==0):
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # ps=1 - Switch power to high program voltage  
    
    def EfusePgm_S68R7_Verify(self, d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,dev_id=0,silicon_vid=1,\
                              kswing_done_12g=0, bcrx_lpfc_dac_12g=175,kswing_done_6g=0, bcrx_lpfc_dac_6g=175,\
                              kswing_done_3g=6, bcrx_lpfc_dac_3g=175,adc_offset=0,tx_bit_order_8b10b=0,\
                              rx_bit_order_8b10b=0,bist_bypass=1,bypass_resid_calib=1,resid_dac_offset=0,\
                              resid_dac_override=0,bcrx_lpfc_12g_66b=160,bcrx_lpfc_6g_66b=160,\
                              bcrx_lpfc_3g_66b=160,bcrx_picode_step=4,bcrx_comp_bw=1,bcrx_csel_txp=0,\
                              bcrx_csel_txn=0,bcrx_comp_vcm=3,rsvd_reg0=0x20,rsvd_reg1=0,rsvd_reg4=12,\
                              bcrx_aldo_vset=4,txldockgen=4,serldo_vset=4,tx_ldodriver=31,tx_idriver=9,\
                              tx_idriver_dac=8,tx_dac_bias_en=0,tx_bias_en=1,tx_normal_bias_en=1,\
                              efuse_ana_reg_en=9,\
                              tx_link_polar=0,rx_link_polar=0,lpf_csel_txp_coax=3,\
                              lpf_csel_txn_coax=0,rsvd_reg0_coax=0,tx_k_pre_3g=0,tx_k_pre_6g=0,tx_k_pre_12g=0,\
                              tx_k_post_3g=0,tx_k_post_6g=0,tx_k_post_12g=0,tx_sign_pre=0,tx_sign_main=0,tx_sign_post=0,\
                              tx_backcali_en=1,tx_override_en=0,bcrx_cktree_div=0,null=0,rsvd_reg6_3g=0,\
                              tx_driver_bias_en=1,bcrx_en_kp=0,bcrx_en_kf=0,bcrx_en_picode_offset=0x0,rsvd_reg6_6g_bit0=0,\
                              bcrx_kp=0,bcrx_kf=0,bcrx_kmr=0x10,rsvd_reg6_6g_bit1to7=0,rsvd_reg7_3g_bit0to6=0,\
                              bcrx_pi_input_slewctr=3,bcrx_pi_output_slewctr=3,xtal_gm=0,digldo_vset=0,\
                              linkpll_aldo_vtune=4,linkpll_dldo_vtune=4,linkpll_pfd_dly=1,linkpll_cp_ictrl=3,\
                              linkpll_cp_vset=0,linkpll_lpf_c2=0,linkpll_lpf_c3=1,linkpll_lpf_r1=3,\
                              linkpll_lpf_r3=4,linkpll_vco_idc=8,linkpll_vco_refb=8,linkpll_vco_refn=2,\
                              linkpll_vco_refp=0xc,linkpll_vco_var=1,linkpll_dldo_mmd_vtune=4,rsvd_reg5=0,\
                              rsvd_reg6_12g=0,linkpll_cp_vset_bit3=1,rsvd_reg7_3g_bit7=0,rsvd_reg7_6g=0,rsvd_reg7_12g=0,\
                              efuse_lock_out=0x0):
        
        '''
        
        input:
                input:
                d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,
                dev_id=0,silicon_vid=0,
                kswing_done_12g=0, bcrx_lpfc_dac_12g=160,kswing_done_6g=0, bcrx_lpfc_dac_6g=160,kswing_done_3g=0, bcrx_lpfc_dac_3g=160,
                adc_offset=0,tx_bit_order_8b10b=0,rx_bit_order_8b10b=0,
                bist_bypass=1,bypass_resid_calib=1,resid_dac_offset=0,resid_dac_override=0,
                bcrx_lpfc_12g_66b=160,bcrx_lpfc_6g_66b=160,bcrx_lpfc_3g_66b=160,
                bcrx_picode_step=4,bcrx_comp_bw=1,bcrx_csel_txp=3,bcrx_csel_txn=0,
                bcrx_comp_vcm=3,rsvd_reg0=0,rsvd_reg1=0,rsvd_reg4=0,bcrx_aldo_vset=4,txldockgen=4,serldo_vset=4,tx_ldodriver=4,
                tx_idriver=9,tx_idriver_dac=8,
                tx_dac_bias_en=0,tx_bias_en=1,tx_normal_bias_en=1
                
                
            M66S68R5 efuse descriptions:
                efuse_assign
                    d2a sig efuse a[11:10]= 2'b00; // shrink from 128 to 32
                    "d2a trim bg reg = efuse trim value[2:0];
                    //d2a trim bg ts vr sel default 2'b0l, it is done by add an inverter"
                    d2a trim bg ts_vr sel = {efuse trim value[4],~efuse trim value[3]};
                    dev id[7:0] = efuse trim value[12:5];
                    silicon_vid[7:0]= efuse trim value[20:13];
                    tx_k_swing_down_12g[5:0]= efuse_trim_value[26:21];
                    bcrx_lpfc_dacctr_12g[7:0]= efuse_trim_value[34:27];// 8B/10B
                    txk_swing_down_6g[5:0]= efuse_trim_value[40:35];
                    bcrx_lpfc_dacctr_6g[7:0]= efuse_trim_valúe[48:41];// 8B/10B
                    tx_k_swing_down_3g[5:0]= efuse_trim_walue[54:49];
                    bcrx_lpfc_dacctr_3g[7:0]= efuse_trim_value[62:55];// 8B/10B
                    adc_offset[6:0]= efuse_trim_value[69:63];
                    tx_bit_order_8bl0b = efuse_trim_value[70]
                    rx_bit_order_8bl0b = efuse_trim value[71];
                    bist_bypass = efuse_trim_value[72];
                    bypass resid calib=~efuse trim value[73];
                    
                    resid dac offset = efuse trim value[81:74];
                    resid dac override =efuse trim value[82];
                    bcrx lpfc dacctr 12g 66b[7:0]= efuse trim value[90:83]: // 66B
                    bcrx lpfc dacctr 6g 66b[7:0]= efuse trim value[98:91];// 66B
                    bcrx lpfc dacctr 3g 66b[7:0]= efuse trim value[106:99];// 66B
                    d2a bcrx picode step trim = efuse trim value[110:107];
                    
                    d2a bcrx comp bw sel trim = efuse trim value[111];
                    d2a bcrx lpf csel txp lpf trim = efúse trim value[113:112];
                    d2a bcrx lpf csel txn lpf trim = efuse trim value[115:114];
                    d2a bcrx comp vcm sel trim= efuse trim value[118:116];
                    d2a rsvd reg0 trim= efuse trim value[126:119];
                    d2a rsvd regl trim = efuse trim value[134:127];
                    d2a rsvd reg4 trim= efuse trim value[142:135];
                    d2a bcrx aldo vset trim= efuse trim value[145:143];
                    d2a txldockgen vout trim= efuse trim value[148:146];
                    d2a tx serldo vset trim = efuse trim value[151:149];
                    d2a tx ldodriver14 vout trim = efuse trim value[156:152];
                    d2a tx idriver trim =efuse trim value[160:157];
                    d2a tx idriver dac trim = efuse trim value[164:161];
                    d2a tx dac bias en trim= efuse trim value[165];
                    d2a tx bias en trim= efuse trim value[166];
                    d2a tx normal bias en trim= efuse trim value[167];
                    
                    #s68r7 new add shadow 172 ~352 
                    efuse_field_name    efuse_bit_range    efuse_field_value
                    tx_link_polar    172    1b0
                    rx_link_polar    173    1b0
                    lpf_csel_txp_coax    175:174    2b11
                    lpf_csel_txn_coax    177:176    2b11
                    rsvd_reg0_coax    185:178    8b00000000
                    tx_k_pre_3g    190:186    5b00000
                    tx_k_pre_6g    195:191    5b00000
                    tx_k_pre_12g    200:196    5b00000
                    tx_k_post_3g    205:201    5b00000
                    tx_k_post_6g    210:206    5b00000
                    tx_k_post_12g    215:211    5b00000
                    tx_sign_pre    216    1b0
                    tx_sign_main    217    1b0
                    tx_sign_post    218    1b0
                    tx_backcali_en    219    1b1
                    tx_override_en    220    1b0
                    bcrx_cktree_div    222:221    2b00
                    null    225:223    3b000
                    rsvd_reg6_3g    233:226    8b00000000
                    tx_driver_bias_en    234    1b1
                    bcrx_en_kp    235    1b0
                    bcrx_en_kf    236    1b0
                    bcrx_en_picode_offset    237    1b0
                    rsvd_reg6_6g[0]    238    1b0
                    bcrx_kp    240:239    2b00
                    bcrx_kf    243:241    3b000
                    bcrx_kmr    248:244    5b10000
                    rsvd_reg6_6g[7:1]    255:249    7b0000000
                    rsvd_reg7_3g[6:0]    262:256    7b0000000
                    bcrx_pi_input_slewctr    264:263    2b11
                    bcrx_pi_output_slewctr    266:265    2b11
                    xtal_gm    268:267    2b00
                    digldo_vset    271:269    3b000
                    linkpll_aldo_vtune    274:272    3b100
                    linkpll_dldo_vtune    277:275    3b100
                    linkpll_pfd_dly    279:278    2b01
                    linkpll_cp_ictrl    282:280    3b011
                    linkpll_cp_vset    285:283    3b000
                    linkpll_lpf_c2    287:286    2b00
                    linkpll_lpf_c3    289:288    2b01
                    linkpll_lpf_r1    292:290    3b011
                    linkpll_lpf_r3    295:293    3b100
                    linkpll_vco_idc    300:296    5b11111
                    linkpll_vco_refb    304:301    4b1000
                    linkpll_vco_refn    308:305    4b0010
                    linkpll_vco_refp    312:309    4b1100
                    linkpll_vco_var    314:313    2b01
                    linkpll_dldo_mmd_vtune    317:315    3b100
                    rsvd_reg5    325:318    8b00000000
                    rsvd_reg6_12g[7:0]    333:326    8b00000000
                    linkpll_cp_vset[3]    334    1b1
                    rsvd_reg7_3g[7]    335    1b0
                    rsvd_reg7_6g[7:0]    343:336    8b00000000
                    rsvd_reg7_12g[7:0]    351:344    8b00000000            
        '''
        
        #write_shadow_data = [0 for i in range(128)] 
        write_shadow_data = [0 for i in range(128)] 
        shadow_data = [0 for i in range(128)] 
        
        a0=(d2a_trim_bg_reg + (d2a_trim_bg_ts_vr_sel<<3)+((dev_id&7)<<5))&0xFF
        a1=((dev_id>>3)+((silicon_vid&0x7)<<5))&0xFF
        a2=((silicon_vid>>3)+((kswing_done_12g&0x07)<<5))&0xFF
        a3=((kswing_done_12g>>3)+((bcrx_lpfc_dac_12g&0x1f)<<3))&0xff
        a4=((bcrx_lpfc_dac_12g>>5)+((kswing_done_6g&0x1f)<<3))&0xFF
        a5=((kswing_done_6g>>5)+((bcrx_lpfc_dac_6g&0x7F)<<1))&0xFF
        a6=((bcrx_lpfc_dac_6g>>7)+(kswing_done_3g<<1)+((bcrx_lpfc_dac_3g&0x1)<<7))&0xFF
        a7=((bcrx_lpfc_dac_3g>>1)+((adc_offset&0x1)<<7))&0xFF
        a8=((adc_offset>>1)+(tx_bit_order_8b10b<<6)+(rx_bit_order_8b10b<<7))&0xFF
        a9=(bist_bypass+(bypass_resid_calib<<1)+((resid_dac_offset&0x3f)<<2))&0xFF
        
        a10=((resid_dac_offset>>6)+(resid_dac_override<<2)+((bcrx_lpfc_12g_66b&0x1f)<<3))&0xff
        a11=((bcrx_lpfc_12g_66b>>5)+((bcrx_lpfc_6g_66b&0x1f)<<3))&0xff
        a12=((bcrx_lpfc_6g_66b>>5)+((bcrx_lpfc_3g_66b&0x1f)<<3))&0xff
        a13=((bcrx_lpfc_3g_66b>>5)+((bcrx_picode_step&0x0f)<<3)+(bcrx_comp_bw<<7))&0xff
        
        a14=(bcrx_csel_txp +(bcrx_csel_txn<<2)+(bcrx_comp_vcm<<4)+((rsvd_reg0&0x01)<<7))&0xff
        a15=((rsvd_reg0>>1)+((rsvd_reg1&0x01)<<7))&0xff
        a16=((rsvd_reg1>>1)+((rsvd_reg4&0x01)<<7))&0xff
        a17=((rsvd_reg4>>1)+((bcrx_aldo_vset&0x01)<<7))&0xff
        a18=((bcrx_aldo_vset>>1)+(txldockgen<<2)+(serldo_vset<<5))&0xff
        a19=(tx_ldodriver+((tx_idriver&0x07)<<5))&0xff
        a20=((tx_idriver>>3)+(tx_idriver_dac<<1)+(tx_dac_bias_en<<5)+(tx_bias_en<<6)+(tx_normal_bias_en<<7))&0xff
        a21=(efuse_ana_reg_en + (tx_link_polar<<4) + (rx_link_polar<<5) + (lpf_csel_txp_coax<<6)) & 0xff   #efuse shadow value transfer into analog
        a22=(lpf_csel_txn_coax+((rsvd_reg0_coax&0x3f)<<2))&0xff
        a23=(((rsvd_reg0_coax&0xc0)>>6)+(tx_k_pre_3g<<2)+((tx_k_pre_6g&0x1)<<7))&0xff
        a24=(((tx_k_pre_6g&0x1e)>1)+((tx_k_pre_12g&0x0f)<<4))&0xff
        a25=(((tx_k_pre_12g&0x10)>>4)+(tx_k_post_3g<<1)+((tx_k_post_6g&0x3)<<6))&0xff
        a26=(((tx_k_post_6g&0x1c)>>2)+(tx_k_post_12g<<3))&0xff
        a27=(tx_sign_pre+(tx_sign_main<<1)+(tx_sign_post<<2)+(tx_backcali_en<<3)+(tx_override_en<<4)+(bcrx_cktree_div<<5)+((null&0x1)<<7))&0xff
        a28=(((null&0x6)>>1)+((rsvd_reg6_3g&0x3f)<<2))&0xff
        a29=(((rsvd_reg6_3g&0xc0)>>6)+(tx_driver_bias_en<<2)+(bcrx_en_kp<<3)+(bcrx_en_kf<<4)+(bcrx_en_picode_offset<<5)+(rsvd_reg6_6g_bit0<<6)+((bcrx_kp&0x1)<<7))&0xff
        a30=(((bcrx_kp&0x2)>>1)+(bcrx_kf<<1)+((bcrx_kmr&0xf)<<4))&0xff
        a31=(((bcrx_kmr&0x10)>>4)+(rsvd_reg6_6g_bit1to7<<1))&0xff
        a32=(rsvd_reg7_3g_bit0to6+((bcrx_pi_input_slewctr&0x1)<<7))&0xff
        a33=(((bcrx_pi_input_slewctr&0x2)>>1)+(bcrx_pi_output_slewctr<<1)+(xtal_gm<<3)+(digldo_vset<<5))&0xff
        a34=(linkpll_aldo_vtune+(linkpll_dldo_vtune<<3)+(linkpll_pfd_dly<<6))&0xff
        a35=(linkpll_cp_ictrl+(linkpll_cp_vset<<3)+(linkpll_lpf_c2<<6))&0xff
        a36=(linkpll_lpf_c3+(linkpll_lpf_r1<<2)+(linkpll_lpf_r3<<5))&0xff
        a37=(linkpll_vco_idc+((linkpll_vco_refb&0x7)<<5))&0xff
        a38=(((linkpll_vco_refb&0x8)>>3)+(linkpll_vco_refn<<1)+((linkpll_vco_refp&0x7)<<5))&0xff
        a39=(((linkpll_vco_refp&0x8)>>3)+(linkpll_vco_var<<1)+(linkpll_dldo_mmd_vtune<<3)+((rsvd_reg5&0x3)<<6))&0xff
        a40=(((rsvd_reg5&0xfc)>>2)+((rsvd_reg6_12g&0x3)<<6))&0xff
        a41=(((rsvd_reg6_12g&0xfc)>>2)+(linkpll_cp_vset_bit3<<6)+(rsvd_reg7_3g_bit7<<7))&0xff
        a42=rsvd_reg7_6g
        a43=rsvd_reg7_12g
        
        
        raw_data = [a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15,a16,a17,a18,a19,a20,a21,a22,a23,a24,a25,a26,a27,a28,a29,a30,a31,a32,a33,a34,a35,a36,a37,a38,a39,a40,a41,a42,a43]
        # raw_data = [a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15,a16,a17,a18,a19,a20,a21,a22,a23,a24,a25,a26,a27,a28,a29,a30,a31,a32,a33,a34,a35,a36,a37,a38,a39,a40,a41,a42]
        print("raw_data = ",raw_data)
        
        for i in range(44,128,1):   #including all 128bytes(equal to 128words)
            shadow_data[i]=0
        
        NUM_WORDS = 12  #raw data map to 0~47words 
        print('rd_efuse_int_st is: ', self.m2c.rd_efuse_int_st())
        
        # enable interrupt flag
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1)   
    
        for word in range(11): # max 47 raw words
            ecc_word_raw = ''
            for byte in range(4):  # 4 bytes for each word
                # if (word*4+byte<43):
                #     print('write raw expected: shadow: ', word*4+byte, ', value: ', raw_data[word*4+byte])
                #     self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=raw_data[word*4+byte])  # write raw data
                #     write_shadow_data[word*4+byte] = raw_data[word*4+byte]
                #
                #     print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', raw_data[word*4+byte])
                #     self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=raw_data[word*4+byte])  # write double data
                #     write_shadow_data[(word+NUM_WORDS)*4+byte] = raw_data[word*4+byte]
                #     tran = str(bin(raw_data[word*4+byte])[2:])
                
                if ((word*4+byte)==0):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a0)  # write raw data
                    write_shadow_data[word*4+byte] = a0
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a0)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a0
                    tran = str(bin(a0)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a0:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==1):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a1)  # write raw data
                    write_shadow_data[word*4+byte] = a1
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a1)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a1
                    tran = str(bin(a1)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a1:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==2):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a2)  # write raw data
                    write_shadow_data[word*4+byte] = a2
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a2)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a2
                    tran = str(bin(a2)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a2:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==3):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a3)  # write raw data
                    write_shadow_data[word*4+byte] = a3
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a3)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a3
                    tran = str(bin(a3)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a3:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==4):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a4)  # write raw data
                    write_shadow_data[word*4+byte] = a4
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a4)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a4
                    tran = str(bin(a4)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a4:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==5):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a5)  # write raw data
                    write_shadow_data[word*4+byte] = a5
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a5)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a5
                    tran = str(bin(a5)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a5:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==6):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a6)  # write raw data
                    write_shadow_data[word*4+byte] = a6
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a6)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a6
                
                    tran = str(bin(a6)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a6:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==7):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a7)  # write raw data
                    write_shadow_data[word*4+byte] = a7
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a7)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a7
                    tran = str(bin(a7)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a7:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==8):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a8)  # write raw data
                    write_shadow_data[word*4+byte] = a8
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a8)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a8
                
                    tran = str(bin(a8)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a8:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==9):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a9)  # write raw data
                    write_shadow_data[word*4+byte] = a9
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a9)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a9
                
                    tran = str(bin(a9)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a9:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==10):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a10)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a10)  # write raw data
                    write_shadow_data[word*4+byte] = a10
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a10)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a10)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a10
                
                    tran = str(bin(a10)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a10:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==11):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a11)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a11)  # write raw data
                    write_shadow_data[word*4+byte] = a11
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a11)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a11)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a11
                
                    tran = str(bin(a11)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a11:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==12):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a12)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a12)  # write raw data
                    write_shadow_data[word*4+byte] = a12
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a12)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a12)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a12
                
                    tran = str(bin(a12)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a12:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==13):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a13)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a13)  # write raw data
                    write_shadow_data[word*4+byte] = a13
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a13)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a13)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a13
                
                    tran = str(bin(a13)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a13:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==14):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a14)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a14)  # write raw data
                    write_shadow_data[word*4+byte] = a14
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a14)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a14)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a14
                
                    tran = str(bin(a14)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a14:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==15):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a15)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a15)  # write raw data
                    write_shadow_data[word*4+byte] = a15
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a15)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a15)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a15
                
                    tran = str(bin(a15)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a15:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==16):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a16)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a16)  # write raw data
                    write_shadow_data[word*4+byte] = a16
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a16)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a16)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a16
                
                    tran = str(bin(a16)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a16:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==17):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a17)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a17)  # write raw data
                    write_shadow_data[word*4+byte] = a17
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a17)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a17)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a17
                
                    tran = str(bin(a17)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a17:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==18):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a18)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a18)  # write raw data
                    write_shadow_data[word*4+byte] = a18
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a18)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a18)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a18
                
                    tran = str(bin(a18)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a18:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==19):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a19)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a19)  # write raw data
                    write_shadow_data[word*4+byte] = a19
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a19)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a19)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a19
                
                    tran = str(bin(a19)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a19:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==20):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a20)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a20)  # write raw data
                    write_shadow_data[word*4+byte] = a20
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a20)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a20)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a20
                
                    tran = str(bin(a20)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a20:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==21):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a21)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a21)  # write raw data
                    write_shadow_data[word*4+byte] = a21
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a21)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a21)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a21
                
                    tran = str(bin(a21)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a21:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                
                elif ((word*4+byte)==22):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a22)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a22)  # write raw data
                    write_shadow_data[word*4+byte] = a22
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a22)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a22)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a22
                
                    tran = str(bin(a22)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a22:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')  
                elif ((word*4+byte)==23):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a23)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a23)  # write raw data
                    write_shadow_data[word*4+byte] = a23
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a23)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a23)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a23
                
                    tran = str(bin(a23)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a23:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data') 
                elif ((word*4+byte)==24):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a24)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a24)  # write raw data
                    write_shadow_data[word*4+byte] = a24
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a24)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a24)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a24
                
                    tran = str(bin(a24)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a24:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data') 
                elif ((word*4+byte)==25):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a25)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a25)  # write raw data
                    write_shadow_data[word*4+byte] = a25
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a25)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a25)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a25
                
                    tran = str(bin(a25)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a25:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data') 
                elif ((word*4+byte)==26):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a26)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a26)  # write raw data
                    write_shadow_data[word*4+byte] = a26
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a26)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a26)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a26
                
                    tran = str(bin(a26)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a26:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data') 
                elif ((word*4+byte)==27):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a27)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a27)  # write raw data
                    write_shadow_data[word*4+byte] = a27
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a27)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a27)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a27
                
                    tran = str(bin(a27)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a27:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data') 
                elif ((word*4+byte)==28):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a28)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a28)  # write raw data
                    write_shadow_data[word*4+byte] = a28
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a28)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a28)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a28
                
                    tran = str(bin(a28)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a28:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data') 
                elif ((word*4+byte)==29):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a29)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a29)  # write raw data
                    write_shadow_data[word*4+byte] = a29
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a29)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a29)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a29
                
                    tran = str(bin(a29)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a29:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data') 
                elif ((word*4+byte)==30):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a30)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a30)  # write raw data
                    write_shadow_data[word*4+byte] = a30
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a30)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a30)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a30
                
                    tran = str(bin(a30)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a30:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==31):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a31)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a31)  # write raw data
                    write_shadow_data[word*4+byte] = a31
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a31)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a31)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a31
                
                    tran = str(bin(a31)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a31:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==32):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a32)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a32)  # write raw data
                    write_shadow_data[word*4+byte] = a32
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a32)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a32)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a32
                
                    tran = str(bin(a32)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a32:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==33):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a33)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a33)  # write raw data
                    write_shadow_data[word*4+byte] = a33
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a33)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a33)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a33
                
                    tran = str(bin(a33)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a33:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==34):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a34)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a34)  # write raw data
                    write_shadow_data[word*4+byte] = a34
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a34)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a34)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a34
                
                    tran = str(bin(a34)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a34:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==35):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a35)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a35)  # write raw data
                    write_shadow_data[word*4+byte] = a35
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a35)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a35)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a35
                
                    tran = str(bin(a35)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a35:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==36):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a36)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a36)  # write raw data
                    write_shadow_data[word*4+byte] = a36
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a36)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a36)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a36
                
                    tran = str(bin(a36)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a36:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==37):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a37)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a37)  # write raw data
                    write_shadow_data[word*4+byte] = a37
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a37)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a37)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a37
                
                    tran = str(bin(a37)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a37:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==38):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a38)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a38)  # write raw data
                    write_shadow_data[word*4+byte] = a38
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a38)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a38)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a38
                
                    tran = str(bin(a38)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a38:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==39):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a39)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a39)  # write raw data
                    write_shadow_data[word*4+byte] = a39
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a39)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a39)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a39
                
                    tran = str(bin(a39)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a39:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==40):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a40)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a40)  # write raw data
                    write_shadow_data[word*4+byte] = a40
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a40)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a40)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a40
                
                    tran = str(bin(a40)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a40:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==41):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a41)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a41)  # write raw data
                    write_shadow_data[word*4+byte] = a41
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a41)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a41)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a41
                
                    tran = str(bin(a41)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a41:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==42):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a42)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a42)  # write raw data
                    write_shadow_data[word*4+byte] = a42
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a42)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a42)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a42
                
                    tran = str(bin(a42)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a42:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                #
                elif ((word*4+byte)==43):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a43)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a43)  # write raw data
                    write_shadow_data[word*4+byte] = a43
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a43)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a43)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a43
                
                    tran = str(bin(a43)[2:])
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                
                    if rd_raw_shadow_data != a43:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                else:
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=word*4+byte)  # write raw data
                    write_shadow_data[word*4+byte] = word*4+byte
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=word*4+byte)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = word*4+byte
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != word*4+byte:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                    tran = str(bin(word*4+byte)[2:])
                    
                cd = tran.zfill(8)
                print('cd: ', cd)
                ne = ''
                for mm in range(8):
                    ne += cd[7-mm]
                ecc_word_raw += ne
            print('ecc_word(str) is: ', ecc_word_raw)
            
            ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
            print('ECC shadow: ', word+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
            
            write_shadow_data[word+8*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
            rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+8*NUM_WORDS)
            print('read raw ecc: shadow: ', word+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
            
            write_shadow_data[word+9*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
            rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+9*NUM_WORDS)
            print('read double ecc: shadow: ', word+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)
            
            if ecc_shadow_expect != rd_raw_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to ecc_shadow_expect')
            elif rd_raw_ecc_shadow_data != rd_double_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to rd_double_ecc_shadow_data')
        
        print('write_shadow_data is: ', write_shadow_data)
        
        crc_shadow_expect = self.EfuseCRCGen(write_shadow_data)
        print('crc_shadow_expect: ', crc_shadow_expect)
        
        # write CRC & calculate ECC for word 47
        ecc_word_raw = ''
        for byte in range(4):
            write_shadow_data[11*4+byte] = crc_shadow_expect[byte]
            self.m2c.wr_efuse_shadow_data_fields(i=11*4+byte, shadow_data=crc_shadow_expect[byte])  # write CRC data
            rd_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(11*4+byte)
            print('read crc: shadow: ', 11*4+byte, ', value: ', rd_crc_shadow_data)
            
            self.m2c.wr_efuse_shadow_data_fields(i=(11+NUM_WORDS)*4+byte, shadow_data=crc_shadow_expect[byte])  # write double data
            write_shadow_data[(11+NUM_WORDS)*4+byte] = crc_shadow_expect[byte]
            
            rd_double_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((11+NUM_WORDS)*4+byte)
            print('read double: shadow: ', (11+NUM_WORDS)*4+byte, ', value: ', rd_double_crc_shadow_data)
            
            if rd_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_crc_shadow_data misalign to ecc_shadow_expect')
            
            if rd_double_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_double_crc_shadow_data misalign to ecc_shadow_expect')
            
            tran = str(bin(crc_shadow_expect[byte])[2:])
            cd = tran.zfill(8)
            print('cd: ', cd)
            ne = ''
            for mm in range(8):
                ne += cd[7-mm]
            ecc_word_raw += ne
        print('ecc_word(str) is: ', ecc_word_raw)
        
        ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
        print('ECC shadow: ', 11+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
        
        write_shadow_data[11+8*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
        rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+8*NUM_WORDS)
        print('read raw ecc: shadow: ', 11+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
        
        write_shadow_data[11+9*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
        rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+9*NUM_WORDS)
        print('read double ecc: shadow: ', 11+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)    
            
        shadow_data_beforeefuse = self.EfuseShadowRecord()
        
        write_shadow_data[31*4] = efuse_lock_out
        self.m2c.wr_efuse_shadow_data_fields(i=31*4, shadow_data=efuse_lock_out)  # 0x38 to lock out, whether to lock out
        
        print('write_shadow_data is: ', write_shadow_data)
        
        pgm = True  # enable program?
        if pgm==True:
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage 
            
            # raw fpgm
            self.m2c.wr_efuse_pgm_start(0)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(31)  # 46
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status!=0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            time.sleep(1)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            '''
            
            # double fpgm
            self.m2c.wr_efuse_pgm_start(0+48)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46+48)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # CRC Ppgm
            self.m2c.wr_efuse_partial_addr(0x47)  # Partial read/program address, in word, A[6:0].
            for i in range(4):
                self.m2c.wr_efuse_ppgm_data(i, crc_shadow_expect[i])
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # ECC and remaining section
            self.m2c.wr_efuse_pgm_start(NUM_WORDS*2-1)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(127)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            '''
            # lock key efuse
            self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status!=0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            time.sleep(1)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage 
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.
            
        return shadow_data_beforeefuse
    
    def EfusePgm_S68R5_Verify(self, d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,dev_id=0,silicon_vid=1,\
                              kswing_done_12g=0, bcrx_lpfc_dac_12g=175,kswing_done_6g=0, bcrx_lpfc_dac_6g=175,\
                              kswing_done_3g=6, bcrx_lpfc_dac_3g=175,adc_offset=0,tx_bit_order_8b10b=0,\
                              rx_bit_order_8b10b=0,bist_bypass=1,bypass_resid_calib=1,resid_dac_offset=0,\
                              resid_dac_override=0,bcrx_lpfc_12g_66b=160,bcrx_lpfc_6g_66b=160,\
                              bcrx_lpfc_3g_66b=160,bcrx_picode_step=4,bcrx_comp_bw=1,bcrx_csel_txp=0,\
                              bcrx_csel_txn=0,bcrx_comp_vcm=3,rsvd_reg0=0,rsvd_reg1=0,rsvd_reg4=0,\
                              bcrx_aldo_vset=4,txldockgen=4,serldo_vset=4,tx_ldodriver=31,tx_idriver=9,\
                              tx_idriver_dac=8,tx_dac_bias_en=0,tx_bias_en=1,tx_normal_bias_en=1,efuse_trim=9,efuse_lock_out=0x0):
        
        '''
        
        input:
                input:
                d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,
                dev_id=0,silicon_vid=0,
                kswing_done_12g=0, bcrx_lpfc_dac_12g=160,kswing_done_6g=0, bcrx_lpfc_dac_6g=160,kswing_done_3g=0, bcrx_lpfc_dac_3g=160,
                adc_offset=0,tx_bit_order_8b10b=0,rx_bit_order_8b10b=0,
                bist_bypass=1,bypass_resid_calib=1,resid_dac_offset=0,resid_dac_override=0,
                bcrx_lpfc_12g_66b=160,bcrx_lpfc_6g_66b=160,bcrx_lpfc_3g_66b=160,
                bcrx_picode_step=4,bcrx_comp_bw=1,bcrx_csel_txp=3,bcrx_csel_txn=0,
                bcrx_comp_vcm=3,rsvd_reg0=0,rsvd_reg1=0,rsvd_reg4=0,bcrx_aldo_vset=4,txldockgen=4,serldo_vset=4,tx_ldodriver=4,
                tx_idriver=9,tx_idriver_dac=8,
                tx_dac_bias_en=0,tx_bias_en=1,tx_normal_bias_en=1
                
                
            M66S68R5 efuse descriptions:
                efuse_assign
                    d2a sig efuse a[11:10]= 2'b00; // shrink from 128 to 32
                    "d2a trim bg reg = efuse trim value[2:0];
                    //d2a trim bg ts vr sel default 2'b0l, it is done by add an inverter"
                    d2a trim bg ts_vr sel = {efuse trim value[4],~efuse trim value[3]};
                    dev id[7:0] = efuse trim value[12:5];
                    silicon_vid[7:0]= efuse trim value[20:13];
                    tx_k_swing_down_12g[5:0]= efuse_trim_value[26:21];
                    bcrx_lpfc_dacctr_12g[7:0]= efuse_trim_value[34:27];// 8B/10B
                    txk_swing_down_6g[5:0]= efuse_trim_value[40:35];
                    bcrx_lpfc_dacctr_6g[7:0]= efuse_trim_valúe[48:41];// 8B/10B
                    tx_k_swing_down_3g[5:0]= efuse_trim_walue[54:49];
                    bcrx_lpfc_dacctr_3g[7:0]= efuse_trim_value[62:55];// 8B/10B
                    adc_offset[6:0]= efuse_trim_value[69:63];
                    tx_bit_order_8bl0b = efuse_trim_value[70]
                    rx_bit_order_8bl0b = efuse_trim value[71];
                    bist_bypass = efuse_trim_value[72];
                    bypass resid calib=~efuse trim value[73];
                    
                    resid dac offset = efuse trim value[81:74];
                    resid dac override =efuse trim value[82];
                    bcrx lpfc dacctr 12g 66b[7:0]= efuse trim value[90:83]: // 66B
                    bcrx lpfc dacctr 6g 66b[7:0]= efuse trim value[98:91];// 66B
                    bcrx lpfc dacctr 3g 66b[7:0]= efuse trim value[106:99];// 66B
                    d2a bcrx picode step trim = efuse trim value[110:107];
                    
                    d2a bcrx comp bw sel trim = efuse trim value[111];
                    d2a bcrx lpf csel txp lpf trim = efúse trim value[113:112];
                    d2a bcrx lpf csel txn lpf trim = efuse trim value[115:114];
                    d2a bcrx comp vcm sel trim= efuse trim value[118:116];
                    d2a rsvd reg0 trim= efuse trim value[126:119];
                    d2a rsvd regl trim = efuse trim value[134:127];
                    d2a rsvd reg4 trim= efuse trim value[142:135];
                    d2a bcrx aldo vset trim= efuse trim value[145:143];
                    d2a txldockgen vout trim= efuse trim value[148:146];
                    d2a tx serldo vset trim = efuse trim value[151:149];
                    d2a tx ldodriver14 vout trim = efuse trim value[156:152];
                    d2a tx idriver trim =efuse trim value[160:157];
                    d2a tx idriver dac trim = efuse trim value[164:161];
                    d2a tx dac bias en trim= efuse trim value[165];
                    d2a tx bias en trim= efuse trim value[166];
                    d2a tx normal bias en trim= efuse trim value[167];
                    
                input:
                    d2a_trim_bg_reg=0, d2a_trim_bg_ts_vr_sel=0,
                    dev_id=0,silicon_vid=0,
                    kswing_done_12g=0, bcrx_lpfc_dac_12g=160,kswing_done_6g=0, bcrx_lpfc_dac_6g=160,kswing_done_3g=0, bcrx_lpfc_dac_3g=160,
                    adc_offset=0,tx_bit_order_8b10b=0,rx_bit_order_8b10b=0,
                    bist_bypass=1,bypass_resid_calib=1,resid_dac_offset=0,resid_dac_override=0,
                    bcrx_lpfc_12g_66b=160,bcrx_lpfc_6g_66b=160,bcrx_lpfc_3g_66b=160,
                    bcrx_picode_step=4,bcrx_comp_bw=1,bcrx_csel_txp=3,bcrx_csel_txn=0,
                    bcrx_comp_vcm=3,rsvd_reg0=0,rsvd_reg1=0,rsvd_reg4=0,bcrx_aldo_vset=4,txldockgen=4,serldo_vset=4,tx_ldodriver=4,
                    tx_idriver=9,tx_idriver_dac=8,
                    tx_dac_bias_en=0,tx_bias_en=1,tx_normal_bias_en=1
            
        '''
        
        #write_shadow_data = [0 for i in range(128)] 
        write_shadow_data = [0 for i in range(512)] 
        shadow_data = [0 for i in range(512)] 
        
        a0=(d2a_trim_bg_reg + (d2a_trim_bg_ts_vr_sel<<3)+((dev_id&7)<<5))&0xFF
        a1=((dev_id>>3)+((silicon_vid&0x7)<<5))&0xFF
        a2=((silicon_vid>>3)+((kswing_done_12g&0x07)<<5))&0xFF
        a3=((kswing_done_12g>>3)+((bcrx_lpfc_dac_12g&0x1f)<<3))&0xff
        a4=((bcrx_lpfc_dac_12g>>5)+((kswing_done_6g&0x1f)<<3))&0xFF
        a5=((kswing_done_6g>>5)+((bcrx_lpfc_dac_6g&0x7F)<<1))&0xFF
        a6=((bcrx_lpfc_dac_6g>>7)+(kswing_done_3g<<1)+((bcrx_lpfc_dac_3g&0x1)<<7))&0xFF
        a7=((bcrx_lpfc_dac_3g>>1)+((adc_offset&0x1)<<7))&0xFF
        a8=((adc_offset>>1)+(tx_bit_order_8b10b<<6)+(rx_bit_order_8b10b<<7))&0xFF
        a9=(bist_bypass+(bypass_resid_calib<<1)+((resid_dac_offset&0x3f)<<2))&0xFF
        
        a10=((resid_dac_offset>>6)+(resid_dac_override<<2)+((bcrx_lpfc_12g_66b&0x1f)<<3))&0xff
        a11=((bcrx_lpfc_12g_66b>>5)+((bcrx_lpfc_6g_66b&0x1f)<<3))&0xff
        a12=((bcrx_lpfc_6g_66b>>5)+((bcrx_lpfc_3g_66b&0x1f)<<3))&0xff
        a13=((bcrx_lpfc_3g_66b>>5)+((bcrx_picode_step&0x0f)<<3)+(bcrx_comp_bw<<7))&0xff
        
        a14=(bcrx_csel_txp +(bcrx_csel_txn<<2)+(bcrx_comp_vcm<<4)+((rsvd_reg0&0x01)<<7))&0xff
        a15=((rsvd_reg0>>1)+((rsvd_reg1&0x01)<<7))&0xff
        a16=((rsvd_reg1>>1)+((rsvd_reg4&0x01)<<7))&0xff
        a17=((rsvd_reg4>>1)+((bcrx_aldo_vset&0x01)<<7))&0xff
        a18=((bcrx_aldo_vset>>1)+(txldockgen<<2)+(serldo_vset<<5))&0xff
        a19=(tx_ldodriver+((tx_idriver&0x07)<<5))&0xff
        a20=((tx_idriver>>3)+(tx_idriver_dac<<1)+(tx_dac_bias_en<<5)+(tx_bias_en<<6)+(tx_normal_bias_en<<7))&0xff
        
        a21= efuse_trim & 0xff   #efuse shdow value transfer into analog
        
        raw_data = [a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15,a16,a17,a18,a19,a20,a21]
        print("raw_data = ",raw_data)
        
        for i in range(22,512,1):   #including all 128bytes(equal to 128words)
            shadow_data[i]=0
        
        NUM_WORDS = 12  #raw data map to 0~47words 
        print('rd_efuse_int_st is: ', self.m2c.rd_efuse_int_st())
        
        # enable interrupt flag
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1)   
    
        for word in range(11): # max 47 raw words
            ecc_word_raw = ''
            for byte in range(4):  # 4 bytes for each word
                if ((word*4+byte)==0):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a0)  # write raw data
                    write_shadow_data[word*4+byte] = a0
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a0)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a0)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a0
                    tran = str(bin(a0)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a0:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==1):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a1)  # write raw data
                    write_shadow_data[word*4+byte] = a1
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a1)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a1)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a1
                    tran = str(bin(a1)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a1:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==2):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a2)  # write raw data
                    write_shadow_data[word*4+byte] = a2
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a2)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a2)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a2
                    tran = str(bin(a2)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a2:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==3):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a3)  # write raw data
                    write_shadow_data[word*4+byte] = a3
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a3)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a3)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a3
                    tran = str(bin(a3)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a3:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==4):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a4)  # write raw data
                    write_shadow_data[word*4+byte] = a4
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a4)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a4)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a4
                    tran = str(bin(a4)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a4:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==5):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a5)  # write raw data
                    write_shadow_data[word*4+byte] = a5
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a5)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a5)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a5
                    tran = str(bin(a5)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a5:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==6):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a6)  # write raw data
                    write_shadow_data[word*4+byte] = a6
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a6)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a6)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a6
                    
                    tran = str(bin(a6)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a6:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==7):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a7)  # write raw data
                    write_shadow_data[word*4+byte] = a7
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a7)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a7)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a7
                    tran = str(bin(a7)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a7:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==8):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a8)  # write raw data
                    write_shadow_data[word*4+byte] = a8
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a8)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a8)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a8
                    
                    tran = str(bin(a8)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a8:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==9):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a9)  # write raw data
                    write_shadow_data[word*4+byte] = a9
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a9)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a9)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a9
                    
                    tran = str(bin(a9)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a9:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==10):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a10)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a10)  # write raw data
                    write_shadow_data[word*4+byte] = a10
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a10)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a10)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a10
                    
                    tran = str(bin(a10)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a10:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')

                elif ((word*4+byte)==11):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a11)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a11)  # write raw data
                    write_shadow_data[word*4+byte] = a11
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a11)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a11)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a11
                    
                    tran = str(bin(a11)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a11:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')

                elif ((word*4+byte)==12):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a12)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a12)  # write raw data
                    write_shadow_data[word*4+byte] = a12
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a12)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a12)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a12
                    
                    tran = str(bin(a12)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a12:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==13):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a13)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a13)  # write raw data
                    write_shadow_data[word*4+byte] = a13
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a13)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a13)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a13
                    
                    tran = str(bin(a13)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a13:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==14):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a14)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a14)  # write raw data
                    write_shadow_data[word*4+byte] = a14
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a14)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a14)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a14
                    
                    tran = str(bin(a14)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a14:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==15):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a15)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a15)  # write raw data
                    write_shadow_data[word*4+byte] = a15
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a15)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a15)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a15
                    
                    tran = str(bin(a15)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a15:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==16):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a16)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a16)  # write raw data
                    write_shadow_data[word*4+byte] = a16
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a16)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a16)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a16
                    
                    tran = str(bin(a16)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a16:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==17):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a17)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a17)  # write raw data
                    write_shadow_data[word*4+byte] = a17
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a17)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a17)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a17
                    
                    tran = str(bin(a17)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a17:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==18):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a18)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a18)  # write raw data
                    write_shadow_data[word*4+byte] = a18
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a18)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a18)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a18
                    
                    tran = str(bin(a18)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a18:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==19):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a19)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a19)  # write raw data
                    write_shadow_data[word*4+byte] = a19
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a19)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a19)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a19
                    
                    tran = str(bin(a19)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a19:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                elif ((word*4+byte)==20):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a20)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a20)  # write raw data
                    write_shadow_data[word*4+byte] = a20
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a20)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a20)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a20
                    
                    tran = str(bin(a20)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a20:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                elif ((word*4+byte)==21):
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', a21)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=a21)  # write raw data
                    write_shadow_data[word*4+byte] = a21
                    
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', a21)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=a21)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = a21
                    
                    tran = str(bin(a21)[2:])
                    
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != a21:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                   
                else:
                    print('write raw expected: shadow: ', word*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=word*4+byte, shadow_data=word*4+byte)  # write raw data
                    write_shadow_data[word*4+byte] = word*4+byte
                
                    print('write double expected: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', word*4+byte)
                    self.m2c.wr_efuse_shadow_data_fields(i=(word+NUM_WORDS)*4+byte, shadow_data=word*4+byte)  # write double data
                    write_shadow_data[(word+NUM_WORDS)*4+byte] = word*4+byte
                
                    rd_raw_shadow_data =self.m2c.rd_efuse_shadow_data_shadow_data(word*4+byte)
                    print('read raw: shadow: ', word*4+byte, ', value: ', rd_raw_shadow_data)
                    rd_double_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((word+NUM_WORDS)*4+byte)
                    print('read double: shadow: ', (word+NUM_WORDS)*4+byte, ', value: ', rd_double_shadow_data)
                    
                    if rd_raw_shadow_data != word*4+byte:
                        raise('rd_raw_shadow_data misalign to write raw data')
                    if rd_raw_shadow_data != rd_double_shadow_data:
                        raise('rd_raw_shadow_data misalign to rd_double_shadow_data')
                    
                    tran = str(bin(word*4+byte)[2:])
                    
                cd = tran.zfill(8)
                print('cd: ', cd)
                ne = ''
                for mm in range(8):
                    ne += cd[7-mm]
                ecc_word_raw += ne
            print('ecc_word(str) is: ', ecc_word_raw)
            
            ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
            print('ECC shadow: ', word+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
            
            write_shadow_data[word+8*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
            rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+8*NUM_WORDS)
            print('read raw ecc: shadow: ', word+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
            
            write_shadow_data[word+9*NUM_WORDS] = ecc_shadow_expect
            self.m2c.wr_efuse_shadow_data_fields(i=word+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
            rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=word+9*NUM_WORDS)
            print('read double ecc: shadow: ', word+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)
            
            if ecc_shadow_expect != rd_raw_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to ecc_shadow_expect')
            elif rd_raw_ecc_shadow_data != rd_double_ecc_shadow_data:
                raise('rd_raw_ecc_shadow_data misalign to rd_double_ecc_shadow_data')
        
        print('write_shadow_data is: ', write_shadow_data)
        
        crc_shadow_expect = self.EfuseCRCGen(write_shadow_data)
        print('crc_shadow_expect: ', crc_shadow_expect)
        
        # write CRC & calculate ECC for word 47
        ecc_word_raw = ''
        for byte in range(4):
            write_shadow_data[11*4+byte] = crc_shadow_expect[byte]
            self.m2c.wr_efuse_shadow_data_fields(i=11*4+byte, shadow_data=crc_shadow_expect[byte])  # write CRC data
            rd_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(11*4+byte)
            print('read crc: shadow: ', 11*4+byte, ', value: ', rd_crc_shadow_data)
            
            self.m2c.wr_efuse_shadow_data_fields(i=(11+NUM_WORDS)*4+byte, shadow_data=crc_shadow_expect[byte])  # write double data
            write_shadow_data[(11+NUM_WORDS)*4+byte] = crc_shadow_expect[byte]
            
            rd_double_crc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data((11+NUM_WORDS)*4+byte)
            print('read double: shadow: ', (11+NUM_WORDS)*4+byte, ', value: ', rd_double_crc_shadow_data)
            
            if rd_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_crc_shadow_data misalign to ecc_shadow_expect')
            
            if rd_double_crc_shadow_data != crc_shadow_expect[byte]:
                raise('rd_double_crc_shadow_data misalign to ecc_shadow_expect')
            
            tran = str(bin(crc_shadow_expect[byte])[2:])
            cd = tran.zfill(8)
            print('cd: ', cd)
            ne = ''
            for mm in range(8):
                ne += cd[7-mm]
            ecc_word_raw += ne
        print('ecc_word(str) is: ', ecc_word_raw)
        
        ecc_shadow_expect = self.EfuseECCGen(ecc_word_raw)
        print('ECC shadow: ', 11+8*NUM_WORDS, ', value: ', ecc_shadow_expect)
        
        write_shadow_data[11+8*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+8*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write raw ecc shadow
        rd_raw_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+8*NUM_WORDS)
        print('read raw ecc: shadow: ', 11+8*NUM_WORDS, ', value: ', rd_raw_ecc_shadow_data)
        
        write_shadow_data[11+9*NUM_WORDS] = ecc_shadow_expect
        self.m2c.wr_efuse_shadow_data_fields(i=11+9*NUM_WORDS, shadow_data=ecc_shadow_expect)  # write double ecc shadow
        rd_double_ecc_shadow_data = self.m2c.rd_efuse_shadow_data_shadow_data(i=11+9*NUM_WORDS)
        print('read double ecc: shadow: ', 11+9*NUM_WORDS, ', value: ', rd_double_ecc_shadow_data)    
            
        shadow_data_beforeefuse = self.EfuseShadowRecord()
        
        write_shadow_data[31*4] = efuse_lock_out
        self.m2c.wr_efuse_shadow_data_fields(i=31*4, shadow_data=efuse_lock_out)  # 0x38 to lock out, whether to lock out
        
        print('write_shadow_data is: ', write_shadow_data)
        
        pgm = True  # enable program?
        if pgm==True:
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- not power down efuse macro.
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.EfuseINTClearStatus()
            self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0)  # ps=1 - Switch power to high program voltage 
            
            # raw fpgm
            self.m2c.wr_efuse_pgm_start(0)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(31)  # 46
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status!=0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            time.sleep(1)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            '''
            # double fpgm
            self.m2c.wr_efuse_pgm_start(0+48)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(46+48)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # CRC Ppgm
            self.m2c.wr_efuse_partial_addr(0x47)  # Partial read/program address, in word, A[6:0].
            for i in range(4):
                self.m2c.wr_efuse_ppgm_data(i, crc_shadow_expect[i])
            self.m2c.wr_efuse_efuse_key(0x3A)  # this key invokes efuse partial programming. This key takes affect only when efuse is unlocked. (EFUSE_PPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            
            
            # ECC and remaining section
            self.m2c.wr_efuse_pgm_start(NUM_WORDS*2-1)  # Full program start address, in word, A[6:0].
            self.m2c.wr_efuse_pgm_end(127)
            self.m2c.wr_efuse_efuse_key(0x9C)  # this key invokes efuse programming. This key takes affect only when efuse is unlocked. (EFUSE_FPGM_KEY)
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            '''
            # lock key efuse
            self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
            status=self.m2c.rd_efuse_int_st_efuse_done_int()
            # while(status!=0):
            #     status=self.m2c.rd_efuse_int_st_efuse_done_int()
            time.sleep(1)
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # ps=1 - Switch power to high program voltage 
            self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
            self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=0- not power down efuse macro.
            
        return shadow_data_beforeefuse
    
    def EfuseStatusCheck(self):
        # bit5-EFUSE_RDY, bit4-EFUSE_BUSY, bit3-REDUNDENCY (Efuse redundency function is enable.), bit2-LOCKOUT
        # bit1-UNLOCK_FAIL, bit0-UNLOCKED
        print('rd_efuse_status: ', hex(self.m2c.rd_efuse_status()))
        
        #States of efuse main controller.
        # 0: Initial power up. (INIT_PWR_UP)
        # 1: Check lock out bit in macro. (CHK_LOCKOUT)
        # 3: Efuse is ready for new operation. (EFUSE_IDLE)
        # 4: Redundency read. (R_READ)
        # 5: Redundency program. (R_PGM)
        # 6: Array read. (A_READ)
        # 7: Array program. (A_PGM)
        # 8: Power control. (PWR_CTRL)
        # 2: Initial power down. (INIT_PWR_DOWN)
        print('rd_efuse_main_st_main_state: ', hex(self.m2c.rd_efuse_main_st_main_state()))
        print('rd_efuse_pgm_st: ', self.m2c.rd_efuse_pgm_st())
        print('rd_efuse_read_st: ', self.m2c.rd_efuse_read_st())
        print('rd_efuse_pwr_st: ', self.m2c.rd_efuse_pwr_st())
        
        print('rd_efuse_int_en: ', self.m2c.rd_efuse_int_en())
        print('rd_efuse_int_st is: ', hex(self.m2c.rd_efuse_int_st()))
        print('rd_efuse_crc_cal[0]: ', self.m2c.rd_efuse_crc_cal(0))
        print('rd_efuse_crc_cal[1]: ', self.m2c.rd_efuse_crc_cal(1))
        print('rd_efuse_crc_gold[0]: ', self.m2c.rd_efuse_crc_gold(0))
        print('rd_efuse_crc_gold[1]: ', self.m2c.rd_efuse_crc_gold(1))
        print('rd_efuse_ecc_1bit_err_addr: ', self.m2c.rd_efuse_ecc_1bit_err_addr())
        print('rd_efuse_ecc_2bit_err_addr: ', self.m2c.rd_efuse_ecc_2bit_err_addr())
        print('rd_efuse_double_err_addr: ', self.m2c.rd_efuse_double_err_addr())
        
        return (self.m2c.rd_efuse_crc_cal(0),self.m2c.rd_efuse_crc_cal(1),self.m2c.rd_efuse_crc_gold(0),self.m2c.rd_efuse_crc_gold(1),self.m2c.rd_efuse_ecc_1bit_err_addr(),self.m2c.rd_efuse_ecc_2bit_err_addr())
        
    def EfuseINTClearStatus(self):
        print('rd_efuse_int_st is: ', self.m2c.rd_efuse_int_st())
        print('ck_st is: ', self.m2c.rd_efuse_chk_st())
        if self.m2c.rd_efuse_chk_st() != 0:
            raise('ck_st error occurred!')
    
    def RedundancyPGM(self, waddr, bitaddr, recselect, expectdata):
        '''
        waddr: word address that need to repair
        bitaddr: which bit need to be repair in a word
        recselect: select which redundancy circuit to repair wrong bit
        expectdata: expect value of repairing bit
        '''
        # enable interrupt flag
        self.m2c.wr_efuse_int_en_fields(efuse_double_err_en=1, efuse_crc_err_en=1, efuse_ecc_2bit_en=1, efuse_ecc_1bit_en=1, efuse_done_en=1)
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(redun_dis = 0, margin = 0, ps = 0, pd = 0)
        self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
        
        self.m2c.wr_efuse_efuse_key(0x51) # this key invokes efuse redundancy reading.(EFUSE_RREAD_KEY)
        
        if (self.m2c.rd_efuse_status_efuse_busy()!=1):
            print ('error: efuse should be in busy status! ')
        
        if (self.m2c.rd_efuse_main_st_main_state()!=4):
            print ('error: efuse main status error', self.m2c.rd_efuse_main_st_main_state())
        
        if (self.m2c.rd_efuse_read_st_read_busy()!=1):
            print ('error: efuse read status error! ')
        
        if (self.m2c.rd_efuse_pgm_st()!=0):
            print ('error: efuse program status error! ')
            
        if (self.m2c.rd_efuse_pwr_st_pwr_busy()!=0):
            print ('error: efuse power status error! ')
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0) # switch efuse macro in high program voltage
            
            
        
        self.m2c.wr_efuse_redundency_waddr_fields(redundency_waddr = waddr)
        self.m2c.wr_efuse_redundency_baddr_fields(redundency_baddr = bitaddr)
        self.m2c.wr_efuse_redundency_bit_ctrl_fields(repair_bits = recselect, redun_disable = 0, repair_data = expectdata, repair_rf = 1)
        
        efuse_redundency_waddr = self.m2c.rd_efuse_redundency_waddr_redundency_waddr()
        efuse_redundency_baddr = self.m2c.rd_efuse_redundency_baddr_redundency_baddr()
        print('efuse_redundancy_waddr is: ', efuse_redundency_waddr)
        print('efuse_redundancy_baddr is: ', efuse_redundency_baddr)
        print('redundancy circuit is: ', self.m2c.rd_efuse_redundency_bit_ctrl_repair_bits())
        print('expect value of reparing bit is: ', self.m2c.rd_efuse_redundency_bit_ctrl_repair_data())
        print('redundancy bit been used flag is: ', self.m2c.rd_efuse_redundency_bit_ctrl_repair_rf())
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- power up efuse macro and efuse macro in low voltage
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.EfuseINTClearStatus()
        
        self.m2c.wr_efuse_efuse_key(0xE4)  # this key unlocks shadow registers for programming. (EFUSE_UNLOCK_KEY)
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=1, pd=0) # switch efuse macro in high program voltage
        self.m2c.wr_efuse_efuse_key(0xF7) # this key invokes efuse redundancy programming (EFUSE_RPGM_KEY)
        
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.m2c.wr_efuse_efuse_key(0x65)  # this key locks shadow registers for programming. (EFUSE_LOCK_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0) # switch efuse macro in normal voltage
        self.m2c.wr_efuse_int_st(0x1f)  # clear interrupt status
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)  # pd=1- power down efuse macro.
        
    def RedundancyRead(self): 
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  # pd=0- power up efuse macro and efuse macro in normal voltage
        self.m2c.wr_efuse_efuse_key(0x51) # this key invokes efuse redundancy reading.(EFUSE_RREAD_KEY)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)
        
    def EfuseFullRefresh(self):
        
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=0)  
        self.m2c.wr_efuse_efuse_key(0x8B)
        self.m2c.wr_efuse_pwr_margin_ctrl_fields(ps=0, pd=1)
        
    def LMN_Status_clear(self,): 
        
        self.m2c.wr_efh_clr_ctrl1_fields(lfm_err1_clr=1, lfm_err0_clr=1) #0x0a35, and lfm_err0/1_clear
        time.sleep(1)
        self.m2c.wr_efh_clr_ctrl1_fields(lfm_err1_clr=0, lfm_err0_clr=0)
        time.sleep(1)
    
    def LMN_Setting_Nocalib(self,lmn0current,lmn1current): 
        
        '''cfg GPIO mode'''
        self.MFNSet(gpio=5, mfn=1)
        self.MFNSet(gpio=6, mfn=1)
        '''need set GPIO is all off,and OEN=1, PU=0, PD=0, AE=0'''
        self.m2c.wr_pinmux_pin_io_ow_ctrl_5(value=0x10)
        self.m2c.wr_pinmux_pin_io_ow_en_5(value=0xFF)
        '''need set GPIO is all off,and OEN=1, PU=0, PD=0, AE=0'''
        self.m2c.wr_pinmux_pin_io_ow_ctrl_6(value=0x10)
        self.m2c.wr_pinmux_pin_io_ow_en_6(value=0xFF)
        
        '''set lmn0/1 current driver for monitor function'''
        self.m2c.wr_ana_d2a_lf_regnamedel0_fields(sel_lf0_bus0 = lmn0current & 0xFF) #regi 0x0baa lmn0_lsb_bit[7:0]
        self.m2c.wr_ana_d2a_lf_regnamedel2_fields(sel_lf1_bus0= lmn1current & 0xFF) #regi 0x0bac lmn1_lsb_bit[7:0]
        self.m2c.wr_ana_d2a_lf_regnamedel1_fields(sel_lf1_bus1=(lmn1current>>8)&0x07, sel_lf0_bus1=(lmn0current>>8)&0x07) #regi 0x0bab lmn0/1_msb_bit[2:0]
        print ('lmn0 current set is: ', self.m2c.rd_ana_d2a_lf_regnamedel1_sel_lf0_bus1()*256 + self.m2c.rd_ana_d2a_lf_regnamedel0_sel_lf0_bus0())
        print ('lmn1 current set is: ', self.m2c.rd_ana_d2a_lf_regnamedel1_sel_lf1_bus1()*256 + self.m2c.rd_ana_d2a_lf_regnamedel2_sel_lf1_bus0())
        
        '''enable line fault monitor function0 and 1'''
        print('\n')
        print('the lmn0 powerup before setting is ',self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf0(),' ,and the lmn1 powerup before setting is ',self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf1())
        self.m2c.wr_ana_d2a_lf_regnamedel3_fields(powerup_lf1=1, powerup_lf0=1)
        powerup_lf1=self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf1()
        powerup_lf0=self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf0()
        print('the lmn0 powerup after setting is ',powerup_lf0,' ,and the lmn1 powerup after setting is ',powerup_lf1)
        time.sleep(1)
        
        '''read back on lfm_err1/0_status from efh module, '''
        LFM_err1_status =   self.m2c.rd_efh_l0_status1_lfm_err1_status()
        LFM_err0_status =   self.m2c.rd_efh_l0_status1_lfm_err0_status()
        print ('LFM_err1_status is: ',LFM_err1_status )#0x0a39 
        print ('LFM_err0_status is: ',LFM_err0_status)
        
        '''read back on Line fault cause(sticky),x00e01[4:0]'''
        LF0_sticky_from_digital =   hex(self.m2c.rd_lf_status0_cause(i=0))
        LF1_sticky_from_digital =   hex(self.m2c.rd_lf_status0_cause(i=1))
        print ('line fault monitor status0[0]_cause(sticky) from digital is: ', LF0_sticky_from_digital)#0x0e01[4:0]
        print ('line fault monitor status0[1]_cause(sticky) from digital is: ', LF1_sticky_from_digital) 
        
        '''read back on Line fault raw status,x00e01[7:5]'''
        LF0_raw_status_from_digital = self.m2c.rd_lf_status0_raw(i=0)
        LF1_raw_status_from_digital = self.m2c.rd_lf_status0_raw(i=1) 
        print ('line fault status from detector on  RAW status0[LMN0] from digital is: ', LF0_raw_status_from_digital)#0x0e01[7:5]
        print ('line fault status from detector on  RAW status0[LMN1] from digital is: ', LF1_raw_status_from_digital)    
        
        '''read back on a2d_rsvd_status0/1/2/3'''
        LMN0_rawstatus_analog_3bit_hex = hex(self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0()&0x07)
        LMN0_rawstatus_analog_3bit_dec = self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0()&0x07  
        LMN0_rawstatus_analog_11bit_hex    =   hex(((self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0() + (self.m2c.rd_ana_a2d_rsvd_regnamedel1_status1()*256))&0xfffff)>>3)
        LMN0_rawstatus_analog_11bit_dec    =   ((self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0() + (self.m2c.rd_ana_a2d_rsvd_regnamedel1_status1()*256))&0xfffff)>>3
        
        LMN1_rawstatus_analog_3bit_hex = hex(self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2()&0x07)
        LMN1_rawstatus_analog_3bit_dec = self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2()&0x07  
        LMN1_rawstatus_analog_11bit_hex    =   hex(((self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2() + (self.m2c.rd_ana_a2d_rsvd_regnamedel3_status3()*256))&0xfffff)>>3)
        LMN1_rawstatus_analog_11bit_dec    =   ((self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2() + (self.m2c.rd_ana_a2d_rsvd_regnamedel3_status3()*256))&0xfffff)>>3
        
        print ('read back on  LMN0 status from analog 3bit(0:battery,1:GND,2:normal,3:OPEN,4:p/n short) is: ', LMN0_rawstatus_analog_3bit_hex,', and dec value is ',LMN0_rawstatus_analog_3bit_dec)
        print ('read back on  LMN0 status from analog 11bit is: ', LMN0_rawstatus_analog_11bit_hex,', and dec value is ',LMN0_rawstatus_analog_11bit_dec)
        print('\n')
        print ('read back on  LMN1 status from analog 3bit(0:battery,1:GND,2:normal,3:OPEN,4:p/n short) is: ', LMN1_rawstatus_analog_3bit_hex,', and dec value is ',LMN1_rawstatus_analog_3bit_dec)
        print ('read back on  LMN1 status from analog 11bit is: ', LMN1_rawstatus_analog_11bit_hex,', and dec value is ',LMN1_rawstatus_analog_11bit_dec)
        
        print('\n')
        
        return (powerup_lf1,powerup_lf0,LFM_err1_status,LFM_err0_status,LF0_sticky_from_digital,LF1_sticky_from_digital,LF0_raw_status_from_digital,LF1_raw_status_from_digital,LMN0_rawstatus_analog_3bit_hex,LMN0_rawstatus_analog_3bit_dec,LMN0_rawstatus_analog_11bit_hex,LMN0_rawstatus_analog_11bit_dec,LMN1_rawstatus_analog_3bit_hex,LMN1_rawstatus_analog_3bit_dec,LMN1_rawstatus_analog_11bit_hex,LMN1_rawstatus_analog_11bit_dec)
        
     
    def LMN_Setting(self,lmn0current,lmn1current,set_vcomm_prog_cfg,setr_calib_reg_cfg,setr_reg_cfg,trim_bg_reg=2): 
        
        '''cfg GPIO mode'''
        self.MFNSet(gpio=5, mfn=1)
        self.MFNSet(gpio=6, mfn=1)
        '''need set GPIO is all off,and OEN=1, PU=0, PD=0, AE=0'''
        self.m2c.wr_pinmux_pin_io_ow_ctrl_5(value=0x10)
        self.m2c.wr_pinmux_pin_io_ow_en_5(value=0xFF)
        '''need set GPIO is all off,and OEN=1, PU=0, PD=0, AE=0'''
        self.m2c.wr_pinmux_pin_io_ow_ctrl_6(value=0x10)
        self.m2c.wr_pinmux_pin_io_ow_en_6(value=0xFF)
        
        '''cfg res calibration and rterm, and setr_calib_reg can adjust LMN pad voltage'''
        self.m2c.wr_mipi_rx_mipi_dig_rx_res_cal0_fields(setr_calib_reg=setr_calib_reg_cfg, setr_reg=setr_reg_cfg)    #regi=0xa106,setr_reg=0~7
        self.m2c.wr_mipi_rx_dphy_rx_reg2_fields(sel_vcomm_prog = set_vcomm_prog_cfg) #regi=0xa301, mipi rx sel_vcomm_
        
        '''set lmn0/1 current driver for monitor function'''
        self.m2c.wr_ana_d2a_lf_regnamedel0_fields(sel_lf0_bus0 = lmn0current & 0xFF) #regi 0x0baa lmn0_lsb_bit[7:0]
        self.m2c.wr_ana_d2a_lf_regnamedel2_fields(sel_lf1_bus0= lmn1current & 0xFF) #regi 0x0bac lmn1_lsb_bit[7:0]
        self.m2c.wr_ana_d2a_lf_regnamedel1_fields(sel_lf1_bus1=(lmn1current>>8)&0x07, sel_lf0_bus1=(lmn0current>>8)&0x07) #regi 0x0bab lmn0/1_msb_bit[2:0]
        print ('lmn0 current set is: ', self.m2c.rd_ana_d2a_lf_regnamedel1_sel_lf0_bus1()*256 + self.m2c.rd_ana_d2a_lf_regnamedel0_sel_lf0_bus0())
        print ('lmn1 current set is: ', self.m2c.rd_ana_d2a_lf_regnamedel1_sel_lf1_bus1()*256 + self.m2c.rd_ana_d2a_lf_regnamedel2_sel_lf1_bus0())
        
        '''select bg as reference voltage, set to 1 will select internal reference voltage(it's a resistance partial voltage connect to VDD18)'''
        print('vref before setting is ',self.m2c.rd_ana_d2a_lf_regnamedel3_vsel())
        self.m2c.wr_ana_d2a_lf_regnamedel3_fields(vsel=0)    #0:BG,1:res_devider
        self.EfusePgm_S68R5_Verify(d2a_trim_bg_reg=trim_bg_reg) #Vbg=1.2V
        self.EfuseFullRefresh()
        
        '''enable line fault monitor function0 and 1'''
        print('\n')
        print('the lmn0 powerup before setting is ',self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf0(),' ,and the lmn1 powerup before setting is ',self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf1())
        self.m2c.wr_ana_d2a_lf_regnamedel3_fields(powerup_lf1=1, powerup_lf0=1)
        powerup_lf1=self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf1()
        powerup_lf0=self.m2c.rd_ana_d2a_lf_regnamedel3_powerup_lf0()
        print('the lmn0 powerup after setting is ',powerup_lf0,' ,and the lmn1 powerup after setting is ',powerup_lf1)
        time.sleep(1)
        
        '''read back on lfm_err1/0_status from efh module'''
        LFM_err1_status =   self.m2c.rd_efh_l0_status1_lfm_err1_status()
        LFM_err0_status =   self.m2c.rd_efh_l0_status1_lfm_err0_status()
        print ('LFM_err1_status is: ',LFM_err1_status )#0x0a39 
        print ('LFM_err0_status is: ',LFM_err0_status)
        
        '''read back on Line fault cause(sticky)'''
        LF0_sticky_from_digital =   hex(self.m2c.rd_lf_status0_cause(i=0))
        LF1_sticky_from_digital =   hex(self.m2c.rd_lf_status0_cause(i=1))
        print ('line fault monitor status0[0]_cause(sticky) from digital is: ', LF0_sticky_from_digital)#0x0e01[4:0]
        print ('line fault monitor status0[1]_cause(sticky) from digital is: ', LF1_sticky_from_digital) 
        
        '''read back on Line fault raw status'''
        LF0_raw_status_from_digital = self.m2c.rd_lf_status0_raw(i=0)
        LF1_raw_status_from_digital = self.m2c.rd_lf_status0_raw(i=1) 
        print ('line fault status from detector on  RAW status0[LMN0] from digital is: ', LF0_raw_status_from_digital)#0x0e01[7:5]
        print ('line fault status from detector on  RAW status0[LMN1] from digital is: ', LF1_raw_status_from_digital)    
        
        '''read back on a2d_rsvd_status0/1/2/3'''
        LMN0_rawstatus_analog_3bit_hex = hex(self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0()&0x07)
        LMN0_rawstatus_analog_3bit_dec = self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0()&0x07  
        LMN0_rawstatus_analog_11bit_hex    =   hex(((self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0() + (self.m2c.rd_ana_a2d_rsvd_regnamedel1_status1()*256))&0xfffff)>>3)
        LMN0_rawstatus_analog_11bit_dec    =   ((self.m2c.rd_ana_a2d_rsvd_regnamedel0_status0() + (self.m2c.rd_ana_a2d_rsvd_regnamedel1_status1()*256))&0xfffff)>>3
        
        LMN1_rawstatus_analog_3bit_hex = hex(self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2()&0x07)
        LMN1_rawstatus_analog_3bit_dec = self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2()&0x07  
        LMN1_rawstatus_analog_11bit_hex    =   hex(((self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2() + (self.m2c.rd_ana_a2d_rsvd_regnamedel3_status3()*256))&0xfffff)>>3)
        LMN1_rawstatus_analog_11bit_dec    =   ((self.m2c.rd_ana_a2d_rsvd_regnamedel2_status2() + (self.m2c.rd_ana_a2d_rsvd_regnamedel3_status3()*256))&0xfffff)>>3
        
        print ('read back on  LMN0 status from analog 3bit(0:battery,1:GND,2:normal,3:OPEN,4:p/n short) is: ', LMN0_rawstatus_analog_3bit_hex,', and dec value is ',LMN0_rawstatus_analog_3bit_dec)
        print ('read back on  LMN0 status from analog 11bit is: ', LMN0_rawstatus_analog_11bit_hex,', and dec value is ',LMN0_rawstatus_analog_11bit_dec)
        print('\n')
        print ('read back on  LMN1 status from analog 3bit(0:battery,1:GND,2:normal,3:OPEN,4:p/n short) is: ', LMN1_rawstatus_analog_3bit_hex,', and dec value is ',LMN1_rawstatus_analog_3bit_dec)
        print ('read back on  LMN1 status from analog 11bit is: ', LMN1_rawstatus_analog_11bit_hex,', and dec value is ',LMN1_rawstatus_analog_11bit_dec)
        
        print('\n')
        
        return (powerup_lf1,powerup_lf0,LFM_err1_status,LFM_err0_status,LF0_sticky_from_digital,LF1_sticky_from_digital,LF0_raw_status_from_digital,LF1_raw_status_from_digital,LMN0_rawstatus_analog_3bit_hex,LMN0_rawstatus_analog_3bit_dec,LMN0_rawstatus_analog_11bit_hex,LMN0_rawstatus_analog_11bit_dec,LMN1_rawstatus_analog_3bit_hex,LMN1_rawstatus_analog_3bit_dec,LMN1_rawstatus_analog_11bit_hex,LMN1_rawstatus_analog_11bit_dec)
        
        
    def LMNEN(self,lmn0current,lmn1current,set_vcomm_prog_cfg,setr_calib_reg_cfg): 
        
        # self.MFNSet(gpio=5, mfn=1)
        # self.MFNSet(gpio=6, mfn=1)
        self.m2c.wr_mipi_rx_reg_mipi_dig_rx_res_cal0_fields(setr_calib_reg = setr_calib_reg_cfg, setr_reg = 4)
        self.m2c.wr_mipi_rx_reg_dphy_rx_reg2_fields(set_vcomm_prog = set_vcomm_prog_cfg)
        #enable mixa_3 and mixa_4 gpio override function
        self.m2c.wr_pinmux_mixa_io_ow_en(3,0xFF) 
        self.m2c.wr_pinmux_mixa_io_ow_en(4,0xFF)
        #disable mixa_3 and mixa_4 gpio function
        self.m2c.wr_pinmux_mixa_io_ow_ctrl(3,0x10) 
        self.m2c.wr_pinmux_mixa_io_ow_ctrl(4,0x10)
        
        #set lmn0 current driver for monitor function 
        self.m2c.wr_lf_ctrl2_fields(sel_lf0_lsb = lmn0current & 0xFF)
        self.m2c.wr_lf_ctrl3_fields(sel_lf0_msb = (lmn0current>>8)&0x07)
        print ('lmn0 current set is: ', self.m2c.rd_lf_ctrl3_sel_lf0_msb()*256 + self.m2c.rd_lf_ctrl2_sel_lf0_lsb())
        
        #set lmn1 current driver for monitor function 
        self.m2c.wr_lf_ctrl5_fields(sel_lf1_msb =(lmn1current>>8)&0x07)
        self.m2c.wr_lf_ctrl4_fields(sel_lf1_lsb = lmn1current & 0xFF)
        print ('lmn1 current set is: ', self.m2c.rd_lf_ctrl5_sel_lf1_msb()*256 + self.m2c.rd_lf_ctrl4_sel_lf1_lsb())
        
        #select bg as reference voltage, set to 1 will select internal reference voltage(it's a resistance partial voltage connect to VDD18)
        self.m2c.wr_lf_ctrl6_fields(vsel = 0)
        print(self.m2c.rd_lf_ctrl6_vsel())
        # self.m2c.wr_lf_ctrl1_fields(err_clr = 1)
        # self.m2c.wr_lf_ctrl1_fields(err_clr = 0)
        
        #enable line fault monitor function0 and 1
        self.m2c.wr_lf_ctrl0_fields(en=3)
        print ('line fault enable is: ', self.m2c.rd_lf_ctrl0_en())
        time.sleep(1)
        print ('line fault error status(line fault error) is: ', self.m2c.rd_lf_status0_err())
        print ('line fault monitor0 status(line fault cause) is: ', self.m2c.rd_lf_status1_cause(0))
        print ('line fault monitor1 status(line fault cause) is: ', self.m2c.rd_lf_status1_cause(1))
        
    def VGPLLConfig(self, mdivint=14, mdivfraction=0, postdiv=3):
        '''
            reg5 (int): [0x0, 0xff]
            <7:5> postdiv 000 1/2, 001 1/4, 010 1/8, 011 1/16, 1xx 1/32; <4:0> intdiv - 13
            
        Pll Frequecy Settting : 25MHz* (mdivint+mdivfraction/65536)*4/(postdiv)
            
        '''
        divider = {0:2, 1:4, 2:8, 3:16, 4:32, 5:32, 6:32, 7:32}
        divider2 = {0:1, 1:2, 2:4, 3:8}
        post_div_val = divider[postdiv]

        Pll_Frequecy_set =  25 * (mdivint+2*mdivfraction/65536)*4/(post_div_val) #MHz
        print('[VG PLL Clock] frequency is',Pll_Frequecy_set, 'MHz')
        
        self.m2c.wr_ana_d2a_vgpll_regnamedel10_fields(d2a_vgpll_aldo_en= 1, d2a_vgpll_dldo_en= 1, en = 1)
        self.m2c.wr_ana_d2a_vgpll_regnamedel10_fields(rst = 1)
        self.m2c.wr_ana_d2a_vgpll_regnamedel10_fields(rst = 0)
        print('VG PLL, do rst because of bug')
        
        self.m2c.wr_ana_d2a_refpll_regnamedel10_fields(d2a_refpll_aldo_en = 1, d2a_refpll_dldo_en= 1,en = 1)
        self.m2c.wr_ana_d2a_refpll_regnamedel10_fields(rst = 1)
        self.m2c.wr_ana_d2a_refpll_regnamedel10_fields(rst = 0)
        print('REF PLL, do rst because of bug')
                      
        vgpllreg5 = (mdivint + (postdiv<<5)) & 0xFF 
        
        self.m2c.wr_ana_d2a_vgpll_regnamedel5_fields(reg5 = vgpllreg5) # set post div & intdiv
        
        mdivfractionLSB=(mdivfraction) & 0xFF
        mdivfractionMSB=(mdivfraction >> 8) & 0xFF
        
        self.m2c.wr_ana_d2a_vgpll_regnamedel3_fields(reg3 = mdivfractionLSB) # divider fraction part lsb
        self.m2c.wr_ana_d2a_vgpll_regnamedel4_fields(reg4 = mdivfractionMSB) # divider fraction part msb
        
        videopll_lock = self.m2c.rd_rcc_pll_status_videopll_lock() # video pll lock status
        print('videopll_lock is: ', videopll_lock)
        
        return (mdivint, postdiv, mdivfraction, videopll_lock)
        
    def M2CVGCase(self):  
         
        #sel=1 select VGPLL 
        
        self.QRegisterAccess.devAddr=0x44
        self.m2c.wr_sys_cfg_video_ctrl_fields(src = 2)
        self.m2c.wr_rcc_ck_video_ctrl_fields(sel = 1, vg_en = 1) 
        
        self.m2c.wr_vg_ext_vsync_lsb(0x05) #
        self.m2c.wr_vg_ext_vsync_msb(0x0)
        
        self.m2c.wr_vg_ext_vfp_lsb(0x05)
        self.m2c.wr_vg_ext_vfp_msb(0x05)
        
        self.m2c.wr_vg_ext_vblank_lsb(0x6C)
        self.m2c.wr_vg_ext_vblank_msb(0x00)
        
        self.m2c.wr_vg_ext_vtotal_lsb(0xA4)
        self.m2c.wr_vg_ext_vtotal_msb(0x04)
        
        self.m2c.wr_vg_ext_hbp_lsb(0x60)
        self.m2c.wr_vg_ext_hbp_msb(0x00)
        
        self.m2c.wr_vg_ext_hfp_lsb(0x20)
        self.m2c.wr_vg_ext_hfp_msb(0x01)
        
        self.m2c.wr_vg_ext_hblank_lsb(0xE0)
        self.m2c.wr_vg_ext_hblank_msb(0x01)
        
        self.m2c.wr_vg_ext_htotal_lsb(0x60)
        self.m2c.wr_vg_ext_htotal_msb(0x09)
        
        self.m2c.wr_vg_p_id(27) # current 0x20  pid:16
        self.m2c.wr_vg_v_id(0x00)
        
        self.m2c.wr_vg_config0(0x02) #current 0 , previous 0x04
        self.m2c.wr_vg_config1(0x71) 
    def M2CVGCase1(self):  
         
        #sel=1 select VGPLL 
        
        self.QRegisterAccess.devAddr=0x44
        self.m2c.wr_sys_cfg_video_ctrl_fields(src = 2)
        self.m2c.wr_rcc_ck_video_ctrl_fields(sel = 1, vg_en = 1) 
        
        self.m2c.wr_vg_ext_vsync_lsb(0x05) #
        self.m2c.wr_vg_ext_vsync_msb(0x0)
        
        self.m2c.wr_vg_ext_vfp_lsb(0x05)
        self.m2c.wr_vg_ext_vfp_msb(0x05)
        
        self.m2c.wr_vg_ext_vblank_lsb(0x6C)
        self.m2c.wr_vg_ext_vblank_msb(0x00)
        
        self.m2c.wr_vg_ext_vtotal_lsb(0xA4)
        self.m2c.wr_vg_ext_vtotal_msb(0x04)
        
        self.m2c.wr_vg_ext_hbp_lsb(0x60)
        self.m2c.wr_vg_ext_hbp_msb(0x00)
        
        self.m2c.wr_vg_ext_hfp_lsb(0x20)
        self.m2c.wr_vg_ext_hfp_msb(0x01)
        
        self.m2c.wr_vg_ext_hblank_lsb(0xE0)
        self.m2c.wr_vg_ext_hblank_msb(0x01)
        
        self.m2c.wr_vg_ext_htotal_lsb(0x60)
        self.m2c.wr_vg_ext_htotal_msb(0x09)
        
        self.m2c.wr_vg_p_id(29) # current 0x20  pid:16
        self.m2c.wr_vg_v_id(0x00)
        
        self.m2c.wr_vg_config0(0x02) #current 0 , previous 0x04
        self.m2c.wr_vg_config1(0x71)    
        
    def M2CRefPLLInit(self):
        '''
        This function is initial ref_pll and vedio_pll, and RCLKOUT is 12.5MHZ
        
        '''
        self.QRegisterAccess.devAddr=0x44
        #reg0_ref_pll,and cp cfg=1(2uA default), ssc=sdm=disable
        self.m2c.wr_ana_d2a_refpll_regnamedel0_fields(reg0 = 0x10)
        #reg1_ref_pll,keep default
        #reg1_vedio_pll,keep default
        
        #reg2_ref_pll, and 
        #[7]:loop brk
        #[6]:dither
        #[5:4]00:vssu;01:ckref_det;10:ckfb_det;11:pll_lk_rb
        #[3] enable test
        #[2] force lock
        #[1:0]pfd dly tune
        self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x01)
        #reg2_vedio_pll
        #reg3_ref_pll, and cfg fractive, lsb
        self.m2c.wr_ana_d2a_refpll_regnamedel3_fields(reg3 = 0)
        self.m2c.wr_ana_d2a_refpll_regnamedel4_fields(reg4 = 0) 
        #reg4_ref_pll, and cfg fractive, msb
        
        #reg5_ref_pll,and set post div and int, 
        '''[6:5] map to post div,and 00/1,01/2,10/4,11/8
            [4:0] map to INT, and range 12~25
        '''
        self.m2c.wr_ana_d2a_refpll_regnamedel5_fields(reg5 = 0x2C)     #0x6c from TJ
        #reg5_vedio_pll
        
        #reg6_ref_pll,and set cnt,range 0~31,but design advise CNT>=5 
        self.m2c.wr_ana_d2a_refpll_regnamedel11_fields(reg10 = 0xe0)
    
    def M2CRefVideoPLLLock(self,):
        '''
        This function is read ref_pll_lock status
        
        ''' 
        ref_pll_lock_status = self.m2c.rd_rcc_pll_status_refpll_lock()
        video_pll_lock_status = self.m2c.rd_rcc_pll_status_videopll_lock()
        
        return (ref_pll_lock_status,video_pll_lock_status)                          
    
    def RefPLLAnaTestMux(self, test_sel):   
        ''' 
        <7> loop brk; 
        <6> dither(rsvd); 
        <5:4> test sel, 11~00 
            dtest: lk_rb/ckfb/ckref/0:svssu, 
            atest: vtopvco/lk_rb/vdddig/0:vssu; 
        <3> enable test; 
        <2> force lock; 
        <1:0> pfd dly tune
        
        '''
        
        if test_sel==0:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x09)
        elif test_sel==1:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x19)
        elif test_sel==2:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x29)
        elif test_sel==3:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x39)
        else:
            raise ('not support')
        
    def RefPLLDigTestMux(self, test_sel):   
        ''' 
        <7> loop brk; 
        <6> dither(rsvd); 
        <5:4> test sel, 11~00 
            dtest: lk_rb/ckfb/ckref/0:svssu, 
            atest: vtopvco/lk_rb/vdddig/0:vssu; 
        <3> enable test; 
        <2> force lock; 
        <1:0> pfd dly tune
        
        '''
        if test_sel==0:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x09)
        elif test_sel==1:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x19)
        elif test_sel==2:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x29)
        elif test_sel==3:
            self.m2c.wr_ana_d2a_refpll_regnamedel2_fields(reg2 = 0x39)
        else:
            raise ('not support')
  
    def VGPLLAnaTestMux(self, test_sel=0):   
        ''' 
        VG PLL atest_sel:
     
        test_sel
            00: vssu;
            01: dldo_0p9v;
            10: aldo_1p2v;
            11: vtop_vco
        
        '''
        if test_sel==0:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x09)
        elif test_sel==1:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x19)
        elif test_sel==2:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x29)
        elif test_sel==3:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x39)
        else:
            raise ('not support') 
 
        
    def VGPLLDigTestMux(self, test_sel):   
        '''
            <7> loop brk; 
            <6> dither(rsvd); 
            <5:4> test sel, 11~00 
                dtest: lk_rb/ckfb/ckref/0:vssu, 
                atest: vtopvco/lk_rb/vdddig/0:vssu; 
            <3> enable test; 
            <2> force lock; 
            <1:0> pfd dly tune
        '''
        
        if test_sel==0:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x09)
        elif test_sel==1:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x19)
        elif test_sel==2:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x29)
        elif test_sel==3:
            self.m2c.wr_ana_d2a_vgpll_regnamedel2_fields(reg2 = 0x39)
        else:
            raise ('not support')
            
    def S68_MIPI_Init(self,term_en = 0, hs_settle =0):
    
        # self.MIPIPHY0Crossbar(lanetype=0, lanelogic=0)    #D0: data
        # self.MIPIPHY1Crossbar(lanetype=0, lanelogic=1)    #D1: disable
        # self.MIPIPHY2Crossbar(lanetype=1, lanelogic=0)    #clk: clock
        # self.MIPIPHY3Crossbar(lanetype=0, lanelogic=2)    #D2: disable
        # self.MIPIPHY4Crossbar(lanetype=0, lanelogic=3)    #D3: disabl
        # self.MIPIRxLaneEN(num_lane=3)      #number of used data lane(=n+1)
    
        self.MIPIRxTiming(term_en = term_en, hs_settle =hs_settle, ls_settle = 10, dly_rx_term = 0,init_time = 7)      #500Mbps
        self.MIPICLKModeSet(mode=1) # MIPI Rx CLK mode select: 0: burst clk, 1: continous clk
    
        # reboot        
        self.MIPIBootEN()
    
        # self.CSI2REST()   

    def M66S68PCILocalWrite(self,pcidevAddr,regAddr, value):  
        
        self.dongle.M66S68PCII2CWrite_normal(pcidevAddr,regAddr, value)
        
    def M66S68PCILocalRead(self,pcidevAddr, regAddr):
        
        value = self.dongle.M66S68PCII2CRead_normal(pcidevAddr, regAddr)
        return value           

    # def rd_gpios_ctrl0(self) -> int: 
    #     """
    #     read register gpios_ctrl0 (address: 0x0600)

    #     return: 
    #         Control Register 0
    #     """
    #     return self.dongle.readReg(self.name, 0x0600) 

    # def wr_gpios_ctrl0(self, value: int) -> int: 
    #     """
    #     write register gpios_ctrl0 (address: 0x0600)

    #     value (int): 
    #         Control Register 0

    #     return: >0 if succeeded
    #     """
    #     return self.dongle.writeReg(self.name, 0x0600, value) 

    # def rd_gpios_ctrl0_fwd_dly(self) -> int: 
    #     """
    #     read bitfield gpios_ctrl0.fwd_dly (address:0x0600, width:6, offset:0) 

    #     return: 
    #         Forward channel delay compensation. 
    #         The delay time equals to (fwd_chnl_dly+1) * 3.5us
    #     """
    #     return self.dongle.readReg(self.name, 0x0600) & 0x3f 

    # def wr_gpios_ctrl0_fields(self, fwd_dly: int = None) -> int: 
    #     """
    #     write register gpios_ctrl0 fields (address: 0x0600) 

    #     fwd_dly (int): [0x0, 0x3f]
    #         Forward channel delay compensation. 
    #         The delay time equals to (fwd_chnl_dly+1) * 3.5us

    #     return: >0 if succeeded
    #     """
    #     value = self.dongle.readReg(self.name, 0x0600) 
    #     if fwd_dly != None: 
    #         value = (value & 0xc0) | ((fwd_dly << 0) & 0x3f) 
    #     return self.dongle.writeReg(self.name, 0x0600, value) 

    # def rd_gpios_ctrl1(self) -> int: 
    #     """
    #     read register gpios_ctrl1 (address: 0x0601)

    #     return: 
    #         Control Register 1
    #     """
    #     return self.dongle.readReg(self.name, 0x0601) 

    # def wr_gpios_ctrl1(self, value: int) -> int: 
    #     """
    #     write register gpios_ctrl1 (address: 0x0601)

    #     value (int): 
    #         Control Register 1

    #     return: >0 if succeeded
    #     """
    #     return self.dongle.writeReg(self.name, 0x0601, value) 

    # def rd_gpios_ctrl1_rvs_dly(self) -> int: 
    #     """
    #     read bitfield gpios_ctrl1.rvs_dly (address:0x0601, width:6, offset:0) 

    #     return: 
    #         Reverse channel delay compensation. 
    #         The delay time equals to (rvs_chnl_dly+1) * 3.5us
    #     """
    #     return self.dongle.readReg(self.name, 0x0601) & 0x3f 

    # def wr_gpios_ctrl1_fields(self, rvs_dly: int = None) -> int: 
    #     """
    #     write register gpios_ctrl1 fields (address: 0x0601) 

    #     rvs_dly (int): [0x0, 0x3f]
    #         Reverse channel delay compensation. 
    #         The delay time equals to (rvs_chnl_dly+1) * 3.5us

    #     return: >0 if succeeded
    #     """
    #     value = self.dongle.readReg(self.name, 0x0601) 
    #     if rvs_dly != None: 
    #         value = (value & 0xc0) | ((rvs_dly << 0) & 0x3f) 
    #     return self.dongle.writeReg(self.name, 0x0601, value) 