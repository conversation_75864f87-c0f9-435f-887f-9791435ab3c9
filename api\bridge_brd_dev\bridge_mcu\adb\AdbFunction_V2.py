import subprocess  
import threading  
import queue  

class Device:  
    def __init__(self, name, serial_number):  
        self.name = name
        self.serial_number = serial_number

class AdbFunction:
    def __init__(self, adb_path='adb'):
        self.adb_path = adb_path
        self.adb_port = None
        self.adb_process = None
        self.output_queue = queue.Queue()

    def __del__(self):
        self.adb_close()

    def adb_open(self, port_name):  
        self.adb_port = port_name  
        self.adb_process = subprocess.Popen(  
            [self.adb_path, '-s', self.adb_port, 'shell'],  
            stdin=subprocess.PIPE,  
            stdout=subprocess.PIPE,  
            stderr=subprocess.PIPE,  
            text=True,  
            bufsize=1  
        )  
        threading.Thread(target=self._enqueue_output, args=(self.adb_process.stdout, self.output_queue), daemon=True).start()  
        print(f"adb {self.adb_port} open")  
        return 0  

    def adb_close(self):  
        if self.adb_process:  
            self.adb_process.stdin.close()  
            self.adb_process.terminate()  
            self.adb_process.wait()  
            self.adb_process = None  
        self.adb_port = None  
        print(f"adb {self.adb_path} close")  

    def get_connected_devices(self):  
        try:  
            # 执行 adb devices 命令  
            result = subprocess.run(  
                [self.adb_path, 'devices'],  
                capture_output=True,  
                text=True,  
                check=True  
            )  
            
            # 获取命令输出并分割为行  
            output_lines = result.stdout.strip().split('\n')  
            
            # 解析设备列表，跳过第一行标题  
            adbdev_list:list = []
            for line in output_lines[1:]:  
                if line.strip():  # 确保行不为空  
                    parts = line.split()  
                    if len(parts) == 2 and parts[1] == 'device':  
                        adbdev_list.append(Device(parts[0], parts[0]))
            
            return adbdev_list  
    
        except subprocess.CalledProcessError as e:  
            print(f"ADB 命令失败: {e}")  
            return []  

    def get_sn(self):  
        return self.adb_port is not None, self.adb_port  

    def _enqueue_output(self, out, queue):  
        for line in iter(out.readline, ''):  
            queue.put(line)  
        out.close()  
    
    def _run_adb_command(self, command, need_exitflag=False, time_count=100):  
        if not self.adb_process:  
            print("ADB shell 未打开")  
            return None

        try:
            self.adb_process.stdin.write(command + '\n')
            if need_exitflag:
                # 检查子进程是否仍然存活  
                self.adb_process.stdin.write("echo $?\n")  # 查询上一条命令的退出状态码  
            self.adb_process.stdin.flush()  

            output = []
            try_time = 0
            while True:
                try:
                    line = self.output_queue.get(timeout=0.002).strip()   
                    if line and not self._is_unwanted_echo(line):  
                        output.append(line)
                        # print(f"return line: {line}")
                    else:
                        # print(f"ignore line: {line}")
                        pass
                except queue.Empty:
                    # 已经有数据了，这里就认为已经执行完成了
                    if len(output):
                        break
                    # 如果尝试time_count多次还是没有数据，这里认为就是没有返回数据，属于异常
                    if try_time <= time_count:
                        try_time += 1
                        continue
                    else:
                        print(f"_run_adb_command error try_time={try_time}")
                        break
            
            return '\n'.join(output)
        except Exception as e:  
            print(f"ADB 命令失败: {e}")  
            return None

    def _is_unwanted_echo(self, line):  
        """检查是否为不需要的回显信息。"""
        # 这里可以添加具体的逻辑来识别不需要的回显信息
        # 例如，过滤掉命令提示符或其他已知的回显模式  
        unwanted_patterns = ["root@", "/#", "echo $?", "i2ctransfer"]  
        return any(pattern in line for pattern in unwanted_patterns)  

    def i2c_set(self, mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):  
        high_byte = (reg_addr >> 8) & 0xFF  
        low_byte = reg_addr & 0xFF  
        command = f'i2ctransfer -y -f {str(bus)} w3@0x{dev_addr:02X} 0x{high_byte:02X} 0x{low_byte:02X} 0x{reg_val:02X}'   
        output = self._run_adb_command(command, need_exitflag=True)
        if output is not None and output.strip() == "0":  
            # print(f"w_r16v8,0x{dev_addr:02X},0x{reg_addr:04X},0x{reg_val:02X}")  
            return 0 
        else:  
            # print(f"adb shell i2c set error: w2@0x{dev_addr:02X} 0x{reg_addr:04X} 0x{reg_val:02X}", output)  
            return -1

    def i2c_get(self, mode: int, bus: int, dev_addr: int, reg_addr: int):  
        high_byte = (reg_addr >> 8) & 0xFF  
        low_byte = reg_addr & 0xFF   
        command = f'i2ctransfer -y -f {str(bus)} w2@0x{dev_addr:02X} 0x{high_byte:02X} 0x{low_byte:02X} r1'  
        output = self._run_adb_command(command)  
        if self.is_hexadecimal(output):  
            # print(f"r_r16v8,0x{dev_addr:02X},0x{reg_addr:04X},{output}")  
            return 0, int(output, 16)  
        else:  
            # print(f"adb shell i2c get error: w2@0x{dev_addr:02X} 0x{reg_addr:04X}, output={output}")  
            return -1, -1  

    def is_hexadecimal(self, output):  
        if not isinstance(output, str):  
            return False  
        output = output.strip()  
        if not output:  
            return False  
        try:  
            int(output, 16)  
            return True  
        except ValueError:  
            return False
