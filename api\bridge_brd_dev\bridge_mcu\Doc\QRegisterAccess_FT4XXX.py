# notes to use ft4222 package (based on python 3.7.9, https://pypi.org/project/ft4222/)
#     1. pip install ft4222
#     2. copy local LibFT4222-64.dll to overwrite the file in ft4222 package
import csv
#FT4222 API functions address: https://msrelectronics.gitlab.io/python-ft4222/api.html#ft4222.SPIMaster.Mode.SPI_IO_NONE
# packages
import ft4222
import time


save_data =  False # False

if save_data:
    data_addr = "C:\\Projects\\m66s68_r1\\Raw_Data\\01_test\\C3_link\\M66S68_C3TX_deug_t6.csv"
    with open(data_addr, "a", encoding="utf-8", newline="") as f:
        csv_writer = csv.writer(f)
        name = ['DeviceAddr','Reg_Addr', 'R_or_W', 'Write_or_Read_Value']
        csv_writer.writerow(name)

devAddr=0x69
#list devices
devNum = ft4222.createDeviceInfoList()
for i in range(devNum):
    print(ft4222.getDeviceInfoDetail(i, False))

# open device with default description
ft4222DevA = ft4222.openByDescription('FT4222 A')
ft4222DevB = ft4222.openByDescription('FT4222 B')

# init sys clock
ft4222DevA.setClock(ft4222.SysClock.CLK_80)

def iicAddInit(devaddr):
    devAddr = devaddr

def findGMSLSpiChan():
    # init spi master
    ft4222DevA.spiMaster_Init(ft4222.SPIMaster.Mode.SINGLE, ft4222.SPIMaster.Clock.DIV_16, ft4222.SPI.Cpol.IDLE_LOW, ft4222.SPI.Cpha.CLK_TRAILING, ft4222.SPIMaster.SlaveSelect.SS0) #IDLE_LOW=0;IDLE_HIGH=1; CLK_LEADING=0; CLK_TRAILING=1;
    return True

def findMeritechSpiChan():
    # init spi master
    ft4222DevA.spiMaster_Init(ft4222.SPIMaster.Mode.SINGLE, ft4222.SPIMaster.Clock.DIV_8, ft4222.SPI.Cpol.IDLE_LOW, ft4222.SPI.Cpha.CLK_LEADING, ft4222.SPIMaster.SlaveSelect.SS0)
    return True

def findMeritechTDC65nmSingleSpiChan():
    '''
    mode Attributes:
        SPI_IO_NONE: No IOs
        SPI_IO_SINGLE: Single mode
        SPI_IO_DUAL: Dual mode
        SPI_IO_QUAD: Quad mode
    clock Attributes:
        NONE:
        DIV_2: 1/2 System Clock
        DIV_4: 1/4 System Clock
        DIV_8: 1/8 System Clock
        DIV_16: 1/16 System Clock
        DIV_32: 1/32 System Clock
        DIV_64: 1/64 System Clock
        DIV_128: 1/128 System Clock
        DIV_256: 1/256 System Clock
        DIV_512: 1/512 System Clock
    slave select Attributes:
        SS0: Slave select 0
        SS1: Slave select 1
        SS2: Slave select 2
        SS3: Slave select 3
    Cpol Attributes:
        IDLE_LOW: Idle low
        IDLE_HIGH: Idle high
        IDLE_LOW  = 0
        IDLE_HIGH = 1
    Cpha Attributes:
        CLK_LEADING: Leading phase
        CLK_TRAILING: Trailing phase
        CLK_LEADING  = 0
        CLK_TRAILING = 1
    '''
    # init spi master
    ft4222DevA.spiMaster_Init(ft4222.SPIMaster.Mode.SINGLE, ft4222.SPIMaster.Clock.DIV_2, ft4222.SPI.Cpol.IDLE_HIGH, ft4222.SPI.Cpha.CLK_LEADING, ft4222.SPIMaster.SlaveSelect.SS0)
    return True

def findMeritechTDC65nmQSpiChan():
    
    # init spi master
    ft4222DevA.spiMaster_Init(ft4222.SPIMaster.Mode.QUAD, ft4222.SPIMaster.Clock.DIV_2, ft4222.SPI.Cpol.IDLE_HIGH, ft4222.SPI.Cpha.CLK_LEADING, ft4222.SPIMaster.SlaveSelect.SS0)
    time.sleep(0.1)
    ''''
    This overrides the mode passed to FT4222_SPIMaster_init. 
    This might be needed if a device accepts commands in single mode but data transfer is to use dual or quad mode.'''
    ft4222DevA.spiMaster_SetLine(ft4222.SPIMaster.Mode.QUAD)
    ft4222DevA.setClock(ft4222.SysClock.CLK_80)#CLK_60,CLK_24,CLK_48
    return True
def findMeritechDACSpiChan():
    # init spi master
    '''
        NONE:
        DIV_2: 1/2 System Clock
        DIV_4: 1/4 System Clock
        DIV_8: 1/8 System Clock
        DIV_16: 1/16 System Clock
        DIV_32: 1/32 System Clock
        DIV_64: 1/64 System Clock
        DIV_128: 1/128 System Clock
        DIV_256: 1/256 System Clock
        DIV_512: 1/512 System Clock
        
        Cpol: IDLE_LOW  = 0, IDLE_HIGH = 1
        Cpha: CLK_LEADING: Leading phase, CLK_TRAILING: Trailing phase; CLK_LEADING  = 0, CLK_TRAILING = 1
    '''
    
    ft4222DevA.spiMaster_Init(ft4222.SPIMaster.Mode.SINGLE, ft4222.SPIMaster.Clock.DIV_32, ft4222.SPI.Cpol.IDLE_LOW, ft4222.SPI.Cpha.CLK_LEADING, ft4222.SPIMaster.SlaveSelect.SS0)
    return True

def findMeritechADCSpiChan():
    # init spi master
    ft4222DevA.spiMaster_Init(ft4222.SPIMaster.Mode.SINGLE, ft4222.SPIMaster.Clock.DIV_32, ft4222.SPI.Cpol.IDLE_LOW, ft4222.SPI.Cpha.CLK_TRAILING, ft4222.SPIMaster.SlaveSelect.SS0)
    return True

def findMeritechI2cChan():
    # init i2c master
    ft4222DevA.i2cMaster_Init(100)
    return True

def findMeritechI2CSlave(): 
    ft4222DevA.i2cSlave_Init()
    # ft4222DevA.FT4222_I2CSlave_Init()
    
    return True
def findMeritechSPISlave(): 
    ft4222DevA.spiSlave_InitEx(ft4222.SPISlave.Protocol.SPI_SLAVE_NO_PROTOCOL)
    ft4222DevA.spiSlave_SetMode(ft4222.SPI.Cpol.IDLE_LOW, ft4222.SPI.Cpha.CLK_TRAILING)
 
    return True


def initGpio23(dir2, dir3):
    # use GPIO2 as gpio (not suspend out)
    ft4222DevB.setSuspendOut(False)
    # use GPIO3 as gpio (not wakeup)
    ft4222DevB.setWakeUpInterrupt(False)
    # 0: use as output
    # 1: use as input
    ft4222DevB.gpio_Init(gpio2 = dir2, gpio3 = dir3)

def setGpio(index, value):
    v = False if value == 0 else True
    if index <= 3:
        ft4222DevB.gpio_Write(index, v)

def getGpio(index):
    v = 0
    if index <= 3:
        v = 1 if ft4222DevB.gpio_Read(index) == True else 0
    return v

def spiRead1860Reg(addr):
    value = 0
    if ft4222DevA != None:
        wrBuf = [0x01, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff, 0x00]
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), False)
        rdBuf = ft4222DevA.spiMaster_SingleRead(1, True)      
        value = rdBuf[0]
    return value

def spiWrite1860Reg(addr, value):
    if ft4222DevA != None:
        wrBuf = [0x00, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff, 0x00, value & 0xff]
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)
    return True

def BitReverse(bitwidth, value):
    addrbin=bin(value)
    addrlsbf=0
    addrbin=addrbin[2:]
    #print (addrbin)
    #      lenabs=len(addrbin)%8
    lenabs=len(addrbin)
   
    #print (lenabs)
    if lenabs == 0:
        pass
    else:
        for i in range(bitwidth-lenabs):
            addrbin='0'+addrbin
    #print (addrbin)
    for i in range(0,len(addrbin),1):
        addrlsbf+=int(addrbin[len(addrbin)-1-i])*(2**(len(addrbin)-1-i))
    return addrlsbf
''' the following module for TDC SPI w/r......................'''
def singleSpiReadTDCReg(addr):
    if ft4222DevA != None:
        addrac=(addr<<1)+1
        wrBuf = (addrac & 0xff,)
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), False)
        rdBuf = ft4222DevA.spiMaster_SingleRead(2, True) 
        value = ((rdBuf[0]<<8)&0xFF00) | (rdBuf[1]&0xFF)    #byte0 is MSB, and byteN is LSB
        print(hex(rdBuf[0]), hex(rdBuf[1]))
    return value

def singleSpiWriteTDCReg(addr, value):
    if ft4222DevA != None:
        addrac=(addr<<1)+0
        wrBuf = (addrac & 0xff, value&0xFF)
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)
    return True

def QSpiReadTDCReg(addr):

    '''
    spiMaster_MultiReadWrite(singleWrite, multiWrite, bytesToRead)
        Write and read data to and from a SPI slave in dual- or quad-mode (multi-mode).
        
        Parameters:
        singleWrite (bytes, bytearray, int) �C Data to write to slave in signle-line mode (max. 15 bytes)
        
        multiWrite (bytes, bytearray, int) �C Data to write to slave in multi-line mode (max. 65535 bytes)
        
        bytesToRead (int) �C Number of bytes to read on multi-line (max. 65535 bytes)
        
        Returns:
        Bytes read from slave in multi-line mode
        
        Return type:
        bytes
        
        Raises:
        FT4222DeviceError �C on error
    '''
    Instruction=0x40
    if ft4222DevA != None:
       
        wrBuf = (Instruction,(addr>>16) & 0xff,(addr>>8) & 0xff,addr & 0xff)
        ft4222DevA.spiMaster_MultiReadWrite(bytearray(wrBuf))
        rdBuf = ft4222DevA.spiMaster_MultiReadWrite(3,) 
        value = ((rdBuf[0]<<16)&0xFF0000) | (rdBuf[1]&0xFF00) | (rdBuf[2]&0xFF)
        print(hex(rdBuf[0]), hex(rdBuf[1]),hex(rdBuf[2]))
    return value

# def QSpiWriteTDCReg(addr,value):
#
#     Instruction=0x40
#     if ft4222DevA != None:
#        ft4222DevA.spi
#         wrBuf = (Instruction,(addr>>16) & 0xff,(addr>>8) & 0xff,addr & 0xff,(value>>16) & 0xff,(value>>8) & 0xff,value & 0xff)
#         ft4222DevA.spiMaster_MultiReadWrite(bytearray(wrBuf))
#     return True

'''.................end......................'''

def spiReadADCReg(addr):
    if ft4222DevA != None:
        # addrbin=bin(addr)
        # addrlsbf=0
        # addrbin=addrbin[2:]
        # print (addrbin)
        # lenabs=len(addrbin)%8
        #
        # print (lenabs)
        # if lenabs == 0:
        #     pass
        # else:
        #     for i in range(14-lenabs):
        #         addrbin='0'+addrbin
        # print (addrbin)
        # for i in range(0,len(addrbin),1):
        #     addrlsbf+=int(addrbin[len(addrbin)-1-i])*(2**(len(addrbin)-1-i))
        addrlsbf = BitReverse(bitwidth=14, value=addr)
        addrac=(0<<14) + addrlsbf
        wrBuf = ((addrac >> 8) & 0xff, addrac & 0xff)
        
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), False)
        rdBuf = ft4222DevA.spiMaster_SingleRead(5, True) 
        data = ((rdBuf[0]&0x3F)<<26)| ((rdBuf[1]&0xFF)<<18) | ((rdBuf[2]&0xFF)<<10) |((rdBuf[3]&0xFF)<<2) | (rdBuf[4]&0xC0)>>6 
        value = BitReverse(bitwidth=32, value=data)
    return value

def spiWriteADCReg(addr, value):
    if ft4222DevA != None:
         
        # addrbin=bin(addr)
        # addrlsbf=0
        # addrbin=addrbin[2:]
        # print (addrbin)
        # lenabs=len(addrbin)%8
        # print (lenabs)
        # if lenabs == 0:
        #     pass
        # else:
        #     for i in range(8-lenabs):
        #         addrbin='0'+addrbin
        # print (addrbin)
        # for i in range(0,len(addrbin),1):
        #     addrlsbf+=int(addrbin[len(addrbin)-1-i])*(2**(len(addrbin)-1-i)) 
        
        addrlsbf = (2<<14) + BitReverse(bitwidth=14, value=addr)
        
        # valuebin=bin(value)
        # valuelsbf=0
        # valuebin=valuebin[2:]
        # print (valuebin)
        # lenabs=len(valuebin)%8
        # print (lenabs)
        # if lenabs == 0:
        #     pass
        # else:
        #     for i in range(8-lenabs):
        #         valuebin='0'+valuebin
        # print (valuebin)
        # for i in range(0,len(valuebin),1):
        #     valuelsbf+=int(valuebin[len(valuebin)-1-i])*(2**(len(valuebin)-1-i)) 
        valuelsbf = BitReverse(bitwidth=32, value=value)
        
        wrBuf = ((addrlsbf >> 8) & 0xff, addrlsbf & 0xff, (valuelsbf>>24)&0xFF, (valuelsbf>>16)&0xFF,(valuelsbf>>8)&0xFF,valuelsbf&0xFF)
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)
    return True

def spiReadDACReg(addr):
    if ft4222DevA != None:
        addrac=(1<<7) + addr
        wrBuf = (addrac & 0xff,)
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), False)
        rdBuf = ft4222DevA.spiMaster_SingleRead(2, True) 
        value = ((rdBuf[0]<<8)&0xFF00) | (rdBuf[1]&0xFF)
        print(hex(rdBuf[0]), hex(rdBuf[1]))
    return value

def spiWriteDACReg(addr, value):
    if ft4222DevA != None:
        wrBuf = (addr & 0xff, (value>>8)&0xFF,value&0xFF)
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)
    return True

def i2cRead96717Reg(addr):
    value = 0
    if ft4222DevA != None:
        wrBuf = [(addr >> 8) & 0xff, addr & 0xff]
        ft4222DevA.i2cMaster_WriteEx(0x35, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(0x35, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    return value

def i2cWrite96717Reg(addr, value):
    if ft4222DevA != None:
        wrBuf = [(addr >> 8) & 0xff, addr & 0xff, value & 0xff]
        ft4222DevA.i2cMaster_WriteEx(0x80, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True

def i2cReadC3TxTestReg(regAddr):
    value = 0
    if ft4222DevA != None:
        wrBuf = (regAddr,)
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    return value

def i2cWriteC3TxTestReg(regAddr, value):
    if ft4222DevA != None:
        wrBuf = (regAddr, value)
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True

def MAX5395I2CWrite(devAddr5, regAddr, value):
    
    if ft4222DevA != None:
        wrBuf = (regAddr, value)
        ft4222DevA.i2cMaster_WriteEx(devAddr5, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True

def MAX5395I2CRead(devAddr5, regAddr):
    
    value = 0
    if ft4222DevA != None:
        wrBuf = (regAddr,)
        ft4222DevA.i2cMaster_WriteEx(devAddr5, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devAddr5, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    return value

#...............this module to w/r register from C3 tested chip with FT4222&py37................start.........................

def i2cReadC3TxTestReg_ft4222py37(devi_addr,regAddr):
    value = 0
    if ft4222DevA != None:
        wrBuf = (regAddr,)
        ft4222DevA.i2cMaster_WriteEx(devi_addr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devi_addr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    return value

def i2cWriteC3TxTestReg_ft4222py37(devi_addr,regAddr, value):
    if ft4222DevA != None:
        wrBuf = (regAddr, value)
        ft4222DevA.i2cMaster_WriteEx(devi_addr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True


def M66S68_Read_Register(devi_addr,regi_addr):
    ''' 
    descriptions: this function to read register value from C3 tested chip with FT4222&py3.7
    '''
    value = 0
    if ft4222DevA != None:
        wrBuf = [(regi_addr & 0xFF)]
        ft4222DevA.i2cMaster_WriteEx(devi_addr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devi_addr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    return value

def M66S68_Write_Register(devi_addr,regi_addr,value):
    ''' 
    descriptions: this function to write register value from C3 tested chip with FT4222&py3.7
    '''
    if ft4222DevA != None:
        wrBuf = [(regi_addr & 0xFF), value & 0xFF]
        ft4222DevA.i2cMaster_WriteEx(devi_addr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True
    
def M66S68_Write_Bitfield(devi_addr,regi_addr, bitnum, startbit,value):
    ''' 
    descriptions: this function to read register filed value from C3 tested chip with FT4222&py3.7
    '''
    bitsToSave = 2 ** 8 - 2 ** (startbit + bitnum) + 2 ** startbit - 1
    reg_data = M66S68_Read_Register(devi_addr,regi_addr) & bitsToSave
    data1 = reg_data + (value << startbit)
    M66S68_Write_Register(devi_addr,regi_addr,data1)

def M66S68_Read_Bitfield(devi_addr,regi_addr, bitnum, startbit,):
    ''' 
    descriptions: this function to write register filed value from C3 tested chip with FT4222&py3.7
    '''
    bitsToGet = 0xff-(2**8-2**(startbit+bitnum)+2**startbit-1) 
        
    value = (M66S68_Read_Register(devi_addr,regi_addr) & bitsToGet) >> startbit
    
    return value
    
#...............this module to w/r register from C3 tested chip with FT4222&py37................end.........................
    

def M66S68I2CWrite_normal(regAddr, value):

    if ft4222DevA != None:
        wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    
    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(devAddr), hex(regAddr), 'W', hex(value)]
            csv_writer.writerow(row) 
    return True

def M66S68I2CRead_normal(regAddr):

    value = 0
    if ft4222DevA != None:
        wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    
    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(devAddr), hex(regAddr), 'R', hex(value)]
            csv_writer.writerow(row) 
    return value


# def M66S68I2CRead_normal(regAddr):
#
#     value=i2cReadC3TxTestReg(regAddr) #devAddr,
#     return value
#
# def M66S68I2CWrite_normal(regAddr, value):
#
#     if ft4222DevA != None:
#         #wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
#         #ftd2xxI2cDev.i2cMaster_WriteEx(devAddr, ftd2xx.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
#         i2cWriteC3TxTestReg(devAddr,regAddr, value)
#
#     return True

def M66S68I2CWrite_CRC(regAddr, value):
    
    if ft4222DevA != None:
        wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF), value)
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True

def M66S68I2CRead_CRC(regAddr):
   
    value = 0
    if ft4222DevA != None:
        wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF))
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    return value

def M65Q68I2CWrite_normal(regAddr, value):
    
    if ft4222DevA != None:
        wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(devAddr), hex(regAddr), 'W', hex(value)]
            csv_writer.writerow(row) 
    return True

def M65Q68I2CRead_normal(regAddr):
   
    value = 0
    if ft4222DevA != None:
        wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(devAddr), hex(regAddr), 'R', hex(value)]
            csv_writer.writerow(row)
    return value

def M65Q68I2CWrite_CRC(regAddr, value):
    
    if ft4222DevA != None:
        wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF), value)
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True

def M65Q68I2CRead_CRC(regAddr):
   
    value = 0
    if ft4222DevA != None:
        wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF))
        ft4222DevA.i2cMaster_WriteEx(devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
        value = rdBuf[0]
    return value

def spiGMSLInBandControl(value):
    if ft4222DevA != None:
        wrBuf = [value & 0xff,]
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)

def spiGMSLReadLocal():
    if ft4222DevA != None:
        rdBuf = ft4222DevA.spiMaster_SingleRead(1, True)      
        value = rdBuf[0]
        return value

def spiSendReadCmdToMcp2515(addr):
    MCP2515_READ_CMD  = 0x03
    if ft4222DevA != None:
        wrBuf = [MCP2515_READ_CMD, addr & 0xff, 0x00]
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)

def spiSendWriteCmdToMcp2515(addr, value):
    MCP2515_WRITE_CMD = 0x02
    if ft4222DevA != None:
        wrBuf = [MCP2515_WRITE_CMD, addr & 0xff, value & 0xff]
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)

def spiReadMcp2515Reg(addr):
    MCP2515_READ_CMD  = 0x03
    value = 0
    if ft4222DevA != None:
        wrBuf = [MCP2515_READ_CMD, addr & 0xff]
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), False)
        rdBuf = ft4222DevA.spiMaster_SingleRead(1, True)      
        value = rdBuf[0]
    return value

def spiWriteMcp2515Reg(addr, value):
    MCP2515_WRITE_CMD = 0x02
    if ft4222DevA != None:
        wrBuf = [MCP2515_WRITE_CMD, addr & 0xff, 0x00, value & 0xff]
        ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), True)
    return True

def spiReadC3(addr, size):
    wrBuf = (0x01, (addr >> 24), (addr >> 16) & 0xff, (addr >> 8) & 0xff, addr & 0xff)
    ft4222DevA.spiMaster_SingleWrite(bytearray(wrBuf), False)
    rdBuf = ft4222DevA.spiMaster_SingleRead(size, True)      
    value = rdBuf[0]
    return value

def i2cReadAxiMasterReg(addr):
    value = 0
    if ft4222DevA != None:
        wrBuf = [(addr >> 8) & 0xff, addr & 0xff]
        ft4222DevA.i2cMaster_WriteEx(0x70, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
        rdBuf = ft4222DevA.i2cMaster_ReadEx(0x70, ft4222.I2CMaster.Flag.START_AND_STOP, 4)
        value = rdBuf[0] + (rdBuf[1] << 8) + (rdBuf[2] << 16) + (rdBuf[3] << 24)
    return value

def i2cWriteAxiMasterReg(addr, value):
    if ft4222DevA != None:
        wrBuf = [(addr >> 8) & 0xff, addr & 0xff, value & 0xff, (value >> 8) & 0xff, (value >> 16) & 0xff, (value >> 24) & 0xff]
        ft4222DevA.i2cMaster_WriteEx(0x70, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
    return True

def axiI2cMasterEnable():
    i2cWriteAxiMasterReg(0x0100, 0x02) # CR, reset tx fifo
    i2cWriteAxiMasterReg(0x0100, 0x01) # CR, enable axi-iic
    return

def axiI2cMasterWrite(devAddraxi, regAddr, regValue):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:            
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddraxi << 1)) # tx fifo, start + dev addr
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x200 | regValue)       # tx fifo, reg value + stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    return True

def axiI2cMasterRead(devAddraxi, regAddr):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddraxi << 1)) # tx fifo, start + dev addr (w)
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x101 | (devAddraxi << 1)) # tx fifo, start + dev addr (r)
    i2cWriteAxiMasterReg(0x0108, 0x200)                  # tx fifo, stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    value = i2cReadAxiMasterReg( 0x010c) # rx fifo
    return value

def I2CSlaveAddressSet(addr,):
    if ft4222DevA != None:
        ft4222DevA.FT4222_I2CSlave_SetAddress(addr)
    return True

def I2CSlaveAddressGet():
    if ft4222DevA != None:
        readbuff = ft4222DevA.FT4222_I2CSlave_GetAddress()
        value = readbuff[0]
    return value
       
def I2CSalveWrite(data,size): # write i2c slave , it will cache the data and wait for i2c master access
    if ft4222DevA != None:
        ft4222DevA.FT4222_I2CSlave_Write(data,size)
    return True

def SPISalveWrite(addr,value): # write spi slave , it will cache the data and wait for i2c master access
    if ft4222DevA != None:
        wrBuf = (addr & 0xff, (value>>8)&0xFF,value&0xFF)
        ft4222DevA.FT4222_I2CSlave_Write(bytearray(wrBuf),True)
    return True     
def SPISlaveRead(): # read spi slave, it will get data from spi master
    if ft4222DevA != None:
        rdBuf = ft4222DevA.spiSlave_Read(2, True) 
        value = rdBuf[0]
    return value
    
def readReg(moduleName, addr):
    # devAddr=0x69
    CRC=False
    if moduleName=='c2m':
        if CRC==False:
            value = M65Q68I2CRead_normal(addr)
        else:
            value = M65Q68I2CRead_CRC(addr)
    elif moduleName=='m2c':
        if CRC==False:
            value = M66S68I2CRead_normal(addr)
        else:
            value = M66S68I2CRead_CRC(addr)   
    
    # if save_data:
    #     with open(data_addr, "a", encoding="utf-8", newline="") as f:
    #         csv_writer = csv.writer(f)
    #         row = [hex(devAddr), hex(addr), 'R', hex(value)]
    #         csv_writer.writerow(row) 
    
    return value

def writeReg(moduleName, addr, value):
    
    CRC=False
    status=False
    if moduleName=='c2m':
        
        if CRC==False:
            M65Q68I2CWrite_normal(addr, value) 
            status=True
        else:
            M65Q68I2CWrite_CRC(addr, value)
            status=True
    elif moduleName=='m2c':
        if CRC==False:
            M66S68I2CWrite_normal(addr, value) 
            status=True
        else:
            M66S68I2CWrite_CRC(addr, value)
            status=True
    # if save_data:
    #     with open(data_addr, "a", encoding="utf-8", newline="") as f:
    #         csv_writer = csv.writer(f)
    #         row = [hex(devAddr), hex(addr), 'W', hex(value)]
    #         csv_writer.writerow(row) 
    return status

def readSingleData(pollingName):
    data  = []
    return data

def readBulkData(pollingName, dataLen):
    data  = []
    return data
