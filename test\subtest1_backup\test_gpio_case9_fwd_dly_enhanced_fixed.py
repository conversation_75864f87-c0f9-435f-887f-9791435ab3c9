# -*- coding: utf-8 -*-
"""
GPIO延迟补偿测试 - 修复版本

基于test_gpio_case9_fwd_dly.py，添加延迟补偿功能
关键修复：添加S68_AddrTrans地址转换设置，解决I2C通信问题
"""
import time
import pytest
import os

# 延迟补偿测试配置
TEST_CONFIG = {
    'active_links': [0],                    # 默认使用Link0
    'q68_source_gpio': 0,                  # Q68源GPIO
    's68_target_gpios': [0, 1, 2, 3, 4, 5, 6, 7, 8],  # S68目标GPIO列表
    'signal_id': 11,                        # GPIO信号ID
    'observation_time': 1,                  # 每个频率的观察时间(秒)
    
    # 延迟补偿配置
    'delay_compensation_config': {
        'enable_dly_comp': True,            # 强制启用延迟补偿
        'fwd_dly_test_values': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 63],  # fwd_dly测试值序列
        'test_mode': 'fwd_dly_sweep',       # 测试模式：fwd_dly扫描
        'default_fwd_dly': 0,               # 默认fwd_dly值
    },
    
    # 示波器配置 - 专门用于延迟补偿测试
    'oscilloscope_config': {
        'enable_screenshot': True,          # 启用示波器截图
        'trigger_source': 'C1',             # 使用模拟通道C1触发
        'frequency': 30,                    # 固定30Hz频率
        'timebase': '20ms',                 # 对应时基
        'waveform_type': 'SQUARE',          # 方波
        'amplitude': 1.8,                   # 1.8Vpp
        'offset': 0.9,                      # 900mVdc偏移
        'screenshot_folder': 'U-disk0/gpiotest/fwd_dly_test',  # 延迟补偿测试截图文件夹
        'persistence_mode': 'INFinite',     # 余晖模式
        'probe_wait_time': 5,               # 第一次截图前等待时间
        'observation_time': 3,              # 每个延迟值的观察时间
    }
}


class GPIO_DelayCompensation_Tester:
    """GPIO延迟补偿测试器 - 修复版本"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.get_link_status = devices['get_link_status']
        self.oscilloscope = devices.get('oscilloscope')

        # 获取top模块的函数
        self.power_off = devices.get('power_off')
        self.power_on = devices.get('power_on')
        self.setup_communication = devices.get('setup_communication')

    def configure_delay_compensation_parameters(self, fwd_dly_value, rvs_dly_value=0, target='q68'):
        """
        配置延迟补偿参数 - 修正版本

        根据参考代码，正确的配置顺序应该是：
        1. 先配置fwd_dly和rvs_dly参数
        2. 再调用GPIORemoteTx方法

        Args:
            fwd_dly_value: 前向延迟值 (0-63)，单位3.5us
            rvs_dly_value: 反向延迟值 (0-63)，单位3.5us，默认0
            target: 目标设备 ('q68' 或 's68')
        """
        try:
            if target == 'q68':
                print(f"  🔧 配置Q68延迟补偿参数...")

                # 1. 配置前向延迟 (fwd_dly) - 控制Q68发送到S68的延迟
                print(f"    📤 配置fwd_dly = {fwd_dly_value} (前向延迟: {fwd_dly_value * 3.5:.1f}us)")
                self.q68.c2m.wr_gpios_ctrl0_fields(fwd_dly=fwd_dly_value)

                # 2. 配置反向延迟 (rvs_dly) - 控制S68返回到Q68的延迟
                print(f"    📥 配置rvs_dly = {rvs_dly_value} (反向延迟: {rvs_dly_value * 3.5:.1f}us)")
                self.q68.c2m.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)

                print(f"    ✅ Q68延迟补偿参数配置完成")

            elif target == 's68':
                print(f"  🔧 配置S68延迟补偿参数...")

                # S68端延迟配置
                print(f"    📤 配置S68 fwd_dly = {fwd_dly_value} (前向延迟: {fwd_dly_value * 3.5:.1f}us)")
                self.q68_remote.m2c.wr_gpios_ctrl0_fields(fwd_dly=fwd_dly_value)

                print(f"    📥 配置S68 rvs_dly = {rvs_dly_value} (反向延迟: {rvs_dly_value * 3.5:.1f}us)")
                self.q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)

                print(f"    ✅ S68延迟补偿参数配置完成")

            print(f"    📊 参数总结: fwd_dly={fwd_dly_value}, rvs_dly={rvs_dly_value}")
            return True

        except Exception as e:
            print(f"    ❌ {target.upper()}延迟补偿参数配置失败: {e}")
            return False

    def setup_address_translation_for_delay_compensation(self, link_id, q68_gpio):
        """
        为延迟补偿测试设置地址转换 - 关键修复步骤！
        
        这是解决I2C通信错误的关键步骤，必须在配置S68 GPIO之前执行
        """
        try:
            print(f"  📡 设置Link{link_id}地址转换...")
            
            # 根据GPIO类型确定I2C总线配置
            if q68_gpio in [15, 16]:  # 特殊GPIO使用I2C1
                i2c_bus_config = 1
                print(f"    ⚠️ 检测到特殊GPIO{q68_gpio} (I2C1引脚)，使用i2c_bus=1")
            else:
                i2c_bus_config = 0
                print(f"    📋 标准GPIO{q68_gpio}，使用i2c_bus=0")
            
            # 设置S68地址转换 - 这是I2C通信的关键步骤
            self.q68_remote.S68_AddrTrans(
                link=link_id,
                q68_iic_addr=0x73,
                s68_iic_addr=0x40,
                s68_retrans_addr=self.s68_res_dev[link_id],
                sensor_addr=0x24,
                sensor_retrans_addr=0x24 + link_id,
                i2c_bus=i2c_bus_config
            )
            
            print(f"    ✅ Link{link_id} 地址转换设置完成")
            print(f"    📊 配置: 0x{self.s68_res_dev[link_id]:02X} -> 0x40 (i2c_bus={i2c_bus_config})")
            
            return True, i2c_bus_config
            
        except Exception as e:
            print(f"    ❌ Link{link_id} 地址转换设置失败: {e}")
            return False, 0

    def configure_q68_gpio_with_delay_compensation(self, q68_gpio, signal_id, link_id, fwd_dly_value):
        """
        配置Q68 GPIO并启用延迟补偿
        """
        try:
            print(f"  📤 配置Q68 GPIO{q68_gpio}为发送端 (延迟补偿模式)...")
            
            # 1. 配置延迟补偿参数 (必须在GPIORemoteTx之前)
            if not self.configure_delay_compensation_parameters(fwd_dly_value, 0, 'q68'):
                return False
            
            # 2. 设置MFN
            self.q68.MFNSet(gpio=q68_gpio, mfn=0)
            print(f"    ✅ GPIO{q68_gpio} MFN设置完成")
            
            # 3. 配置远程发送，强制启用延迟补偿
            dly_comp_en = 1 if TEST_CONFIG['delay_compensation_config']['enable_dly_comp'] else 0
            self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id, link_id=link_id, dly_comp_en=dly_comp_en)
            
            comp_status = "启用" if dly_comp_en else "禁用"
            print(f"    ✅ GPIO{q68_gpio} 远程发送配置完成")
            print(f"    📊 延迟补偿: {comp_status}, fwd_dly: {fwd_dly_value} ({fwd_dly_value * 3.5:.1f}us)")
            
            return True
            
        except Exception as e:
            print(f"    ❌ Q68 GPIO{q68_gpio} 延迟补偿配置失败: {e}")
            return False

    def check_i2c_communication_error(self, error_message):
        """
        检查I2C通信错误

        Args:
            error_message: 错误信息字符串

        Returns:
            bool: True表示是I2C通信错误，False表示其他错误
        """
        i2c_error_keywords = [
            "send_cmd_i2c_get cmd ret error",
            "send_cmd_i2c_set cmd ret error",
            "send_cmd_i2c_get respond error",
            "send_cmd_i2c_set respond error",
            "send_cmd_i2c_get usb_send_buff error",
            "send_cmd_i2c_set usb_send_buff error"
        ]

        error_str = str(error_message).lower()
        for keyword in i2c_error_keywords:
            if keyword.lower() in error_str:
                return True
        return False

    def configure_s68_gpios_standard(self, link_id, s68_gpio_range, signal_id):
        """
        标准S68 GPIO配置 - 增强错误检测版本
        """
        try:
            print(f"  📥 配置S68 GPIO{s68_gpio_range}为接收端...")

            # 设置设备地址
            target_addr = self.s68_res_dev[link_id]
            self.q68_remote.dongle.devAddr = target_addr
            print(f"    📍 设备地址: 0x{target_addr:02X}")

            time.sleep(0.1)  # 等待地址设置生效

            success_count = 0
            i2c_error_count = 0

            for s68_gpio in s68_gpio_range:
                try:
                    # 设置MFN为0 (GPIO功能)
                    self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                    # 设置远程接收
                    self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
                    print(f"      ✅ S68 GPIO{s68_gpio} 配置完成")
                    success_count += 1

                except Exception as e:
                    error_msg = str(e)
                    if self.check_i2c_communication_error(error_msg):
                        i2c_error_count += 1
                        print(f"      🔴 S68 GPIO{s68_gpio} I2C通信错误: {e}")
                        print(f"      💡 提示: 这可能是由于地址转换未正确设置导致的")
                    else:
                        print(f"      ❌ S68 GPIO{s68_gpio} 其他错误: {e}")

            print(f"    📊 S68 GPIO配置: {success_count}/{len(s68_gpio_range)} 成功")
            if i2c_error_count > 0:
                print(f"    🔴 I2C通信错误: {i2c_error_count}/{len(s68_gpio_range)}")
                print(f"    💡 建议: 检查S68_AddrTrans地址转换设置是否正确")

            # 如果有成功的配置，就认为测试可以继续
            return success_count > 0

        except Exception as e:
            print(f"    ❌ S68 GPIO配置失败: {e}")
            if self.check_i2c_communication_error(str(e)):
                print(f"    🔴 检测到I2C通信错误，可能需要检查地址转换设置")
            return False

    def test_single_gpio_fwd_dly_single_value(self, link_id, q68_gpio, s68_gpio_range, signal_id, fwd_dly_value):
        """
        单个GPIO单个fwd_dly值的测试 - 修复版本
        """
        try:
            print(f"\n🔧 开始延迟补偿测试: Link{link_id}, GPIO{q68_gpio}, fwd_dly={fwd_dly_value}")
            
            # 步骤1: 配置Link
            print(f"  🔗 步骤1: 配置Link{link_id}...")
            if not self.configure_links([link_id]):
                print(f"    ❌ Link{link_id}配置失败")
                return False
            print(f"    ✅ Link{link_id}配置完成")
            
            # 步骤2: 设置地址转换 (关键的修复步骤!)
            print(f"  📡 步骤2: 设置地址转换...")
            addr_success, _ = self.setup_address_translation_for_delay_compensation(link_id, q68_gpio)
            if not addr_success:
                return False
            
            # 步骤3: 配置Q68 GPIO（带延迟补偿）
            print(f"  📤 步骤3: 配置Q68 GPIO{q68_gpio} (延迟补偿)...")
            if not self.configure_q68_gpio_with_delay_compensation(q68_gpio, signal_id, link_id, fwd_dly_value):
                return False
            
            # 步骤4: 配置S68 GPIOs
            print(f"  📥 步骤4: 配置S68 GPIO{s68_gpio_range}...")
            if not self.configure_s68_gpios_standard(link_id, s68_gpio_range, signal_id):
                return False
            
            # 步骤5: 示波器测试
            print(f"  📷 步骤5: 示波器延迟测试...")
            self.oscilloscope_delay_compensation_screenshot(link_id, q68_gpio, fwd_dly_value)
            
            print(f"  ✅ 延迟补偿测试完成: fwd_dly={fwd_dly_value}")
            return True
            
        except Exception as e:
            print(f"    ❌ 延迟补偿测试失败: {e}")
            return False

    def oscilloscope_delay_compensation_screenshot(self, link_id, q68_gpio, fwd_dly_value):
        """
        延迟补偿专用示波器截图 - 修正版本

        修正频率切换逻辑：先设置不同频率，再切换到目标频率，确保波形正常显示
        """
        osc_config = TEST_CONFIG['oscilloscope_config']

        if not osc_config['enable_screenshot'] or self.oscilloscope is None:
            print("  📷 示波器截图已禁用或不可用")
            return

        try:
            print(f"  📷 开始延迟补偿示波器测试...")

            # 设置时基
            self.oscilloscope.Set_Timebase_Scale(timebase_scale=osc_config['timebase'])
            print(f"    ⏱️ 时基设置: {osc_config['timebase']}")

            # 修正：频率切换逻辑，确保波形正常显示
            target_frequency = osc_config['frequency']
            temp_frequency = 1000 if target_frequency != 1000 else 500  # 选择不同的临时频率

            print(f"    🔄 频率切换修正逻辑...")

            # 步骤1: 先设置临时频率
            print(f"      📡 设置临时频率: {temp_frequency}Hz")
            self.oscilloscope.Set_Wavegen_Basic(
                waveform=osc_config['waveform_type'],
                frequency=temp_frequency,
                amplitude=osc_config['amplitude'],
                offset=osc_config['offset'],
                output_state='ON',
                load=50
            )
            time.sleep(0.5)  # 短暂等待

            # 步骤2: 切换到目标频率
            print(f"      🎯 切换到目标频率: {target_frequency}Hz")
            self.oscilloscope.Set_Wavegen_Basic(
                waveform=osc_config['waveform_type'],
                frequency=target_frequency,
                amplitude=osc_config['amplitude'],
                offset=osc_config['offset'],
                output_state='ON',
                load=50
            )
            print(f"    ✅ 波形配置完成: {target_frequency}Hz {osc_config['waveform_type']}")

            # 等待信号稳定
            print(f"    ⏳ 等待信号稳定 ({osc_config['observation_time']}秒)...")
            time.sleep(osc_config['observation_time'])

            # 截图
            timestamp = time.strftime('%m%d_%H%M%S')
            screenshot_filename = f"DelayComp_Link{link_id}_GPIO{q68_gpio}_fwd_dly{fwd_dly_value:02d}_{timestamp}.png"
            screenshot_path = f"{osc_config['screenshot_folder']}/{screenshot_filename}"

            # 创建截图目录
            os.makedirs(osc_config['screenshot_folder'], exist_ok=True)

            self.oscilloscope.Save_Image(
                filepath=screenshot_path,
                image_format="PNG",
                invert="OFF",
                menu="MOF"
            )
            print(f"    📸 截图保存: {screenshot_filename}")

        except Exception as e:
            print(f"    ❌ 示波器测试失败: {e}")


# 测试函数
@pytest.mark.fast
def test_gpio_delay_compensation_single_link(devices):
    """
    单Link GPIO延迟补偿测试 - 修复版本
    """
    print("\n" + "="*80)
    print("🚀 GPIO延迟补偿测试 - 单Link模式 (修复版)")
    print("="*80)
    
    tester = GPIO_DelayCompensation_Tester(devices)
    
    # 使用配置中的参数
    link_id = TEST_CONFIG['active_links'][0]
    q68_gpio = TEST_CONFIG['q68_source_gpio']
    s68_gpio_range = TEST_CONFIG['s68_target_gpios']
    signal_id = TEST_CONFIG['signal_id']
    
    # 测试几个关键的fwd_dly值
    test_fwd_dly_values = [0, 10, 20, 30, 63]  # 简化测试
    
    results = {}
    for i, fwd_dly_value in enumerate(test_fwd_dly_values, 1):
        print(f"\n📍 测试进度: {i}/{len(test_fwd_dly_values)} - fwd_dly = {fwd_dly_value}")
        
        result = tester.test_single_gpio_fwd_dly_single_value(
            link_id, q68_gpio, s68_gpio_range, signal_id, fwd_dly_value
        )
        results[fwd_dly_value] = result
        
        if result:
            print(f"  ✅ fwd_dly = {fwd_dly_value} 测试通过")
        else:
            print(f"  ❌ fwd_dly = {fwd_dly_value} 测试失败")
        
        # 测试间隔
        if i < len(test_fwd_dly_values):
            time.sleep(1)
    
    # 验证测试结果
    assert len(results) > 0, "延迟补偿测试未执行"
    successful_tests = sum(1 for result in results.values() if result)
    assert successful_tests > 0, "所有延迟补偿测试都失败了"
    
    print(f"\n✅ 延迟补偿测试完成，成功率: {successful_tests}/{len(results)}")


if __name__ == "__main__":
    """
    GPIO延迟补偿测试用例 - 修复版本
    
    🔧 关键修复:
    - 添加S68_AddrTrans地址转换设置，解决I2C通信错误
    - 正确的测试流程：Link配置 → 地址转换 → Q68配置 → S68配置 → 示波器测试
    
    🎯 测试目标:
    - 验证fwd_dly延迟补偿参数的效果 (0-63, 单位3.5us)
    - 测试不同延迟值对GPIO信号传输的影响
    - 通过示波器观察延迟补偿的实际效果
    
    🚀 使用方法:
    pytest test_gpio_case9_fwd_dly_enhanced_fixed.py::test_gpio_delay_compensation_single_link
    """
    print("GPIO延迟补偿测试用例 - 修复版本")
    print("请使用pytest运行测试")
