# I2C错误检测功能使用指南

## 概述

新的I2C错误检测功能已集成到 `conftest.py` 中，可以自动检测和处理 `send_cmd_i2c_get cmd ret error: 1` 等I2C通信错误。

## 功能特性

### 1. 自动错误检测
- 监控输出中的I2C错误模式
- 支持多种错误模式匹配
- 实时错误计数和历史记录

### 2. 自动重试机制
- 检测到错误时自动重试
- 可配置重试次数和延迟
- 重试失败时提供详细错误信息

### 3. 安全的GPIO操作函数
- `safe_gpio_remote_tx()` - 安全的GPIO发送配置
- `safe_gpio_remote_rx()` - 安全的GPIO接收配置
- `safe_link_status_check()` - 安全的链路状态检查

## 配置选项

在 `conftest.py` 中的配置：

```python
I2C_ERROR_DETECTION = {
    'enable': True,                        # 是否启用I2C错误检测
    'retry_count': 3,                      # 重试次数
    'retry_delay': 0.1,                    # 重试间隔(秒)
    'error_patterns': [                    # 错误模式匹配
        'send_cmd_i2c_get cmd ret error: 1',
        'i2c communication error',
        'I2C_ERROR',
        'timeout'
    ]
}
```

## 使用方法

### 1. 在现有测试中使用安全函数

**原来的代码：**
```python
# 配置S68 GPIO
q68_remote.dongle.devAddr = s68_res_dev[link]
q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
q68_remote.GPIORemoteTx(gpio=s68_gpio, tx_id=signal_id)

# 配置Q68 GPIO
q68_remote.M2CMFNSet(gpio=q68_gpio, mfn=0)
q68_remote.M2CGPIORemoteRx(gpio=q68_gpio, rx_id=signal_id)
```

**使用安全函数的代码：**
```python
# 获取安全函数
safe_gpio_remote_tx = devices['safe_gpio_remote_tx']
safe_gpio_remote_rx = devices['safe_gpio_remote_rx']

# 配置S68 GPIO (带错误检测和重试)
success = safe_gpio_remote_tx(
    q68_remote=q68_remote,
    gpio=s68_gpio,
    tx_id=signal_id,
    s68_dev_addr=s68_res_dev[link]
)

# 配置Q68 GPIO (带错误检测和重试)
success = safe_gpio_remote_rx(
    q68_remote=q68_remote,
    gpio=q68_gpio,
    rx_id=signal_id
)
```

### 2. 使用通用重试机制

```python
retry_on_i2c_error = devices['retry_on_i2c_error']

def my_i2c_operation():
    # 你的I2C操作代码
    q68_remote.some_operation()
    return result

# 带重试的执行
try:
    result = retry_on_i2c_error(my_i2c_operation)
    print("操作成功")
except Exception as e:
    print(f"操作失败: {e}")
```

### 3. 错误统计和监控

```python
i2c_error_detector = devices['i2c_error_detector']

# 重置错误计数
i2c_error_detector.reset_error_count()

# 执行测试...

# 获取错误统计
error_summary = i2c_error_detector.get_error_summary()
print(f"I2C错误统计: {error_summary}")
```

## 在现有测试文件中集成

### 修改 test_gpio_case6_8auto2.py

在 `GPIO_S68_Q68_AutoTester2` 类中使用安全函数：

```python
def configure_gpio_path_safe(self, link, s68_gpio, q68_gpio, signal_id):
    """安全的GPIO路径配置 (带I2C错误检测)"""
    
    # 获取安全函数
    safe_gpio_remote_tx = self.devices['safe_gpio_remote_tx']
    safe_gpio_remote_rx = self.devices['safe_gpio_remote_rx']
    
    s68_dev_addr = self.s68_res_dev[link]
    
    try:
        # 配置S68 GPIO为发送模式
        tx_success = safe_gpio_remote_tx(
            q68_remote=self.q68_remote,
            gpio=s68_gpio,
            tx_id=signal_id,
            s68_dev_addr=s68_dev_addr
        )
        
        if not tx_success:
            return False
        
        # 配置Q68 GPIO为接收模式
        rx_success = safe_gpio_remote_rx(
            q68_remote=self.q68_remote,
            gpio=q68_gpio,
            rx_id=signal_id
        )
        
        return rx_success
        
    except Exception as e:
        print(f"❌ GPIO路径配置失败: {e}")
        return False
```

## 错误处理策略

### 1. 错误检测
- 自动监控所有I2C操作的输出
- 匹配预定义的错误模式
- 记录错误发生的时间和上下文

### 2. 重试策略
- 检测到错误时立即重试
- 重试间隔可配置 (默认0.1秒)
- 最大重试次数可配置 (默认3次)

### 3. 错误报告
- 详细的错误统计信息
- 错误历史记录
- 测试结束时的错误摘要

## 调试和监控

### 启用详细日志
```python
# 在测试开始时
i2c_error_detector = devices['i2c_error_detector']
i2c_error_detector.reset_error_count()

# 在测试结束时
error_summary = i2c_error_detector.get_error_summary()
if error_summary['error_count'] > 0:
    print(f"⚠️ 检测到 {error_summary['error_count']} 次I2C错误")
    print(f"错误历史: {i2c_error_detector.error_history}")
```

### 临时禁用错误检测
```python
# 修改配置
from test.conftest import I2C_ERROR_DETECTION
I2C_ERROR_DETECTION['enable'] = False
```

## 最佳实践

1. **在测试开始时重置错误计数器**
2. **使用安全函数替代直接的I2C操作**
3. **在测试结束时检查错误统计**
4. **根据错误频率调整重试参数**
5. **保留原有的错误处理逻辑作为备份**

## 示例测试

参考 `test/test_i2c_error_detection_example.py` 文件中的完整示例。
