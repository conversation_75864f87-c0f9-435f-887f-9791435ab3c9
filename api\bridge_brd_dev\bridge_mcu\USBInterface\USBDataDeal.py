#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import serial
import serial.tools.list_ports
import time
import threading
from USBInterface import CmdFuncation
from threading import Lock

serial_port = serial.Serial(None, baudrate=115200, timeout=0.5)
is_exit: bool = False
usbdev_Lock = Lock()


def check_cmd(cmd_buff: bytearray, cmd_len: int):
    return True


# 处理完整指令
def deal_cmd(cmd_buff: bytearray, cmd_len: int):
    if not check_cmd(cmd_buff, cmd_len):
        return False

    CmdFuncation.publish_cmd(cmd_buff, cmd_len)


cmd_data = []
cmd_idx: int = 0


# 解析数据缓冲区
def deal_cmd_buff(data_all):
    global cmd_idx
    global cmd_data

    for byte in data_all:
        if cmd_idx != 0:
            cmd_data.append(byte)
            cmd_idx += 1
            # 判断cmd_data中是否存储了一条完整的cmd数据，如果是一条完整的数据，就进行cmd处理。
            if cmd_data[cmd_idx - 1] == 0xA5 and cmd_data[cmd_idx - 2] == 0xA5:
                deal_cmd(bytearray(cmd_data), cmd_idx)
                cmd_data.clear()
                cmd_idx = 0

            # 当接收到1024个字节，还是没有找到cmd结束标记，说明数据已经出问题了，这里直接将数据丢弃，重新解析。
            if cmd_idx >= 1024:
                cmd_idx = 0
        else:
            # 一条新的数据，必须由AA开头，否则就丢弃异常数据。
            if byte == 0xAA:
                cmd_data.append(byte)
                cmd_idx += 1


# 接收usb传递过来的数据，放入到缓冲区中
def usb_receive_data_thread():
    print("USB Port Receive Thread Start....")

    serial_port.flush()
    while not is_exit:
        # 获取当前可读的数据
        data_num = serial_port.inWaiting()
        if data_num > 0:
            datas = serial_port.read(data_num)
            deal_cmd_buff(datas)

        else:
            time.sleep(0.005)

    print("USB Port Receive Thread Exit....\n")


# 扫码符合要求的设备
def ser_ee_dev_scan():
    usbdev_list:list = []
    port_list = list(serial.tools.list_ports.comports())
    for i in range(0, len(port_list)):
        if port_list[i].vid == 1155 and port_list[i].pid == 22336:
            usbdev_list.append(port_list[i])

    return usbdev_list


# 打开设备
def serial_open(port_name):
    global serial_port
    global is_exit

    if len(port_name) == 0:
        return -1
    serial_port.name = port_name
    serial_port = serial.Serial(port_name, baudrate=115200, timeout=0.5)
    if not serial_port.isOpen():  # 判断串口是否成功打开
        print("打开串口成功: ", serial_port.name)  # 输出串口号
    else:
        print("打开串口成功: ", serial_port.name)  # 输出串口号

    is_exit = False
    run_thread = threading.Thread(target=usb_receive_data_thread, name="serial_receive")
    # 设置为daemon线程，程序在退出的时候，线程自动退出
    run_thread.daemon = True
    run_thread.start()

    return 0


# 关闭设备
def serial_close():
    global is_exit

    time.sleep(0.1)
    is_exit = True
    time.sleep(0.1)
    if serial_port.isOpen():
        serial_port.close()
        print("关闭串口成功: ", serial_port.name)  # 输出串口号
    else:
        print("串口已处于关闭状态: ", serial_port.name)


# 发送函数
def usb_send_buff(data):
    if not serial_port.isOpen():    # 串口是打开的，才进行数据传递
        return -1

    usbdev_Lock.acquire()
    serial_port.write(data)
    usbdev_Lock.release()
    return 0
