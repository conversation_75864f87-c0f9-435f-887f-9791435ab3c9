# -*- coding: utf-8 -*-
"""

GPIO延迟补偿测试 - 4个Link一起测试

基于test_gpio_case9_fwd_dly.py，专门用于4个Link的延迟补偿测试
关键修复：添加S68_AddrTrans地址转换设置，解决I2C通信问题

在配置前不要打开wgen
"""
import logging
import time
import pytest
import os
import tkinter as tk
from tkinter import messagebox

# 延迟补偿测试配置
TEST_CONFIG = {
    'all_links': [0, 1, 2, 3],              # 所有4个Link
    'q68_source_gpio': 0,                   # Q68源GPIO
    's68_target_gpios': [0, 1, 2, 3, 4, 5, 6, 7, 8],  # S68目标GPIO列表
    'signal_id': 11,                        # GPIO信号ID
    
    # 延迟补偿配置
    'delay_compensation_config': {
        'enable_dly_comp': True,            # 强制启用延迟补偿
        'fwd_dly_test_values': [0],         # fwd_dly测试值序列，固定为0
        'rvs_dly_test_values': [0, 4, 10, 20, 30, 40, 50, 60, 63],  # rvs_dly测试值序列，全范围测试
        # 'rvs_dly_test_values': [0, 4, 63],
        'test_both_delays': True,           # 启用rvs_dly全面测试
        'delay_explanation': {
            'fwd_dly': '前向延迟',
            'rvs_dly': '反向延迟',
            'unit': '3.5us per step',
            'range': '0-63 (6-bit parameter)'
        }
    },
    
    # 示波器配置
    'oscilloscope_config': {
        'enable_screenshot': True,          # 启用示波器截图
        'frequency': 30,                    # 固定30Hz频率
        'timebase': '50us',                 # 对应时基
        'waveform_type': 'SQUARE',          # 方波
        'amplitude': 1.8,                   # 1.8Vpp
        'offset': 0.9,                      # 900mVdc偏移
        'screenshot_folder': 'U-disk0/gpiotest/fwd_dly_all_links',  # 截图文件夹
        'observation_time': 3,              # 每个延迟值的观察时间
    }
}


class GPIO_DelayCompensation_AllLinks_Tester:
    """GPIO延迟补偿测试器 - 专门用于4个Link一起测试"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def show_gpio_delay_compensation_dialog(self, q68_gpio, fwd_dly_values, rvs_dly_values):
        """显示GPIO延迟补偿测试确认对话框"""
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            root.attributes('-topmost', True)  # 置顶显示

            # 计算测试数量
            total_delay_tests = len(fwd_dly_values) * len(rvs_dly_values)

            # 显示确认对话框
            title = f"Q68 GPIO{q68_gpio} 延迟补偿测试确认"
            message = f"""即将测试:
所有Links(0-3) - Q68 GPIO{q68_gpio} 延迟补偿测试

📊 测试参数:
  - fwd_dly值: {fwd_dly_values} (前向延迟)
  - rvs_dly值: {rvs_dly_values} (反向延迟)
  - 总测试数: {total_delay_tests} 个延迟组合
  - S68目标GPIO: 0-8 (所有Links同时配置)

⚠️ 注意：每个延迟组合都会生成独立的示波器截图
预计耗时：约 {total_delay_tests * 10} 秒 (每个延迟组合约10秒)

📷 截图保存路径: U-disk0/gpiotest/fwd_dly_all_links/Q68TOS68_GPIO{q68_gpio}/

是否继续测试此Q68 GPIO?

[是] = 继续测试
[否] = 跳过此Q68 GPIO
[取消] = 停止所有测试"""

            result = messagebox.askyesnocancel(
                title=title,
                message=message,
                icon='question'
            )

            root.destroy()  # 销毁窗口

            # 返回结果：True=继续, False=跳过, None=取消全部
            return result

        except Exception as e:
            print(f"    - 弹窗显示失败: {e}")
            print(f"    - 自动继续测试...")
            return True  # 如果弹窗失败，默认继续

    def configure_all_links_delay_compensation(self, q68_gpio, signal_id, fwd_dly_value, rvs_dly_value):
        """
        配置所有Links的延迟补偿 - 合并版本

        按照参考代码的配置顺序：
        1. 先配置所有Links的S68设备（包含所有目标GPIO）
        2. 最后配置Q68并打开TX

        Args:
            q68_gpio: Q68源GPIO编号
            signal_id: GPIO信号ID
            fwd_dly_value: 前向延迟值 (0-63)，单位3.5us
            rvs_dly_value: 反向延迟值 (0-63)，单位3.5us
        """
        try:
            print(f"  🔧 配置所有Links延迟补偿 (GPIO{q68_gpio}, fwd_dly={fwd_dly_value}, rvs_dly={rvs_dly_value})...")

            # 按照参考代码模式：先配置所有Links的S68
            for link in TEST_CONFIG['all_links']:
                if link < len(self.s68_res_dev):
                    print(f"    � 配置Link{link} S68设备...")
                    s68_iic_dev = self.s68_res_dev[link]
                    self.q68_remote.dongle.devAddr = s68_iic_dev

                    # 配置所有S68目标GPIO
                    s68_target_gpios = TEST_CONFIG['s68_target_gpios']
                    print(f"      📋 配置S68 GPIO{s68_target_gpios}...")

                    for s68_gpio in s68_target_gpios:
                        try:
                            self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                            self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)  # enable frame sync output to camera
                            print(f"        ✅ GPIO{s68_gpio} 配置成功")
                        except Exception as e:
                            print(f"        ❌ GPIO{s68_gpio} 配置失败: {e}")

                    # 配置延迟补偿参数
                    self.q68_remote.m2c.wr_gpios_ctrl0_fields(fwd_dly=fwd_dly_value)
                    self.q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)
                    self.q68_remote.m2c.rd_gpios_ctrl0_fwd_dly()
                    print(f"      ✅ Link{link} S68配置完成 (GPIO数量: {len(s68_target_gpios)})")

            # 最后配置Q68并打开TX (在循环外面，按照参考代码的缩进)
            print(f"    � 配置Q68并启动TX...")
            self.q68.c2m.wr_gpios_ctrl0_fields(fwd_dly=fwd_dly_value)
            self.q68.c2m.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)
            # 配置Q68 GPIO的MFN
            self.q68.MFNSet(gpio=q68_gpio, mfn=0)

            # 启动Q68远程传输，使用Link0作为主Link，启用延迟补偿
            primary_link = TEST_CONFIG['all_links'][3]
            dly_comp_en = 1 if TEST_CONFIG['delay_compensation_config']['enable_dly_comp'] else 0
            self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id, link_id=primary_link, dly_comp_en=dly_comp_en)
            comp_status = "启用" if dly_comp_en else "禁用"
            print(f"    ✅ Q68配置完成，TX已启动")
            print(f"    📊 延迟补偿: {comp_status}, 主Link: {primary_link}")
            print(f"    📊 延迟参数: fwd_dly={fwd_dly_value} ({fwd_dly_value * 3.5:.1f}us), rvs_dly={rvs_dly_value} ({rvs_dly_value * 3.5:.1f}us)")

            return True
        except Exception as e:
            print(f"    ❌ 所有Links延迟补偿配置失败: {e}")
            return False

    def setup_address_translation_for_all_links(self, q68_gpio):
        """为所有Links设置地址转换 - 关键修复步骤"""
        try:
            print(f"  📡 为所有Links设置地址转换...")
            
            # 根据GPIO类型确定I2C总线配置
            i2c_bus_config = 1 if q68_gpio in [15, 16] else 0
            gpio_type = "特殊GPIO (I2C1)" if i2c_bus_config == 1 else "标准GPIO"
            print(f"    📋 GPIO{q68_gpio} ({gpio_type})，使用i2c_bus={i2c_bus_config}")
            
            # 为所有Links设置地址转换
            for link_id in TEST_CONFIG['all_links']:
                if link_id < len(self.s68_res_dev):
                    self.q68_remote.S68_AddrTrans(
                        link=link_id,
                        q68_iic_addr=0x73,
                        s68_iic_addr=0x40,
                        s68_retrans_addr=self.s68_res_dev[link_id],
                        sensor_addr=0x24,
                        sensor_retrans_addr=0x24 + link_id,
                        i2c_bus=i2c_bus_config
                    )
                    print(f"      ✅ Link{link_id} 地址转换: 0x{self.s68_res_dev[link_id]:02X} -> 0x40")
                else:
                    print(f"      ⚠️ Link{link_id} 超出范围，跳过")
            
            print(f"    ✅ 所有Links地址转换设置完成")
            return True
            
        except Exception as e:
            print(f"    ❌ 地址转换设置失败: {e}")
            return False


    def oscilloscope_screenshot(self, q68_gpio, fwd_dly_value, rvs_dly_value):
        """
        示波器截图 - 支持rvs_dly参数版本

        修正频率切换逻辑：先设置不同频率，再切换到目标频率，确保波形正常显示
        截图文件名包含fwd_dly和rvs_dly值
        """
        osc_config = TEST_CONFIG['oscilloscope_config']

        if not osc_config['enable_screenshot'] or self.oscilloscope is None:
            print("  📷 示波器截图已禁用或不可用")
            return

        try:
            print(f"  📷 示波器延迟补偿测试...")

            # 设置时基
            self.oscilloscope.Set_Timebase_Scale(timebase_scale=osc_config['timebase'])
            print(f"    ⏱️ 时基设置: {osc_config['timebase']}")

            # 修正：频率切换逻辑，确保波形正常显示
            target_frequency = osc_config['frequency']
            temp_frequency = 1000 if target_frequency != 1000 else 467  # 选择不同的临时频率

            print(f"    🔄 频率切换修正逻辑...")

            print(f"      🎯 切换到目标频率: {target_frequency}Hz")
            self.oscilloscope.Set_Wavegen_Basic(
                waveform=osc_config['waveform_type'],
                frequency=target_frequency,
                amplitude=osc_config['amplitude'],
                offset=osc_config['offset'],
                output_state='OFF',
                load=50
            )
            # 步骤1: 先设置临时频率
            print(f"      📡 设置临时频率: {temp_frequency}Hz")
            self.oscilloscope.Set_Wavegen_Basic(
                waveform=osc_config['waveform_type'],
                frequency=temp_frequency,
                amplitude=osc_config['amplitude'],
                offset=osc_config['offset'],
                output_state='ON',
                load=50
            )
            time.sleep(0.5)  # 短暂等待

            self.oscilloscope.Set_Wavegen_Frequency(frequency=target_frequency)
            print(f"        - 频率切换完成: {target_frequency}")
            print(f"    ✅ 波形配置完成: {target_frequency}Hz {osc_config['waveform_type']}")

            # 等待信号稳定
            print(f"    ⏳ 等待信号稳定 ({osc_config['observation_time']}秒)...")
            time.sleep(osc_config['observation_time'])

            # # 截图 - 按GPIO分文件夹存储
            # timestamp = time.strftime('%m%d_%H%M%S')
            # screenshot_filename = f"AllLinks_GPIO{q68_gpio}_fwd_dly{fwd_dly_value:02d}_rvs_dly{rvs_dly_value:02d}_{timestamp}.png"

            # # 创建GPIO专用文件夹
            # gpio_folder = f"U-disk0/gpiotest/fwd_dly_all_links/Q68TOS68_GPIO{q68_gpio}"
            # # gpio_folder = f"U-disk0/"
            # screenshot_path = f"{gpio_folder}/{screenshot_filename}"

            # os.makedirs(gpio_folder, exist_ok=True)
            # 截图
            timestamp = time.strftime('%m%d_%H%M%S')
            screenshot_filename = f"AllLinks_GPIO{q68_gpio}_fwd_dly{fwd_dly_value:02d}_rvs_dly{rvs_dly_value:02d}_{timestamp}.png"
            screenshot_path = f"{osc_config['screenshot_folder']}/{screenshot_filename}"

            os.makedirs(osc_config['screenshot_folder'], exist_ok=True)

            self.oscilloscope.Save_Image(
                filepath=screenshot_path,
                image_format="PNG",
                invert="OFF",
                menu="MOFf"
            )
            print(f"    📸 截图保存: {screenshot_filename}")
            time.sleep(1)  #等待保证截图成功
            self.oscilloscope.Set_Wavegen_Output(state='OFF')

        except Exception as e:
            print(f"    ❌ 示波器测试失败: {e}")

    def test_all_links_fwd_dly_single_value(self, q68_gpio, signal_id, fwd_dly_value, rvs_dly_value):
        """4个Link一起的延迟补偿测试 - 修正版本"""
        try:
            print(f"\n🔧 开始全Link延迟补偿测试: GPIO{q68_gpio}")
            print(f"   延迟参数: fwd_dly={fwd_dly_value} ({fwd_dly_value * 3.5:.1f}us), rvs_dly={rvs_dly_value} ({rvs_dly_value * 3.5:.1f}us)")

            # 步骤1: 配置所有Links
            print(f"  🔗 步骤1: 配置所有Links{TEST_CONFIG['all_links']}...")
            if not self.configure_links(TEST_CONFIG['all_links']):
                print(f"    ❌ Links配置失败")
                return False
            print(f"    ✅ 所有Links配置完成")

            # 步骤2: 设置地址转换
            print(f"  📡 步骤2: 设置地址转换...")
            if not self.setup_address_translation_for_all_links(q68_gpio):
                return False

            # 步骤3: 配置所有Links延迟补偿（包含Q68和S68配置）
            print(f"  � 步骤3: 配置所有Links延迟补偿...")
            if not self.configure_all_links_delay_compensation(q68_gpio, signal_id, fwd_dly_value, rvs_dly_value):
                return False

            # 步骤4: 示波器测试
            print(f"  📷 步骤4: 示波器延迟测试...")
            self.oscilloscope_screenshot(q68_gpio, fwd_dly_value, rvs_dly_value)
            
            print(f"  ✅ 全Link延迟补偿测试完成: fwd_dly={fwd_dly_value}, rvs_dly={rvs_dly_value}")
            return True
            
        except Exception as e:
            print(f"    ❌ 全Link延迟补偿测试失败: {e}")
            return False

    def test_all_q68_gpios_delay_compensation(self, q68_gpio_range=None, enable_dialog=True):
        """遍历所有Q68 GPIO进行延迟补偿测试"""
        if q68_gpio_range is None:
            # 默认测试Q68 GPIO 0-14，排除GPIO13 (GPIO13通常用于特殊用途)
            q68_gpio_range = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14]

        fwd_dly_values = TEST_CONFIG['delay_compensation_config']['fwd_dly_test_values']
        rvs_dly_values = TEST_CONFIG['delay_compensation_config']['rvs_dly_test_values']
        signal_id = TEST_CONFIG['signal_id']

        # 计算总测试数量
        delay_tests_per_gpio = len(fwd_dly_values) * len(rvs_dly_values)
        total_tests = len(q68_gpio_range) * delay_tests_per_gpio

        print(f"\n{'='*80}")
        print(f"开始Q68 GPIO延迟补偿自动遍历测试:")
        print(f"  - Q68 GPIOs: {q68_gpio_range}")
        print(f"  - fwd_dly值: {fwd_dly_values}")
        print(f"  - rvs_dly值: {rvs_dly_values}")
        print(f"  - 每个GPIO测试数: {delay_tests_per_gpio} 个延迟组合")
        print(f"  - 总测试数量: {len(q68_gpio_range)} GPIOs × {delay_tests_per_gpio} = {total_tests} 个测试")
        print(f"  - 确认对话框: {'启用' if enable_dialog else '禁用'}")
        print(f"{'='*80}")

        results = {}
        current_gpio = 0
        skipped_gpios = []
        failed_gpios = []

        for q68_gpio in q68_gpio_range:
            current_gpio += 1
            gpio_key = f"Q68_GPIO{q68_gpio}"

            print(f"\n📍 GPIO进度: {current_gpio}/{len(q68_gpio_range)}")
            print(f"当前测试: Q68 GPIO{q68_gpio} 延迟补偿测试")

            # 显示确认对话框 (如果启用)
            if enable_dialog:
                dialog_result = self.show_gpio_delay_compensation_dialog(q68_gpio, fwd_dly_values, rvs_dly_values)

                if dialog_result is None:  # 用户点击取消
                    print(f"❌ 用户取消测试，停止所有后续测试")
                    results[gpio_key] = "cancelled"
                    return {
                        'results': results,
                        'summary': {
                            'total_gpios': current_gpio - 1,
                            'completed_gpios': len([r for r in results.values() if isinstance(r, dict) and r.get('success', False)]),
                            'failed_gpios': len(failed_gpios),
                            'skipped_gpios': len(skipped_gpios),
                            'cancelled': True
                        }
                    }

                elif dialog_result is False:  # 用户选择跳过
                    print(f"⏭️  跳过测试: {gpio_key}")
                    skipped_gpios.append(gpio_key)
                    results[gpio_key] = "skipped"
                    continue

            # 执行延迟补偿测试
            print(f"🔧 开始Q68 GPIO{q68_gpio}延迟补偿测试...")
            gpio_results = {}
            gpio_success_count = 0

            # 双重循环：fwd_dly × rvs_dly
            for fwd_dly_value in fwd_dly_values:
                for rvs_dly_value in rvs_dly_values:
                    test_key = f"fwd{fwd_dly_value}_rvs{rvs_dly_value}"
                    print(f"  📊 测试参数: fwd_dly={fwd_dly_value}, rvs_dly={rvs_dly_value}")

                    try:
                        result = self.test_all_links_fwd_dly_single_value(
                            q68_gpio, signal_id, fwd_dly_value, rvs_dly_value
                        )
                        gpio_results[test_key] = result
                        if result:
                            gpio_success_count += 1
                            print(f"    ✅ 延迟组合测试成功")
                        else:
                            print(f"    ❌ 延迟组合测试失败")
                    except Exception as e:
                        print(f"    ❌ 延迟组合测试异常: {e}")
                        gpio_results[test_key] = False

            # 统计GPIO测试结果
            gpio_total_tests = len(fwd_dly_values) * len(rvs_dly_values)
            gpio_success_rate = (gpio_success_count / gpio_total_tests) * 100 if gpio_total_tests > 0 else 0

            gpio_summary = {
                'success': gpio_success_count > 0,
                'total_tests': gpio_total_tests,
                'successful_tests': gpio_success_count,
                'failed_tests': gpio_total_tests - gpio_success_count,
                'success_rate': gpio_success_rate,
                'results': gpio_results
            }

            results[gpio_key] = gpio_summary

            if gpio_success_count > 0:
                print(f"✅ Q68 GPIO{q68_gpio} 测试完成: {gpio_success_count}/{gpio_total_tests} 成功 ({gpio_success_rate:.1f}%)")
            else:
                print(f"❌ Q68 GPIO{q68_gpio} 测试失败: 所有延迟组合都失败")
                failed_gpios.append(gpio_key)

        # 最终测试总结
        completed_gpios = len([r for r in results.values() if isinstance(r, dict) and r.get('success', False)])
        total_delay_tests = sum([r.get('total_tests', 0) for r in results.values() if isinstance(r, dict)])
        successful_delay_tests = sum([r.get('successful_tests', 0) for r in results.values() if isinstance(r, dict)])

        print(f"\n📊 最终测试总结:")
        print(f"  - 总GPIO数: {len(q68_gpio_range)}")
        print(f"  - 完成GPIO数: {completed_gpios}")
        print(f"  - 失败GPIO数: {len(failed_gpios)}")
        print(f"  - 跳过GPIO数: {len(skipped_gpios)}")
        print(f"  - 总延迟测试数: {total_delay_tests}")
        print(f"  - 成功延迟测试数: {successful_delay_tests}")
        print(f"  - 整体成功率: {(successful_delay_tests/total_delay_tests*100) if total_delay_tests > 0 else 0:.1f}%")

        return {
            'results': results,
            'summary': {
                'total_gpios': len(q68_gpio_range),
                'completed_gpios': completed_gpios,
                'failed_gpios': len(failed_gpios),
                'skipped_gpios': len(skipped_gpios),
                'total_delay_tests': total_delay_tests,
                'successful_delay_tests': successful_delay_tests,
                'cancelled': False
            }
        }


@pytest.mark.fast
def test_gpio_delay_compensation_all_q68_gpios(devices):
    """
    遍历所有Q68 GPIO的延迟补偿测试 - 交互式版本
    """
    print("\n" + "="*80)
    print("🚀 Q68 GPIO延迟补偿自动遍历测试")
    print("📱 每个Q68 GPIO测试前会弹窗确认")
    print("="*80)

    tester = GPIO_DelayCompensation_AllLinks_Tester(devices)

    # 执行遍历测试
    result = tester.test_all_q68_gpios_delay_compensation(
        q68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14 ,15 ,16],  # 排除GPIO13
        enable_dialog=True
    )

    # 验证测试结果
    summary = result['summary']
    assert summary['total_gpios'] > 0, "没有执行任何GPIO测试"

    if not summary['cancelled']:
        assert summary['completed_gpios'] > 0, "所有GPIO测试都失败了"

    print(f"\n✅ Q68 GPIO延迟补偿遍历测试完成")


# @pytest.mark.fast
# def test_gpio_delay_compensation_all_links(devices):
#     """
#     4个Link一起的GPIO延迟补偿测试
#     """
#     print("\n" + "="*80)
#     print("🚀 GPIO延迟补偿测试 - 4个Link一起")
#     print("="*80)
    
#     tester = GPIO_DelayCompensation_AllLinks_Tester(devices)
    
#     # 测试参数
#     q68_gpio = TEST_CONFIG['q68_source_gpio']
#     s68_gpio_range = TEST_CONFIG['s68_target_gpios']
#     signal_id = TEST_CONFIG['signal_id']
#     fwd_dly_values = TEST_CONFIG['delay_compensation_config']['fwd_dly_test_values']
#     rvs_dly_values = TEST_CONFIG['delay_compensation_config']['rvs_dly_test_values']  # 获取所有rvs_dly值
    
#     # 计算总测试数
#     total_tests = len(fwd_dly_values) * len(rvs_dly_values)

#     print(f"📊 测试配置:")
#     print(f"  - Links: {TEST_CONFIG['all_links']}")
#     print(f"  - Q68 GPIO: {q68_gpio}")
#     print(f"  - S68 GPIOs: {s68_gpio_range}")
#     print(f"  - fwd_dly值: {fwd_dly_values}")
#     print(f"  - rvs_dly值: {rvs_dly_values}")
#     print(f"  - rvs_dly延迟范围: {[v * 3.5 for v in rvs_dly_values]} us")
#     print(f"  - 总测试数: {total_tests} (fwd_dly: {len(fwd_dly_values)} × rvs_dly: {len(rvs_dly_values)})")
    
#     results = {}
#     test_count = 0

#     # 双重循环：外层fwd_dly，内层rvs_dly
#     for fwd_dly_value in fwd_dly_values:
#         for rvs_dly_value in rvs_dly_values:
#             test_count += 1
#             print(f"\n📍 测试进度: {test_count}/{total_tests}")
#             print(f"   参数: fwd_dly = {fwd_dly_value} ({fwd_dly_value * 3.5:.1f}us), rvs_dly = {rvs_dly_value} ({rvs_dly_value * 3.5:.1f}us)")

#             result = tester.test_all_links_fwd_dly_single_value(
#                 q68_gpio, signal_id, fwd_dly_value, rvs_dly_value
#             )

#             # 使用组合键存储结果
#             test_key = f"fwd{fwd_dly_value}_rvs{rvs_dly_value}"
#             results[test_key] = result

#             if result:
#                 print(f"  ✅ 测试通过: fwd_dly={fwd_dly_value}, rvs_dly={rvs_dly_value}")
#             else:
#                 print(f"  ❌ 测试失败: fwd_dly={fwd_dly_value}, rvs_dly={rvs_dly_value}")

#             # 测试间隔
#             if test_count < total_tests:
#                 time.sleep(2)
    
#     # 测试总结
#     successful_tests = sum(1 for result in results.values() if result)
#     failed_tests = total_tests - successful_tests

#     print(f"\n📊 测试总结:")
#     print(f"  - 总测试: {total_tests} (fwd_dly: {len(fwd_dly_values)} × rvs_dly: {len(rvs_dly_values)})")
#     print(f"  - 成功: {successful_tests}")
#     print(f"  - 失败: {failed_tests}")
#     print(f"  - 成功率: {successful_tests/total_tests*100:.1f}%")

#     # 详细结果展示
#     print(f"\n📋 详细测试结果:")
#     for fwd_dly_value in fwd_dly_values:
#         for rvs_dly_value in rvs_dly_values:
#             test_key = f"fwd{fwd_dly_value}_rvs{rvs_dly_value}"
#             status = "✅" if results.get(test_key, False) else "❌"
#             print(f"  {status} fwd_dly={fwd_dly_value:2d} ({fwd_dly_value * 3.5:5.1f}us), rvs_dly={rvs_dly_value:2d} ({rvs_dly_value * 3.5:5.1f}us)")
    
#     # 验证测试结果
#     assert len(results) > 0, "延迟补偿测试未执行"
#     assert successful_tests > 0, "所有延迟补偿测试都失败了"
    
#     print(f"\n✅ 4个Link延迟补偿测试完成")


if __name__ == "__main__":
    """
    GPIO延迟补偿测试 - 4个Link一起测试
    
    🎯 测试目标:
    - 验证4个Link同时的延迟补偿效果
    - 测试多Link环境下的信号同步性
    - 通过示波器观察延迟补偿的实际效果
    
    🔧 核心功能:
    - fwd_dly参数扫描 (0-63, 单位3.5us)
    - 强制启用dly_comp_en=1
    - 4个Link并行GPIO配置
    - 示波器30Hz频率测试和截图
    
    🚀 使用方法:
    # 单个GPIO测试
    pytest test_gpio_case9_fwd_dly_all_links.py::test_gpio_delay_compensation_all_links

    # 遍历所有GPIO测试 (交互式)
    pytest test_gpio_case9_fwd_dly_all_links.py::test_gpio_delay_compensation_all_q68_gpios

    📊 测试配置:
    - Links: [0, 1, 2, 3] (所有4个Link)
    - Q68 GPIOs: 0-14 (排除GPIO13)
    - fwd_dly值: [0] (固定前向延迟)
    - rvs_dly值: [0, 4, 10, 20, 30, 40, 50, 60, 63] (全范围反向延迟)
    - 延迟范围: 0 ~ 220.5us
    - 截图保存: U-disk0/gpiotest/fwd_dly_all_links/Q68TOS68_GPIO{n}/

    🆕 新功能特点:
    - 📱 交互式GPIO选择: 每个GPIO测试前弹窗确认
    - 🔄 全GPIO遍历: 自动测试Q68 GPIO 0-14
    - 📊 全面延迟测试: 每个GPIO测试9个rvs_dly值
    - 📷 分类截图存储: 每个GPIO独立文件夹
    - 📈 详细测试统计: GPIO级别和延迟级别的成功率统计
    """
    print("GPIO延迟补偿测试 - 4个Link一起测试")
    print("请使用pytest运行测试")
