# -*- coding: utf-8 -*-
"""
GPIO Case-6: Signal Transmission Path from S68 to Q68

This test verifies the configuration of a signal transmission path from a
specific S68 GPIO pin to a specific GPIO pin on the Q68. The actual
signal presence should be verified externally using an oscilloscope.
"""
import logging
import time
import pytest
import os

# 测试配置 - 可以手动修改
TEST_CONFIG = {
    'active_links': [0, 1, 2, 3],  # 默认使用所有Link (S68到Q68信号传输需要测试所有Link)
    'signal_id': 11,               # GPIO信号ID
    'observation_time': 1,         # 观察时间(秒)
    's68_source_gpio': 8,          # S68源GPIO (用于截图文件命名，每次测试都设置同一个)

    # 示波器自动截图配置
    'oscilloscope_config': {
        'enable_screenshot': True,              # 是否启用自动截图
        'digital_trigger_channel': 1,           # 数字通道D1作为触发源
        'waveform_type': 'SQUARE',              # 方波
        'frequency_range': {
            'start': 1210000,                    # 300kHz
            'end': 1240000,                      # 400kHz
            'step': 10000                       # 10kHz步进
        },
        'amplitude': 1.8,                      # 1.26Vpp
        'offset': 0.9,                          # 900mVdc偏移
        'timebase_scale': '500ns',                # 时基设置 2us/div
        'persistence_mode': 'INFinite',         # 余晖模式 (该机型唯一选项)
        'screenshot_folder': 'U-disk0/gpiotest/s68toq68',  # 截图保存文件夹
        # 'freq_observation_time': 1.5,             # 每个频率的观察时间(秒)
    },


    # 动态生成configurations，使用统一的S68 GPIO变量
    'configurations': None  # 将在运行时动态生成
}

def test_gpio_s68_external_to_q68(devices):
    """
    Configures multiple signal paths from S68 GPIOs to Q68 GPIOs in a single run.

    Test Steps:
    0.  Configure specific Links for this test
    1.  Define a list of all S68-to-Q68 GPIO configurations.
    2.  Iterate through each configuration:
        a. Select the target S68 device based on the link.
        b. Configure the S68 GPIO as a remote transmitter (Tx).
        c. Configure the corresponding Q68 GPIO as a remote receiver (Rx).
    3.  After configuring all paths, wait for observation.
    4.  Check and assert that all communication links remain stable.
    """
    print(f"\n[CASE-6] 开始执行: S68 -> Q68 信号路径配置")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")

    # 动态生成configurations，使用统一的S68 GPIO变量
    s68_gpio = TEST_CONFIG['s68_source_gpio']
    signal_id = TEST_CONFIG['signal_id']

    # 生成配置：测试所有Link的指定S68 GPIO到Q68 GPIO0，然后测试Link0的指定S68 GPIO到Q68多个GPIO
    configurations = []

    # 测试所有Link的指定S68 GPIO到Q68 GPIO0
    for link in [0, 1, 2, 3]:
        configurations.append((link, s68_gpio, 0, signal_id))

    # 测试Link0的指定S68 GPIO到Q68多个GPIO
    for q68_gpio in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]:
        configurations.append((0, s68_gpio, q68_gpio, signal_id))

        # 测试Link1的指定S68 GPIO到Q68多个GPIO
    for q68_gpio in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]:
        configurations.append((1, s68_gpio, q68_gpio, signal_id))

        # 测试Link2的指定S68 GPIO到Q68多个GPIO
    for q68_gpio in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]:
        configurations.append((2, s68_gpio, q68_gpio, signal_id))

        # 测试Link3的指定S68 GPIO到Q68多个GPIO
    for q68_gpio in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]:
        configurations.append((3, s68_gpio, q68_gpio, signal_id))

    # 更新TEST_CONFIG中的configurations
    TEST_CONFIG['configurations'] = configurations

    print(f"使用S68 GPIO{s68_gpio}进行测试，共生成{len(configurations)}个配置")

    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']

    # ---------------------------------------------------------------------
    # 0. Configure specific Links for this test
    # ---------------------------------------------------------------------
    print(f"\n步骤0: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")

    # 设置地址转换 (关键步骤！)
    print(f"\n步骤0.5: 设置激活Links的地址转换...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            # 设置地址转换：转译地址 -> 实际设备地址
            q68_remote.S68_AddrTrans(
                link=link,
                q68_iic_addr=0x73,                    # Q68地址
                s68_iic_addr=0x40,                    # S68实际地址
                s68_retrans_addr=s68_res_dev[link],   # S68转译地址 (0x20, 0x21, 0x22, 0x23)
                sensor_addr=0x24,                     # sensor地址 (如果需要)
                sensor_retrans_addr=0x24 + link      # sensor转译地址
            )
            print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40")
    print("  - 地址转换设置完成")

    # 过滤配置，只保留激活Links的配置
    active_configurations = [
        config for config in TEST_CONFIG['configurations']
        if config[0] in TEST_CONFIG['active_links']
    ]

    print(f"\n有效配置数量: {len(active_configurations)} (过滤后只包含激活Links)")

    # ---------------------------------------------------------------------
    # 1. & 2. S68 and Q68 Side Configuration (Transmitter & Receiver)
    # ---------------------------------------------------------------------
    print("\n步骤1&2: 循环配置激活Links的 S68 和 Q68 GPIO...")

    for link_to_test, s68_gpio_pin, q68_gpio_pin, signal_id in active_configurations:
        print(f"  - 配置: S68 (Link-{link_to_test}, GPIO-{s68_gpio_pin}) -> Q68 (GPIO-{q68_gpio_pin}), Signal ID: {signal_id}")

        # q68.M65Q68I2CLocalWrite(regAddr=0x440d,value=0x40)
        # print(q68.M65Q68I2CLocalRead(regAddr=0x440d, CRC=False))

        # q68.M65Q68I2CLocalWrite(regAddr=0x4420,value=0x01)
        # print(q68.M65Q68I2CLocalRead(regAddr=0x4420, CRC=False))

        # q68.M65Q68I2CLocalWrite(regAddr=0x4420,value=0x41)
        # print(q68.M65Q68I2CLocalRead(regAddr=0x4420, CRC=False))

        # 只配置激活Links中的配置
        if link_to_test < len(s68_res_dev):
            # S68 Side Configuration (Transmitter)
            target_s68_addr = s68_res_dev[link_to_test]
            q68_remote.dongle.devAddr = target_s68_addr

            q68_remote.M2CMFNSet(gpio=s68_gpio_pin, mfn=0)
            q68_remote.M2CGPIORemoteTx(gpio=s68_gpio_pin, tx_id=signal_id)

            # Q68 Side Configuration (Receiver)
            q68.MFNSet(gpio=q68_gpio_pin, mfn=0)
            q68.GPIORemoteRx(gpio=q68_gpio_pin, rx_id=signal_id)

            # 可选的寄存器读取 (调试用)
            # print(q68.M65Q68I2CLocalRead(regAddr=0x440d, CRC=False))
        else:
            print(f"    - 警告: Link{link_to_test} 超出s68_res_dev范围，跳过")

    print("    - 激活Links的GPIO配置完成。")

    # ---------------------------------------------------------------------
    # 3. Observation and Verification
    # ---------------------------------------------------------------------
    print(f"\n步骤3: 等待{TEST_CONFIG['observation_time']}秒以便用示波器捕获波形...")

    # 示波器频率扫描截图功能 (可选)
    oscilloscope = devices.get('oscilloscope')
    if TEST_CONFIG['oscilloscope_config']['enable_screenshot'] and oscilloscope is not None:
        try:
            print("  - 开始示波器频率扫描截图...")

            # 配置示波器
            osc_config = TEST_CONFIG['oscilloscope_config']

            # 设置数字通道触发
            trigger_source = oscilloscope.Set_Digital_Trigger(digital_channel=osc_config['digital_trigger_channel'])
            print(f"    * 数字触发设置: {trigger_source.strip()}")

            # 设置余晖模式
            persistence_time = oscilloscope.Set_Display_Persistence(time=osc_config['persistence_mode'])
            print(f"    * 余晖模式设置: {persistence_time.strip()}")

            # 设置时基
            current_timebase = oscilloscope.Set_Timebase_Scale(timebase_scale=osc_config['timebase_scale'])
            print(f"    * 时基设置: {osc_config['timebase_scale']} ({current_timebase:.2e}s/div)")

            # 创建截图保存目录
            screenshot_folder = osc_config['screenshot_folder']
            os.makedirs(screenshot_folder, exist_ok=True)

            # 设置初始波形参数 (使用临时频率，为第一个目标频率做准备)
            waveform = osc_config['waveform_type']
            amplitude = osc_config['amplitude']
            offset = osc_config['offset']
            target_first_freq = osc_config['frequency_range']['start']
            temp_freq = target_first_freq + 50000  # 临时频率：比第一个目标频率高50kHz

            print(f"    * 设置初始波形参数...")
            oscilloscope.Set_Wavegen_Basic(
                waveform=waveform,
                frequency=temp_freq,  # 先设置临时频率
                amplitude=amplitude,
                offset=offset,
                output_state='OFF',  # 初始不开启输出
                load=50
            )
            print(f"      - 波形类型: {waveform}, 幅值: {amplitude}Vpp, 偏移: {offset}Vdc")
            print(f"      - 临时频率: {temp_freq/1000}kHz (为第一个目标频率{target_first_freq/1000}kHz做准备)")

            # 频率扫描
            freq_config = osc_config['frequency_range']
            frequency_list = range(freq_config['start'], freq_config['end'] + freq_config['step'], freq_config['step'])

            print(f"    * 开始频率扫描: {freq_config['start']/1000}-{freq_config['end']/1000}kHz, 步进{freq_config['step']/1000}kHz")

            for i, freq_hz in enumerate(frequency_list):
                freq_khz = freq_hz / 1000
                print(f"      -> 测试频率: {freq_khz}kHz [{i+1}/{len(frequency_list)}]")

                # 第一个频率的特殊处理：需要先开启输出，然后进行频率切换
                if i == 0:
                    print(f"        - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")

                    # 开启输出
                    output_on_status = oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                    print(f"        - 临时频率输出已开启: {output_on_status.strip()}")
                    time.sleep(0.2)

                    # 准备切换频率
                    oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz-10000)
                    print(f"        - 准备切换到目标频率")
                    time.sleep(0.2)

                    # 切换到目标频率
                    oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                    print(f"        - 频率切换完成: {freq_khz}kHz")
                    # time.sleep(0.3)

                    # 清除扫描并重新开始采集
                    oscilloscope.Clear_Display_Waveform()
                    print(f"        - 已清除扫描，重新开始采集目标频率")
                    time.sleep(0.1)

                else:
                    # 非第一个频率的正常处理流程
                    # 清除之前的扫描并重新开始采集

                    # 开启输出
                    output_on_status = oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                    print(f"        - 输出已开启: {output_on_status.strip()}")
                    time.sleep(0.2)

                    # 设置新频率
                    oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                    print(f"        - 频率设置完成: {freq_khz}kHz")
                    time.sleep(0.2)

                    oscilloscope.Clear_Display_Waveform()
                    print(f"        - 已清除扫描，重新开始采集目标频率")
                    time.sleep(0.1)

                # 等待信号稳定和余晖累积
                print(f"        - 等待{TEST_CONFIG['observation_time']}秒进行余晖观察...")
                time.sleep(TEST_CONFIG['observation_time'])

                # 自动截图
                active_links=  TEST_CONFIG['active_links']
                s68_gpio_num = TEST_CONFIG['s68_source_gpio']
                timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒，不包含年份
                screenshot_filename = f"{i+1:02d}_S68GPIO{s68_gpio_num}_LINK{active_links}_{freq_khz}kHz_{timestamp}.png"
                screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

                # 使用正确的截图参数：PNG格式，正常色彩，隐藏菜单
                oscilloscope.Save_Image(
                    filepath=screenshot_path,
                    image_format="PNG",
                    invert="OFF",      # 正常色彩
                    menu="MOF"         # 隐藏菜单
                )
                print(f"        - 截图已保存: {screenshot_filename} (PNG格式，隐藏菜单)")

                # 短暂延时确保截图完成
                time.sleep(2)

            print(f"    * 频率扫描完成，共测试 {len(frequency_list)} 个频率点")

        except Exception as e:
            print(f"    - 示波器频率扫描截图失败: {e}")
            print(f"    - 继续原有观察流程...")
            time.sleep(TEST_CONFIG['observation_time'])
    else:
        # 原有的观察逻辑保持不变
        if not TEST_CONFIG['oscilloscope_config']['enable_screenshot']:
            print("  - 示波器自动截图已禁用")
        elif oscilloscope is None:
            print("  - 示波器不可用")
        time.sleep(TEST_CONFIG['observation_time'])

    # 只检查激活Links的状态
    print(f"\n步骤4: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    active_statuses = {}
    for link_id in TEST_CONFIG['active_links']:
        if 0 <= link_id <= 3:
            status = link_status_funcs[link_id]()
            active_statuses[f'link{link_id}'] = status
            print(f"    - Link{link_id} 状态: {status}")

    # 验证激活Links的状态
    failed_links = [link for link, status in active_statuses.items() if status != 5]
    assert len(failed_links) == 0, \
        f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"

    print(f"\n==> [CASE-6] 测试通过: S68 -> Q68 信号路径已配置到Links {TEST_CONFIG['active_links']}，链路状态正常。")


@pytest.mark.parametrize("link_combination", [
    [0],           # 只测试Link0
    [1],           # 只测试Link1
    [2],           # 只测试Link2
    [3],           # 只测试Link3
    [0, 1],        # 测试Link0和Link1
    [0, 1, 2, 3],  # 测试所有Link (默认)
])
def test_gpio_case6_parametrized(devices, link_combination):
    """参数化测试 - 可以测试不同的Link组合"""
    # 临时修改配置
    original_links = TEST_CONFIG['active_links']
    TEST_CONFIG['active_links'] = link_combination

    try:
        test_gpio_s68_external_to_q68(devices)
        print(f"✅ Link组合 {link_combination} 测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['active_links'] = original_links


def test_gpio_case6_single_link_focus(devices):
    """专门测试单个Link的详细配置"""
    # 简化配置，只测试Link0的基本功能，使用统一的S68 GPIO变量
    s68_gpio = TEST_CONFIG['s68_source_gpio']
    signal_id = TEST_CONFIG['signal_id']

    simplified_config = [
        (0, s68_gpio, 0, signal_id),  # S68 Link0 指定GPIO -> Q68 GPIO0
        (0, s68_gpio, 1, signal_id),  # S68 Link0 指定GPIO -> Q68 GPIO1
        (0, s68_gpio, 2, signal_id),  # S68 Link0 指定GPIO -> Q68 GPIO2
    ]

    # 临时修改配置
    original_configs = TEST_CONFIG['configurations']
    original_links = TEST_CONFIG['active_links']

    TEST_CONFIG['configurations'] = simplified_config
    TEST_CONFIG['active_links'] = [0]

    try:
        test_gpio_s68_external_to_q68(devices)
        print("✅ 单Link详细测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['configurations'] = original_configs
        TEST_CONFIG['active_links'] = original_links


if __name__ == "__main__":
    """
    使用说明:

    1. 测试默认配置 (所有Link):
       pytest test_gpio_case6_8.py::test_gpio_s68_external_to_q68 -v -s

    2. 参数化测试特定Link组合:
       pytest test_gpio_case6_8.py::test_gpio_case6_parametrized[link_combination0] -v -s

    3. 测试所有Link组合:
       pytest test_gpio_case6_8.py::test_gpio_case6_parametrized -v -s

    4. 单Link详细测试:
       pytest test_gpio_case6_8.py::test_gpio_case6_single_link_focus -v -s

    5. 手动修改TEST_CONFIG中的active_links来测试特定组合
    """
    print("GPIO Case-6 灵活Link测试")
    print("请使用pytest运行测试")