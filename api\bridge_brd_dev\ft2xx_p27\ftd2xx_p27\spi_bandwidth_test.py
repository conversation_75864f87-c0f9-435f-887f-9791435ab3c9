# packages
import QRegisterAccess
import time

# ft4232H (loop = 1024, size = 1024 * 5)
#     ~2.40Mbytes/s for 30MHz SPI clock
#     ~0.94Mbytes/s for 10MHz SPI clock

if QRegisterAccess.findMeritechSpiChan():
    t0 = time.time()
    loop = 1024
    size = 1
    for i in range(loop):
        # QRegisterAccess.spiWrite1860Reg(0x30, size)
        QRegisterAccess.spiReadC3(0x30, size)
    t1 = time.time()
    d  = t1 - t0
    print("spi read speed: %.3f Mbytes/s"%(1.0*loop*size/1024/1024/d)) 
