#!python3

import time

# package for supporting stm32
from stm32 import USBImpl
from adb import ADBImpl
# from ssh import SSHImpl

# global stm32 usb hardware instances list, as some silicon may share same instance
stm32_inst_list = None
adb_inst_list = None
ssh_inst_list = None

q68_dev = None
q68_search = True
devlist = 0
dutsearch = None

class QRegisterAccess:

    # i2c device address
    devAddr = 0x00
    
    # dongle name, 'stm32'
    dongle = None

    # stm32 support
    stm32_dev = None
    stm32_bus = None
    stm32_dev_index = 0x00
    stm32_dev_sn    = 0x00
    stm32_bus_chan  = 0x00 # 1: i2c1, 2:i2c2, 3:i2c3
    
    def __init__(self, dongle='stm32', dev=None, i2c_addr=None, dongle_id=None, bus='i2c', bus_chan=1, product='c2m', id=0, hostlist=None):
        global stm32_inst_list
        global adb_inst_list
        global ssh_inst_list
        global q68_search 
        global q68_dev
        global devlist
        global ft4222_devA
        global dutsearch
        
        self.dongle = dongle
        if dongle == 'stm32':
        
            #find all device
            if stm32_inst_list is None:
                stm32_inst_list = []
                self.stm32_dev = USBImpl.USBImpl()
                devlist = self.stm32_dev.usbdev_scan()
                self.stm32_dev = None
                print("Find %d STM32 Devices:"%(len(devlist)))
                for i in range(len(devlist)):
                    dev = USBImpl.USBImpl()
                    dev.usbdev_open(devlist[i].name)
                    dev.usbdev_i2c_setbusspeed(1, 400)
                    dev.usbdev_i2c_setbusspeed(2, 400)
                    dev.usbdev_i2c_setbusspeed(3, 400)
                    (ret, sn) = dev.usbdev_get_sn()
                    stm32_inst_list.append((dev, sn))
                    print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                    
            i = 0
            for inst in stm32_inst_list:
                    if len(devlist)==1:  # means 1 Q68, 1 s8 or 1 Q68+1s68
                # if inst[1] == dongle_id:
                        self.stm32_dev = inst[0]
                        self.stm32_dev_sn = dongle_id
                        self.stm32_bus = bus
                        self.stm32_bus_chan = bus_chan
                        if self.stm32_dev == None:
                            raise BaseException('unable to find specified stm32 dongle')
                        if id<=1:
                            if bus == 'i2c':
                                self.stm32_bus = bus
                                if product=='c2m':
                                    if id==0: 
                                        if i2c_addr is None:
                                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                                # self.dongle.stm32_dev = inst[0]
                                                # self.dongle.stm32_dev_sn = inst[1]
                                                self.devAddr = devAddr
                                                data = self.readReg(moduleName=product, addr=0x1001)
                                                if data == 0x5:
                                                    i2c_addr = devAddr
                                                    # self.dongle.devAddr = i2c_addr
                                                    print('find q68 device 0x%x'%(devAddr))
                                                    q68_dev = inst
                                                    break
                                            if i2c_addr is None:
                                                raise('q68 not found')
                                    else:
                                        raise('please connect Q68 to MCU channel 1 !!!')
                                if product=='m2c' and id==1:
                                    if i2c_addr is None:
                                        for devAddr in [0x40, 0x44]: 
                                            # self.dongle.stm32_dev = inst[0]
                                            # self.dongle.stm32_dev_sn = inst[1]
                                            self.devAddr = devAddr
                                            data = self.readReg(moduleName=product, addr=0x0101)
                                            if data == 0x55:
                                                i2c_addr = devAddr
                                                # self.dongle.devAddr = i2c_addr
                                                print('find s68 device 0x%x'%(devAddr))
                                                break
                                        if i2c_addr is None:
                                            raise('s68 not found')
    
                    if len(devlist)>1:  # means q68 + s681, s682 or more
                        
                        self.stm32_dev = inst[0]
                        self.stm32_dev_sn = dongle_id
                        self.stm32_bus = bus
                        self.stm32_bus_chan = bus_chan
                        if self.stm32_dev == None:
                            raise BaseException('unable to find specified stm32 dongle')
                        if id<=1:
                            if bus == 'i2c':
                                self.stm32_bus = bus
                                if product=='c2m':
                                    if id==0: 
                                        if i2c_addr is None:
                                            if q68_search:
                                                for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                                    # self.stm32_dev = inst[0]
                                                    # self.stm32_dev_sn = inst[1]
                                                    self.devAddr = devAddr
                                                    data = self.readReg(moduleName=product, addr=0x1001)
                                                    if data == 0x5:
                                                        i2c_addr = devAddr
                                                        # self.dongle.devAddr = i2c_addr
                                                        print('find q68 device 0x%x'%(devAddr))
                                                        q68_dev = inst
                                                        q68_search = False
                                                        
                                                        break
                                                    if i2c_addr is None:
                                                        raise('q68 not found')
                                    else:
                                        raise('please connect Q68 to MCU channel 1 !!!')
                                
                                if product=='m2c' and id==1:
                                    if i2c_addr is None:
                                        for devAddr in [0x40, 0x44]: 
                                            self.stm32_dev = q68_dev[0]
                                            # self.stm32_dev_sn = inst[1]
                                            self.devAddr = devAddr
                                            data = self.readReg(moduleName=product, addr=0x0101)
                                            if data == 0x55:
                                                i2c_addr = devAddr
                                                # self.devAddr = i2c_addr
                                                print('find s68 device 0x%x'%(devAddr))
                                                break
                                        if i2c_addr is None:
                                            raise('s68 not found')
                    if q68_search ==False:
                        break
            if product=='m2c' and id>1:
                for inst in stm32_inst_list:
                    if inst[1] != q68_dev[1]:
                        if i2c_addr is None:
                            for devAddr in [0x40, 0x44]: 
                                self.stm32_dev = inst[0]
                                # self.stm32_dev_sn = inst[1]
                                self.devAddr = devAddr
                                self.stm32_bus_chan = id-1
                                data = self.readReg(moduleName=product, addr=0x0101)
                                if data == 0x55:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find s68 device 0x%x'%(devAddr))
                                    break
                    
                                # if i2c_addr is None:
                                #     raise('s68 not found')
        elif dongle == 'adb':
            #find all device
            if adb_inst_list is None:
                adb_inst_list = []
                self.adb_dev = ADBImpl.ADBImpl()
                devlist = self.adb_dev.usbdev_scan()
                self.adb_dev = None
                print("Find %d adb Devices:"%(len(devlist)))
                for i in range(len(devlist)):
                    dev = ADBImpl.ADBImpl()
                    dev.usbdev_open(devlist[i].name)
                    dev.usbdev_i2c_setbusspeed(1, 400)
                    dev.usbdev_i2c_setbusspeed(2, 400)
                    dev.usbdev_i2c_setbusspeed(3, 400)
                    (ret, sn) = dev.usbdev_get_sn()
                    adb_inst_list.append((dev, sn))
                    print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                    
            i = 0
            for inst in adb_inst_list:
                    if len(devlist)==1:  # means 1 Q68, 1 s8 or 1 Q68+1s68
                # if inst[1] == dongle_id:
                        self.adb_dev = inst[0]
                        self.adb_dev_sn = dongle_id
                        self.adb_bus = bus
                        self.adb_bus_chan = bus_chan
                        if self.adb_dev == None:
                            raise BaseException('unable to find specified adb dongle')
                        if id<=1:
                            if bus == 'i2c':
                                self.adb_bus = bus
                                if product=='c2m':
                                    if id==0: 
                                        if i2c_addr is None:
                                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                                # self.dongle.adb_dev = inst[0]
                                                # self.dongle.adb_dev_sn = inst[1]
                                                self.devAddr = devAddr
                                                data = self.readReg(moduleName=product, addr=0x1001)
                                                if data == 0x05:
                                                    i2c_addr = devAddr
                                                    # self.dongle.devAddr = i2c_addr
                                                    print('find q68 device 0x%x'%(devAddr))
                                                    q68_dev = inst
                                                    break
                                            if i2c_addr is None:
                                                raise('q68 not found')
                                    else:
                                        raise('please connect Q68 to MCU channel 1 !!!')
                                if product=='m2c' and id==1:
                                    if i2c_addr is None:
                                        for devAddr in [0x40, 0x44]: 
                                            # self.dongle.adb_dev = inst[0]
                                            # self.dongle.adb_dev_sn = inst[1]
                                            self.devAddr = devAddr
                                            data = self.readReg(moduleName=product, addr=0x0101)
                                            if data == 0x55:
                                                i2c_addr = devAddr
                                                # self.dongle.devAddr = i2c_addr
                                                print('find s68 device 0x%x'%(devAddr))
                                                break
                                        if i2c_addr is None:
                                            raise('s68 not found')
    
                    if len(devlist)>1:  # means q68 + s681, s682 or more
                        
                        self.adb_dev = inst[0]
                        self.adb_dev_sn = dongle_id
                        self.adb_bus = bus
                        self.adb_bus_chan = bus_chan
                        if self.adb_dev == None:
                            raise BaseException('unable to find specified adb dongle')
                        if id<=1:
                            if bus == 'i2c':
                                self.adb_bus = bus
                                if product=='c2m':
                                    if id==0: 
                                        if i2c_addr is None:
                                            if q68_search:
                                                for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                                    # self.adb_dev = inst[0]
                                                    # self.adb_dev_sn = inst[1]
                                                    self.devAddr = devAddr
                                                    
                                                    self.adb_bus_chan = 1
                                                    data = self.readReg(moduleName=product, addr=0x1001)
                                                    if data == 0x5:
                                                        i2c_addr = devAddr
                                                        # self.dongle.devAddr = i2c_addr
                                                        print('find q68 device 0x%x'%(devAddr))
                                                        q68_dev = inst
                                                        q68_search = False
                                                        
                                                        break
                                                    if i2c_addr is None:
                                                        raise('q68 not found')
                                    else:
                                        raise('please connect Q68 to MCU channel 1 !!!')
                                
                                if product=='m2c' and id==1:
                                    if i2c_addr is None:
                                        for devAddr in [0x40, 0x44]: 
                                            self.adb_dev = q68_dev[0]
                                            # self.adb_dev_sn = inst[1]
                                            self.devAddr = devAddr
                                            self.adb_bus_chan = 2
                                            data = self.readReg(moduleName=product, addr=0x0101)
                                            if data == 0x55:
                                                i2c_addr = devAddr
                                                # self.devAddr = i2c_addr
                                                print('find s68 device 0x%x'%(devAddr))
                                                break
                                        if i2c_addr is None:
                                            raise('s68 not found')
                    if q68_search ==False:
                        break
            if product=='m2c' and id>1:
                for inst in adb_inst_list:
                    if inst[1] != q68_dev[1]:
                        if i2c_addr is None:
                            for devAddr in [0x40, 0x44]: 
                                self.adb_dev = inst[0]
                                # self.adb_dev_sn = inst[1]
                                self.devAddr = devAddr
                                self.adb_bus_chan = id-1
                                data = self.readReg(moduleName=product, addr=0x0101)
                                if data == 0x55:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find s68 device 0x%x'%(devAddr))
                                    break
                    
                                # if i2c_addr is None:
                                #     raise('s68 not found')
        elif dongle == 'ssh':
            #find all device
            if ssh_inst_list is None:
                ssh_inst_list = []
                self.ssh_dev = SSHImpl.SSHImpl()
                devlist = self.ssh_dev.sshdev_scan(hostlist)
                self.ssh_dev = None
                print("Find %d ssh Devices:"%(len(devlist)))
                for i in range(len(devlist)):
                    dev = SSHImpl.SSHImpl()
                    dev.sshdev_open(devlist[i].hostname,devlist[i].username, devlist[i].password )
                    dev.sshdev_i2c_setbusspeed(1, 400)
                    dev.sshdev_i2c_setbusspeed(2, 400)
                    dev.sshdev_i2c_setbusspeed(3, 400)
                    (ret, sn) = dev.sshdev_get_sn()
                    ssh_inst_list.append((dev, sn))
                    print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                    
            i = 0
            for inst in ssh_inst_list:
                if len(devlist)==1:  # means 1 Q68, 1 s8 or 1 Q68+1s68
                    self.ssh_dev = inst[0]
                    self.ssh_dev_sn = dongle_id
                    self.ssh_bus = bus
                    self.ssh_bus_chan = bus_chan
                    if self.ssh_dev == None:
                        raise BaseException('unable to find specified ssh dongle')
                    if id<=1:
                        if bus == 'i2c':
                            self.ssh_bus = bus
                            if product=='c2m':
                                if id==0: 
                                    if i2c_addr is None:
                                        for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                            # self.dongle.ssh_dev = inst[0]
                                            # self.dongle.ssh_dev_sn = inst[1]
                                            self.devAddr = devAddr
                                            data = self.readReg(moduleName=product, addr=0x1001)
                                            if data == 0x05:
                                                i2c_addr = devAddr
                                                # self.dongle.devAddr = i2c_addr
                                                print('find q68 device 0x%x'%(devAddr))
                                                q68_dev = inst
                                                break
                                        if i2c_addr is None:
                                            raise('q68 not found')
                                else:
                                    raise('please connect Q68 to MCU channel 1 !!!')
                            if product=='m2c' and id==1:
                                if i2c_addr is None:
                                    for devAddr in [0x40, 0x44]: 
                                        # self.dongle.ssh_dev = inst[0]
                                        # self.dongle.ssh_dev_sn = inst[1]
                                        self.devAddr = devAddr
                                        data = self.readReg(moduleName=product, addr=0x0101)
                                        if data == 0x55:
                                            i2c_addr = devAddr
                                            # self.dongle.devAddr = i2c_addr
                                            print('find s68 device 0x%x'%(devAddr))
                                            break
                                    if i2c_addr is None:
                                        raise('s68 not found')
        else:
            raise BaseException('unknown dongle')
        
    def __del__(self):
        global stm32_inst_list
        global adb_inst_list
        global ssh_inst_list
        if stm32_inst_list != None:
            if self.dongle == 'stm32':
                for i in range(len(stm32_inst_list)):
                    print("closing stm32 device %d (sn = %s)"%(i, stm32_inst_list[i][1]))
                    stm32_inst_list[i][0].usbdev_close()
                stm32_inst_list = None
        if adb_inst_list != None:
            if self.dongle == 'adb':
                for i in range(len(adb_inst_list)):
                    print("closing adb device %d (sn = %s)"%(i, adb_inst_list[i][1]))
                    adb_inst_list[i][0].usbdev_close()
                adb_inst_list = None
        if ssh_inst_list != None:
            if self.dongle == 'adb':
                for i in range(len(ssh_inst_list)):
                    print("closing adb device %d (sn = %s)"%(i, ssh_inst_list[i][1]))
                    ssh_inst_list[i][0].sshdev_close()
                ssh_inst_list = None
                    
    def readReg(self, moduleName, addr, crc = False):
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, self.devAddr, addr)
                # print(f"r_r16v8, 0x{hex(self.devAddr)[2:].zfill(2)}, 0x{hex(addr)[2:].zfill(4)}, 0x{hex(value)[2:].zfill(2)},   [ret={ret}]")
                # print(f"0x03, 0x{hex(self.devAddr)[2:].zfill(2)}, 0x{hex(addr>>8&0xFF)[2:].zfill(2)}, 0x{hex(addr&0xFF)[2:].zfill(2)}, 0x{hex(value)[2:].zfill(2)},   [ret={ret}]")
            elif self.dongle == 'adb':
                ret, value = self.adb_dev.usbdev_i2c_get(1, self.adb_bus_chan, self.devAddr, addr)
            elif self.dongle == 'ssh':
                ret, value = self.ssh_dev.sshdev_i2c_get(1, self.ssh_bus_chan, self.devAddr, addr)
        else:
            raise BaseException('unknown module')
        return value

    def writeReg(self, moduleName, addr, value, crc=False):
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                print(f"w_r16v8, 0x{hex(self.devAddr)[2:].zfill(2)}, 0x{hex(addr)[2:].zfill(4)}, 0x{hex(value)[2:].zfill(2)}")
                # print(f"0x04, 0x{hex(self.devAddr)[2:].zfill(2)}, 0x{hex(addr>>8&0xFF)[2:].zfill(2)}, 0x{hex(addr&0xFF)[2:].zfill(2)}, 0x{hex(value)[2:].zfill(2)},")
                # print(f"/bin/camera/ccidbgr /dev/cci2 0x{hex(self.devAddr)[2:].zfill(2)} write21 0x{hex(addr)[2:].zfill(4)} 0x{hex(value)[2:].zfill(2)}")

                self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, self.devAddr, addr, value)
            elif self.dongle == 'adb':
                # print(f"w_r16v8,0x{hex(self.devAddr)[2:].zfill(2)},0x{hex(addr)[2:].zfill(4)},0x{hex(value)[2:].zfill(2)},")
                self.adb_dev.usbdev_i2c_set(1, self.adb_bus_chan, self.devAddr, addr, value)
            elif self.dongle == 'ssh':
                print(f"w_r16v8,0x{hex(self.devAddr)[2:].zfill(2)},0x{hex(addr)[2:].zfill(4)},0x{hex(value)[2:].zfill(2)},")
                self.ssh_dev.sshdev_i2c_set(1, self.ssh_bus_chan, self.devAddr, addr, value)
        else:
            raise BaseException('unknown module')
        return True
    
    def initGpio(self, index, mode, pull):
        if self.dongle == 'stm32':
            # index: [0~23]
            # mode: [0:input 1:output_pp 2:output_od 3:ad_pp 4:ad_od]
            # pull: [0:nopull 1:pullup 2:pulldown]
            self.stm32_dev.usbdev_init_gpio(index, mode, pull)
    
    def setGpio(self, index, value):
        if self.dongle == 'stm32':
            # index: [0~23]
            self.stm32_dev.usbdev_set_gpio(index, value)
    
    def getGpio(self, index):
        value = 0
        if self.dongle == 'stm32':
            # index: [0~23]
            value = self.stm32_dev.usbdev_get_gpio(index)
        return value
    
    def resetDongle(self):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_mcu_reset()
            if ret < 0:
                print("failed to reset stm32.")
            else:
                print("reset stm32 successfully.")
                self.stm32_dev.uninitUsbDev()
        return ret

    def setBusSpeed(self, bus, speed):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_i2c_setbusspeed(bus, speed)
            if ret < 0:
                print("failed to set stm32 bus speed.")
            else:
                print("")
        return ret
    

    def M66S68PCII2CWrite_normal(self, devaddr, regAddr, value):
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, devaddr, regAddr, value)
            if ret < 0:
                print(f"w_r16v8,0x{hex(devaddr)[2:].zfill(2)},0x{hex(regAddr)[2:].zfill(4)},0x{hex(value)[2:].zfill(2)} Error")
            else:
                print(f"w_r16v8,0x{hex(devaddr)[2:].zfill(2)},0x{hex(regAddr)[2:].zfill(4)},0x{hex(value)[2:].zfill(2)} Ok")
        return True
    