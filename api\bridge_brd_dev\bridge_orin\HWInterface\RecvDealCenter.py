#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 处理接收到的数据

from HWInterface import SendDealCenter
from threading import Event

wait_hw_ok_event = Event()

# 处理对方发过来的指令：ID=0x1001
def receive_cmd_1001_fun(cmd_buff: bytearray, cmd_len: int):
    if cmd_buff[6] == 0:
        print("cmd_1001_fun ic init success")
    else:
        print("cmd_1001_fun ic init error")
    send_cmd_respond_nodata(0x1001, 0)

# 处理对方发过来的指令：ID=0x4000:硬件设备的心跳指令
def receive_cmd_4000_fun(cmd_buff: bytearray, cmd_len: int):
    if cmd_buff[6] == 0:
        # print("cmd_4000_fun hw heart ok")
        wait_hw_ok_event.set()
    else:
        print("cmd_4000_fun hw heart err")

# 等待硬件OK状态
def wait_hw_ok(timeout:int):
    # 等待响应
    wait_hw_ok_event.clear()
    if not wait_hw_ok_event.wait(timeout):
        print("wait_hw_ok time out")
        return -1

    return 0

# 处理发送过来的指令
function_cmd_map = {
    0x1001: receive_cmd_1001_fun,  # 硬件设备上，ic init结果
    0x4000: receive_cmd_4000_fun,  # 硬件设备的心跳指令
    0xFFFF: SendDealCenter.receive_cmd_ffff_fun,  # HW对PC发送指令的响应
    # 添加更多的函数映射
}


# 按照指令ID, 分发到对应的函数处理
def publish_cmd(cmd_buff: bytearray, cmd_len: int):
    target_function = function_cmd_map.get((cmd_buff[2]<<8) | cmd_buff[3])
    if target_function is not None:
        target_function(cmd_buff, cmd_len)
    else:
        hex_data = ' '.join(f'{byte:02x}' for byte in cmd_buff)
        print("publish_cmd ID not support:", "[" , hex((cmd_buff[2]<<8) | cmd_buff[3]), "]", "->", hex_data)
