# -*- coding: utf-8 -*-
"""
重构后的GPIO Case-6: S68到Q68信号传输路径测试
基于新的统一硬件管理框架，消除对top.py的依赖
"""

import logging
import time
import pytest
import os
import tkinter as tk
from tkinter import messagebox
from dataclasses import dataclass, field
from typing import List, Dict, Optional

# =============================================================================
# 测试配置
# =============================================================================

@dataclass
class OscilloscopeConfig:
    """示波器配置"""
    enable_screenshot: bool = True
    use_fixed_frequencies: bool = True
    digital_trigger_channel: int = 1
    waveform_type: str = 'SQUARE'
    frequency_list: List[int] = field(default_factory=lambda: [30, 1000, 10000, 50000, 100000])
    timebase_list: List[str] = field(default_factory=lambda: ['20ms', '1ms', '100us', '10us', '10us'])
    amplitude: float = 1.8
    offset: float = 0.9
    screenshot_folder_base: str = 'U-disk0/gpiotest/s68toq68_0_8'
    persistence_mode: str = 'INFinite'
    probe_wait_time: int = 1
    freq_observation_time: int = 1

@dataclass
class TestConfig:
    """测试配置"""
    active_links: List[int] = field(default_factory=lambda: [0, 1, 2, 3])
    signal_id: int = 11
    observation_time: int = 1
    s68_source_gpio: int = 8
    oscilloscope: OscilloscopeConfig = field(default_factory=OscilloscopeConfig)

# 全局测试配置实例
TEST_CONFIG = TestConfig()

# =============================================================================
# 核心测试函数
# =============================================================================

def test_gpio_s68_external_to_q68_refactored(devices):
    """
    重构后的S68到Q68 GPIO信号路径配置测试
    
    使用新的硬件管理框架，专注于测试逻辑实现
    """
    print(f"\n[CASE-6 重构版] 开始执行: S68 -> Q68 信号路径配置")
    print(f"测试配置: 激活Links {TEST_CONFIG.active_links}")
    
    # 获取硬件管理器和设备
    hardware_manager = devices['hardware_manager']
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    oscilloscope = devices['oscilloscope']
    
    # 配置通信链路和地址转换
    hardware_manager.setup_communication(TEST_CONFIG.active_links)
    hardware_manager.setup_address_translation(TEST_CONFIG.active_links)
    
    # 生成测试配置
    configurations = _generate_test_configurations()
    print(f"使用S68 GPIO{TEST_CONFIG.s68_source_gpio}进行测试，共生成{len(configurations)}个配置")
    
    # 过滤激活Links的配置
    active_configurations = [
        config for config in configurations
        if config[0] in TEST_CONFIG.active_links
    ]
    
    print(f"步骤1: 配置{len(active_configurations)}个S68->Q68信号路径...")
    
    # 配置所有信号路径
    success_count = 0
    for i, (link, s68_gpio, q68_gpio, signal_id) in enumerate(active_configurations):
        try:
            print(f"  配置 {i+1}/{len(active_configurations)}: Link{link} S68_GPIO{s68_gpio} -> Q68_GPIO{q68_gpio}")
            
            # 配置Q68 GPIO
            _configure_q68_gpio(q68, q68_gpio, signal_id)
            
            # 配置S68 GPIO
            _configure_s68_gpio(q68_remote, s68_res_dev, link, s68_gpio, signal_id)
            
            success_count += 1
            
        except Exception as e:
            print(f"    ❌ 配置失败: {e}")
    
    print(f"步骤1完成: {success_count}/{len(active_configurations)} 配置成功")
    
    # 示波器自动截图
    if TEST_CONFIG.oscilloscope.enable_screenshot and oscilloscope:
        _perform_oscilloscope_screenshot(oscilloscope)
    else:
        print(f"步骤2: 等待{TEST_CONFIG.observation_time}秒进行信号观察...")
        time.sleep(TEST_CONFIG.observation_time)
    
    # 验证链路状态
    _verify_link_status(q68, TEST_CONFIG.active_links)
    
    print("[CASE-6 重构版] 测试完成")

# =============================================================================
# 自动遍历测试类
# =============================================================================

class GPIO_S68_Q68_AutoTester_Refactored:
    """重构后的S68到Q68 GPIO自动遍历测试器"""
    
    def __init__(self, devices):
        self.devices = devices
        self.hardware_manager = devices['hardware_manager']
        
    def full_system_reinit(self, s68_gpio):
        """完整的系统重新初始化"""
        print(f"\n🔄 开始完整系统重新初始化...")
        print(f"   目标: S68 GPIO{s68_gpio} → Q68 GPIO0-14")
        
        try:
            # 步骤1: 断电
            print(f"  ⚡ 步骤1: 系统断电...")
            self.hardware_manager.power_control('off')
            
            # 步骤2: 重新上电
            print(f"  ⚡ 步骤2: 系统重新上电...")
            self.hardware_manager.power_control('on')
            
            # 步骤3: 重新建立通信
            print(f"  📡 步骤3: 重新建立Q68-S68通信...")
            self.hardware_manager.setup_communication()
            
            # 步骤4: 设置地址转换
            print(f"  🔧 步骤4: 设置地址转换...")
            self.hardware_manager.setup_address_translation()
            
            print(f"✅ 系统重新初始化完成，准备测试S68 GPIO{s68_gpio} → Q68 GPIO0-14")
            return True
            
        except Exception as e:
            print(f"❌ 系统重新初始化失败: {e}")
            return False
    
    def test_single_s68_gpio_with_original_flow(self, s68_gpio):
        """使用原有流程测试单个S68 GPIO"""
        # 临时修改配置
        original_s68_gpio = TEST_CONFIG.s68_source_gpio
        original_links = TEST_CONFIG.active_links
        
        TEST_CONFIG.s68_source_gpio = s68_gpio
        TEST_CONFIG.active_links = [0]  # 只测试Link0
        
        try:
            print(f"\n🚀 开始测试S68 GPIO{s68_gpio} (使用重构流程)")
            
            # 调用重构后的测试函数
            test_gpio_s68_external_to_q68_refactored(self.devices)
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        finally:
            # 恢复原始配置
            TEST_CONFIG.s68_source_gpio = original_s68_gpio
            TEST_CONFIG.active_links = original_links

# =============================================================================
# 辅助函数
# =============================================================================

def _generate_test_configurations():
    """生成测试配置"""
    configurations = []
    s68_gpio = TEST_CONFIG.s68_source_gpio
    signal_id = TEST_CONFIG.signal_id
    
    # 测试所有Link的指定S68 GPIO到Q68 GPIO0
    for link in [0, 1, 2, 3]:
        configurations.append((link, s68_gpio, 0, signal_id))
    
    # 测试每个Link的指定S68 GPIO到Q68多个GPIO
    for link in [0, 1, 2, 3]:
        for q68_gpio in range(1, 15):
            configurations.append((link, s68_gpio, q68_gpio, signal_id))
    
    return configurations

def _configure_q68_gpio(q68, q68_gpio, signal_id):
    """配置Q68 GPIO"""
    # GPIO15/16特殊处理
    if q68_gpio in [15, 16]:
        # 特殊GPIO配置逻辑
        pass
    
    # 标准GPIO配置
    q68.MFNSet(gpio=q68_gpio, mfn=0)
    q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id)

def _configure_s68_gpio(q68_remote, s68_res_dev, link, s68_gpio, signal_id):
    """配置S68 GPIO"""
    target_addr = s68_res_dev[link]
    q68_remote.dongle.devAddr = target_addr
    time.sleep(0.1)
    
    q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
    q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)

def _perform_oscilloscope_screenshot(oscilloscope):
    """执行示波器自动截图"""
    print("步骤2: 示波器自动截图...")
    
    osc_config = TEST_CONFIG.oscilloscope
    
    try:
        # 配置示波器
        oscilloscope.Set_Persistence_Mode(osc_config.persistence_mode)
        oscilloscope.Set_Digital_Trigger(
            channel=osc_config.digital_trigger_channel,
            edge='RISING'
        )
        
        # 固定频率测试
        if osc_config.use_fixed_frequencies:
            _fixed_frequency_test(oscilloscope, osc_config)
        
        print("示波器自动截图完成")
        
    except Exception as e:
        print(f"示波器截图失败: {e}")

def _fixed_frequency_test(oscilloscope, osc_config):
    """固定频率测试"""
    print(f"开始固定频率测试: {len(osc_config.frequency_list)}个频率点")
    
    for i, (freq_hz, timebase) in enumerate(zip(osc_config.frequency_list, osc_config.timebase_list)):
        freq_display = f"{freq_hz}Hz" if freq_hz < 1000 else f"{freq_hz/1000:.0f}kHz"
        print(f"  测试频率 {i+1}/{len(osc_config.frequency_list)}: {freq_display}")
        
        # 设置波形发生器
        oscilloscope.Set_Wavegen_Waveform(
            waveform=osc_config.waveform_type,
            frequency=freq_hz,
            amplitude=osc_config.amplitude,
            offset=osc_config.offset
        )
        oscilloscope.Set_Wavegen_Output(state='ON', load=50)
        
        # 设置时基
        oscilloscope.Set_Timebase(timebase)
        
        # 等待信号稳定
        time.sleep(osc_config.freq_observation_time)
        
        # 截图
        timestamp = time.strftime('%m%d_%H%M%S')
        screenshot_filename = f"{i+1:02d}_S68GPIO{TEST_CONFIG.s68_source_gpio}_LINK{TEST_CONFIG.active_links}_{freq_display}_{timebase}_{timestamp}.png"
        
        oscilloscope.Save_Screenshot(
            folder=osc_config.screenshot_folder_base,
            filename=screenshot_filename,
            image_format="PNG",
            invert="OFF",
            menu="MOF"
        )
        print(f"    截图已保存: {screenshot_filename}")

def _verify_link_status(q68, active_links):
    """验证链路状态"""
    print(f"\n步骤3: 验证激活Links {active_links} 的状态...")
    
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3
    ]
    
    all_stable = True
    for link in active_links:
        if link < len(link_status_funcs):
            try:
                status = link_status_funcs[link]()
                print(f"  Link{link} 状态: {status}")
                if status != 5:  # 假设5是稳定状态
                    all_stable = False
            except Exception as e:
                print(f"  Link{link} 状态检查失败: {e}")
                all_stable = False
    
    assert all_stable, "部分链路状态不稳定"
    print("所有激活链路状态验证通过")

# =============================================================================
# pytest测试函数
# =============================================================================

@pytest.mark.fast
def test_gpio_s68_q68_auto_traverse_single_link_refactored(devices):
    """重构后的自动遍历测试所有S68 GPIO"""
    tester = GPIO_S68_Q68_AutoTester_Refactored(devices)
    
    print(f"\n🚀 开始S68→Q68 GPIO自动遍历测试 (重构版)")
    print(f"📱 每个S68 GPIO测试前会弹窗确认")
    print(f"🔄 每个S68 GPIO会同时测试所有Q68 GPIO 0-14")
    
    # 测试所有S68 GPIO
    s68_gpio_range = [0, 1, 2, 3, 4, 5, 6, 7, 8]
    
    for s68_gpio in s68_gpio_range:
        print(f"\n📍 当前测试: S68 GPIO{s68_gpio} → Q68 GPIO0-14")
        
        # 执行完整的系统重新初始化
        reinit_success = tester.full_system_reinit(s68_gpio)
        
        if not reinit_success:
            print(f"❌ 系统重新初始化失败，跳过此S68 GPIO测试")
            continue
        
        # 执行测试
        try:
            result = tester.test_single_s68_gpio_with_original_flow(s68_gpio)
            if result:
                print(f"✅ 测试通过: S68 GPIO{s68_gpio}")
            else:
                print(f"❌ 测试失败: S68 GPIO{s68_gpio}")
        except Exception as e:
            print(f"💥 测试异常: S68 GPIO{s68_gpio} - {e}")
    
    print(f"✅ S68→Q68 GPIO自动遍历测试完成 (重构版)")

if __name__ == "__main__":
    """
    重构后的使用说明:
    
    1. 基础测试:
       pytest test_gpio_case6_8auto2_refactored.py::test_gpio_s68_external_to_q68_refactored -v -s
    
    2. 自动遍历测试:
       pytest test_gpio_case6_8auto2_refactored.py::test_gpio_s68_q68_auto_traverse_single_link_refactored -v -s
    
    🔥 重构版特点:
    - 消除对top.py的依赖
    - 使用统一的硬件管理框架
    - 配置管理更加清晰
    - 代码结构更加模块化
    - 保持所有原有功能
    """
    pass
