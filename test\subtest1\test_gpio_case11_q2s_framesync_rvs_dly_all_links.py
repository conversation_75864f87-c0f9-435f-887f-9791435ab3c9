"""
GPIO Case 10: Q68 FrameSync to S68 - rvs_dly延迟补偿全范围测试

📋 测试目的:
    测试Q68内部生成的FrameSync信号到S68 GPIO的传输，重点测试rvs_dly延迟补偿参数

🔧 测试配置:
    - 信号源: Q68内部FrameSync生成器 (GPIO2, MFN=1)
    - 信号频率: 30Hz
    - 目标: S68 GPIO0, GPIO7, GPIO8 (所有Links)
    - 延迟测试: rvs_dly [0, 4, 10, 20, 30, 40, 50, 60, 63]
    - 延迟范围: 0 ~ 220.5us (每步3.5us)

🎯 核心功能:
    - Q68 FrameSync信号生成和配置
    - S68多GPIO接收配置 (所有Links)
    - rvs_dly延迟补偿参数全范围测试
    - 示波器自动截图和结果分析
    - 详细的测试统计和进度显示

🚀 使用方法:
    pytest test_gpio_case10_q2s_framesync_rvs_dly_all_links.py::test_framesync_rvs_dly_compensation_all_links

📊 测试配置:
    - Links: [0, 1, 2, 3] (所有4个Link)
    - FrameSync频率: 30Hz
    - rvs_dly值: [0, 4, 10, 20, 30, 40, 50, 60, 63] (全范围反向延迟)
    - 延迟范围: 0 ~ 220.5us
    - 截图保存: U-disk0/gpiotest/framesync_rvs_dly_all_links/

🆕 FrameSync特点:
    - 📡 内部信号生成: 无需外部信号源
    - 🎯 精确频率控制: 可配置的周期和占空比
    - 🔄 自动帧同步: 支持自动帧同步模式
    - 📊 延迟补偿测试: 专注于rvs_dly参数优化
    - 📷 自动截图存储: 按延迟参数分类存储
"""

import logging
import time
import pytest
import os

# 测试配置
TEST_CONFIG = {
    # Links配置
    'all_links': [0, 1, 2, 3],          # 测试所有4个Link
    
    # FrameSync信号配置
    'framesync_config': {
        'q68_gpio': 2,                   # Q68 GPIO2用于FrameSync输出
        'mfn': 1,                        # MFN=1设置为FrameSync功能
        'signal_id': 11,                 # FrameSync信号ID
        'frequencies': [12],     # 多频率测试 (Hz)
        'periods': {                     # 各频率对应的周期参数
            30: 17361,                   # 30Hz周期
            12: 43403,                   # 12Hz周期 
            50: 15625,                   # 50Hz周期 
        },
        'per_divs': {                    # 各频率对应的分频器设置
            30: 0x0B,                    # 30Hz分频器
            12: 0x0B,                    # 12Hz分频器 
            50: 0x0A,                    # 50Hz分频器
        },
        'i_values': {                    # 各频率对应的i参数
            30: 0,                       # 30Hz的i参数 (需要确认)
            12: 0,                       # 12Hz的i参数 (需要确认)
            50: 0,                       # 50Hz的i参数 (确认值)
        },
        'duty_cycle': 4,                 # 占空比
        'tx_dly_en_values': [1],     # tx_dly_en测试值：1=使能, 0=禁用
    },
    
    # S68目标GPIO配置
    's68_target_gpios': [0, 1, 2],      # S68接收FrameSync的GPIO
    
    # 延迟补偿配置
    'delay_compensation_config': {
        'enable_dly_comp': True,         # 强制启用延迟补偿
        'rvs_dly_test_values': [0, 4, 10, 20, 30, 40, 50, 60, 63],  # rvs_dly测试值序列，全范围测试
        'test_rvs_dly': True,           # 启用rvs_dly全面测试
    },
    
    # 示波器配置
    'oscilloscope_config': {
        'timebases': {                  # 各频率对应的时基
            30: '50us',                 # 30Hz时基
            12: '50us',                 # 12Hz时基 (需要确认)
            50: '50us',                 # 50Hz时基 (需要确认)
        },
        'waveform_type': 'SQUARE',      # 方波
        'amplitude': 1.26,              # 1.26Vpp
        'offset': 0.9,                  # 900mVDC偏置
        'screenshot_folder': 'U-disk0/gpiotest/framesync_rvs_dly_all_links',
        'observation_time': 3,          # 每个延迟值的观察时间
    }
}


class GPIO_FrameSync_RvsDelay_AllLinks_Tester:
    """Q68 FrameSync到S68的rvs_dly延迟补偿测试器"""
    
    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def setup_address_translation_for_all_links(self):
        """为所有Links设置地址转换"""
        try:
            print(f"  📡 为所有Links设置地址转换...")
            
            for link_id in TEST_CONFIG['all_links']:
                if link_id < len(self.s68_res_dev):
                    # 确定I2C总线配置
                    framesync_gpio = TEST_CONFIG['framesync_config']['q68_gpio']
                    if framesync_gpio in [15, 16]:
                        i2c_bus_config = 1  # GPIO15/16使用I2C总线1
                        print(f"    📋 GPIO{framesync_gpio} (特殊GPIO)，使用i2c_bus={i2c_bus_config}")
                    else:
                        i2c_bus_config = 0  # 其他GPIO使用I2C总线0
                        print(f"    📋 GPIO{framesync_gpio} (标准GPIO)，使用i2c_bus={i2c_bus_config}")

                    self.q68_remote.S68_AddrTrans(
                        link=link_id,
                        q68_iic_addr=0x73,
                        s68_iic_addr=0x40,
                        s68_retrans_addr=self.s68_res_dev[link_id],
                        sensor_addr=0x24,
                        sensor_retrans_addr=0x24 + link_id,
                        i2c_bus=i2c_bus_config
                    )
                    print(f"      ✅ Link{link_id} 地址转换: 0x{self.s68_res_dev[link_id]:02X} -> 0x40")
                    # 等待地址转换生效
                    time.sleep(0.1)
                else:
                    print(f"      ⚠️ Link{link_id} 超出范围，跳过")
            
            print(f"    ✅ 所有Links地址转换设置完成")
            return True
            
        except Exception as e:
            print(f"    ❌ 地址转换设置失败: {e}")
            return False

    def configure_q68_framesync_generator(self, frequency, tx_dly_en):
        """配置Q68 FrameSync生成器"""
        try:
            print(f"  📡 配置Q68 FrameSync生成器...")

            framesync_config = TEST_CONFIG['framesync_config']
            period = framesync_config['periods'][frequency]
            per_div = framesync_config['per_divs'][frequency]
            i_value = framesync_config['i_values'][frequency]
            signal_id = framesync_config['signal_id']  # 统一使用signal_id=11

            # 步骤1: 配置Q68 GPIO2为FrameSync功能
            print(f"    📤 配置Q68 GPIO{framesync_config['q68_gpio']}为FrameSync输出...")
            self.q68.MFNSet(gpio=framesync_config['q68_gpio'], mfn=framesync_config['mfn'])

            # 步骤2: 配置FrameSync生成器参数
            print(f"    ⚙️ 配置FrameSync生成器参数 (频率: {frequency}Hz, per_div: 0x{per_div:02X}, i: {i_value}, tx_dly_en: {tx_dly_en})...")
            self.q68.FrameSyncOutConifg(
                i=i_value,                                     # i参数 (50Hz时为1)
                per_div=per_div,                               # 分频器设置 (根据频率)
                duty_cycle=framesync_config['duty_cycle'],     # 占空比
                period=period,                                 # 周期 (根据频率)
                fs_tx_id=signal_id,                            # FrameSync信号ID
                auto_fs=1,                                     # 自动帧同步
                outen=1,                                       # 输出使能
                tx_dly_en=tx_dly_en,                          # 传输延迟使能 (参数化)
                frame_comp_sync_man=0,                         # 帧补偿同步手动模式
                frame_man_trans=0                              # 帧手动传输
            )

            print(f"    ✅ Q68 FrameSync生成器配置完成")
            print(f"    📊 参数: GPIO{framesync_config['q68_gpio']}, 频率{frequency}Hz, i={i_value}, per_div=0x{per_div:02X}, period={period}, tx_dly_en={tx_dly_en}, 信号ID{signal_id}")

            return True

        except Exception as e:
            print(f"    ❌ Q68 FrameSync生成器配置失败: {e}")
            return False

    def configure_all_links_s68_framesync_rx(self, rvs_dly_value):
        """配置所有Links的S68 FrameSync接收"""
        try:
            print(f"  📥 配置所有Links的S68 FrameSync接收 (rvs_dly={rvs_dly_value})...")
            
            signal_id = TEST_CONFIG['framesync_config']['signal_id']
            s68_target_gpios = TEST_CONFIG['s68_target_gpios']
            
            # 配置所有Links的S68设备
            for link in TEST_CONFIG['all_links']:
                if link < len(self.s68_res_dev):
                    print(f"    🔗 配置Link{link} S68设备...")
                    s68_iic_dev = self.s68_res_dev[link]
                    self.q68_remote.dongle.devAddr = s68_iic_dev
                    
                    # 等待设备地址切换稳定
                    time.sleep(0.1)
                    print(f"      📍 设备地址切换到: 0x{s68_iic_dev:02X}")

                    # 配置反向延迟补偿参数
                    print(f"      ⏱️ 配置rvs_dly延迟补偿: {rvs_dly_value} ({rvs_dly_value * 3.5:.1f}us)")
                    self.q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)
                    
                    # 配置S68 GPIO接收FrameSync信号
                    print(f"      📋 配置S68 GPIO{s68_target_gpios}接收FrameSync...")
                    success_count = 0
                    
                    for s68_gpio in s68_target_gpios:
                        try:
                            self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                            self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
                            success_count += 1
                            print(f"        ✅ GPIO{s68_gpio} 配置成功")
                        except Exception as e:
                            print(f"        ❌ GPIO{s68_gpio} 配置失败: {e}")
                    
                    print(f"      📊 Link{link} GPIO配置结果: 成功{success_count}/{len(s68_target_gpios)}")
                else:
                    print(f"    ⚠️ Link{link} 超出范围，跳过")
            
            print(f"    ✅ 所有Links S68 FrameSync接收配置完成")
            return True
            
        except Exception as e:
            print(f"    ❌ S68 FrameSync接收配置失败: {e}")
            return False

    def oscilloscope_screenshot(self, frequency, rvs_dly_value, tx_dly_en):
        """示波器截图 - FrameSync信号"""
        try:
            print(f"  📷 示波器截图 (频率={frequency}Hz, rvs_dly={rvs_dly_value}, tx_dly_en={tx_dly_en})...")

            if not self.oscilloscope:
                print(f"    ⚠️ 示波器未连接，跳过截图")
                return True

            osc_config = TEST_CONFIG['oscilloscope_config']

            # 步骤1: 配置示波器基本参数
            print(f"      ⚙️ 配置示波器参数...")

            # 设置时基 (根据频率)
            timebases = osc_config.get('timebases', {})
            if frequency in timebases:
                timebase = timebases[frequency]
            else:
                # 默认时基设置
                timebase_map = {30: '20ms', 12: '50ms', 50: '10ms'}
                timebase = timebase_map.get(frequency, '20ms')
                print(f"        ⚠️ 频率{frequency}Hz未配置时基，使用默认值: {timebase}")

            self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
            print(f"        📊 时基: {timebase} (频率: {frequency}Hz)")

            # 设置触发 - 使用数字通道1
            self.oscilloscope.Set_Digital_Trigger(digital_channel=15)
            print(f"        🎯 触发: 数字通道D15")

            # 步骤2: 等待信号稳定
            print(f"      ⏳ 等待FrameSync信号稳定...")
            time.sleep(osc_config['observation_time'])

            # 步骤3: 截图
            timestamp = time.strftime('%m%d_%H%M%S')

            # 根据tx_dly_en和rvs_dly_value生成文件名
            if tx_dly_en == 0:
                # tx_dly_en=0时，rvs_dly无效，使用"none"
                screenshot_filename = f"AllLinks_FrameSync_{frequency}Hz_rvs_dly_none_txdly{tx_dly_en}_{timestamp}.png"
            else:
                # tx_dly_en=1时，显示具体的rvs_dly值
                screenshot_filename = f"AllLinks_FrameSync_{frequency}Hz_rvs_dly{rvs_dly_value:02d}_txdly{tx_dly_en}_{timestamp}.png"

            # 创建文件夹
            screenshot_folder = osc_config['screenshot_folder']
            screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

            os.makedirs(screenshot_folder, exist_ok=True)

            print(f"      📸 保存截图: {screenshot_path}")
            self.oscilloscope.Save_Image(
                filepath=screenshot_path,
                image_format="PNG",
                invert="OFF",      # 正常色彩
                menu="MOF"         # 隐藏菜单
            )

            time.sleep(1)  # 等待保证截图成功

            print(f"    ✅ 示波器截图完成")
            return True

        except Exception as e:
            print(f"    ❌ 示波器截图失败: {e}")
            return False

    def test_framesync_single_combination(self, frequency, rvs_dly_value, tx_dly_en):
        """测试单个频率、rvs_dly值和tx_dly_en组合的FrameSync传输"""
        try:
            # 根据tx_dly_en决定是否显示rvs_dly
            if tx_dly_en == 0:
                print(f"\n🔧 测试组合: 频率={frequency}Hz, tx_dly_en={tx_dly_en} (rvs_dly无效)...")
            else:
                print(f"\n🔧 测试组合: 频率={frequency}Hz, rvs_dly={rvs_dly_value} ({rvs_dly_value * 3.5:.1f}us), tx_dly_en={tx_dly_en}...")

            # 步骤1: 设置地址转换
            print(f"  📡 步骤1: 设置地址转换...")
            if not self.setup_address_translation_for_all_links():
                return False

            # 步骤2: 配置所有Links的S68 FrameSync接收 (调整顺序：先配置S68)
            print(f"  � 步骤2: 配置所有Links S68 FrameSync接收...")
            if not self.configure_all_links_s68_framesync_rx(rvs_dly_value if tx_dly_en else 0):
                return False

            # 步骤3: 配置Q68 FrameSync生成器 (调整顺序：后配置Q68)
            print(f"  � 步骤3: 配置Q68 FrameSync生成器...")
            if not self.configure_q68_framesync_generator(frequency, tx_dly_en):
                return False

            # 步骤4: 示波器测试
            print(f"  📷 步骤4: 示波器FrameSync测试...")
            self.oscilloscope_screenshot(frequency, rvs_dly_value, tx_dly_en)

            print(f"  ✅ FrameSync测试完成: 频率={frequency}Hz, tx_dly_en={tx_dly_en}")
            return True

        except Exception as e:
            print(f"  ❌ FrameSync测试失败: {e}")
            return False


@pytest.mark.fast
def test_framesync_rvs_dly_compensation_all_links(devices):
    """
    Q68 FrameSync到S68的多频率、rvs_dly延迟补偿测试
    """
    print("\n" + "="*80)
    print("🚀 Q68 FrameSync到S68 - 多频率rvs_dly延迟补偿测试")
    print("📡 信号源: Q68内部FrameSync生成器")
    print("="*80)

    tester = GPIO_FrameSync_RvsDelay_AllLinks_Tester(devices)

    # 测试参数
    frequencies = TEST_CONFIG['framesync_config']['frequencies']
    rvs_dly_values = TEST_CONFIG['delay_compensation_config']['rvs_dly_test_values']
    tx_dly_en_values = TEST_CONFIG['framesync_config']['tx_dly_en_values']
    framesync_config = TEST_CONFIG['framesync_config']
    s68_target_gpios = TEST_CONFIG['s68_target_gpios']

    # 计算总测试数
    total_tests = 0
    for frequency in frequencies:
        for tx_dly_en in tx_dly_en_values:
            if tx_dly_en == 1:
                total_tests += len(rvs_dly_values)  # tx_dly_en=1时测试所有rvs_dly值
            else:
                total_tests += 1  # tx_dly_en=0时只测试一次

    print(f"📊 测试配置:")
    print(f"  - Links: {TEST_CONFIG['all_links']}")
    print(f"  - FrameSync频率: {frequencies} Hz")
    print(f"  - Q68 FrameSync GPIO: {framesync_config['q68_gpio']} (MFN={framesync_config['mfn']})")
    print(f"  - S68目标GPIOs: {s68_target_gpios}")
    print(f"  - rvs_dly值: {rvs_dly_values}")
    print(f"  - rvs_dly延迟范围: {[v * 3.5 for v in rvs_dly_values]} us")
    print(f"  - tx_dly_en值: {tx_dly_en_values}")
    print(f"  - 总测试数: {total_tests}")

    results = {}
    test_count = 0

    # 三重循环：频率 -> tx_dly_en -> rvs_dly
    for frequency in frequencies:
        print(f"\n🎵 开始测试频率: {frequency}Hz")

        for tx_dly_en in tx_dly_en_values:
            print(f"\n⚙️ tx_dly_en = {tx_dly_en} ({'使能' if tx_dly_en else '禁用'})")

            if tx_dly_en == 1:
                # tx_dly_en=1时，测试所有rvs_dly值
                for rvs_dly_value in rvs_dly_values:
                    test_count += 1
                    print(f"\n📍 测试进度: {test_count}/{total_tests}")

                    result = tester.test_framesync_single_combination(frequency, rvs_dly_value, tx_dly_en)
                    test_key = f"freq{frequency}_rvs{rvs_dly_value}_txdly{tx_dly_en}"
                    results[test_key] = result

                    if result:
                        print(f"  ✅ 测试通过: {frequency}Hz, rvs_dly={rvs_dly_value}, tx_dly_en={tx_dly_en}")
                    else:
                        print(f"  ❌ 测试失败: {frequency}Hz, rvs_dly={rvs_dly_value}, tx_dly_en={tx_dly_en}")

                    # 测试间隔
                    if test_count < total_tests:
                        time.sleep(2)
            else:
                # tx_dly_en=0时，只测试一次（rvs_dly无效）
                test_count += 1
                print(f"\n📍 测试进度: {test_count}/{total_tests}")

                result = tester.test_framesync_single_combination(frequency, 0, tx_dly_en)  # rvs_dly=0（无效）
                test_key = f"freq{frequency}_rvs_none_txdly{tx_dly_en}"
                results[test_key] = result

                if result:
                    print(f"  ✅ 测试通过: {frequency}Hz, tx_dly_en={tx_dly_en} (rvs_dly无效)")
                else:
                    print(f"  ❌ 测试失败: {frequency}Hz, tx_dly_en={tx_dly_en} (rvs_dly无效)")

                # 测试间隔
                if test_count < total_tests:
                    time.sleep(2)

    # 测试总结
    successful_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - successful_tests

    print(f"\n📊 测试总结:")
    print(f"  - 总测试: {total_tests}")
    print(f"  - 成功: {successful_tests}")
    print(f"  - 失败: {failed_tests}")
    print(f"  - 成功率: {successful_tests/total_tests*100:.1f}%")

    # 详细结果展示
    print(f"\n📋 详细测试结果:")
    for frequency in frequencies:
        print(f"  🎵 频率 {frequency}Hz:")
        for tx_dly_en in tx_dly_en_values:
            if tx_dly_en == 1:
                for rvs_dly_value in rvs_dly_values:
                    test_key = f"freq{frequency}_rvs{rvs_dly_value}_txdly{tx_dly_en}"
                    status = "✅" if results.get(test_key, False) else "❌"
                    print(f"    {status} rvs_dly={rvs_dly_value:2d} ({rvs_dly_value * 3.5:5.1f}us), tx_dly_en={tx_dly_en}")
            else:
                test_key = f"freq{frequency}_rvs_none_txdly{tx_dly_en}"
                status = "✅" if results.get(test_key, False) else "❌"
                print(f"    {status} rvs_dly=无效, tx_dly_en={tx_dly_en}")

    # 验证测试结果
    assert successful_tests > 0, "所有FrameSync测试都失败了"

    print(f"\n✅ Q68 FrameSync到S68多频率rvs_dly延迟补偿测试完成")


if __name__ == "__main__":
    print("请使用 pytest 运行此测试文件")
    print("示例: pytest test_gpio_case10_q2s_framesync_rvs_dly_all_links.py::test_framesync_rvs_dly_compensation_all_links")
