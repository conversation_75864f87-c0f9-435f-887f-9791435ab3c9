import time
import QRegisterAccess

if QRegisterAccess.findMeritechI2cChan():

    # memory map:
    #     0x10:0x00~0xff, ana_tx
    #     0x11:0x00~0xff, ana_pll
    #     0x12:0x00~0xff, dig_clk_rst
    #     0x13:0x00~0xff, dig_core
    #     0x14:0x00~0xff, dig_irq
    #     0x15:0x00~0xff, dig_pinmux
    ANA_TX     = 0x10
    ANA_PLL    = 0x11
    DIG_CLK    = 0x12
    DIG_CORE   = 0x13
    DIG_IRQ    = 0x14
    DIG_PINMUX = 0x15
    TESTBENCH  = 0x60

    # reset tb and dut
    QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x0) # tb_resetn
    QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x1) # tb_resetn

    # test register r/w
    print('')
    print('testing reg r/w ...')
    print('    ana_tx, read  cfg_1(0x04): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(ANA_TX, 4)))
    print('    ana_tx, write cfg_1(0x04): 0x%02x'%(0x55))
    QRegisterAccess.i2cWriteC3TxTestReg(ANA_TX, 4, 0x55)
    print('    ana_tx, read  cfg_1(0x04): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(ANA_TX, 4)))
    print('    ana_tx, write cfg_1(0x04): 0x%02x'%(0xaa))
    QRegisterAccess.i2cWriteC3TxTestReg(ANA_TX, 4, 0xaa)
    print('    ana_tx, read  cfg_1(0x04): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(ANA_TX, 4)))
    print('')
    print('    testbench, read  id(0x00): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x00)))
    print('')

    prbs_modes = [(0, 'prbs7'), (1, 'prbs11'), (2, 'prbs15'), (3, 'prbs23'), (4, 'prbs31')]

    for loop in range(1):
        
        for prbs_mode in prbs_modes:

            mode = prbs_mode[0]
            print('testing %s'%(prbs_mode[1]))

            # diable tb to insert error
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x07, 0x0) # ins_err_en

            # reset tb and dut
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x0) # tb_resetn
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x1) # tb_resetn

            # set tb test mode
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x03, mode) # prbs mode

            # config dut prbs generator
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0x70, 0x0)  # prbs_gen_en
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0x6c, mode) # prbs_gen_mode, 0-7,1-11,2-15,3-23,4-31
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0x70, 0x1)  # prbs_gen_en

            # config dut prbs checker
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb4, 0x0)  # prbs_chk_en
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb0, 0x1)  # prbs_err_clr
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb8, mode) # prbs_chk_mode, 0-7,1-11,2-15,3-23,4-31
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xbc, 0x2)  # prbs_chk_node_sel, 0-before fifo, 1-after fifo, 2-phy input
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xf0, 0x1)  # pack
            QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb4, 0x1)  # prbs_chk_en

            # delay
            time.sleep(0.1)

            # read dut prbs checker result w/o inserting error
            dut_prbs_err_cnt_vld = QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc0)
            dut_prbs_err_cnt     = (QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc4) << 8) + QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc8)
            if dut_prbs_err_cnt > 0:
                print('    dig_core, prbs checker valid: %x, error cnt = 0x%x (should be 0)'%(dut_prbs_err_cnt_vld, dut_prbs_err_cnt))

            # enable tb to insert error
            err_gap = 10
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x0a, err_gap & 0xff) # ins_err_gap
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x0b, (err_gap >> 8) & 0xff) # ins_err_gap
            err_num = 0x1234
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x08, err_num & 0xff) # ins_err_num
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x09, (err_num >> 8) & 0xff) # ins_err_num
            QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x07, 0x1) # ins_err_en

            # delay
            time.sleep(0.1)

            # read dut prbs checker result
            dut_prbs_err_cnt_vld = QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc0)
            dut_prbs_err_cnt     = (QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc4) << 8) + QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc8)
            if (dut_prbs_err_cnt != err_num):
                print('    dig_core, prbs checker valid: %x, error cnt = 0x%x (should be 0x%x)'%(dut_prbs_err_cnt_vld, dut_prbs_err_cnt, err_num))
            
            # read testbench prbs checker result
            tb_prbs_match    = QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x02)
            tb_prbs_err_stat = (QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x06) << 16) + (QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x05) << 8) + QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x04)
            if tb_prbs_err_stat > 0:
                print('    testbench, prbs checker match: %x, error status = 0x%x (should be 0)'%(tb_prbs_match, tb_prbs_err_stat))
