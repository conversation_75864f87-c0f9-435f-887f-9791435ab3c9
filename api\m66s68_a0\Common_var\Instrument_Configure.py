import GPIB_ObjectList as GPIB_ObjectList



# SCOPE = GPIB_ObjectList.Key86100D(address="7", porttype='usb')
# Multimeter = GPIB_ObjectList.DMM6500(address="7", porttype='usb', usb_address="USB0::0x05E6::0x6500::04440058::INSTR")

def VoltageSet(volset=True,getcurrent=False,turnoff=False,vdd33a=3.3,vdd33=3.3,vdda=0.9,vdd=0.9):
    
    if vdd33a > 3.7 or vdd33 > 3.7 or vdda > 1.1 or vdd > 0.99:
        raise 'voltage settings is unreaonable!'
    
    powersupply1 = GPIB_ObjectList.IT6332B()
    powersupply  = GPIB_ObjectList.IT6932A()
    
def multimeterSetUp(testtype='dcvoltage', rang=0.1):
    
    if  testtype=='dcvoltage':
        rvc=Multimeter.DcVoltgeMeasure(rang=rang)
    elif testtype=='curr':
        rvc=Multimeter.DCCurrentMeasure(rang=range)
        
    return rvc