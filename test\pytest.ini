[pytest]
log_cli=1
log_cli_level=debug
testpaths = ./
python_files = test_*.py
python_functions = test_*
pythonpath =
    ../api/bridge_brd_dev/bridge_mcu
    ../api/m65q68_a0/
    ../api/m66s68_a0/
    ../api/instr/
; -s: see print statements
; -v: verbose output
; -x: stop after first failure
; -k: run tests that match the expression
; -m: run tests with the specified marker, like -m "not slow" to deselect slow tests
; -q: quiet mode, show overall results only
addopts = -vs --html=test/report.html --self-contained-html -m "not slow"

; markers to categorize tests
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    fast: marks tests as fast (default)
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    regression: marks tests as regression tests
