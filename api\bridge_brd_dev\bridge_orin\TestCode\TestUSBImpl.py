#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import HWImpl
import time

if __name__ == '__main__':
    devlist = HWImpl.hw_serial_scan()
    print("探测到 USBCOMM 设备列表:")
    for i in range(0, len(devlist)):
        print("    ", devlist[i])

    # 使用第一个设备
    HWImpl.hw_open(port_name=devlist[0].name)

    ret = HWImpl.hw_waithw_ok(3)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")
    time.sleep(1)
    ret = HWImpl.hw_set_cam_wxh(3333, 3333)
    print("operation=",ret," hw_set_wxh")
    time.sleep(1)
    ret = HWImpl.hw_set_cam_wxh(4444, 4444)
    print("operation=",ret," hw_set_wxh")
    time.sleep(1)
    ret = HWImpl.hw_set_cam_wxh(1111, 1111)
    print("operation=",ret," hw_set_wxh")
    time.sleep(1)
    ret = HWImpl.hw_set_cam_wxh(2222, 2222)
    print("operation=",ret," hw_set_wxh")

    # 关闭设备
    HWImpl.hw_close()
