#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import HWImpl
# from HWInterface import SSHFileCtrl
import time
import datetime


host_ip = "*************"                   # orin ip
local_path = "D://Test_log//orin//log//T37"      # download log(log+image) to local folder
    
if __name__ == '__main__':
    # 使用第一个设备

    
    HWImpl.hw_open(ip_addr=host_ip)

    ret = HWImpl.hw_waithw_ok(100)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")

    # # 创建cvs文件(local)
    # result_csv_file_name = "mipi_trace_" + datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S') + ".csv"
    # result_csv = open(result_csv_file_name, 'w', encoding='utf-8')
    # name = ['time', "idx", "result"]
    # result_csv.write(','.join(name))
    # result_csv.write('\n')

    for idx in range(2):
    
        ret = HWImpl.hw_clear_tracelog_status()     #clear log
        print("operation=",ret," hw_clear_tracelog_status")
    
        ret = HWImpl.hw_start_tracelog()            #start log
        print("operation=",ret," hw_start_mipilog")
    
        time.sleep(5)
    
        ret = HWImpl.hw_stop_tracelog()     #stop log
    
        time.sleep(0.2)
        print("operation=",ret," hw_stop_mipilog")
        log_err = HWImpl.hw_get_tracelog_status()   #1=error during start-stop hit(all vedio)
        print("log error = ",log_err)
        
    
    # SSHFileCtrl.get_orin_picture_and_tracelog_folder(host_ip, local_path)   # download
        
    # 关闭设备
    HWImpl.hw_close()
