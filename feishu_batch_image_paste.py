#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书文档批量粘贴图片脚本
自动化完成：选择单元格 -> 复制图片 -> 粘贴 -> 下一个单元格
"""

import pyautogui
import time
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading

class FeishuImagePaster:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("飞书批量粘贴图片工具")
        self.root.geometry("520x600")
        
        self.image_folder = ""
        self.image_files = []
        self.subfolders = []  # 子文件夹列表
        self.start_pos = None
        self.next_col_pos = None  # 下一列位置
        self.next_row_pos = None  # 下一行位置
        self.cell_width = 100  # 单元格宽度
        self.row_height = 50   # 行间距
        self.paste_delay = 1   # 粘贴后等待时间
        self.multi_folder_mode = False  # 多文件夹模式
        self.ps_process = None  # 持久化PowerShell进程

        self.setup_ui()
        self.init_powershell()
        
    def setup_ui(self):
        # 文件夹选择
        tk.Label(self.root, text="1. 选择图片文件夹:").pack(pady=5)
        frame1 = tk.Frame(self.root)
        frame1.pack(pady=5)
        
        self.folder_label = tk.Label(frame1, text="未选择文件夹", width=40, relief="sunken")
        self.folder_label.pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame1, text="选择单个文件夹", command=self.select_folder).pack(side=tk.LEFT, padx=2)
        tk.Button(frame1, text="选择多文件夹", command=self.select_multi_folders).pack(side=tk.LEFT, padx=2)
        
        # 图片列表
        tk.Label(self.root, text="2. 图片文件列表:").pack(pady=(10,5))
        
        list_frame = tk.Frame(self.root)
        list_frame.pack(pady=5, fill=tk.BOTH, expand=True)
        
        self.listbox = tk.Listbox(list_frame, height=8)
        scrollbar = tk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.listbox.yview)
        self.listbox.config(yscrollcommand=scrollbar.set)
        
        self.listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 参数设置
        param_frame = tk.Frame(self.root)
        param_frame.pack(pady=10)
        
        tk.Label(param_frame, text="列间距(像素):").grid(row=0, column=0, padx=5)
        self.cell_width_var = tk.StringVar(value="100")
        self.cell_width_entry = tk.Entry(param_frame, textvariable=self.cell_width_var, width=8)
        self.cell_width_entry.grid(row=0, column=1, padx=5)

        tk.Label(param_frame, text="行间距(像素):").grid(row=0, column=2, padx=5)
        self.row_height_var = tk.StringVar(value="50")
        self.row_height_entry = tk.Entry(param_frame, textvariable=self.row_height_var, width=8)
        self.row_height_entry.grid(row=0, column=3, padx=5)

        tk.Label(param_frame, text="粘贴等待时间(秒):").grid(row=1, column=0, padx=5, pady=5)
        self.delay_var = tk.StringVar(value="0.5")
        tk.Entry(param_frame, textvariable=self.delay_var, width=8).grid(row=1, column=1, padx=5)
        
        # 位置设置
        tk.Label(self.root, text="3. 设置单元格位置 (自动计算间距):").pack(pady=(10,5))

        pos_frame = tk.Frame(self.root)
        pos_frame.pack(pady=5)

        tk.Button(pos_frame, text="1.起始位置", command=self.set_start_position,
                 bg="orange", width=12).pack(side=tk.LEFT, padx=2)
        tk.Button(pos_frame, text="2.下一列", command=self.set_next_col_position,
                 bg="lightblue", width=12).pack(side=tk.LEFT, padx=2)
        tk.Button(pos_frame, text="3.下一行", command=self.set_next_row_position,
                 bg="lightgreen", width=12).pack(side=tk.LEFT, padx=2)

        # 位置显示
        self.pos_label = tk.Label(self.root, text="未设置位置", relief="sunken", height=3)
        self.pos_label.pack(pady=5, fill=tk.X, padx=20)
        
        # 控制按钮
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(pady=20)
        
        self.start_btn = tk.Button(btn_frame, text="开始批量粘贴", command=self.start_pasting, 
                                  bg="green", fg="white", font=("Arial", 12))
        self.start_btn.pack(side=tk.LEFT, padx=10)
        
        tk.Button(btn_frame, text="停止", command=self.stop_pasting,
                 bg="red", fg="white").pack(side=tk.LEFT, padx=10)

        # ESC键提示
        tk.Label(self.root, text="💡 提示：运行过程中可按 ESC 键立即停止",
                fg="blue", font=("Arial", 9)).pack(pady=5)
        
        self.progress = ttk.Progressbar(self.root, mode='determinate')
        self.progress.pack(pady=10, fill=tk.X, padx=20)
        
        self.status_label = tk.Label(self.root, text="准备就绪")
        self.status_label.pack(pady=5)
        
        self.running = False
        self.esc_pressed = False

    def check_esc_key(self):
        """检测ESC键是否被按下"""
        try:
            import msvcrt
            if msvcrt.kbhit():
                key = msvcrt.getch()
                if key == b'\x1b':  # ESC键的ASCII码
                    self.esc_pressed = True
                    self.running = False
                    print("检测到ESC键，正在停止...")
        except:
            pass

    def init_powershell(self):
        """初始化持久化PowerShell进程"""
        try:
            import subprocess
            self.ps_process = subprocess.Popen(
                ['powershell', '-NoProfile', '-NoLogo', '-NonInteractive', '-Command', '-'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            print("PowerShell进程初始化成功")
        except Exception as e:
            print(f"PowerShell进程初始化失败: {e}")
            self.ps_process = None

    def select_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.image_folder = folder
            self.multi_folder_mode = False
            self.subfolders = []
            self.folder_label.config(text=os.path.basename(folder))
            self.load_images()

    def select_multi_folders(self):
        parent_folder = filedialog.askdirectory(title="选择包含多个子文件夹的父目录")
        if parent_folder:
            self.image_folder = parent_folder
            self.multi_folder_mode = True
            self.load_subfolders()
            self.load_images()
            
    def load_subfolders(self):
        """加载子文件夹列表"""
        self.subfolders = []
        if not self.image_folder:
            return

        for item in sorted(os.listdir(self.image_folder)):
            item_path = os.path.join(self.image_folder, item)
            if os.path.isdir(item_path):
                self.subfolders.append(item)

    def load_images(self):
        self.listbox.delete(0, tk.END)
        self.image_files = []

        if not self.image_folder:
            return

        # 支持的图片格式
        extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp')

        if self.multi_folder_mode:
            # 多文件夹模式：显示所有子文件夹的图片
            total_images = 0
            for subfolder in self.subfolders:
                subfolder_path = os.path.join(self.image_folder, subfolder)
                folder_images = []
                for file in sorted(os.listdir(subfolder_path)):
                    if file.lower().endswith(extensions):
                        folder_images.append((subfolder, file))
                        total_images += 1

                # 在列表中显示文件夹分组
                if folder_images:
                    self.listbox.insert(tk.END, f"📁 {subfolder} ({len(folder_images)}张)")
                    for _, file in folder_images:
                        self.listbox.insert(tk.END, f"  📷 {file}")

            self.image_files = []  # 在多文件夹模式下，这个列表用于存储所有图片信息
            self.status_label.config(text=f"找到 {len(self.subfolders)} 个文件夹，共 {total_images} 个图片文件")
        else:
            # 单文件夹模式
            for file in sorted(os.listdir(self.image_folder)):
                if file.lower().endswith(extensions):
                    self.image_files.append(file)
                    self.listbox.insert(tk.END, file)

            self.status_label.config(text=f"找到 {len(self.image_files)} 个图片文件")
        
    def set_start_position(self):
        self.root.withdraw()  # 隐藏窗口
        messagebox.showinfo("设置起始位置", "3秒后请将鼠标移动到第一个单元格位置")

        time.sleep(3)
        self.start_pos = pyautogui.position()

        self.root.deiconify()  # 显示窗口
        self.update_position_display()

    def set_next_col_position(self):
        if not self.start_pos:
            messagebox.showerror("错误", "请先设置起始位置")
            return

        self.root.withdraw()  # 隐藏窗口
        messagebox.showinfo("设置下一列位置", "3秒后请将鼠标移动到同一行下一个单元格位置")

        time.sleep(3)
        self.next_col_pos = pyautogui.position()

        # 计算列间距
        self.cell_width = abs(self.next_col_pos.x - self.start_pos.x)
        self.cell_width_var.set(str(self.cell_width))

        self.root.deiconify()  # 显示窗口
        self.update_position_display()

    def set_next_row_position(self):
        if not self.start_pos:
            messagebox.showerror("错误", "请先设置起始位置")
            return

        self.root.withdraw()  # 隐藏窗口
        messagebox.showinfo("设置下一行位置", "3秒后请将鼠标移动到下一行第一个单元格位置")

        time.sleep(3)
        self.next_row_pos = pyautogui.position()

        # 计算行间距
        self.row_height = abs(self.next_row_pos.y - self.start_pos.y)
        self.row_height_var.set(str(self.row_height))

        self.root.deiconify()  # 显示窗口
        self.update_position_display()

    def update_position_display(self):
        """更新位置显示信息"""
        info_lines = []

        if self.start_pos:
            info_lines.append(f"起始位置: ({self.start_pos.x}, {self.start_pos.y})")
        else:
            info_lines.append("起始位置: 未设置")

        if self.next_col_pos:
            info_lines.append(f"下一列位置: ({self.next_col_pos.x}, {self.next_col_pos.y}) → 列间距: {self.cell_width}px")
        else:
            info_lines.append(f"下一列位置: 未设置 → 列间距: {self.cell_width}px (手动设置)")

        if self.next_row_pos:
            info_lines.append(f"下一行位置: ({self.next_row_pos.x}, {self.next_row_pos.y}) → 行间距: {self.row_height}px")
        else:
            info_lines.append(f"下一行位置: 未设置 → 行间距: {self.row_height}px (手动设置)")

        self.pos_label.config(text="\n".join(info_lines))
        
    def start_pasting(self):
        if not self.image_folder:
            messagebox.showerror("错误", "请先选择包含图片的文件夹")
            return

        if not self.start_pos:
            messagebox.showerror("错误", "请先设置起始单元格位置")
            return

        try:
            self.cell_width = int(self.cell_width_var.get())
            self.row_height = int(self.row_height_var.get())
            self.paste_delay = float(self.delay_var.get())
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字参数")
            return

        self.running = True
        self.start_btn.config(state="disabled")

        # 在新线程中执行粘贴操作
        if self.multi_folder_mode:
            thread = threading.Thread(target=self.paste_multi_folder_images)
        else:
            thread = threading.Thread(target=self.paste_images)
        thread.daemon = True
        thread.start()
        
    def paste_images(self):
        try:
            total = len(self.image_files)
            self.progress.config(maximum=total)
            
            for i, image_file in enumerate(self.image_files):
                # 检测ESC键和运行状态
                self.check_esc_key()
                if not self.running:
                    break

                start_time = time.time()
                self.status_label.config(text=f"处理第 {i+1}/{total} 个图片: {image_file}")

                # 计算当前单元格位置
                current_x = self.start_pos.x + i * self.cell_width
                current_y = self.start_pos.y

                # 点击单元格
                click_start = time.time()
                pyautogui.click(current_x, current_y)
                time.sleep(0.1)
                click_time = time.time() - click_start

                # 复制图片文件
                copy_start = time.time()
                image_path = os.path.join(self.image_folder, image_file)
                self.copy_image_file(image_path)
                copy_time = time.time() - copy_start

                # 粘贴
                paste_start = time.time()
                pyautogui.hotkey('ctrl', 'v')
                paste_time = time.time() - paste_start

                # 等待上传完成
                upload_start = time.time()
                time.sleep(self.paste_delay)
                upload_time = time.time() - upload_start

                total_time = time.time() - start_time
                print(f"图片{i+1}: 点击={click_time:.3f}s, 复制={copy_time:.3f}s, 粘贴={paste_time:.3f}s, 上传等待={upload_time:.3f}s, 总计={total_time:.3f}s")

                self.progress['value'] = i + 1
                self.root.update()
                
            if self.running:
                self.status_label.config(text="批量粘贴完成！")
                messagebox.showinfo("完成", "所有图片已成功粘贴！")
            else:
                if self.esc_pressed:
                    self.status_label.config(text="已通过ESC键停止操作")
                    messagebox.showinfo("停止", "操作已通过ESC键停止")
                else:
                    self.status_label.config(text="操作已停止")

        except Exception as e:
            messagebox.showerror("错误", f"操作过程中出现错误: {str(e)}")
        finally:
            self.running = False
            self.start_btn.config(state="normal")

    def paste_multi_folder_images(self):
        """多文件夹模式的粘贴处理"""
        try:
            extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp')
            total_processed = 0
            current_row = 0

            # 计算总图片数量用于进度条
            total_images = 0
            for subfolder in self.subfolders:
                subfolder_path = os.path.join(self.image_folder, subfolder)
                for file in os.listdir(subfolder_path):
                    if file.lower().endswith(extensions):
                        total_images += 1

            self.progress.config(maximum=total_images)

            # 逐个处理子文件夹
            for folder_index, subfolder in enumerate(self.subfolders):
                if not self.running:
                    break

                subfolder_path = os.path.join(self.image_folder, subfolder)
                folder_images = []

                # 获取当前文件夹的所有图片
                for file in sorted(os.listdir(subfolder_path)):
                    if file.lower().endswith(extensions):
                        folder_images.append(file)

                if not folder_images:
                    continue

                self.status_label.config(text=f"处理文件夹 {folder_index+1}/{len(self.subfolders)}: {subfolder}")

                # 处理当前文件夹的所有图片（横向排列）
                for img_index, image_file in enumerate(folder_images):
                    # 检测ESC键和运行状态
                    self.check_esc_key()
                    if not self.running:
                        break

                    # 计算当前图片位置
                    current_x = self.start_pos.x + img_index * self.cell_width
                    current_y = self.start_pos.y + current_row * self.row_height

                    self.status_label.config(text=f"处理 {subfolder}/{image_file} ({total_processed+1}/{total_images})")

                    # 点击单元格
                    pyautogui.click(current_x, current_y)
                    time.sleep(0.1)

                    # 复制图片文件
                    image_path = os.path.join(subfolder_path, image_file)
                    self.copy_image_file(image_path)

                    # 粘贴
                    pyautogui.hotkey('ctrl', 'v')

                    # 等待上传完成
                    time.sleep(self.paste_delay)

                    total_processed += 1
                    self.progress['value'] = total_processed
                    self.root.update()

                # 移动到下一行
                current_row += 1

            if self.running:
                self.status_label.config(text="多文件夹批量粘贴完成！")
                messagebox.showinfo("完成", f"已处理 {len(self.subfolders)} 个文件夹，共 {total_processed} 张图片！")
            else:
                if self.esc_pressed:
                    self.status_label.config(text="已通过ESC键停止操作")
                    messagebox.showinfo("停止", f"操作已通过ESC键停止，已处理 {total_processed} 张图片")
                else:
                    self.status_label.config(text="操作已停止")

        except Exception as e:
            messagebox.showerror("错误", f"操作过程中出现错误: {str(e)}")
        finally:
            self.running = False
            self.start_btn.config(state="normal")
            
    def copy_image_file(self, file_path):
        """复制图片文件到剪贴板"""
        import subprocess
        import os

        # 方法1: 使用持久化PowerShell进程
        if self.ps_process and self.ps_process.poll() is None:
            try:
                ps_start = time.time()
                cmd = f'Set-Clipboard -Path "{file_path}"\n'
                self.ps_process.stdin.write(cmd)
                self.ps_process.stdin.flush()
                # 短暂等待命令执行
                time.sleep(0.2)
                ps_time = time.time() - ps_start
                print(f"    持久化PowerShell耗时: {ps_time:.3f}s")
                return
            except Exception as e:
                print(f"    持久化PowerShell失败: {str(e)}")
                # 重新初始化PowerShell进程
                self.init_powershell()

        # 方法2: 备用的一次性PowerShell方法
        try:
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            ps_start = time.time()
            ps_cmd = f'Set-Clipboard -Path "{file_path}"'
            subprocess.run(['powershell', '-Command', ps_cmd], check=True, capture_output=True)
            ps_time = time.time() - ps_start
            print(f"    一次性PowerShell耗时: {ps_time:.3f}s (文件大小: {file_size:.2f}MB)")
        except Exception as e:
            print(f"    一次性PowerShell失败: {str(e)}")
            # 方法3: 如果PowerShell失败，尝试直接选择文件并复制
            # 打开文件资源管理器并选择文件
            explorer_start = time.time()
            subprocess.run(['explorer', '/select,', file_path])
            time.sleep(0.1)
            # 复制选中的文件
            pyautogui.hotkey('ctrl', 'c')
            time.sleep(0.1)
            explorer_time = time.time() - explorer_start
            print(f"    文件管理器方法耗时: {explorer_time:.3f}s")
        
    def stop_pasting(self):
        self.running = False
        self.esc_pressed = True
        self.status_label.config(text="正在停止...")
        
    def cleanup_powershell(self):
        """清理PowerShell进程"""
        if self.ps_process:
            try:
                self.ps_process.terminate()
                self.ps_process.wait(timeout=2)
            except:
                try:
                    self.ps_process.kill()
                except:
                    pass
            self.ps_process = None

    def run(self):
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        finally:
            self.cleanup_powershell()

    def on_closing(self):
        """窗口关闭时的处理"""
        self.cleanup_powershell()
        self.root.destroy()

if __name__ == "__main__":
    # 检查依赖
    try:
        import pyautogui
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请安装: pip install pyautogui")
        exit(1)

    app = FeishuImagePaster()
    app.run()
