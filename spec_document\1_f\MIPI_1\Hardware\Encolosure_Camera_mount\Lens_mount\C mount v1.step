ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'C:/Users/<USER>/Desktop/C mount v1.step',
/* time_stamp */ '2022-06-10T19:09:40+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v19',
/* originating_system */ 'Autodesk Translation Framework v11.7.0.108',
/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#1542);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#1549,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#1541);
#13=STYLED_ITEM('',(#1558),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#876);
#15=FACE_BOUND('',#125,.T.);
#16=FACE_BOUND('',#135,.T.);
#17=FACE_BOUND('',#161,.T.);
#18=FACE_BOUND('',#162,.T.);
#19=FACE_BOUND('',#163,.T.);
#20=FACE_BOUND('',#164,.T.);
#21=FACE_BOUND('',#165,.T.);
#22=PLANE('',#918);
#23=PLANE('',#920);
#24=PLANE('',#922);
#25=PLANE('',#932);
#26=PLANE('',#937);
#27=PLANE('',#956);
#28=PLANE('',#961);
#29=PLANE('',#962);
#30=PLANE('',#963);
#31=PLANE('',#964);
#32=PLANE('',#969);
#33=PLANE('',#972);
#34=PLANE('',#975);
#35=PLANE('',#978);
#36=PLANE('',#985);
#37=PLANE('',#988);
#38=PLANE('',#991);
#39=PLANE('',#994);
#40=PLANE('',#997);
#41=CYLINDRICAL_SURFACE('',#893,1.6);
#42=CYLINDRICAL_SURFACE('',#900,1.6);
#43=CYLINDRICAL_SURFACE('',#907,1.6);
#44=CYLINDRICAL_SURFACE('',#914,1.6);
#45=CYLINDRICAL_SURFACE('',#924,15.05);
#46=CYLINDRICAL_SURFACE('',#938,12.5);
#47=CYLINDRICAL_SURFACE('',#941,12.5);
#48=CYLINDRICAL_SURFACE('',#944,12.5);
#49=CYLINDRICAL_SURFACE('',#947,12.5);
#50=CYLINDRICAL_SURFACE('',#950,2.);
#51=CYLINDRICAL_SURFACE('',#952,2.00000000000001);
#52=CYLINDRICAL_SURFACE('',#954,2.);
#53=CYLINDRICAL_SURFACE('',#965,1.);
#54=CYLINDRICAL_SURFACE('',#967,2.25);
#55=CYLINDRICAL_SURFACE('',#970,2.25);
#56=CYLINDRICAL_SURFACE('',#973,2.25);
#57=CYLINDRICAL_SURFACE('',#976,2.25);
#58=CYLINDRICAL_SURFACE('',#979,1.);
#59=CYLINDRICAL_SURFACE('',#981,1.);
#60=CYLINDRICAL_SURFACE('',#983,1.);
#61=CYLINDRICAL_SURFACE('',#986,2.);
#62=CYLINDRICAL_SURFACE('',#989,2.);
#63=CYLINDRICAL_SURFACE('',#992,2.);
#64=CYLINDRICAL_SURFACE('',#995,2.);
#65=FACE_OUTER_BOUND('',#112,.T.);
#66=FACE_OUTER_BOUND('',#113,.T.);
#67=FACE_OUTER_BOUND('',#114,.T.);
#68=FACE_OUTER_BOUND('',#115,.T.);
#69=FACE_OUTER_BOUND('',#116,.T.);
#70=FACE_OUTER_BOUND('',#117,.T.);
#71=FACE_OUTER_BOUND('',#118,.T.);
#72=FACE_OUTER_BOUND('',#119,.T.);
#73=FACE_OUTER_BOUND('',#120,.T.);
#74=FACE_OUTER_BOUND('',#121,.T.);
#75=FACE_OUTER_BOUND('',#122,.T.);
#76=FACE_OUTER_BOUND('',#123,.T.);
#77=FACE_OUTER_BOUND('',#124,.T.);
#78=FACE_OUTER_BOUND('',#126,.T.);
#79=FACE_OUTER_BOUND('',#127,.T.);
#80=FACE_OUTER_BOUND('',#128,.T.);
#81=FACE_OUTER_BOUND('',#129,.T.);
#82=FACE_OUTER_BOUND('',#130,.T.);
#83=FACE_OUTER_BOUND('',#131,.T.);
#84=FACE_OUTER_BOUND('',#132,.T.);
#85=FACE_OUTER_BOUND('',#133,.T.);
#86=FACE_OUTER_BOUND('',#134,.T.);
#87=FACE_OUTER_BOUND('',#136,.T.);
#88=FACE_OUTER_BOUND('',#137,.T.);
#89=FACE_OUTER_BOUND('',#138,.T.);
#90=FACE_OUTER_BOUND('',#139,.T.);
#91=FACE_OUTER_BOUND('',#140,.T.);
#92=FACE_OUTER_BOUND('',#141,.T.);
#93=FACE_OUTER_BOUND('',#142,.T.);
#94=FACE_OUTER_BOUND('',#143,.T.);
#95=FACE_OUTER_BOUND('',#144,.T.);
#96=FACE_OUTER_BOUND('',#145,.T.);
#97=FACE_OUTER_BOUND('',#146,.T.);
#98=FACE_OUTER_BOUND('',#147,.T.);
#99=FACE_OUTER_BOUND('',#148,.T.);
#100=FACE_OUTER_BOUND('',#149,.T.);
#101=FACE_OUTER_BOUND('',#150,.T.);
#102=FACE_OUTER_BOUND('',#151,.T.);
#103=FACE_OUTER_BOUND('',#152,.T.);
#104=FACE_OUTER_BOUND('',#153,.T.);
#105=FACE_OUTER_BOUND('',#154,.T.);
#106=FACE_OUTER_BOUND('',#155,.T.);
#107=FACE_OUTER_BOUND('',#156,.T.);
#108=FACE_OUTER_BOUND('',#157,.T.);
#109=FACE_OUTER_BOUND('',#158,.T.);
#110=FACE_OUTER_BOUND('',#159,.T.);
#111=FACE_OUTER_BOUND('',#160,.T.);
#112=EDGE_LOOP('',(#569,#570,#571,#572));
#113=EDGE_LOOP('',(#573,#574,#575,#576,#577,#578));
#114=EDGE_LOOP('',(#579,#580,#581,#582));
#115=EDGE_LOOP('',(#583,#584,#585,#586,#587,#588));
#116=EDGE_LOOP('',(#589,#590,#591,#592));
#117=EDGE_LOOP('',(#593,#594,#595,#596,#597,#598));
#118=EDGE_LOOP('',(#599,#600,#601,#602));
#119=EDGE_LOOP('',(#603,#604,#605,#606,#607,#608));
#120=EDGE_LOOP('',(#609,#610));
#121=EDGE_LOOP('',(#611,#612,#613));
#122=EDGE_LOOP('',(#614,#615,#616));
#123=EDGE_LOOP('',(#617,#618,#619,#620,#621,#622,#623,#624,#625,#626,#627,
#628));
#124=EDGE_LOOP('',(#629,#630,#631,#632,#633,#634,#635,#636));
#125=EDGE_LOOP('',(#637));
#126=EDGE_LOOP('',(#638,#639));
#127=EDGE_LOOP('',(#640,#641,#642,#643));
#128=EDGE_LOOP('',(#644,#645,#646,#647));
#129=EDGE_LOOP('',(#648,#649,#650,#651));
#130=EDGE_LOOP('',(#652,#653,#654,#655));
#131=EDGE_LOOP('',(#656,#657,#658,#659));
#132=EDGE_LOOP('',(#660,#661,#662,#663));
#133=EDGE_LOOP('',(#664,#665,#666,#667));
#134=EDGE_LOOP('',(#668,#669,#670,#671,#672,#673,#674,#675,#676,#677,#678));
#135=EDGE_LOOP('',(#679,#680,#681,#682,#683,#684,#685,#686,#687,#688,#689,
#690,#691,#692,#693,#694));
#136=EDGE_LOOP('',(#695,#696));
#137=EDGE_LOOP('',(#697,#698));
#138=EDGE_LOOP('',(#699,#700));
#139=EDGE_LOOP('',(#701,#702));
#140=EDGE_LOOP('',(#703,#704,#705,#706));
#141=EDGE_LOOP('',(#707,#708,#709,#710));
#142=EDGE_LOOP('',(#711,#712,#713,#714,#715,#716,#717,#718));
#143=EDGE_LOOP('',(#719,#720,#721,#722));
#144=EDGE_LOOP('',(#723,#724,#725,#726,#727,#728,#729,#730));
#145=EDGE_LOOP('',(#731,#732,#733,#734));
#146=EDGE_LOOP('',(#735,#736,#737,#738,#739,#740,#741,#742));
#147=EDGE_LOOP('',(#743,#744,#745,#746));
#148=EDGE_LOOP('',(#747,#748,#749,#750,#751,#752,#753,#754));
#149=EDGE_LOOP('',(#755,#756,#757,#758));
#150=EDGE_LOOP('',(#759,#760,#761,#762));
#151=EDGE_LOOP('',(#763,#764,#765,#766));
#152=EDGE_LOOP('',(#767,#768,#769,#770,#771));
#153=EDGE_LOOP('',(#772,#773,#774,#775));
#154=EDGE_LOOP('',(#776,#777,#778,#779,#780));
#155=EDGE_LOOP('',(#781,#782,#783,#784));
#156=EDGE_LOOP('',(#785,#786,#787,#788,#789,#790));
#157=EDGE_LOOP('',(#791,#792,#793,#794));
#158=EDGE_LOOP('',(#795,#796,#797,#798,#799,#800));
#159=EDGE_LOOP('',(#801,#802,#803,#804));
#160=EDGE_LOOP('',(#805,#806,#807,#808,#809,#810,#811,#812));
#161=EDGE_LOOP('',(#813));
#162=EDGE_LOOP('',(#814));
#163=EDGE_LOOP('',(#815));
#164=EDGE_LOOP('',(#816,#817,#818,#819,#820,#821,#822,#823));
#165=EDGE_LOOP('',(#824));
#166=LINE('',#1288,#233);
#167=LINE('',#1296,#234);
#168=LINE('',#1302,#235);
#169=LINE('',#1310,#236);
#170=LINE('',#1316,#237);
#171=LINE('',#1324,#238);
#172=LINE('',#1330,#239);
#173=LINE('',#1338,#240);
#174=LINE('',#1350,#241);
#175=LINE('',#1360,#242);
#176=LINE('',#1364,#243);
#177=LINE('',#1368,#244);
#178=LINE('',#1372,#245);
#179=LINE('',#1380,#246);
#180=LINE('',#1383,#247);
#181=LINE('',#1389,#248);
#182=LINE('',#1392,#249);
#183=LINE('',#1398,#250);
#184=LINE('',#1401,#251);
#185=LINE('',#1407,#252);
#186=LINE('',#1410,#253);
#187=LINE('',#1415,#254);
#188=LINE('',#1416,#255);
#189=LINE('',#1421,#256);
#190=LINE('',#1422,#257);
#191=LINE('',#1427,#258);
#192=LINE('',#1428,#259);
#193=LINE('',#1431,#260);
#194=LINE('',#1434,#261);
#195=LINE('',#1436,#262);
#196=LINE('',#1439,#263);
#197=LINE('',#1441,#264);
#198=LINE('',#1444,#265);
#199=LINE('',#1446,#266);
#200=LINE('',#1449,#267);
#201=LINE('',#1451,#268);
#202=LINE('',#1453,#269);
#203=LINE('',#1455,#270);
#204=LINE('',#1457,#271);
#205=LINE('',#1460,#272);
#206=LINE('',#1466,#273);
#207=LINE('',#1467,#274);
#208=LINE('',#1470,#275);
#209=LINE('',#1471,#276);
#210=LINE('',#1475,#277);
#211=LINE('',#1478,#278);
#212=LINE('',#1479,#279);
#213=LINE('',#1483,#280);
#214=LINE('',#1486,#281);
#215=LINE('',#1487,#282);
#216=LINE('',#1491,#283);
#217=LINE('',#1493,#284);
#218=LINE('',#1496,#285);
#219=LINE('',#1500,#286);
#220=LINE('',#1504,#287);
#221=LINE('',#1509,#288);
#222=LINE('',#1510,#289);
#223=LINE('',#1511,#290);
#224=LINE('',#1515,#291);
#225=LINE('',#1518,#292);
#226=LINE('',#1519,#293);
#227=LINE('',#1523,#294);
#228=LINE('',#1526,#295);
#229=LINE('',#1527,#296);
#230=LINE('',#1531,#297);
#231=LINE('',#1534,#298);
#232=LINE('',#1535,#299);
#233=VECTOR('',#1004,0.8);
#234=VECTOR('',#1013,1.6);
#235=VECTOR('',#1020,0.8);
#236=VECTOR('',#1029,1.6);
#237=VECTOR('',#1036,0.8);
#238=VECTOR('',#1045,1.6);
#239=VECTOR('',#1052,0.8);
#240=VECTOR('',#1061,1.6);
#241=VECTOR('',#1080,15.05);
#242=VECTOR('',#1095,10.);
#243=VECTOR('',#1098,10.);
#244=VECTOR('',#1101,10.);
#245=VECTOR('',#1104,10.);
#246=VECTOR('',#1113,10.);
#247=VECTOR('',#1116,10.);
#248=VECTOR('',#1121,10.);
#249=VECTOR('',#1124,10.);
#250=VECTOR('',#1129,10.);
#251=VECTOR('',#1132,10.);
#252=VECTOR('',#1137,10.);
#253=VECTOR('',#1140,10.);
#254=VECTOR('',#1145,10.);
#255=VECTOR('',#1146,10.);
#256=VECTOR('',#1151,10.);
#257=VECTOR('',#1152,10.);
#258=VECTOR('',#1157,10.);
#259=VECTOR('',#1158,10.);
#260=VECTOR('',#1161,10.);
#261=VECTOR('',#1164,10.);
#262=VECTOR('',#1165,10.);
#263=VECTOR('',#1168,10.);
#264=VECTOR('',#1169,10.);
#265=VECTOR('',#1172,10.);
#266=VECTOR('',#1173,10.);
#267=VECTOR('',#1176,10.);
#268=VECTOR('',#1179,10.);
#269=VECTOR('',#1182,10.);
#270=VECTOR('',#1185,10.);
#271=VECTOR('',#1188,10.);
#272=VECTOR('',#1191,1.);
#273=VECTOR('',#1198,10.);
#274=VECTOR('',#1199,10.);
#275=VECTOR('',#1202,10.);
#276=VECTOR('',#1203,10.);
#277=VECTOR('',#1208,10.);
#278=VECTOR('',#1211,10.);
#279=VECTOR('',#1212,10.);
#280=VECTOR('',#1217,10.);
#281=VECTOR('',#1220,10.);
#282=VECTOR('',#1221,10.);
#283=VECTOR('',#1226,10.);
#284=VECTOR('',#1229,10.);
#285=VECTOR('',#1232,1.);
#286=VECTOR('',#1237,1.);
#287=VECTOR('',#1242,1.);
#288=VECTOR('',#1247,10.);
#289=VECTOR('',#1248,10.);
#290=VECTOR('',#1249,10.);
#291=VECTOR('',#1254,10.);
#292=VECTOR('',#1257,10.);
#293=VECTOR('',#1258,10.);
#294=VECTOR('',#1263,10.);
#295=VECTOR('',#1266,10.);
#296=VECTOR('',#1267,10.);
#297=VECTOR('',#1272,10.);
#298=VECTOR('',#1275,10.);
#299=VECTOR('',#1276,10.);
#300=CIRCLE('',#891,1.6);
#301=CIRCLE('',#892,1.);
#302=CIRCLE('',#894,1.6);
#303=CIRCLE('',#895,1.6);
#304=CIRCLE('',#896,1.6);
#305=CIRCLE('',#898,1.6);
#306=CIRCLE('',#899,1.);
#307=CIRCLE('',#901,1.6);
#308=CIRCLE('',#902,1.6);
#309=CIRCLE('',#903,1.6);
#310=CIRCLE('',#905,1.6);
#311=CIRCLE('',#906,1.);
#312=CIRCLE('',#908,1.6);
#313=CIRCLE('',#909,1.6);
#314=CIRCLE('',#910,1.6);
#315=CIRCLE('',#912,1.6);
#316=CIRCLE('',#913,1.);
#317=CIRCLE('',#915,1.6);
#318=CIRCLE('',#916,1.6);
#319=CIRCLE('',#917,1.6);
#320=CIRCLE('',#919,15.05);
#321=CIRCLE('',#921,15.05);
#322=CIRCLE('',#923,15.05);
#323=CIRCLE('',#925,15.05);
#324=CIRCLE('',#926,15.05);
#325=CIRCLE('',#927,15.05);
#326=CIRCLE('',#928,15.05);
#327=CIRCLE('',#929,15.05);
#328=CIRCLE('',#930,15.05);
#329=CIRCLE('',#931,15.05);
#330=CIRCLE('',#933,2.);
#331=CIRCLE('',#934,2.00000000000001);
#332=CIRCLE('',#935,2.);
#333=CIRCLE('',#936,2.);
#334=CIRCLE('',#939,12.5);
#335=CIRCLE('',#940,12.5);
#336=CIRCLE('',#942,12.5);
#337=CIRCLE('',#943,12.5);
#338=CIRCLE('',#945,12.5);
#339=CIRCLE('',#946,12.5);
#340=CIRCLE('',#948,12.5);
#341=CIRCLE('',#949,12.5);
#342=CIRCLE('',#951,2.);
#343=CIRCLE('',#953,2.);
#344=CIRCLE('',#955,2.);
#345=CIRCLE('',#957,2.25);
#346=CIRCLE('',#958,2.25);
#347=CIRCLE('',#959,2.25);
#348=CIRCLE('',#960,2.25);
#349=CIRCLE('',#966,1.);
#350=CIRCLE('',#968,2.25);
#351=CIRCLE('',#971,2.25);
#352=CIRCLE('',#974,2.25);
#353=CIRCLE('',#977,2.25);
#354=CIRCLE('',#980,1.);
#355=CIRCLE('',#982,1.);
#356=CIRCLE('',#984,1.);
#357=CIRCLE('',#987,2.);
#358=CIRCLE('',#990,2.);
#359=CIRCLE('',#993,2.);
#360=CIRCLE('',#996,2.);
#361=VERTEX_POINT('',#1285);
#362=VERTEX_POINT('',#1287);
#363=VERTEX_POINT('',#1291);
#364=VERTEX_POINT('',#1292);
#365=VERTEX_POINT('',#1294);
#366=VERTEX_POINT('',#1299);
#367=VERTEX_POINT('',#1301);
#368=VERTEX_POINT('',#1305);
#369=VERTEX_POINT('',#1306);
#370=VERTEX_POINT('',#1308);
#371=VERTEX_POINT('',#1313);
#372=VERTEX_POINT('',#1315);
#373=VERTEX_POINT('',#1319);
#374=VERTEX_POINT('',#1320);
#375=VERTEX_POINT('',#1322);
#376=VERTEX_POINT('',#1327);
#377=VERTEX_POINT('',#1329);
#378=VERTEX_POINT('',#1333);
#379=VERTEX_POINT('',#1334);
#380=VERTEX_POINT('',#1336);
#381=VERTEX_POINT('',#1347);
#382=VERTEX_POINT('',#1349);
#383=VERTEX_POINT('',#1358);
#384=VERTEX_POINT('',#1359);
#385=VERTEX_POINT('',#1361);
#386=VERTEX_POINT('',#1363);
#387=VERTEX_POINT('',#1365);
#388=VERTEX_POINT('',#1367);
#389=VERTEX_POINT('',#1369);
#390=VERTEX_POINT('',#1371);
#391=VERTEX_POINT('',#1376);
#392=VERTEX_POINT('',#1377);
#393=VERTEX_POINT('',#1379);
#394=VERTEX_POINT('',#1381);
#395=VERTEX_POINT('',#1385);
#396=VERTEX_POINT('',#1386);
#397=VERTEX_POINT('',#1388);
#398=VERTEX_POINT('',#1390);
#399=VERTEX_POINT('',#1394);
#400=VERTEX_POINT('',#1395);
#401=VERTEX_POINT('',#1397);
#402=VERTEX_POINT('',#1399);
#403=VERTEX_POINT('',#1403);
#404=VERTEX_POINT('',#1404);
#405=VERTEX_POINT('',#1406);
#406=VERTEX_POINT('',#1408);
#407=VERTEX_POINT('',#1412);
#408=VERTEX_POINT('',#1413);
#409=VERTEX_POINT('',#1418);
#410=VERTEX_POINT('',#1419);
#411=VERTEX_POINT('',#1424);
#412=VERTEX_POINT('',#1425);
#413=VERTEX_POINT('',#1430);
#414=VERTEX_POINT('',#1432);
#415=VERTEX_POINT('',#1435);
#416=VERTEX_POINT('',#1437);
#417=VERTEX_POINT('',#1440);
#418=VERTEX_POINT('',#1442);
#419=VERTEX_POINT('',#1445);
#420=VERTEX_POINT('',#1447);
#421=VERTEX_POINT('',#1459);
#422=VERTEX_POINT('',#1463);
#423=VERTEX_POINT('',#1464);
#424=VERTEX_POINT('',#1469);
#425=VERTEX_POINT('',#1473);
#426=VERTEX_POINT('',#1477);
#427=VERTEX_POINT('',#1481);
#428=VERTEX_POINT('',#1485);
#429=VERTEX_POINT('',#1489);
#430=VERTEX_POINT('',#1495);
#431=VERTEX_POINT('',#1499);
#432=VERTEX_POINT('',#1503);
#433=VERTEX_POINT('',#1507);
#434=VERTEX_POINT('',#1508);
#435=VERTEX_POINT('',#1513);
#436=VERTEX_POINT('',#1517);
#437=VERTEX_POINT('',#1521);
#438=VERTEX_POINT('',#1525);
#439=VERTEX_POINT('',#1529);
#440=VERTEX_POINT('',#1533);
#441=EDGE_CURVE('',#361,#361,#300,.T.);
#442=EDGE_CURVE('',#361,#362,#166,.T.);
#443=EDGE_CURVE('',#362,#362,#301,.T.);
#444=EDGE_CURVE('',#363,#364,#302,.T.);
#445=EDGE_CURVE('',#365,#364,#303,.T.);
#446=EDGE_CURVE('',#365,#361,#167,.T.);
#447=EDGE_CURVE('',#363,#365,#304,.T.);
#448=EDGE_CURVE('',#366,#366,#305,.T.);
#449=EDGE_CURVE('',#366,#367,#168,.T.);
#450=EDGE_CURVE('',#367,#367,#306,.T.);
#451=EDGE_CURVE('',#368,#369,#307,.T.);
#452=EDGE_CURVE('',#368,#370,#308,.T.);
#453=EDGE_CURVE('',#370,#366,#169,.T.);
#454=EDGE_CURVE('',#370,#369,#309,.T.);
#455=EDGE_CURVE('',#371,#371,#310,.T.);
#456=EDGE_CURVE('',#371,#372,#170,.T.);
#457=EDGE_CURVE('',#372,#372,#311,.T.);
#458=EDGE_CURVE('',#373,#374,#312,.T.);
#459=EDGE_CURVE('',#375,#374,#313,.T.);
#460=EDGE_CURVE('',#375,#371,#171,.T.);
#461=EDGE_CURVE('',#373,#375,#314,.T.);
#462=EDGE_CURVE('',#376,#376,#315,.T.);
#463=EDGE_CURVE('',#376,#377,#172,.T.);
#464=EDGE_CURVE('',#377,#377,#316,.T.);
#465=EDGE_CURVE('',#378,#379,#317,.T.);
#466=EDGE_CURVE('',#378,#380,#318,.T.);
#467=EDGE_CURVE('',#380,#376,#173,.T.);
#468=EDGE_CURVE('',#380,#379,#319,.T.);
#469=EDGE_CURVE('',#363,#364,#320,.T.);
#470=EDGE_CURVE('',#368,#369,#321,.T.);
#471=EDGE_CURVE('',#378,#379,#322,.T.);
#472=EDGE_CURVE('',#381,#381,#323,.T.);
#473=EDGE_CURVE('',#381,#382,#174,.T.);
#474=EDGE_CURVE('',#364,#382,#324,.T.);
#475=EDGE_CURVE('',#369,#363,#325,.T.);
#476=EDGE_CURVE('',#379,#368,#326,.T.);
#477=EDGE_CURVE('',#374,#378,#327,.T.);
#478=EDGE_CURVE('',#373,#374,#328,.T.);
#479=EDGE_CURVE('',#382,#373,#329,.T.);
#480=EDGE_CURVE('',#383,#384,#175,.T.);
#481=EDGE_CURVE('',#384,#385,#330,.T.);
#482=EDGE_CURVE('',#385,#386,#176,.T.);
#483=EDGE_CURVE('',#386,#387,#331,.T.);
#484=EDGE_CURVE('',#387,#388,#177,.T.);
#485=EDGE_CURVE('',#388,#389,#332,.T.);
#486=EDGE_CURVE('',#389,#390,#178,.T.);
#487=EDGE_CURVE('',#390,#383,#333,.T.);
#488=EDGE_CURVE('',#391,#392,#334,.T.);
#489=EDGE_CURVE('',#391,#393,#179,.T.);
#490=EDGE_CURVE('',#394,#393,#335,.T.);
#491=EDGE_CURVE('',#392,#394,#180,.T.);
#492=EDGE_CURVE('',#395,#396,#336,.T.);
#493=EDGE_CURVE('',#395,#397,#181,.T.);
#494=EDGE_CURVE('',#398,#397,#337,.T.);
#495=EDGE_CURVE('',#396,#398,#182,.T.);
#496=EDGE_CURVE('',#399,#400,#338,.T.);
#497=EDGE_CURVE('',#399,#401,#183,.T.);
#498=EDGE_CURVE('',#402,#401,#339,.T.);
#499=EDGE_CURVE('',#400,#402,#184,.T.);
#500=EDGE_CURVE('',#403,#404,#340,.T.);
#501=EDGE_CURVE('',#403,#405,#185,.T.);
#502=EDGE_CURVE('',#406,#405,#341,.T.);
#503=EDGE_CURVE('',#404,#406,#186,.T.);
#504=EDGE_CURVE('',#407,#408,#342,.T.);
#505=EDGE_CURVE('',#408,#389,#187,.T.);
#506=EDGE_CURVE('',#407,#388,#188,.T.);
#507=EDGE_CURVE('',#409,#410,#343,.T.);
#508=EDGE_CURVE('',#410,#387,#189,.T.);
#509=EDGE_CURVE('',#409,#386,#190,.T.);
#510=EDGE_CURVE('',#411,#412,#344,.T.);
#511=EDGE_CURVE('',#412,#385,#191,.T.);
#512=EDGE_CURVE('',#411,#384,#192,.T.);
#513=EDGE_CURVE('',#393,#413,#193,.T.);
#514=EDGE_CURVE('',#413,#414,#345,.T.);
#515=EDGE_CURVE('',#414,#406,#194,.T.);
#516=EDGE_CURVE('',#405,#415,#195,.T.);
#517=EDGE_CURVE('',#415,#416,#346,.T.);
#518=EDGE_CURVE('',#416,#402,#196,.T.);
#519=EDGE_CURVE('',#401,#417,#197,.T.);
#520=EDGE_CURVE('',#417,#418,#347,.T.);
#521=EDGE_CURVE('',#418,#398,#198,.T.);
#522=EDGE_CURVE('',#397,#419,#199,.T.);
#523=EDGE_CURVE('',#419,#420,#348,.T.);
#524=EDGE_CURVE('',#420,#394,#200,.T.);
#525=EDGE_CURVE('',#404,#403,#201,.T.);
#526=EDGE_CURVE('',#392,#391,#202,.T.);
#527=EDGE_CURVE('',#396,#395,#203,.T.);
#528=EDGE_CURVE('',#400,#399,#204,.T.);
#529=EDGE_CURVE('',#367,#421,#205,.T.);
#530=EDGE_CURVE('',#421,#421,#349,.T.);
#531=EDGE_CURVE('',#422,#423,#350,.T.);
#532=EDGE_CURVE('',#423,#418,#206,.T.);
#533=EDGE_CURVE('',#422,#417,#207,.T.);
#534=EDGE_CURVE('',#424,#422,#208,.T.);
#535=EDGE_CURVE('',#424,#416,#209,.T.);
#536=EDGE_CURVE('',#425,#424,#351,.T.);
#537=EDGE_CURVE('',#425,#415,#210,.T.);
#538=EDGE_CURVE('',#426,#425,#211,.T.);
#539=EDGE_CURVE('',#426,#414,#212,.T.);
#540=EDGE_CURVE('',#427,#426,#352,.T.);
#541=EDGE_CURVE('',#427,#413,#213,.T.);
#542=EDGE_CURVE('',#428,#427,#214,.T.);
#543=EDGE_CURVE('',#428,#420,#215,.T.);
#544=EDGE_CURVE('',#429,#428,#353,.T.);
#545=EDGE_CURVE('',#429,#419,#216,.T.);
#546=EDGE_CURVE('',#423,#429,#217,.T.);
#547=EDGE_CURVE('',#362,#430,#218,.T.);
#548=EDGE_CURVE('',#430,#430,#354,.T.);
#549=EDGE_CURVE('',#372,#431,#219,.T.);
#550=EDGE_CURVE('',#431,#431,#355,.T.);
#551=EDGE_CURVE('',#377,#432,#220,.T.);
#552=EDGE_CURVE('',#432,#432,#356,.T.);
#553=EDGE_CURVE('',#433,#434,#221,.T.);
#554=EDGE_CURVE('',#434,#411,#222,.T.);
#555=EDGE_CURVE('',#433,#383,#223,.T.);
#556=EDGE_CURVE('',#435,#433,#357,.T.);
#557=EDGE_CURVE('',#435,#390,#224,.T.);
#558=EDGE_CURVE('',#436,#435,#225,.T.);
#559=EDGE_CURVE('',#436,#408,#226,.T.);
#560=EDGE_CURVE('',#437,#436,#358,.T.);
#561=EDGE_CURVE('',#437,#407,#227,.T.);
#562=EDGE_CURVE('',#438,#437,#228,.T.);
#563=EDGE_CURVE('',#438,#410,#229,.T.);
#564=EDGE_CURVE('',#439,#438,#359,.T.);
#565=EDGE_CURVE('',#439,#409,#230,.T.);
#566=EDGE_CURVE('',#440,#439,#231,.T.);
#567=EDGE_CURVE('',#440,#412,#232,.T.);
#568=EDGE_CURVE('',#434,#440,#360,.T.);
#569=ORIENTED_EDGE('',*,*,#441,.F.);
#570=ORIENTED_EDGE('',*,*,#442,.T.);
#571=ORIENTED_EDGE('',*,*,#443,.F.);
#572=ORIENTED_EDGE('',*,*,#442,.F.);
#573=ORIENTED_EDGE('',*,*,#444,.T.);
#574=ORIENTED_EDGE('',*,*,#445,.F.);
#575=ORIENTED_EDGE('',*,*,#446,.T.);
#576=ORIENTED_EDGE('',*,*,#441,.T.);
#577=ORIENTED_EDGE('',*,*,#446,.F.);
#578=ORIENTED_EDGE('',*,*,#447,.F.);
#579=ORIENTED_EDGE('',*,*,#448,.F.);
#580=ORIENTED_EDGE('',*,*,#449,.T.);
#581=ORIENTED_EDGE('',*,*,#450,.F.);
#582=ORIENTED_EDGE('',*,*,#449,.F.);
#583=ORIENTED_EDGE('',*,*,#451,.F.);
#584=ORIENTED_EDGE('',*,*,#452,.T.);
#585=ORIENTED_EDGE('',*,*,#453,.T.);
#586=ORIENTED_EDGE('',*,*,#448,.T.);
#587=ORIENTED_EDGE('',*,*,#453,.F.);
#588=ORIENTED_EDGE('',*,*,#454,.T.);
#589=ORIENTED_EDGE('',*,*,#455,.F.);
#590=ORIENTED_EDGE('',*,*,#456,.T.);
#591=ORIENTED_EDGE('',*,*,#457,.F.);
#592=ORIENTED_EDGE('',*,*,#456,.F.);
#593=ORIENTED_EDGE('',*,*,#458,.T.);
#594=ORIENTED_EDGE('',*,*,#459,.F.);
#595=ORIENTED_EDGE('',*,*,#460,.T.);
#596=ORIENTED_EDGE('',*,*,#455,.T.);
#597=ORIENTED_EDGE('',*,*,#460,.F.);
#598=ORIENTED_EDGE('',*,*,#461,.F.);
#599=ORIENTED_EDGE('',*,*,#462,.F.);
#600=ORIENTED_EDGE('',*,*,#463,.T.);
#601=ORIENTED_EDGE('',*,*,#464,.F.);
#602=ORIENTED_EDGE('',*,*,#463,.F.);
#603=ORIENTED_EDGE('',*,*,#465,.F.);
#604=ORIENTED_EDGE('',*,*,#466,.T.);
#605=ORIENTED_EDGE('',*,*,#467,.T.);
#606=ORIENTED_EDGE('',*,*,#462,.T.);
#607=ORIENTED_EDGE('',*,*,#467,.F.);
#608=ORIENTED_EDGE('',*,*,#468,.T.);
#609=ORIENTED_EDGE('',*,*,#469,.T.);
#610=ORIENTED_EDGE('',*,*,#444,.F.);
#611=ORIENTED_EDGE('',*,*,#470,.T.);
#612=ORIENTED_EDGE('',*,*,#454,.F.);
#613=ORIENTED_EDGE('',*,*,#452,.F.);
#614=ORIENTED_EDGE('',*,*,#471,.T.);
#615=ORIENTED_EDGE('',*,*,#468,.F.);
#616=ORIENTED_EDGE('',*,*,#466,.F.);
#617=ORIENTED_EDGE('',*,*,#472,.F.);
#618=ORIENTED_EDGE('',*,*,#473,.T.);
#619=ORIENTED_EDGE('',*,*,#474,.F.);
#620=ORIENTED_EDGE('',*,*,#469,.F.);
#621=ORIENTED_EDGE('',*,*,#475,.F.);
#622=ORIENTED_EDGE('',*,*,#470,.F.);
#623=ORIENTED_EDGE('',*,*,#476,.F.);
#624=ORIENTED_EDGE('',*,*,#471,.F.);
#625=ORIENTED_EDGE('',*,*,#477,.F.);
#626=ORIENTED_EDGE('',*,*,#478,.F.);
#627=ORIENTED_EDGE('',*,*,#479,.F.);
#628=ORIENTED_EDGE('',*,*,#473,.F.);
#629=ORIENTED_EDGE('',*,*,#480,.T.);
#630=ORIENTED_EDGE('',*,*,#481,.T.);
#631=ORIENTED_EDGE('',*,*,#482,.T.);
#632=ORIENTED_EDGE('',*,*,#483,.T.);
#633=ORIENTED_EDGE('',*,*,#484,.T.);
#634=ORIENTED_EDGE('',*,*,#485,.T.);
#635=ORIENTED_EDGE('',*,*,#486,.T.);
#636=ORIENTED_EDGE('',*,*,#487,.T.);
#637=ORIENTED_EDGE('',*,*,#472,.T.);
#638=ORIENTED_EDGE('',*,*,#478,.T.);
#639=ORIENTED_EDGE('',*,*,#458,.F.);
#640=ORIENTED_EDGE('',*,*,#488,.F.);
#641=ORIENTED_EDGE('',*,*,#489,.T.);
#642=ORIENTED_EDGE('',*,*,#490,.F.);
#643=ORIENTED_EDGE('',*,*,#491,.F.);
#644=ORIENTED_EDGE('',*,*,#492,.F.);
#645=ORIENTED_EDGE('',*,*,#493,.T.);
#646=ORIENTED_EDGE('',*,*,#494,.F.);
#647=ORIENTED_EDGE('',*,*,#495,.F.);
#648=ORIENTED_EDGE('',*,*,#496,.F.);
#649=ORIENTED_EDGE('',*,*,#497,.T.);
#650=ORIENTED_EDGE('',*,*,#498,.F.);
#651=ORIENTED_EDGE('',*,*,#499,.F.);
#652=ORIENTED_EDGE('',*,*,#500,.F.);
#653=ORIENTED_EDGE('',*,*,#501,.T.);
#654=ORIENTED_EDGE('',*,*,#502,.F.);
#655=ORIENTED_EDGE('',*,*,#503,.F.);
#656=ORIENTED_EDGE('',*,*,#504,.T.);
#657=ORIENTED_EDGE('',*,*,#505,.T.);
#658=ORIENTED_EDGE('',*,*,#485,.F.);
#659=ORIENTED_EDGE('',*,*,#506,.F.);
#660=ORIENTED_EDGE('',*,*,#507,.T.);
#661=ORIENTED_EDGE('',*,*,#508,.T.);
#662=ORIENTED_EDGE('',*,*,#483,.F.);
#663=ORIENTED_EDGE('',*,*,#509,.F.);
#664=ORIENTED_EDGE('',*,*,#510,.T.);
#665=ORIENTED_EDGE('',*,*,#511,.T.);
#666=ORIENTED_EDGE('',*,*,#481,.F.);
#667=ORIENTED_EDGE('',*,*,#512,.F.);
#668=ORIENTED_EDGE('',*,*,#447,.T.);
#669=ORIENTED_EDGE('',*,*,#445,.T.);
#670=ORIENTED_EDGE('',*,*,#474,.T.);
#671=ORIENTED_EDGE('',*,*,#479,.T.);
#672=ORIENTED_EDGE('',*,*,#461,.T.);
#673=ORIENTED_EDGE('',*,*,#459,.T.);
#674=ORIENTED_EDGE('',*,*,#477,.T.);
#675=ORIENTED_EDGE('',*,*,#465,.T.);
#676=ORIENTED_EDGE('',*,*,#476,.T.);
#677=ORIENTED_EDGE('',*,*,#451,.T.);
#678=ORIENTED_EDGE('',*,*,#475,.T.);
#679=ORIENTED_EDGE('',*,*,#513,.T.);
#680=ORIENTED_EDGE('',*,*,#514,.T.);
#681=ORIENTED_EDGE('',*,*,#515,.T.);
#682=ORIENTED_EDGE('',*,*,#502,.T.);
#683=ORIENTED_EDGE('',*,*,#516,.T.);
#684=ORIENTED_EDGE('',*,*,#517,.T.);
#685=ORIENTED_EDGE('',*,*,#518,.T.);
#686=ORIENTED_EDGE('',*,*,#498,.T.);
#687=ORIENTED_EDGE('',*,*,#519,.T.);
#688=ORIENTED_EDGE('',*,*,#520,.T.);
#689=ORIENTED_EDGE('',*,*,#521,.T.);
#690=ORIENTED_EDGE('',*,*,#494,.T.);
#691=ORIENTED_EDGE('',*,*,#522,.T.);
#692=ORIENTED_EDGE('',*,*,#523,.T.);
#693=ORIENTED_EDGE('',*,*,#524,.T.);
#694=ORIENTED_EDGE('',*,*,#490,.T.);
#695=ORIENTED_EDGE('',*,*,#500,.T.);
#696=ORIENTED_EDGE('',*,*,#525,.T.);
#697=ORIENTED_EDGE('',*,*,#488,.T.);
#698=ORIENTED_EDGE('',*,*,#526,.T.);
#699=ORIENTED_EDGE('',*,*,#492,.T.);
#700=ORIENTED_EDGE('',*,*,#527,.T.);
#701=ORIENTED_EDGE('',*,*,#496,.T.);
#702=ORIENTED_EDGE('',*,*,#528,.T.);
#703=ORIENTED_EDGE('',*,*,#450,.T.);
#704=ORIENTED_EDGE('',*,*,#529,.T.);
#705=ORIENTED_EDGE('',*,*,#530,.T.);
#706=ORIENTED_EDGE('',*,*,#529,.F.);
#707=ORIENTED_EDGE('',*,*,#531,.T.);
#708=ORIENTED_EDGE('',*,*,#532,.T.);
#709=ORIENTED_EDGE('',*,*,#520,.F.);
#710=ORIENTED_EDGE('',*,*,#533,.F.);
#711=ORIENTED_EDGE('',*,*,#534,.T.);
#712=ORIENTED_EDGE('',*,*,#533,.T.);
#713=ORIENTED_EDGE('',*,*,#519,.F.);
#714=ORIENTED_EDGE('',*,*,#497,.F.);
#715=ORIENTED_EDGE('',*,*,#528,.F.);
#716=ORIENTED_EDGE('',*,*,#499,.T.);
#717=ORIENTED_EDGE('',*,*,#518,.F.);
#718=ORIENTED_EDGE('',*,*,#535,.F.);
#719=ORIENTED_EDGE('',*,*,#536,.T.);
#720=ORIENTED_EDGE('',*,*,#535,.T.);
#721=ORIENTED_EDGE('',*,*,#517,.F.);
#722=ORIENTED_EDGE('',*,*,#537,.F.);
#723=ORIENTED_EDGE('',*,*,#538,.T.);
#724=ORIENTED_EDGE('',*,*,#537,.T.);
#725=ORIENTED_EDGE('',*,*,#516,.F.);
#726=ORIENTED_EDGE('',*,*,#501,.F.);
#727=ORIENTED_EDGE('',*,*,#525,.F.);
#728=ORIENTED_EDGE('',*,*,#503,.T.);
#729=ORIENTED_EDGE('',*,*,#515,.F.);
#730=ORIENTED_EDGE('',*,*,#539,.F.);
#731=ORIENTED_EDGE('',*,*,#540,.T.);
#732=ORIENTED_EDGE('',*,*,#539,.T.);
#733=ORIENTED_EDGE('',*,*,#514,.F.);
#734=ORIENTED_EDGE('',*,*,#541,.F.);
#735=ORIENTED_EDGE('',*,*,#542,.T.);
#736=ORIENTED_EDGE('',*,*,#541,.T.);
#737=ORIENTED_EDGE('',*,*,#513,.F.);
#738=ORIENTED_EDGE('',*,*,#489,.F.);
#739=ORIENTED_EDGE('',*,*,#526,.F.);
#740=ORIENTED_EDGE('',*,*,#491,.T.);
#741=ORIENTED_EDGE('',*,*,#524,.F.);
#742=ORIENTED_EDGE('',*,*,#543,.F.);
#743=ORIENTED_EDGE('',*,*,#544,.T.);
#744=ORIENTED_EDGE('',*,*,#543,.T.);
#745=ORIENTED_EDGE('',*,*,#523,.F.);
#746=ORIENTED_EDGE('',*,*,#545,.F.);
#747=ORIENTED_EDGE('',*,*,#546,.T.);
#748=ORIENTED_EDGE('',*,*,#545,.T.);
#749=ORIENTED_EDGE('',*,*,#522,.F.);
#750=ORIENTED_EDGE('',*,*,#493,.F.);
#751=ORIENTED_EDGE('',*,*,#527,.F.);
#752=ORIENTED_EDGE('',*,*,#495,.T.);
#753=ORIENTED_EDGE('',*,*,#521,.F.);
#754=ORIENTED_EDGE('',*,*,#532,.F.);
#755=ORIENTED_EDGE('',*,*,#443,.T.);
#756=ORIENTED_EDGE('',*,*,#547,.T.);
#757=ORIENTED_EDGE('',*,*,#548,.T.);
#758=ORIENTED_EDGE('',*,*,#547,.F.);
#759=ORIENTED_EDGE('',*,*,#457,.T.);
#760=ORIENTED_EDGE('',*,*,#549,.T.);
#761=ORIENTED_EDGE('',*,*,#550,.T.);
#762=ORIENTED_EDGE('',*,*,#549,.F.);
#763=ORIENTED_EDGE('',*,*,#464,.T.);
#764=ORIENTED_EDGE('',*,*,#551,.T.);
#765=ORIENTED_EDGE('',*,*,#552,.T.);
#766=ORIENTED_EDGE('',*,*,#551,.F.);
#767=ORIENTED_EDGE('',*,*,#553,.T.);
#768=ORIENTED_EDGE('',*,*,#554,.T.);
#769=ORIENTED_EDGE('',*,*,#512,.T.);
#770=ORIENTED_EDGE('',*,*,#480,.F.);
#771=ORIENTED_EDGE('',*,*,#555,.F.);
#772=ORIENTED_EDGE('',*,*,#556,.T.);
#773=ORIENTED_EDGE('',*,*,#555,.T.);
#774=ORIENTED_EDGE('',*,*,#487,.F.);
#775=ORIENTED_EDGE('',*,*,#557,.F.);
#776=ORIENTED_EDGE('',*,*,#558,.T.);
#777=ORIENTED_EDGE('',*,*,#557,.T.);
#778=ORIENTED_EDGE('',*,*,#486,.F.);
#779=ORIENTED_EDGE('',*,*,#505,.F.);
#780=ORIENTED_EDGE('',*,*,#559,.F.);
#781=ORIENTED_EDGE('',*,*,#560,.T.);
#782=ORIENTED_EDGE('',*,*,#559,.T.);
#783=ORIENTED_EDGE('',*,*,#504,.F.);
#784=ORIENTED_EDGE('',*,*,#561,.F.);
#785=ORIENTED_EDGE('',*,*,#562,.T.);
#786=ORIENTED_EDGE('',*,*,#561,.T.);
#787=ORIENTED_EDGE('',*,*,#506,.T.);
#788=ORIENTED_EDGE('',*,*,#484,.F.);
#789=ORIENTED_EDGE('',*,*,#508,.F.);
#790=ORIENTED_EDGE('',*,*,#563,.F.);
#791=ORIENTED_EDGE('',*,*,#564,.T.);
#792=ORIENTED_EDGE('',*,*,#563,.T.);
#793=ORIENTED_EDGE('',*,*,#507,.F.);
#794=ORIENTED_EDGE('',*,*,#565,.F.);
#795=ORIENTED_EDGE('',*,*,#566,.T.);
#796=ORIENTED_EDGE('',*,*,#565,.T.);
#797=ORIENTED_EDGE('',*,*,#509,.T.);
#798=ORIENTED_EDGE('',*,*,#482,.F.);
#799=ORIENTED_EDGE('',*,*,#511,.F.);
#800=ORIENTED_EDGE('',*,*,#567,.F.);
#801=ORIENTED_EDGE('',*,*,#568,.T.);
#802=ORIENTED_EDGE('',*,*,#567,.T.);
#803=ORIENTED_EDGE('',*,*,#510,.F.);
#804=ORIENTED_EDGE('',*,*,#554,.F.);
#805=ORIENTED_EDGE('',*,*,#568,.F.);
#806=ORIENTED_EDGE('',*,*,#553,.F.);
#807=ORIENTED_EDGE('',*,*,#556,.F.);
#808=ORIENTED_EDGE('',*,*,#558,.F.);
#809=ORIENTED_EDGE('',*,*,#560,.F.);
#810=ORIENTED_EDGE('',*,*,#562,.F.);
#811=ORIENTED_EDGE('',*,*,#564,.F.);
#812=ORIENTED_EDGE('',*,*,#566,.F.);
#813=ORIENTED_EDGE('',*,*,#552,.F.);
#814=ORIENTED_EDGE('',*,*,#550,.F.);
#815=ORIENTED_EDGE('',*,*,#548,.F.);
#816=ORIENTED_EDGE('',*,*,#546,.F.);
#817=ORIENTED_EDGE('',*,*,#531,.F.);
#818=ORIENTED_EDGE('',*,*,#534,.F.);
#819=ORIENTED_EDGE('',*,*,#536,.F.);
#820=ORIENTED_EDGE('',*,*,#538,.F.);
#821=ORIENTED_EDGE('',*,*,#540,.F.);
#822=ORIENTED_EDGE('',*,*,#542,.F.);
#823=ORIENTED_EDGE('',*,*,#544,.F.);
#824=ORIENTED_EDGE('',*,*,#530,.F.);
#825=CONICAL_SURFACE('',#890,0.8,1.02974425867665);
#826=CONICAL_SURFACE('',#897,0.8,1.02974425867665);
#827=CONICAL_SURFACE('',#904,0.8,1.02974425867665);
#828=CONICAL_SURFACE('',#911,0.8,1.02974425867665);
#829=ADVANCED_FACE('',(#65),#825,.F.);
#830=ADVANCED_FACE('',(#66),#41,.F.);
#831=ADVANCED_FACE('',(#67),#826,.F.);
#832=ADVANCED_FACE('',(#68),#42,.F.);
#833=ADVANCED_FACE('',(#69),#827,.F.);
#834=ADVANCED_FACE('',(#70),#43,.F.);
#835=ADVANCED_FACE('',(#71),#828,.F.);
#836=ADVANCED_FACE('',(#72),#44,.F.);
#837=ADVANCED_FACE('',(#73),#22,.F.);
#838=ADVANCED_FACE('',(#74),#23,.F.);
#839=ADVANCED_FACE('',(#75),#24,.F.);
#840=ADVANCED_FACE('',(#76),#45,.F.);
#841=ADVANCED_FACE('',(#77,#15),#25,.T.);
#842=ADVANCED_FACE('',(#78),#26,.F.);
#843=ADVANCED_FACE('',(#79),#46,.F.);
#844=ADVANCED_FACE('',(#80),#47,.F.);
#845=ADVANCED_FACE('',(#81),#48,.F.);
#846=ADVANCED_FACE('',(#82),#49,.F.);
#847=ADVANCED_FACE('',(#83),#50,.T.);
#848=ADVANCED_FACE('',(#84),#51,.T.);
#849=ADVANCED_FACE('',(#85),#52,.T.);
#850=ADVANCED_FACE('',(#86,#16),#27,.T.);
#851=ADVANCED_FACE('',(#87),#28,.T.);
#852=ADVANCED_FACE('',(#88),#29,.T.);
#853=ADVANCED_FACE('',(#89),#30,.T.);
#854=ADVANCED_FACE('',(#90),#31,.T.);
#855=ADVANCED_FACE('',(#91),#53,.F.);
#856=ADVANCED_FACE('',(#92),#54,.T.);
#857=ADVANCED_FACE('',(#93),#32,.T.);
#858=ADVANCED_FACE('',(#94),#55,.T.);
#859=ADVANCED_FACE('',(#95),#33,.T.);
#860=ADVANCED_FACE('',(#96),#56,.T.);
#861=ADVANCED_FACE('',(#97),#34,.T.);
#862=ADVANCED_FACE('',(#98),#57,.T.);
#863=ADVANCED_FACE('',(#99),#35,.T.);
#864=ADVANCED_FACE('',(#100),#58,.F.);
#865=ADVANCED_FACE('',(#101),#59,.F.);
#866=ADVANCED_FACE('',(#102),#60,.F.);
#867=ADVANCED_FACE('',(#103),#36,.T.);
#868=ADVANCED_FACE('',(#104),#61,.T.);
#869=ADVANCED_FACE('',(#105),#37,.T.);
#870=ADVANCED_FACE('',(#106),#62,.T.);
#871=ADVANCED_FACE('',(#107),#38,.T.);
#872=ADVANCED_FACE('',(#108),#63,.T.);
#873=ADVANCED_FACE('',(#109),#39,.T.);
#874=ADVANCED_FACE('',(#110),#64,.T.);
#875=ADVANCED_FACE('',(#111,#17,#18,#19,#20,#21),#40,.F.);
#876=CLOSED_SHELL('',(#829,#830,#831,#832,#833,#834,#835,#836,#837,#838,
#839,#840,#841,#842,#843,#844,#845,#846,#847,#848,#849,#850,#851,#852,#853,
#854,#855,#856,#857,#858,#859,#860,#861,#862,#863,#864,#865,#866,#867,#868,
#869,#870,#871,#872,#873,#874,#875));
#877=DERIVED_UNIT_ELEMENT(#879,1.);
#878=DERIVED_UNIT_ELEMENT(#1544,-3.);
#879=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#880=DERIVED_UNIT((#877,#878));
#881=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#880);
#882=PROPERTY_DEFINITION_REPRESENTATION(#887,#884);
#883=PROPERTY_DEFINITION_REPRESENTATION(#888,#885);
#884=REPRESENTATION('material name',(#886),#1541);
#885=REPRESENTATION('density',(#881),#1541);
#886=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#887=PROPERTY_DEFINITION('material property','material name',#1551);
#888=PROPERTY_DEFINITION('material property','density of part',#1551);
#889=AXIS2_PLACEMENT_3D('placement',#1283,#998,#999);
#890=AXIS2_PLACEMENT_3D('',#1284,#1000,#1001);
#891=AXIS2_PLACEMENT_3D('',#1286,#1002,#1003);
#892=AXIS2_PLACEMENT_3D('',#1289,#1005,#1006);
#893=AXIS2_PLACEMENT_3D('',#1290,#1007,#1008);
#894=AXIS2_PLACEMENT_3D('',#1293,#1009,#1010);
#895=AXIS2_PLACEMENT_3D('',#1295,#1011,#1012);
#896=AXIS2_PLACEMENT_3D('',#1297,#1014,#1015);
#897=AXIS2_PLACEMENT_3D('',#1298,#1016,#1017);
#898=AXIS2_PLACEMENT_3D('',#1300,#1018,#1019);
#899=AXIS2_PLACEMENT_3D('',#1303,#1021,#1022);
#900=AXIS2_PLACEMENT_3D('',#1304,#1023,#1024);
#901=AXIS2_PLACEMENT_3D('',#1307,#1025,#1026);
#902=AXIS2_PLACEMENT_3D('',#1309,#1027,#1028);
#903=AXIS2_PLACEMENT_3D('',#1311,#1030,#1031);
#904=AXIS2_PLACEMENT_3D('',#1312,#1032,#1033);
#905=AXIS2_PLACEMENT_3D('',#1314,#1034,#1035);
#906=AXIS2_PLACEMENT_3D('',#1317,#1037,#1038);
#907=AXIS2_PLACEMENT_3D('',#1318,#1039,#1040);
#908=AXIS2_PLACEMENT_3D('',#1321,#1041,#1042);
#909=AXIS2_PLACEMENT_3D('',#1323,#1043,#1044);
#910=AXIS2_PLACEMENT_3D('',#1325,#1046,#1047);
#911=AXIS2_PLACEMENT_3D('',#1326,#1048,#1049);
#912=AXIS2_PLACEMENT_3D('',#1328,#1050,#1051);
#913=AXIS2_PLACEMENT_3D('',#1331,#1053,#1054);
#914=AXIS2_PLACEMENT_3D('',#1332,#1055,#1056);
#915=AXIS2_PLACEMENT_3D('',#1335,#1057,#1058);
#916=AXIS2_PLACEMENT_3D('',#1337,#1059,#1060);
#917=AXIS2_PLACEMENT_3D('',#1339,#1062,#1063);
#918=AXIS2_PLACEMENT_3D('',#1340,#1064,#1065);
#919=AXIS2_PLACEMENT_3D('',#1341,#1066,#1067);
#920=AXIS2_PLACEMENT_3D('',#1342,#1068,#1069);
#921=AXIS2_PLACEMENT_3D('',#1343,#1070,#1071);
#922=AXIS2_PLACEMENT_3D('',#1344,#1072,#1073);
#923=AXIS2_PLACEMENT_3D('',#1345,#1074,#1075);
#924=AXIS2_PLACEMENT_3D('',#1346,#1076,#1077);
#925=AXIS2_PLACEMENT_3D('',#1348,#1078,#1079);
#926=AXIS2_PLACEMENT_3D('',#1351,#1081,#1082);
#927=AXIS2_PLACEMENT_3D('',#1352,#1083,#1084);
#928=AXIS2_PLACEMENT_3D('',#1353,#1085,#1086);
#929=AXIS2_PLACEMENT_3D('',#1354,#1087,#1088);
#930=AXIS2_PLACEMENT_3D('',#1355,#1089,#1090);
#931=AXIS2_PLACEMENT_3D('',#1356,#1091,#1092);
#932=AXIS2_PLACEMENT_3D('',#1357,#1093,#1094);
#933=AXIS2_PLACEMENT_3D('',#1362,#1096,#1097);
#934=AXIS2_PLACEMENT_3D('',#1366,#1099,#1100);
#935=AXIS2_PLACEMENT_3D('',#1370,#1102,#1103);
#936=AXIS2_PLACEMENT_3D('',#1373,#1105,#1106);
#937=AXIS2_PLACEMENT_3D('',#1374,#1107,#1108);
#938=AXIS2_PLACEMENT_3D('',#1375,#1109,#1110);
#939=AXIS2_PLACEMENT_3D('',#1378,#1111,#1112);
#940=AXIS2_PLACEMENT_3D('',#1382,#1114,#1115);
#941=AXIS2_PLACEMENT_3D('',#1384,#1117,#1118);
#942=AXIS2_PLACEMENT_3D('',#1387,#1119,#1120);
#943=AXIS2_PLACEMENT_3D('',#1391,#1122,#1123);
#944=AXIS2_PLACEMENT_3D('',#1393,#1125,#1126);
#945=AXIS2_PLACEMENT_3D('',#1396,#1127,#1128);
#946=AXIS2_PLACEMENT_3D('',#1400,#1130,#1131);
#947=AXIS2_PLACEMENT_3D('',#1402,#1133,#1134);
#948=AXIS2_PLACEMENT_3D('',#1405,#1135,#1136);
#949=AXIS2_PLACEMENT_3D('',#1409,#1138,#1139);
#950=AXIS2_PLACEMENT_3D('',#1411,#1141,#1142);
#951=AXIS2_PLACEMENT_3D('',#1414,#1143,#1144);
#952=AXIS2_PLACEMENT_3D('',#1417,#1147,#1148);
#953=AXIS2_PLACEMENT_3D('',#1420,#1149,#1150);
#954=AXIS2_PLACEMENT_3D('',#1423,#1153,#1154);
#955=AXIS2_PLACEMENT_3D('',#1426,#1155,#1156);
#956=AXIS2_PLACEMENT_3D('',#1429,#1159,#1160);
#957=AXIS2_PLACEMENT_3D('',#1433,#1162,#1163);
#958=AXIS2_PLACEMENT_3D('',#1438,#1166,#1167);
#959=AXIS2_PLACEMENT_3D('',#1443,#1170,#1171);
#960=AXIS2_PLACEMENT_3D('',#1448,#1174,#1175);
#961=AXIS2_PLACEMENT_3D('',#1450,#1177,#1178);
#962=AXIS2_PLACEMENT_3D('',#1452,#1180,#1181);
#963=AXIS2_PLACEMENT_3D('',#1454,#1183,#1184);
#964=AXIS2_PLACEMENT_3D('',#1456,#1186,#1187);
#965=AXIS2_PLACEMENT_3D('',#1458,#1189,#1190);
#966=AXIS2_PLACEMENT_3D('',#1461,#1192,#1193);
#967=AXIS2_PLACEMENT_3D('',#1462,#1194,#1195);
#968=AXIS2_PLACEMENT_3D('',#1465,#1196,#1197);
#969=AXIS2_PLACEMENT_3D('',#1468,#1200,#1201);
#970=AXIS2_PLACEMENT_3D('',#1472,#1204,#1205);
#971=AXIS2_PLACEMENT_3D('',#1474,#1206,#1207);
#972=AXIS2_PLACEMENT_3D('',#1476,#1209,#1210);
#973=AXIS2_PLACEMENT_3D('',#1480,#1213,#1214);
#974=AXIS2_PLACEMENT_3D('',#1482,#1215,#1216);
#975=AXIS2_PLACEMENT_3D('',#1484,#1218,#1219);
#976=AXIS2_PLACEMENT_3D('',#1488,#1222,#1223);
#977=AXIS2_PLACEMENT_3D('',#1490,#1224,#1225);
#978=AXIS2_PLACEMENT_3D('',#1492,#1227,#1228);
#979=AXIS2_PLACEMENT_3D('',#1494,#1230,#1231);
#980=AXIS2_PLACEMENT_3D('',#1497,#1233,#1234);
#981=AXIS2_PLACEMENT_3D('',#1498,#1235,#1236);
#982=AXIS2_PLACEMENT_3D('',#1501,#1238,#1239);
#983=AXIS2_PLACEMENT_3D('',#1502,#1240,#1241);
#984=AXIS2_PLACEMENT_3D('',#1505,#1243,#1244);
#985=AXIS2_PLACEMENT_3D('',#1506,#1245,#1246);
#986=AXIS2_PLACEMENT_3D('',#1512,#1250,#1251);
#987=AXIS2_PLACEMENT_3D('',#1514,#1252,#1253);
#988=AXIS2_PLACEMENT_3D('',#1516,#1255,#1256);
#989=AXIS2_PLACEMENT_3D('',#1520,#1259,#1260);
#990=AXIS2_PLACEMENT_3D('',#1522,#1261,#1262);
#991=AXIS2_PLACEMENT_3D('',#1524,#1264,#1265);
#992=AXIS2_PLACEMENT_3D('',#1528,#1268,#1269);
#993=AXIS2_PLACEMENT_3D('',#1530,#1270,#1271);
#994=AXIS2_PLACEMENT_3D('',#1532,#1273,#1274);
#995=AXIS2_PLACEMENT_3D('',#1536,#1277,#1278);
#996=AXIS2_PLACEMENT_3D('',#1537,#1279,#1280);
#997=AXIS2_PLACEMENT_3D('',#1538,#1281,#1282);
#998=DIRECTION('axis',(0.,0.,1.));
#999=DIRECTION('refdir',(1.,0.,0.));
#1000=DIRECTION('center_axis',(0.,0.,1.));
#1001=DIRECTION('ref_axis',(1.,0.,0.));
#1002=DIRECTION('center_axis',(0.,0.,-1.));
#1003=DIRECTION('ref_axis',(1.,0.,0.));
#1004=DIRECTION('',(0.857167300702112,-1.04972719113862E-16,-0.515038074910054));
#1005=DIRECTION('center_axis',(0.,0.,1.));
#1006=DIRECTION('ref_axis',(1.,0.,0.));
#1007=DIRECTION('center_axis',(0.,0.,-1.));
#1008=DIRECTION('ref_axis',(1.,0.,0.));
#1009=DIRECTION('center_axis',(0.,0.,1.));
#1010=DIRECTION('ref_axis',(1.,0.,0.));
#1011=DIRECTION('center_axis',(0.,0.,-1.));
#1012=DIRECTION('ref_axis',(1.,0.,0.));
#1013=DIRECTION('',(0.,0.,-1.));
#1014=DIRECTION('center_axis',(0.,0.,-1.));
#1015=DIRECTION('ref_axis',(1.,0.,0.));
#1016=DIRECTION('center_axis',(0.,0.,1.));
#1017=DIRECTION('ref_axis',(1.,0.,0.));
#1018=DIRECTION('center_axis',(0.,0.,-1.));
#1019=DIRECTION('ref_axis',(1.,0.,0.));
#1020=DIRECTION('',(0.857167300702112,-1.04972719113862E-16,-0.515038074910054));
#1021=DIRECTION('center_axis',(0.,0.,1.));
#1022=DIRECTION('ref_axis',(1.,0.,0.));
#1023=DIRECTION('center_axis',(0.,0.,-1.));
#1024=DIRECTION('ref_axis',(1.,0.,0.));
#1025=DIRECTION('center_axis',(0.,0.,-1.));
#1026=DIRECTION('ref_axis',(1.,0.,0.));
#1027=DIRECTION('center_axis',(0.,0.,1.));
#1028=DIRECTION('ref_axis',(1.,0.,0.));
#1029=DIRECTION('',(0.,0.,-1.));
#1030=DIRECTION('center_axis',(0.,0.,1.));
#1031=DIRECTION('ref_axis',(1.,0.,0.));
#1032=DIRECTION('center_axis',(0.,0.,1.));
#1033=DIRECTION('ref_axis',(1.,0.,0.));
#1034=DIRECTION('center_axis',(0.,0.,-1.));
#1035=DIRECTION('ref_axis',(1.,0.,0.));
#1036=DIRECTION('',(0.857167300702112,-1.04972719113862E-16,-0.515038074910054));
#1037=DIRECTION('center_axis',(0.,0.,1.));
#1038=DIRECTION('ref_axis',(1.,0.,0.));
#1039=DIRECTION('center_axis',(0.,0.,-1.));
#1040=DIRECTION('ref_axis',(1.,0.,0.));
#1041=DIRECTION('center_axis',(0.,0.,1.));
#1042=DIRECTION('ref_axis',(1.,0.,0.));
#1043=DIRECTION('center_axis',(0.,0.,-1.));
#1044=DIRECTION('ref_axis',(1.,0.,0.));
#1045=DIRECTION('',(0.,0.,-1.));
#1046=DIRECTION('center_axis',(0.,0.,-1.));
#1047=DIRECTION('ref_axis',(1.,0.,0.));
#1048=DIRECTION('center_axis',(0.,0.,1.));
#1049=DIRECTION('ref_axis',(1.,0.,0.));
#1050=DIRECTION('center_axis',(0.,0.,-1.));
#1051=DIRECTION('ref_axis',(1.,0.,0.));
#1052=DIRECTION('',(0.857167300702112,-1.04972719113862E-16,-0.515038074910054));
#1053=DIRECTION('center_axis',(0.,0.,1.));
#1054=DIRECTION('ref_axis',(1.,0.,0.));
#1055=DIRECTION('center_axis',(0.,0.,-1.));
#1056=DIRECTION('ref_axis',(1.,0.,0.));
#1057=DIRECTION('center_axis',(0.,0.,-1.));
#1058=DIRECTION('ref_axis',(1.,0.,0.));
#1059=DIRECTION('center_axis',(0.,0.,1.));
#1060=DIRECTION('ref_axis',(1.,0.,0.));
#1061=DIRECTION('',(0.,0.,-1.));
#1062=DIRECTION('center_axis',(0.,0.,1.));
#1063=DIRECTION('ref_axis',(1.,0.,0.));
#1064=DIRECTION('center_axis',(0.,0.,1.));
#1065=DIRECTION('ref_axis',(-1.,0.,0.));
#1066=DIRECTION('center_axis',(0.,0.,1.));
#1067=DIRECTION('ref_axis',(-1.,0.,0.));
#1068=DIRECTION('center_axis',(0.,0.,1.));
#1069=DIRECTION('ref_axis',(-1.,0.,0.));
#1070=DIRECTION('center_axis',(0.,0.,1.));
#1071=DIRECTION('ref_axis',(-1.,0.,0.));
#1072=DIRECTION('center_axis',(0.,0.,1.));
#1073=DIRECTION('ref_axis',(-1.,0.,0.));
#1074=DIRECTION('center_axis',(0.,0.,1.));
#1075=DIRECTION('ref_axis',(-1.,0.,0.));
#1076=DIRECTION('center_axis',(0.,0.,1.));
#1077=DIRECTION('ref_axis',(-1.,0.,0.));
#1078=DIRECTION('center_axis',(0.,0.,-1.));
#1079=DIRECTION('ref_axis',(-1.,0.,0.));
#1080=DIRECTION('',(0.,0.,-1.));
#1081=DIRECTION('center_axis',(0.,0.,1.));
#1082=DIRECTION('ref_axis',(-1.,0.,0.));
#1083=DIRECTION('center_axis',(0.,0.,1.));
#1084=DIRECTION('ref_axis',(-1.,0.,0.));
#1085=DIRECTION('center_axis',(0.,0.,1.));
#1086=DIRECTION('ref_axis',(-1.,0.,0.));
#1087=DIRECTION('center_axis',(0.,0.,1.));
#1088=DIRECTION('ref_axis',(-1.,0.,0.));
#1089=DIRECTION('center_axis',(0.,0.,1.));
#1090=DIRECTION('ref_axis',(-1.,0.,0.));
#1091=DIRECTION('center_axis',(0.,0.,1.));
#1092=DIRECTION('ref_axis',(-1.,0.,0.));
#1093=DIRECTION('center_axis',(0.,0.,1.));
#1094=DIRECTION('ref_axis',(-1.,0.,0.));
#1095=DIRECTION('',(1.,2.97522965674185E-32,0.));
#1096=DIRECTION('center_axis',(0.,0.,1.));
#1097=DIRECTION('ref_axis',(2.22044604925031E-15,-1.,0.));
#1098=DIRECTION('',(1.53134207696506E-16,1.,0.));
#1099=DIRECTION('center_axis',(0.,0.,1.));
#1100=DIRECTION('ref_axis',(1.,2.22044604925031E-15,0.));
#1101=DIRECTION('',(-1.,0.,0.));
#1102=DIRECTION('center_axis',(0.,0.,1.));
#1103=DIRECTION('ref_axis',(-5.55111512312578E-16,1.,0.));
#1104=DIRECTION('',(-3.82835519241264E-17,-1.,0.));
#1105=DIRECTION('center_axis',(0.,0.,1.));
#1106=DIRECTION('ref_axis',(-1.,2.7755575615629E-16,0.));
#1107=DIRECTION('center_axis',(0.,0.,1.));
#1108=DIRECTION('ref_axis',(-1.,0.,0.));
#1109=DIRECTION('center_axis',(0.,0.,1.));
#1110=DIRECTION('ref_axis',(-1.,0.,0.));
#1111=DIRECTION('center_axis',(0.,0.,1.));
#1112=DIRECTION('ref_axis',(-1.,0.,0.));
#1113=DIRECTION('',(0.,0.,1.));
#1114=DIRECTION('center_axis',(0.,0.,-1.));
#1115=DIRECTION('ref_axis',(-1.,0.,0.));
#1116=DIRECTION('',(0.,0.,1.));
#1117=DIRECTION('center_axis',(0.,0.,1.));
#1118=DIRECTION('ref_axis',(-1.,0.,0.));
#1119=DIRECTION('center_axis',(0.,0.,1.));
#1120=DIRECTION('ref_axis',(-1.,0.,0.));
#1121=DIRECTION('',(0.,0.,1.));
#1122=DIRECTION('center_axis',(0.,0.,-1.));
#1123=DIRECTION('ref_axis',(-1.,0.,0.));
#1124=DIRECTION('',(0.,0.,1.));
#1125=DIRECTION('center_axis',(0.,0.,1.));
#1126=DIRECTION('ref_axis',(1.,-1.22464679914735E-16,0.));
#1127=DIRECTION('center_axis',(0.,0.,1.));
#1128=DIRECTION('ref_axis',(-1.,0.,0.));
#1129=DIRECTION('',(0.,0.,1.));
#1130=DIRECTION('center_axis',(0.,0.,-1.));
#1131=DIRECTION('ref_axis',(-1.,0.,0.));
#1132=DIRECTION('',(0.,0.,1.));
#1133=DIRECTION('center_axis',(0.,0.,1.));
#1134=DIRECTION('ref_axis',(-1.,0.,0.));
#1135=DIRECTION('center_axis',(0.,0.,1.));
#1136=DIRECTION('ref_axis',(-1.,0.,0.));
#1137=DIRECTION('',(0.,0.,1.));
#1138=DIRECTION('center_axis',(0.,0.,-1.));
#1139=DIRECTION('ref_axis',(-1.,0.,0.));
#1140=DIRECTION('',(0.,0.,1.));
#1141=DIRECTION('center_axis',(0.,0.,1.));
#1142=DIRECTION('ref_axis',(-5.55111512312578E-16,1.,0.));
#1143=DIRECTION('center_axis',(0.,0.,1.));
#1144=DIRECTION('ref_axis',(3.88578058618805E-15,1.,0.));
#1145=DIRECTION('',(0.,0.,1.));
#1146=DIRECTION('',(0.,0.,1.));
#1147=DIRECTION('center_axis',(0.,0.,1.));
#1148=DIRECTION('ref_axis',(1.,2.22044604925031E-15,0.));
#1149=DIRECTION('center_axis',(0.,0.,1.));
#1150=DIRECTION('ref_axis',(1.,-4.44089209850063E-15,0.));
#1151=DIRECTION('',(0.,0.,1.));
#1152=DIRECTION('',(0.,0.,1.));
#1153=DIRECTION('center_axis',(0.,0.,1.));
#1154=DIRECTION('ref_axis',(2.22044604925031E-15,-1.,0.));
#1155=DIRECTION('center_axis',(0.,0.,1.));
#1156=DIRECTION('ref_axis',(-2.22044604925031E-15,-1.,0.));
#1157=DIRECTION('',(0.,0.,1.));
#1158=DIRECTION('',(0.,0.,1.));
#1159=DIRECTION('center_axis',(0.,0.,1.));
#1160=DIRECTION('ref_axis',(-1.,0.,0.));
#1161=DIRECTION('',(0.,1.,0.));
#1162=DIRECTION('center_axis',(0.,0.,1.));
#1163=DIRECTION('ref_axis',(0.,-1.,0.));
#1164=DIRECTION('',(1.,0.,0.));
#1165=DIRECTION('',(1.,0.,0.));
#1166=DIRECTION('center_axis',(0.,0.,1.));
#1167=DIRECTION('ref_axis',(-1.,0.,0.));
#1168=DIRECTION('',(0.,-1.,0.));
#1169=DIRECTION('',(0.,-1.,0.));
#1170=DIRECTION('center_axis',(0.,0.,1.));
#1171=DIRECTION('ref_axis',(0.,1.,0.));
#1172=DIRECTION('',(-1.,0.,0.));
#1173=DIRECTION('',(-1.,0.,0.));
#1174=DIRECTION('center_axis',(0.,0.,1.));
#1175=DIRECTION('ref_axis',(1.,0.,0.));
#1176=DIRECTION('',(0.,1.,0.));
#1177=DIRECTION('center_axis',(0.,0.,1.));
#1178=DIRECTION('ref_axis',(1.,0.,0.));
#1179=DIRECTION('',(1.,0.,0.));
#1180=DIRECTION('center_axis',(0.,0.,1.));
#1181=DIRECTION('ref_axis',(1.,0.,0.));
#1182=DIRECTION('',(0.,1.,0.));
#1183=DIRECTION('center_axis',(0.,0.,1.));
#1184=DIRECTION('ref_axis',(1.,0.,0.));
#1185=DIRECTION('',(-1.,0.,0.));
#1186=DIRECTION('center_axis',(0.,0.,1.));
#1187=DIRECTION('ref_axis',(1.,0.,0.));
#1188=DIRECTION('',(0.,-1.,0.));
#1189=DIRECTION('center_axis',(0.,0.,1.));
#1190=DIRECTION('ref_axis',(1.,0.,0.));
#1191=DIRECTION('',(0.,0.,-1.));
#1192=DIRECTION('center_axis',(0.,0.,-1.));
#1193=DIRECTION('ref_axis',(1.,0.,0.));
#1194=DIRECTION('center_axis',(0.,0.,1.));
#1195=DIRECTION('ref_axis',(1.,0.,0.));
#1196=DIRECTION('center_axis',(0.,0.,1.));
#1197=DIRECTION('ref_axis',(1.,0.,0.));
#1198=DIRECTION('',(0.,0.,1.));
#1199=DIRECTION('',(0.,0.,1.));
#1200=DIRECTION('center_axis',(-1.,0.,0.));
#1201=DIRECTION('ref_axis',(0.,-1.,0.));
#1202=DIRECTION('',(0.,-1.,0.));
#1203=DIRECTION('',(0.,0.,1.));
#1204=DIRECTION('center_axis',(0.,0.,1.));
#1205=DIRECTION('ref_axis',(1.,0.,0.));
#1206=DIRECTION('center_axis',(0.,0.,1.));
#1207=DIRECTION('ref_axis',(1.,0.,0.));
#1208=DIRECTION('',(0.,0.,1.));
#1209=DIRECTION('center_axis',(0.,-1.,0.));
#1210=DIRECTION('ref_axis',(1.,0.,0.));
#1211=DIRECTION('',(1.,0.,0.));
#1212=DIRECTION('',(0.,0.,1.));
#1213=DIRECTION('center_axis',(0.,0.,1.));
#1214=DIRECTION('ref_axis',(1.,0.,0.));
#1215=DIRECTION('center_axis',(0.,0.,1.));
#1216=DIRECTION('ref_axis',(1.,0.,0.));
#1217=DIRECTION('',(0.,0.,1.));
#1218=DIRECTION('center_axis',(1.,0.,0.));
#1219=DIRECTION('ref_axis',(0.,1.,0.));
#1220=DIRECTION('',(0.,1.,0.));
#1221=DIRECTION('',(0.,0.,1.));
#1222=DIRECTION('center_axis',(0.,0.,1.));
#1223=DIRECTION('ref_axis',(1.,0.,0.));
#1224=DIRECTION('center_axis',(0.,0.,1.));
#1225=DIRECTION('ref_axis',(1.,0.,0.));
#1226=DIRECTION('',(0.,0.,1.));
#1227=DIRECTION('center_axis',(0.,1.,0.));
#1228=DIRECTION('ref_axis',(-1.,0.,0.));
#1229=DIRECTION('',(-1.,0.,0.));
#1230=DIRECTION('center_axis',(0.,0.,1.));
#1231=DIRECTION('ref_axis',(1.,0.,0.));
#1232=DIRECTION('',(0.,0.,-1.));
#1233=DIRECTION('center_axis',(0.,0.,-1.));
#1234=DIRECTION('ref_axis',(1.,0.,0.));
#1235=DIRECTION('center_axis',(0.,0.,1.));
#1236=DIRECTION('ref_axis',(1.,0.,0.));
#1237=DIRECTION('',(0.,0.,-1.));
#1238=DIRECTION('center_axis',(0.,0.,-1.));
#1239=DIRECTION('ref_axis',(1.,0.,0.));
#1240=DIRECTION('center_axis',(0.,0.,1.));
#1241=DIRECTION('ref_axis',(1.,0.,0.));
#1242=DIRECTION('',(0.,0.,-1.));
#1243=DIRECTION('center_axis',(0.,0.,-1.));
#1244=DIRECTION('ref_axis',(1.,0.,0.));
#1245=DIRECTION('center_axis',(0.,-1.,0.));
#1246=DIRECTION('ref_axis',(1.,0.,0.));
#1247=DIRECTION('',(1.,0.,0.));
#1248=DIRECTION('',(0.,0.,1.));
#1249=DIRECTION('',(0.,0.,1.));
#1250=DIRECTION('center_axis',(0.,0.,1.));
#1251=DIRECTION('ref_axis',(-1.,1.11022302462516E-15,0.));
#1252=DIRECTION('center_axis',(0.,0.,1.));
#1253=DIRECTION('ref_axis',(-1.,1.11022302462516E-15,0.));
#1254=DIRECTION('',(0.,0.,1.));
#1255=DIRECTION('center_axis',(-1.,9.57088814332032E-18,0.));
#1256=DIRECTION('ref_axis',(-9.57088814332032E-18,-1.,0.));
#1257=DIRECTION('',(-9.57088814332032E-18,-1.,0.));
#1258=DIRECTION('',(0.,0.,1.));
#1259=DIRECTION('center_axis',(0.,0.,1.));
#1260=DIRECTION('ref_axis',(3.88578058618805E-15,1.,0.));
#1261=DIRECTION('center_axis',(0.,0.,1.));
#1262=DIRECTION('ref_axis',(3.88578058618805E-15,1.,0.));
#1263=DIRECTION('',(0.,0.,1.));
#1264=DIRECTION('center_axis',(0.,1.,0.));
#1265=DIRECTION('ref_axis',(-1.,0.,0.));
#1266=DIRECTION('',(-1.,0.,0.));
#1267=DIRECTION('',(0.,0.,1.));
#1268=DIRECTION('center_axis',(0.,0.,1.));
#1269=DIRECTION('ref_axis',(1.,-4.44089209850063E-15,0.));
#1270=DIRECTION('center_axis',(0.,0.,1.));
#1271=DIRECTION('ref_axis',(1.,-4.44089209850063E-15,0.));
#1272=DIRECTION('',(0.,0.,1.));
#1273=DIRECTION('center_axis',(1.,0.,0.));
#1274=DIRECTION('ref_axis',(0.,1.,0.));
#1275=DIRECTION('',(0.,1.,0.));
#1276=DIRECTION('',(0.,0.,1.));
#1277=DIRECTION('center_axis',(0.,0.,1.));
#1278=DIRECTION('ref_axis',(-2.22044604925031E-15,-1.,0.));
#1279=DIRECTION('center_axis',(0.,0.,1.));
#1280=DIRECTION('ref_axis',(-2.22044604925031E-15,-1.,0.));
#1281=DIRECTION('center_axis',(0.,0.,1.));
#1282=DIRECTION('ref_axis',(1.,0.,0.));
#1283=CARTESIAN_POINT('',(0.,0.,0.));
#1284=CARTESIAN_POINT('Origin',(27.5,5.5,6.51931150477795));
#1285=CARTESIAN_POINT('',(25.9,5.5,7.));
#1286=CARTESIAN_POINT('Origin',(27.5,5.5,7.));
#1287=CARTESIAN_POINT('',(26.5,5.5,6.63948362858346));
#1288=CARTESIAN_POINT('',(26.7,5.5,6.51931150477795));
#1289=CARTESIAN_POINT('Origin',(27.5,5.5,6.63948362858346));
#1290=CARTESIAN_POINT('Origin',(27.5,5.5,8.));
#1291=CARTESIAN_POINT('',(26.0352903144107,4.85608576895613,9.));
#1292=CARTESIAN_POINT('',(28.1439142310439,6.96470968558933,9.));
#1293=CARTESIAN_POINT('Origin',(27.5,5.5,9.));
#1294=CARTESIAN_POINT('',(25.9,5.5,9.));
#1295=CARTESIAN_POINT('Origin',(27.5,5.5,9.));
#1296=CARTESIAN_POINT('',(25.9,5.5,8.));
#1297=CARTESIAN_POINT('Origin',(27.5,5.5,9.));
#1298=CARTESIAN_POINT('Origin',(5.5,5.5,6.51931150477795));
#1299=CARTESIAN_POINT('',(3.9,5.5,7.));
#1300=CARTESIAN_POINT('Origin',(5.5,5.5,7.));
#1301=CARTESIAN_POINT('',(4.5,5.5,6.63948362858346));
#1302=CARTESIAN_POINT('',(4.7,5.5,6.51931150477795));
#1303=CARTESIAN_POINT('Origin',(5.5,5.5,6.63948362858346));
#1304=CARTESIAN_POINT('Origin',(5.5,5.5,8.));
#1305=CARTESIAN_POINT('',(4.85608576895612,6.96470968558932,9.));
#1306=CARTESIAN_POINT('',(6.96470968558932,4.85608576895612,9.));
#1307=CARTESIAN_POINT('Origin',(5.5,5.5,9.));
#1308=CARTESIAN_POINT('',(3.9,5.5,9.));
#1309=CARTESIAN_POINT('Origin',(5.5,5.5,9.));
#1310=CARTESIAN_POINT('',(3.9,5.5,8.));
#1311=CARTESIAN_POINT('Origin',(5.5,5.5,9.));
#1312=CARTESIAN_POINT('Origin',(27.5,27.5,6.51931150477795));
#1313=CARTESIAN_POINT('',(25.9,27.5,7.));
#1314=CARTESIAN_POINT('Origin',(27.5,27.5,7.));
#1315=CARTESIAN_POINT('',(26.5,27.5,6.63948362858346));
#1316=CARTESIAN_POINT('',(26.7,27.5,6.51931150477795));
#1317=CARTESIAN_POINT('Origin',(27.5,27.5,6.63948362858346));
#1318=CARTESIAN_POINT('Origin',(27.5,27.5,8.));
#1319=CARTESIAN_POINT('',(28.1439142310439,26.0352903144107,9.));
#1320=CARTESIAN_POINT('',(26.0352903144107,28.1439142310439,9.));
#1321=CARTESIAN_POINT('Origin',(27.5,27.5,9.));
#1322=CARTESIAN_POINT('',(25.9,27.5,9.));
#1323=CARTESIAN_POINT('Origin',(27.5,27.5,9.));
#1324=CARTESIAN_POINT('',(25.9,27.5,8.));
#1325=CARTESIAN_POINT('Origin',(27.5,27.5,9.));
#1326=CARTESIAN_POINT('Origin',(5.5,27.5,6.51931150477795));
#1327=CARTESIAN_POINT('',(3.9,27.5,7.));
#1328=CARTESIAN_POINT('Origin',(5.5,27.5,7.));
#1329=CARTESIAN_POINT('',(4.5,27.5,6.63948362858346));
#1330=CARTESIAN_POINT('',(4.7,27.5,6.51931150477795));
#1331=CARTESIAN_POINT('Origin',(5.5,27.5,6.63948362858346));
#1332=CARTESIAN_POINT('Origin',(5.5,27.5,8.));
#1333=CARTESIAN_POINT('',(6.96470968558932,28.1439142310439,9.));
#1334=CARTESIAN_POINT('',(4.85608576895612,26.0352903144107,9.));
#1335=CARTESIAN_POINT('Origin',(5.5,27.5,9.));
#1336=CARTESIAN_POINT('',(3.9,27.5,9.));
#1337=CARTESIAN_POINT('Origin',(5.5,27.5,9.));
#1338=CARTESIAN_POINT('',(3.9,27.5,8.));
#1339=CARTESIAN_POINT('Origin',(5.5,27.5,9.));
#1340=CARTESIAN_POINT('Origin',(16.5000002458692,16.5000002458692,9.));
#1341=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1342=CARTESIAN_POINT('Origin',(16.5000002458692,16.5000002458692,9.));
#1343=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1344=CARTESIAN_POINT('Origin',(16.5000002458692,16.5000002458692,9.));
#1345=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1346=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1347=CARTESIAN_POINT('',(31.55,16.5,14.2));
#1348=CARTESIAN_POINT('Origin',(16.5,16.5,14.2));
#1349=CARTESIAN_POINT('',(31.55,16.5,9.));
#1350=CARTESIAN_POINT('',(31.55,16.5,9.));
#1351=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1352=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1353=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1354=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1355=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1356=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1357=CARTESIAN_POINT('Origin',(16.5000002458692,16.5000002458692,14.2));
#1358=CARTESIAN_POINT('',(2.,0.,14.2));
#1359=CARTESIAN_POINT('',(31.0000004917383,0.,14.2));
#1360=CARTESIAN_POINT('',(2.,-8.62816615085482E-31,14.2));
#1361=CARTESIAN_POINT('',(33.0000004917383,2.,14.2));
#1362=CARTESIAN_POINT('Origin',(31.0000004917383,2.,14.2));
#1363=CARTESIAN_POINT('',(33.0000004917383,31.0000004917383,14.2));
#1364=CARTESIAN_POINT('',(33.0000004917383,2.,14.2));
#1365=CARTESIAN_POINT('',(31.0000004917383,33.0000004917383,14.2));
#1366=CARTESIAN_POINT('Origin',(31.0000004917383,31.0000004917383,14.2));
#1367=CARTESIAN_POINT('',(2.,33.0000004917383,14.2));
#1368=CARTESIAN_POINT('',(31.0000004917383,33.0000004917383,14.2));
#1369=CARTESIAN_POINT('',(0.,31.0000004917383,14.2));
#1370=CARTESIAN_POINT('Origin',(2.,31.0000004917383,14.2));
#1371=CARTESIAN_POINT('',(0.,2.,14.2));
#1372=CARTESIAN_POINT('',(1.11022302462516E-15,31.0000004917383,14.2));
#1373=CARTESIAN_POINT('Origin',(2.,2.,14.2));
#1374=CARTESIAN_POINT('Origin',(16.5000002458692,16.5000002458692,9.));
#1375=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1376=CARTESIAN_POINT('',(5.5,22.437171043519,5.));
#1377=CARTESIAN_POINT('',(5.5,10.562828956481,5.));
#1378=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1379=CARTESIAN_POINT('',(5.5,22.437171043519,9.));
#1380=CARTESIAN_POINT('',(5.5,22.437171043519,5.));
#1381=CARTESIAN_POINT('',(5.5,10.562828956481,9.));
#1382=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1383=CARTESIAN_POINT('',(5.5,10.562828956481,5.));
#1384=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1385=CARTESIAN_POINT('',(10.562828956481,5.5,5.));
#1386=CARTESIAN_POINT('',(22.437171043519,5.50000000000001,5.));
#1387=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1388=CARTESIAN_POINT('',(10.562828956481,5.5,9.));
#1389=CARTESIAN_POINT('',(10.562828956481,5.5,5.));
#1390=CARTESIAN_POINT('',(22.437171043519,5.50000000000001,9.));
#1391=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1392=CARTESIAN_POINT('',(22.437171043519,5.50000000000001,5.));
#1393=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1394=CARTESIAN_POINT('',(27.5,10.562828956481,5.));
#1395=CARTESIAN_POINT('',(27.5,22.4371710435189,5.));
#1396=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1397=CARTESIAN_POINT('',(27.5,10.562828956481,9.));
#1398=CARTESIAN_POINT('',(27.5,10.562828956481,5.));
#1399=CARTESIAN_POINT('',(27.5,22.4371710435189,9.));
#1400=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1401=CARTESIAN_POINT('',(27.5,22.4371710435189,5.));
#1402=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1403=CARTESIAN_POINT('',(22.437171043519,27.5,5.));
#1404=CARTESIAN_POINT('',(10.562828956481,27.5,5.));
#1405=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1406=CARTESIAN_POINT('',(22.437171043519,27.5,9.));
#1407=CARTESIAN_POINT('',(22.437171043519,27.5,5.));
#1408=CARTESIAN_POINT('',(10.562828956481,27.5,9.));
#1409=CARTESIAN_POINT('Origin',(16.5,16.5,9.));
#1410=CARTESIAN_POINT('',(10.562828956481,27.5,5.));
#1411=CARTESIAN_POINT('Origin',(2.,31.0000004917383,5.));
#1412=CARTESIAN_POINT('',(2.00000000000001,33.,5.));
#1413=CARTESIAN_POINT('',(1.11022302462516E-15,31.,5.));
#1414=CARTESIAN_POINT('Origin',(2.,31.,5.));
#1415=CARTESIAN_POINT('',(1.11022302462516E-15,31.0000004917383,5.));
#1416=CARTESIAN_POINT('',(2.,33.0000004917383,5.));
#1417=CARTESIAN_POINT('Origin',(31.0000004917383,31.0000004917383,5.));
#1418=CARTESIAN_POINT('',(33.,31.,5.));
#1419=CARTESIAN_POINT('',(31.,33.,5.));
#1420=CARTESIAN_POINT('Origin',(31.,31.,5.));
#1421=CARTESIAN_POINT('',(31.0000004917383,33.0000004917383,5.));
#1422=CARTESIAN_POINT('',(33.0000004917383,31.0000004917383,5.));
#1423=CARTESIAN_POINT('Origin',(31.0000004917383,2.,5.));
#1424=CARTESIAN_POINT('',(31.,0.,5.));
#1425=CARTESIAN_POINT('',(33.,2.,5.));
#1426=CARTESIAN_POINT('Origin',(31.,2.,5.));
#1427=CARTESIAN_POINT('',(33.0000004917383,2.,5.));
#1428=CARTESIAN_POINT('',(31.0000004917383,0.,5.));
#1429=CARTESIAN_POINT('Origin',(16.5000002458692,16.5000002458692,9.));
#1430=CARTESIAN_POINT('',(5.5,25.25,9.));
#1431=CARTESIAN_POINT('',(5.5,7.75,9.));
#1432=CARTESIAN_POINT('',(7.75,27.5,9.));
#1433=CARTESIAN_POINT('Origin',(5.5,27.5,9.));
#1434=CARTESIAN_POINT('',(7.75,27.5,9.));
#1435=CARTESIAN_POINT('',(25.25,27.5,9.));
#1436=CARTESIAN_POINT('',(7.75,27.5,9.));
#1437=CARTESIAN_POINT('',(27.5,25.25,9.));
#1438=CARTESIAN_POINT('Origin',(27.5,27.5,9.));
#1439=CARTESIAN_POINT('',(27.5,25.25,9.));
#1440=CARTESIAN_POINT('',(27.5,7.75,9.));
#1441=CARTESIAN_POINT('',(27.5,25.25,9.));
#1442=CARTESIAN_POINT('',(25.25,5.5,9.));
#1443=CARTESIAN_POINT('Origin',(27.5,5.5,9.));
#1444=CARTESIAN_POINT('',(25.25,5.5,9.));
#1445=CARTESIAN_POINT('',(7.75,5.5,9.));
#1446=CARTESIAN_POINT('',(25.25,5.5,9.));
#1447=CARTESIAN_POINT('',(5.5,7.75,9.));
#1448=CARTESIAN_POINT('Origin',(5.5,5.5,9.));
#1449=CARTESIAN_POINT('',(5.5,7.75,9.));
#1450=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1451=CARTESIAN_POINT('',(5.5,27.5,5.));
#1452=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1453=CARTESIAN_POINT('',(5.5,5.5,5.));
#1454=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1455=CARTESIAN_POINT('',(5.5,5.5,5.));
#1456=CARTESIAN_POINT('Origin',(16.5,16.5,5.));
#1457=CARTESIAN_POINT('',(27.5,27.5,5.));
#1458=CARTESIAN_POINT('Origin',(5.5,5.5,0.));
#1459=CARTESIAN_POINT('',(4.5,5.5,0.));
#1460=CARTESIAN_POINT('',(4.5,5.5,0.));
#1461=CARTESIAN_POINT('Origin',(5.5,5.5,0.));
#1462=CARTESIAN_POINT('Origin',(27.5,5.5,0.));
#1463=CARTESIAN_POINT('',(27.5,7.75,0.));
#1464=CARTESIAN_POINT('',(25.25,5.5,0.));
#1465=CARTESIAN_POINT('Origin',(27.5,5.5,0.));
#1466=CARTESIAN_POINT('',(25.25,5.5,0.));
#1467=CARTESIAN_POINT('',(27.5,7.75,0.));
#1468=CARTESIAN_POINT('Origin',(27.5,25.25,0.));
#1469=CARTESIAN_POINT('',(27.5,25.25,0.));
#1470=CARTESIAN_POINT('',(27.5,27.5,0.));
#1471=CARTESIAN_POINT('',(27.5,25.25,0.));
#1472=CARTESIAN_POINT('Origin',(27.5,27.5,0.));
#1473=CARTESIAN_POINT('',(25.25,27.5,0.));
#1474=CARTESIAN_POINT('Origin',(27.5,27.5,0.));
#1475=CARTESIAN_POINT('',(25.25,27.5,0.));
#1476=CARTESIAN_POINT('Origin',(7.75,27.5,0.));
#1477=CARTESIAN_POINT('',(7.75,27.5,0.));
#1478=CARTESIAN_POINT('',(5.5,27.5,0.));
#1479=CARTESIAN_POINT('',(7.75,27.5,0.));
#1480=CARTESIAN_POINT('Origin',(5.5,27.5,0.));
#1481=CARTESIAN_POINT('',(5.5,25.25,0.));
#1482=CARTESIAN_POINT('Origin',(5.5,27.5,0.));
#1483=CARTESIAN_POINT('',(5.5,25.25,0.));
#1484=CARTESIAN_POINT('Origin',(5.5,7.75,0.));
#1485=CARTESIAN_POINT('',(5.5,7.75,0.));
#1486=CARTESIAN_POINT('',(5.5,5.5,0.));
#1487=CARTESIAN_POINT('',(5.5,7.75,0.));
#1488=CARTESIAN_POINT('Origin',(5.5,5.5,0.));
#1489=CARTESIAN_POINT('',(7.75,5.5,0.));
#1490=CARTESIAN_POINT('Origin',(5.5,5.5,0.));
#1491=CARTESIAN_POINT('',(7.75,5.5,0.));
#1492=CARTESIAN_POINT('Origin',(25.25,5.5,0.));
#1493=CARTESIAN_POINT('',(5.5,5.5,0.));
#1494=CARTESIAN_POINT('Origin',(27.5,5.5,0.));
#1495=CARTESIAN_POINT('',(26.5,5.5,0.));
#1496=CARTESIAN_POINT('',(26.5,5.5,0.));
#1497=CARTESIAN_POINT('Origin',(27.5,5.5,0.));
#1498=CARTESIAN_POINT('Origin',(27.5,27.5,0.));
#1499=CARTESIAN_POINT('',(26.5,27.5,0.));
#1500=CARTESIAN_POINT('',(26.5,27.5,0.));
#1501=CARTESIAN_POINT('Origin',(27.5,27.5,0.));
#1502=CARTESIAN_POINT('Origin',(5.5,27.5,0.));
#1503=CARTESIAN_POINT('',(4.5,27.5,0.));
#1504=CARTESIAN_POINT('',(4.5,27.5,0.));
#1505=CARTESIAN_POINT('Origin',(5.5,27.5,0.));
#1506=CARTESIAN_POINT('Origin',(2.,0.,0.));
#1507=CARTESIAN_POINT('',(2.,0.,0.));
#1508=CARTESIAN_POINT('',(31.,0.,0.));
#1509=CARTESIAN_POINT('',(2.,0.,0.));
#1510=CARTESIAN_POINT('',(31.,0.,0.));
#1511=CARTESIAN_POINT('',(2.,0.,0.));
#1512=CARTESIAN_POINT('Origin',(2.,2.,0.));
#1513=CARTESIAN_POINT('',(0.,2.,0.));
#1514=CARTESIAN_POINT('Origin',(2.,2.,0.));
#1515=CARTESIAN_POINT('',(0.,2.,0.));
#1516=CARTESIAN_POINT('Origin',(2.77555756156289E-16,31.,0.));
#1517=CARTESIAN_POINT('',(1.11022302462516E-15,31.,0.));
#1518=CARTESIAN_POINT('',(2.77555756156289E-16,31.,0.));
#1519=CARTESIAN_POINT('',(1.11022302462516E-15,31.,0.));
#1520=CARTESIAN_POINT('Origin',(2.,31.,0.));
#1521=CARTESIAN_POINT('',(2.00000000000001,33.,0.));
#1522=CARTESIAN_POINT('Origin',(2.,31.,0.));
#1523=CARTESIAN_POINT('',(2.00000000000001,33.,0.));
#1524=CARTESIAN_POINT('Origin',(31.,33.,0.));
#1525=CARTESIAN_POINT('',(31.,33.,0.));
#1526=CARTESIAN_POINT('',(31.,33.,0.));
#1527=CARTESIAN_POINT('',(31.,33.,0.));
#1528=CARTESIAN_POINT('Origin',(31.,31.,0.));
#1529=CARTESIAN_POINT('',(33.,31.,0.));
#1530=CARTESIAN_POINT('Origin',(31.,31.,0.));
#1531=CARTESIAN_POINT('',(33.,31.,0.));
#1532=CARTESIAN_POINT('Origin',(33.,2.,0.));
#1533=CARTESIAN_POINT('',(33.,2.,0.));
#1534=CARTESIAN_POINT('',(33.,2.,0.));
#1535=CARTESIAN_POINT('',(33.,2.,0.));
#1536=CARTESIAN_POINT('Origin',(31.,2.,0.));
#1537=CARTESIAN_POINT('Origin',(31.,2.,0.));
#1538=CARTESIAN_POINT('Origin',(16.5,16.5,0.));
#1539=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1543,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1540=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1543,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1541=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1539))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1543,#1545,#1546))
REPRESENTATION_CONTEXT('','3D')
);
#1542=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1540))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1543,#1545,#1546))
REPRESENTATION_CONTEXT('','3D')
);
#1543=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#1544=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1545=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1546=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1547=SHAPE_DEFINITION_REPRESENTATION(#1548,#1549);
#1548=PRODUCT_DEFINITION_SHAPE('',$,#1551);
#1549=SHAPE_REPRESENTATION('',(#889),#1541);
#1550=PRODUCT_DEFINITION_CONTEXT('part definition',#1555,'design');
#1551=PRODUCT_DEFINITION('C mount','C mount v1',#1552,#1550);
#1552=PRODUCT_DEFINITION_FORMATION('',$,#1557);
#1553=PRODUCT_RELATED_PRODUCT_CATEGORY('C mount v1','C mount v1',(#1557));
#1554=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#1555);
#1555=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#1556=PRODUCT_CONTEXT('part definition',#1555,'mechanical');
#1557=PRODUCT('C mount','C mount v1',$,(#1556));
#1558=PRESENTATION_STYLE_ASSIGNMENT((#1559));
#1559=SURFACE_STYLE_USAGE(.BOTH.,#1560);
#1560=SURFACE_SIDE_STYLE('',(#1561));
#1561=SURFACE_STYLE_FILL_AREA(#1562);
#1562=FILL_AREA_STYLE('Steel - Satin',(#1563));
#1563=FILL_AREA_STYLE_COLOUR('Steel - Satin',#1564);
#1564=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
