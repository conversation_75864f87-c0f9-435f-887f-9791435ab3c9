# -*- coding: utf-8 -*-
"""
GPIO Oscilloscope Auto Screenshot Test - D68 Chip Version

基于test_gpio_case5_7_q2s_auto_v2.py模板，适配D68芯片测试。
主要修改：
1. 导入模块改为M65D68_Common_Fuction_A0
2. MFN设置函数改为MFNSet_D68
3. 添加D68特定初始化代码
4. 仅使用Link0和Link2进行测试
"""
import logging
import time
import pytest
import os
import sys
import tkinter as tk
from tkinter import messagebox

# 添加D68模块路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65d68_a0'))  # D68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'))  # S68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # 示波器模块路径
# D68芯片专用导入
try:
    from Common_d.M65D68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    print("✅ D68模块导入成功")
except ImportError as e:
    print(f"⚠️ D68模块导入失败: {e}")
    # 定义空类以避免错误
    class M65Q68_A0: pass
    class M65Q68_A0_Remote_M66S68: pass

# D68设备初始化配置
D68_DEVICE_CONFIG = {
    'q68_iic_addr': 0x73,
    's68_iic_dev': [0x40, 0x40, 0x40, 0x40],
    's68_res_dev': [0x20, 0x21, 0x22, 0x23],
    's68_res_sensor_dev': [0x24, 0x25, 0x26, 0x27],
    'active_links': [0, 2],  # D68仅支持Link0和Link2
    'RATE':         [2, 2, 2, 2],
    'RATE_final':   [2, 2, 2, 2],
    'BCRATE':       [0, 0, 0, 0]  
    
}

# 测试配置
TEST_CONFIG = {
    'active_links': [0, 2],                 # D68版本仅使用Link0和Link2
    'q68_source_gpio': 0,                  # Q68源GPIO
    's68_target_gpios': [0, 1, 2, 3, 4, 5, 6, 7, 8],          # S68目标GPIO列表
    'signal_id': 11,                        # GPIO信号ID
    'observation_time': 1,                  # 每个频率的观察时间(秒)
    
    # 示波器配置
    'oscilloscope_config': {
        'enable_screenshot': False,          # 是否启用示波器截图功能
        'test_mode': 'combined',            # 测试模式: 'fixed'=仅固定频率, 'sweep'=仅扫频, 'combined'=组合测试
        'digital_trigger_channel': 1,       # 数字通道D1作为触发源
        'waveform_type': 'SQUARE',          # 方波
        # 固定频率模式配置
        'frequency_list': [30, 1000, 10000, 50000, 100000],  # 固定频率列表 (Hz)
        'timebase_list': ['20ms', '1ms', '100us', '20us', '10us'],  # 对应的时基
        # 扫频模式配置
        'frequency_range': {
            'start': 300000,                # 300kHz
            'end': 420000,                  # 420kHz
            'step': 10000                   # 10kHz步进
        },
        'sweep_timebase': '1us',            # 扫频模式的时基
        'amplitude': 1.8,                  # 1.8Vpp
        'offset': 0.9,                      # 900mVdc偏移
        'screenshot_folder': 'U-disk0/gpiotest/d68tos68_0_2',  # D68版本截图保存文件夹
        'persistence_mode': 'INFinite',     # 余晖模式 (该机型只支持INFinite)
        'probe_wait_time': 5,              # 第一次截图前等待插探头时间(秒)
        'freq_observation_time': 3,         # 每个频率的观察时间(秒)
    }
}


class D68_DeviceManager:
    """D68设备管理器 - 独立的D68设备初始化和管理"""

    def __init__(self):
        self.q68 = None
        self.q68_remote = None
        self.oscilloscope = None
        self.devices = {}

    def initialize_d68_devices(self):
        """初始化D68设备"""
        try:
            print("🔧 初始化D68设备...")

            # 初始化D68 Q68设备
            print("  📡 初始化D68 Q68设备...")
            self.q68 = M65Q68_A0(dongle='stm32', id=0, bus='i2c')
            print("    ✅ D68 Q68设备初始化完成")

            # 初始化D68 Q68远程设备
            print("  📡 初始化D68 Q68远程设备...")
            self.q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')
            print("    ✅ D68 Q68远程设备初始化完成")

            # D68特定初始化
            print("  🔧 执行D68特定初始化...")
            self.q68.c2m.wr_test_glb_ctrl0_fields(key=0x5c)  # Test register write access key
            self.q68.c2m.wr_test_tx_link_data_inv_fields(tx_polar_sel=0x6)    # R5/R7 polarity
            self.q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel=0x6)
            print("    ✅ D68特定初始化完成")

            # 初始化示波器（可选）
            try:
                print("  📊 尝试初始化示波器...")
                from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
                self.oscilloscope = SiglentSDS5034X()
                print("    ✅ 示波器初始化完成")
            except Exception as e:
                print(f"    ⚠️ 示波器初始化失败: {e}")
                self.oscilloscope = None

            # 设置链路参数
            print("  🔗 配置D68链路参数...")
            config = D68_DEVICE_CONFIG

            # 设置链路速率
            self.q68.c2m.wr_sys_cfg_link_ctrl1_fields(
                rate0=config['RATE'][0],
                rate1=config['RATE'][1],
                rate2=config['RATE'][2],
                rate3=config['RATE'][3]
            )
            self.q68.c2m.wr_sys_cfg_link_ctrl3_fields(
                bc_rate0=config['BCRATE'][0],
                bc_rate1=config['BCRATE'][1],
                bc_rate2=config['BCRATE'][2],
                bc_rate3=config['BCRATE'][3]
            )

            # 初始化链路
            self.q68.Q68_C3_6G_Init(
                rate0=config['RATE'][0],
                rate1=config['RATE'][1],
                rate2=config['RATE'][2],
                rate3=config['RATE'][3]
            )

            # 设置最终链路速率
            self.q68.c2m.wr_sys_cfg_link_ctrl1_fields(
                rate0=config['RATE_final'][0],
                rate1=config['RATE_final'][1],
                rate2=config['RATE_final'][2],
                rate3=config['RATE_final'][3]
            )
            self.q68.Q68_C3_6G_Init(
                rate0=config['RATE_final'][0],
                rate1=config['RATE_final'][1],
                rate2=config['RATE_final'][2],
                rate3=config['RATE_final'][3]
            )
            print("    ✅ D68链路参数配置完成")

            # 创建设备字典
            self.devices = {
                'q68': self.q68,
                'q68_remote': self.q68_remote,
                's68_res_dev': config['s68_res_dev'],
                'configure_links': config['active_links'],
                'oscilloscope': self.oscilloscope
            }

            print("✅ D68设备初始化完成")
            return True

        except Exception as e:
            print(f"❌ D68设备初始化失败: {e}")
            return False

    def get_devices(self):
        """获取设备字典"""
        return self.devices

    def cleanup(self):
        """清理设备资源"""
        try:
            print("🧹 清理D68设备资源...")
            # 这里可以添加设备清理代码
            print("✅ D68设备资源清理完成")
        except Exception as e:
            print(f"⚠️ D68设备资源清理失败: {e}")


class GPIO_D68_S68_AutoTester:
    """D68到S68的GPIO自动测试器"""
    
    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']  # 保持q68变量名，但实际是D68芯片
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')



    def setup_address_translation_for_links(self, active_links=None):
        """为指定Links设置地址转换"""
        if active_links is None:
            active_links = TEST_CONFIG['active_links']
            
        try:
            print(f"  📡 为Links {active_links} 设置地址转换...")
            
            for link_id in active_links:
                if link_id < len(self.s68_res_dev):
                    self.q68_remote.S68_AddrTrans(
                        link=link_id,
                        q68_iic_addr=0x73,
                        s68_iic_addr=0x40,
                        s68_retrans_addr=self.s68_res_dev[link_id],
                        sensor_addr=0x24,
                        sensor_retrans_addr=0x24 + link_id,
                    )
                    print(f"      ✅ Link{link_id} 地址转换: 0x{self.s68_res_dev[link_id]:02X} -> 0x40")
                else:
                    print(f"      ⚠️ Link{link_id} 超出范围，跳过")
            
            print(f"    ✅ Links地址转换设置完成")
            return True
            
        except Exception as e:
            print(f"    ❌ 地址转换设置失败: {e}")
            return False

    def configure_q68_gpio_d68(self, q68_gpio, signal_id):
        """配置Q68 GPIO - D68版本"""
        try:
            print(f"  📤 配置Q68 GPIO{q68_gpio} (D68版本)...")
            
            # 使用D68特定的MFN设置方法
            self.q68.MFNSet_D68(gpio=q68_gpio, mfn=0)
            print(f"    ✅ GPIO{q68_gpio} MFN设置完成 (使用MFNSet_D68)")
            
            # 配置远程发送，使用Link0作为主Link
            primary_link = TEST_CONFIG['active_links'][0]  # 使用第一个活跃Link
            self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id, link_id=primary_link)
            
            print(f"    ✅ GPIO{q68_gpio} 远程发送配置完成")
            print(f"    📊 主Link: {primary_link}, 信号ID: {signal_id}")
            
            return True
            
        except Exception as e:
            print(f"    ❌ Q68 GPIO{q68_gpio} 配置失败: {e}")
            return False

    def configure_s68_gpios_for_links(self, s68_gpio_range, signal_id, active_links=None):
        """为指定Links配置S68 GPIOs"""
        if active_links is None:
            active_links = TEST_CONFIG['active_links']
            
        try:
            print(f"  📥 为Links {active_links} 配置S68 GPIO{s68_gpio_range}...")
            
            for link in active_links:
                if link < len(self.s68_res_dev):
                    print(f"    🔗 配置Link{link} S68设备...")
                    s68_iic_dev = self.s68_res_dev[link]
                    self.q68_remote.dongle.devAddr = s68_iic_dev
                    
                    for s68_gpio in s68_gpio_range:
                        try:
                            self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                            self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
                            print(f"      ✅ Link{link} GPIO{s68_gpio} 配置成功")
                        except Exception as e:
                            print(f"      ❌ Link{link} GPIO{s68_gpio} 配置失败: {e}")
                else:
                    print(f"    ⚠️ Link{link} 超出范围，跳过")
            
            print(f"    ✅ S68 GPIOs配置完成")
            return True
            
        except Exception as e:
            print(f"    ❌ S68 GPIOs配置失败: {e}")
            return False

    def oscilloscope_screenshot_fixed_frequencies(self, q68_gpio, s68_gpio_range):
        """示波器固定频率截图测试"""
        try:
            print(f"  📷 示波器固定频率截图测试...")
            
            if not self.oscilloscope:
                print(f"    ⚠️ 示波器未连接，跳过截图")
                return True
            
            osc_config = TEST_CONFIG['oscilloscope_config']
            frequency_list = osc_config['frequency_list']
            timebase_list = osc_config['timebase_list']
            
            # 第一次截图前等待插探头
            if osc_config['probe_wait_time'] > 0:
                print(f"    ⏳ 等待插探头 {osc_config['probe_wait_time']} 秒...")
                time.sleep(osc_config['probe_wait_time'])
            
            for i, (frequency, timebase) in enumerate(zip(frequency_list, timebase_list)):
                print(f"    🎯 测试频率: {frequency}Hz, 时基: {timebase}")
                
                # 配置示波器
                self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
                self.oscilloscope.Set_Digital_Trigger(digital_channel=osc_config['digital_trigger_channel'])
                self.oscilloscope.Set_Persistence_Mode(mode=osc_config['persistence_mode'])
                
                # 配置波形发生器
                self.oscilloscope.Set_Wavegen_Basic(
                    waveform=osc_config['waveform_type'],
                    frequency=frequency,
                    amplitude=osc_config['amplitude'],
                    offset=osc_config['offset'],
                    output_state='ON',
                    load=50
                )
                
                # 等待信号稳定
                time.sleep(osc_config['freq_observation_time'])
                
                # 截图
                timestamp = time.strftime('%m%d_%H%M%S')
                screenshot_filename = f"D68_GPIO{q68_gpio}_S68GPIO{min(s68_gpio_range)}-{max(s68_gpio_range)}_{frequency}Hz_{timestamp}.png"
                
                # 创建GPIO专用文件夹
                gpio_folder = f"{osc_config['screenshot_folder']}/gpio{q68_gpio}"
                os.makedirs(gpio_folder, exist_ok=True)
                
                screenshot_path = f"{gpio_folder}/{screenshot_filename}"
                
                self.oscilloscope.Save_Image(
                    filepath=screenshot_path,
                    image_format="PNG",
                    invert="OFF",
                    menu="MOF"
                )
                
                print(f"      📸 截图保存: {screenshot_path}")
                
                # 频率间隔
                if i < len(frequency_list) - 1:
                    time.sleep(1)
            
            print(f"    ✅ 固定频率截图测试完成")
            return True
            
        except Exception as e:
            print(f"    ❌ 示波器截图失败: {e}")
            return False

    def oscilloscope_screenshot_sweep_frequencies(self, q68_gpio, s68_gpio_range):
        """示波器扫频截图测试"""
        try:
            print(f"  📷 示波器扫频截图测试...")

            if not self.oscilloscope:
                print(f"    ⚠️ 示波器未连接，跳过扫频截图")
                return True

            osc_config = TEST_CONFIG['oscilloscope_config']
            freq_range = osc_config['frequency_range']
            sweep_timebase = osc_config['sweep_timebase']

            # 计算扫频参数
            start_freq = freq_range['start']
            end_freq = freq_range['end']
            step_freq = freq_range['step']

            print(f"    🌊 扫频范围: {start_freq}Hz - {end_freq}Hz, 步进: {step_freq}Hz")
            print(f"    ⏱️ 扫频时基: {sweep_timebase}")

            # 生成频率列表
            frequencies = list(range(start_freq, end_freq + step_freq, step_freq))
            print(f"    📊 扫频点数: {len(frequencies)} 个频率点")

            # 创建扫频专用子文件夹
            gpio_folder = f"{osc_config['screenshot_folder']}/gpio{q68_gpio}"
            sweep_folder = f"{gpio_folder}/sweep"
            os.makedirs(sweep_folder, exist_ok=True)

            for i, frequency in enumerate(frequencies):
                print(f"    🎯 扫频测试: {frequency}Hz ({i+1}/{len(frequencies)})")

                # 配置示波器
                self.oscilloscope.Set_Timebase_Scale(timebase_scale=sweep_timebase)
                self.oscilloscope.Set_Digital_Trigger(digital_channel=osc_config['digital_trigger_channel'])
                self.oscilloscope.Set_Persistence_Mode(mode=osc_config['persistence_mode'])

                # 配置波形发生器
                self.oscilloscope.Set_Wavegen_Basic(
                    waveform=osc_config['waveform_type'],
                    frequency=frequency,
                    amplitude=osc_config['amplitude'],
                    offset=osc_config['offset'],
                    output_state='ON',
                    load=50
                )

                # 等待信号稳定
                time.sleep(osc_config['freq_observation_time'])

                # 截图
                timestamp = time.strftime('%m%d_%H%M%S')
                screenshot_filename = f"D68_GPIO{q68_gpio}_S68GPIO{min(s68_gpio_range)}-{max(s68_gpio_range)}_SWEEP_{frequency}Hz_{timestamp}.png"
                screenshot_path = f"{sweep_folder}/{screenshot_filename}"

                self.oscilloscope.Save_Image(
                    filepath=screenshot_path,
                    image_format="PNG",
                    invert="OFF",
                    menu="MOF"
                )

                print(f"      📸 扫频截图保存: {screenshot_path}")

                # 频率间隔
                if i < len(frequencies) - 1:
                    time.sleep(0.5)  # 扫频间隔稍短

            print(f"    ✅ 扫频截图测试完成")
            return True

        except Exception as e:
            print(f"    ❌ 扫频截图失败: {e}")
            return False

    def oscilloscope_screenshot_combined(self, q68_gpio, s68_gpio_range):
        """示波器组合测试：先固定频率，后扫频"""
        try:
            print(f"  📷 示波器组合测试 (固定频率 + 扫频)...")

            if not self.oscilloscope:
                print(f"    ⚠️ 示波器未连接，跳过组合测试")
                return True

            # 阶段1: 固定频率测试
            print(f"\n  📊 阶段1: 固定频率测试")
            fixed_result = self.oscilloscope_screenshot_fixed_frequencies(q68_gpio, s68_gpio_range)

            if fixed_result:
                print(f"    ✅ 固定频率测试完成")
            else:
                print(f"    ❌ 固定频率测试失败")

            # 阶段间隔
            print(f"    ⏳ 阶段间隔，等待3秒...")
            time.sleep(3)

            # 阶段2: 扫频测试
            print(f"\n  🌊 阶段2: 扫频测试")
            sweep_result = self.oscilloscope_screenshot_sweep_frequencies(q68_gpio, s68_gpio_range)

            if sweep_result:
                print(f"    ✅ 扫频测试完成")
            else:
                print(f"    ❌ 扫频测试失败")

            # 组合结果
            overall_result = fixed_result and sweep_result

            if overall_result:
                print(f"  🎉 组合测试完全成功")
            else:
                print(f"  ⚠️ 组合测试部分失败 (固定频率: {'✅' if fixed_result else '❌'}, 扫频: {'✅' if sweep_result else '❌'})")

            return overall_result

        except Exception as e:
            print(f"  ❌ 组合测试失败: {e}")
            return False

    def test_single_gpio_d68(self, q68_gpio, s68_gpio_range, signal_id):
        """测试单个Q68 GPIO到S68 GPIOs - D68版本"""
        try:
            print(f"\n🔧 测试Q68 GPIO{q68_gpio} -> S68 GPIO{s68_gpio_range} (D68版本)...")

            # 步骤1: 设置地址转换
            print(f"  📡 步骤1: 设置地址转换...")
            if not self.setup_address_translation_for_links():
                return False

            # 步骤2: 配置Q68 GPIO
            print(f"  📤 步骤2: 配置Q68 GPIO...")
            if not self.configure_q68_gpio_d68(q68_gpio, signal_id):
                return False

            # 步骤3: 配置S68 GPIOs
            print(f"  📥 步骤3: 配置S68 GPIOs...")
            if not self.configure_s68_gpios_for_links(s68_gpio_range, signal_id):
                return False

            # 步骤4: 示波器测试 (根据配置选择测试模式)
            print(f"  📷 步骤4: 示波器测试...")
            test_mode = TEST_CONFIG['oscilloscope_config']['test_mode']

            if test_mode == 'fixed':
                print(f"    📊 测试模式: 仅固定频率")
                self.oscilloscope_screenshot_fixed_frequencies(q68_gpio, s68_gpio_range)
            elif test_mode == 'sweep':
                print(f"    🌊 测试模式: 仅扫频")
                self.oscilloscope_screenshot_sweep_frequencies(q68_gpio, s68_gpio_range)
            elif test_mode == 'combined':
                print(f"    🎯 测试模式: 组合测试 (固定频率 + 扫频)")
                self.oscilloscope_screenshot_combined(q68_gpio, s68_gpio_range)
            else:
                print(f"    ⚠️ 未知测试模式: {test_mode}，使用默认固定频率模式")
                self.oscilloscope_screenshot_fixed_frequencies(q68_gpio, s68_gpio_range)
            
            print(f"  ✅ Q68 GPIO{q68_gpio} D68测试完成")
            return True
            
        except Exception as e:
            print(f"  ❌ Q68 GPIO{q68_gpio} D68测试失败: {e}")
            return False


@pytest.mark.fast
def test_gpio_d68_auto_screenshot():
    """
    D68芯片GPIO自动截图测试 - 使用独立设备初始化
    """
    print("\n" + "="*80)
    print("🚀 D68芯片GPIO自动截图测试")
    print("📡 信号方向: D68 Q68 GPIO -> S68 GPIO")
    print("🔗 测试Links: Link0, Link2")
    print("="*80)

    # 初始化D68设备管理器
    device_manager = D68_DeviceManager()

    # 初始化D68设备
    if not device_manager.initialize_d68_devices():
        pytest.fail("D68设备初始化失败")

    # 获取设备并创建测试器
    devices = device_manager.get_devices()
    tester = GPIO_D68_S68_AutoTester(devices)

    # 测试参数
    q68_source_gpio = TEST_CONFIG['q68_source_gpio']
    s68_target_gpios = TEST_CONFIG['s68_target_gpios']
    signal_id = TEST_CONFIG['signal_id']
    active_links = TEST_CONFIG['active_links']

    print(f"📊 测试配置:")
    print(f"  - D68 Q68源GPIO: {q68_source_gpio}")
    print(f"  - S68目标GPIOs: {s68_target_gpios}")
    print(f"  - 活跃Links: {active_links}")
    print(f"  - 信号ID: {signal_id}")
    print(f"  - 示波器截图: {'启用' if TEST_CONFIG['oscilloscope_config']['enable_screenshot'] else '禁用'}")

    # 执行测试
    result = tester.test_single_gpio_d68(
        q68_gpio=q68_source_gpio,
        s68_gpio_range=s68_target_gpios,
        signal_id=signal_id
    )

    if result:
        print(f"\n✅ D68芯片GPIO测试成功完成")
    else:
        print(f"\n❌ D68芯片GPIO测试失败")

    # 验证测试结果
    assert result, "D68芯片GPIO测试失败"

    # 清理设备资源
    device_manager.cleanup()


@pytest.mark.fast
def test_gpio_d68_interactive_all_gpios():
    """
    D68芯片GPIO交互式全GPIO测试 - 使用独立设备初始化
    """
    print("\n" + "="*80)
    print("🚀 D68芯片GPIO交互式全GPIO测试")
    print("📡 信号方向: D68 Q68 GPIO -> S68 GPIO")
    print("🔗 测试Links: Link0, Link2")
    print("="*80)

    # 初始化D68设备管理器
    device_manager = D68_DeviceManager()

    # 初始化D68设备
    if not device_manager.initialize_d68_devices():
        pytest.fail("D68设备初始化失败")

    # 获取设备并创建测试器
    devices = device_manager.get_devices()
    tester = GPIO_D68_S68_AutoTester(devices)

    # 测试参数
    s68_target_gpios = TEST_CONFIG['s68_target_gpios']
    signal_id = TEST_CONFIG['signal_id']
    active_links = TEST_CONFIG['active_links']

    print(f"📊 测试配置:")
    print(f"  - S68目标GPIOs: {s68_target_gpios}")
    print(f"  - 活跃Links: {active_links}")
    print(f"  - 信号ID: {signal_id}")

    # GPIO范围 (0-10)
    gpio_range = list(range(9))
    successful_tests = 0
    failed_tests = 0

    for i, q68_gpio in enumerate(gpio_range):
        print(f"\n📍 测试进度: {i+1}/{len(gpio_range)}")
        print(f"   当前GPIO: Q68 GPIO{q68_gpio}")

        # 弹窗确认
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
                # 确保窗口更新和居中显示
        root.update_idletasks()
        root.update()

        # 获取屏幕尺寸并居中显示
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width // 2) - 200
        y = (screen_height // 2) - 100
        root.geometry(f"+{x}+{y}")

        # 再次确保置顶
        root.attributes('-topmost', True)
        root.lift()
        root.focus_force()
        
        response = messagebox.askyesnocancel(
            "D68 GPIO测试确认",
            f"是否测试 Q68 GPIO{q68_gpio} -> S68 GPIO{s68_target_gpios}?\n\n"
            f"当前进度: {i+1}/{len(gpio_range)}\n"
            f"活跃Links: {active_links}\n\n"
            f"点击:\n"
            f"• 是 - 执行测试\n"
            f"• 否 - 跳过此GPIO\n"
            f"• 取消 - 终止所有测试"
        )

        root.destroy()

        if response is None:  # 取消
            print(f"  🛑 用户取消测试")
            break
        elif response:  # 是
            print(f"  ✅ 用户确认测试 GPIO{q68_gpio}")

            result = tester.test_single_gpio_d68(
                q68_gpio=q68_gpio,
                s68_gpio_range=s68_target_gpios,
                signal_id=signal_id
            )

            if result:
                successful_tests += 1
                print(f"  ✅ GPIO{q68_gpio} 测试成功")
            else:
                failed_tests += 1
                print(f"  ❌ GPIO{q68_gpio} 测试失败")
        else:  # 否
            print(f"  ⏭️ 跳过 GPIO{q68_gpio}")
            continue

        # 测试间隔
        if i < len(gpio_range) - 1:
            time.sleep(1)

    # 测试总结
    total_attempted = successful_tests + failed_tests

    print(f"\n📊 D68 GPIO测试总结:")
    print(f"  - 尝试测试: {total_attempted}")
    print(f"  - 成功: {successful_tests}")
    print(f"  - 失败: {failed_tests}")
    if total_attempted > 0:
        print(f"  - 成功率: {successful_tests/total_attempted*100:.1f}%")

    # 验证测试结果
    assert successful_tests > 0, "没有成功的D68 GPIO测试"

    print(f"\n✅ D68芯片GPIO交互式测试完成")

    # 清理设备资源
    device_manager.cleanup()


@pytest.mark.fast
def test_gpio_d68_combined_frequency_modes():
    """
    D68芯片GPIO组合频率测试 - 演示固定频率+扫频组合模式
    """
    print("\n" + "="*80)
    print("🚀 D68芯片GPIO组合频率测试")
    print("📡 信号方向: D68 Q68 GPIO -> S68 GPIO")
    print("🎯 测试模式: 固定频率 + 扫频组合")
    print("🔗 测试Links: Link0, Link2")
    print("="*80)

    # 临时修改配置为组合模式
    original_mode = TEST_CONFIG['oscilloscope_config']['test_mode']
    original_enable = TEST_CONFIG['oscilloscope_config']['enable_screenshot']

    TEST_CONFIG['oscilloscope_config']['test_mode'] = 'combined'
    TEST_CONFIG['oscilloscope_config']['enable_screenshot'] = True

    try:
        # 初始化D68设备管理器
        device_manager = D68_DeviceManager()

        # 初始化D68设备
        if not device_manager.initialize_d68_devices():
            pytest.fail("D68设备初始化失败")

        # 获取设备并创建测试器
        devices = device_manager.get_devices()
        tester = GPIO_D68_S68_AutoTester(devices)

        # 测试参数
        q68_source_gpio = TEST_CONFIG['q68_source_gpio']
        s68_target_gpios = TEST_CONFIG['s68_target_gpios']
        signal_id = TEST_CONFIG['signal_id']

        print(f"📊 测试配置:")
        print(f"  - D68 Q68源GPIO: {q68_source_gpio}")
        print(f"  - S68目标GPIOs: {s68_target_gpios}")
        print(f"  - 信号ID: {signal_id}")
        print(f"  - 固定频率: {TEST_CONFIG['oscilloscope_config']['frequency_list']}")
        print(f"  - 扫频范围: {TEST_CONFIG['oscilloscope_config']['frequency_range']}")

        # 执行组合测试
        result = tester.test_single_gpio_d68(
            q68_gpio=q68_source_gpio,
            s68_gpio_range=s68_target_gpios,
            signal_id=signal_id
        )

        if result:
            print(f"\n✅ D68芯片GPIO组合频率测试成功完成")
        else:
            print(f"\n❌ D68芯片GPIO组合频率测试失败")

        # 验证测试结果
        assert result, "D68芯片GPIO组合频率测试失败"

        # 清理设备资源
        device_manager.cleanup()

    finally:
        # 恢复原始配置
        TEST_CONFIG['oscilloscope_config']['test_mode'] = original_mode
        TEST_CONFIG['oscilloscope_config']['enable_screenshot'] = original_enable


@pytest.mark.fast
def test_gpio_d68_sweep_only():
    """
    D68芯片GPIO扫频测试 - 仅扫频模式
    """
    print("\n" + "="*80)
    print("🚀 D68芯片GPIO扫频测试")
    print("📡 信号方向: D68 Q68 GPIO -> S68 GPIO")
    print("🌊 测试模式: 仅扫频")
    print("🔗 测试Links: Link0, Link2")
    print("="*80)

    # 临时修改配置为扫频模式
    original_mode = TEST_CONFIG['oscilloscope_config']['test_mode']
    original_enable = TEST_CONFIG['oscilloscope_config']['enable_screenshot']

    TEST_CONFIG['oscilloscope_config']['test_mode'] = 'sweep'
    TEST_CONFIG['oscilloscope_config']['enable_screenshot'] = True

    try:
        # 初始化D68设备管理器
        device_manager = D68_DeviceManager()

        # 初始化D68设备
        if not device_manager.initialize_d68_devices():
            pytest.fail("D68设备初始化失败")

        # 获取设备并创建测试器
        devices = device_manager.get_devices()
        tester = GPIO_D68_S68_AutoTester(devices)

        # 测试参数
        q68_source_gpio = TEST_CONFIG['q68_source_gpio']
        s68_target_gpios = TEST_CONFIG['s68_target_gpios']
        signal_id = TEST_CONFIG['signal_id']

        print(f"📊 测试配置:")
        print(f"  - D68 Q68源GPIO: {q68_source_gpio}")
        print(f"  - S68目标GPIOs: {s68_target_gpios}")
        print(f"  - 信号ID: {signal_id}")
        print(f"  - 扫频范围: {TEST_CONFIG['oscilloscope_config']['frequency_range']}")

        # 执行扫频测试
        result = tester.test_single_gpio_d68(
            q68_gpio=q68_source_gpio,
            s68_gpio_range=s68_target_gpios,
            signal_id=signal_id
        )

        if result:
            print(f"\n✅ D68芯片GPIO扫频测试成功完成")
        else:
            print(f"\n❌ D68芯片GPIO扫频测试失败")

        # 验证测试结果
        assert result, "D68芯片GPIO扫频测试失败"

        # 清理设备资源
        device_manager.cleanup()

    finally:
        # 恢复原始配置
        TEST_CONFIG['oscilloscope_config']['test_mode'] = original_mode
        TEST_CONFIG['oscilloscope_config']['enable_screenshot'] = original_enable


if __name__ == "__main__":
    print("D68芯片GPIO测试用例 - 支持多种频率测试模式")
    print("="*60)
    print("请使用 pytest 运行此测试文件")
    print()
    print("🔧 基础测试:")
    print("  pytest test_gpio_case5_7_d2s_auto_v2.py::test_gpio_d68_auto_screenshot")
    print("  pytest test_gpio_case5_7_d2s_auto_v2.py::test_gpio_d68_interactive_all_gpios")
    print()
    print("🎯 频率测试模式:")
    print("  pytest test_gpio_case5_7_d2s_auto_v2.py::test_gpio_d68_combined_frequency_modes")
    print("  pytest test_gpio_case5_7_d2s_auto_v2.py::test_gpio_d68_sweep_only")
    print()
    print("📊 测试模式说明:")
    print("  - fixed: 仅固定频率测试 (30Hz, 1kHz, 10kHz, 50kHz, 100kHz)")
    print("  - sweep: 仅扫频测试 (300kHz-420kHz, 10kHz步进)")
    print("  - combined: 组合测试 (先固定频率，后扫频)")
    print()
    print("⚙️ 配置修改:")
    print("  在TEST_CONFIG['oscilloscope_config']['test_mode']中设置:")
    print("  - 'fixed': 仅固定频率")
    print("  - 'sweep': 仅扫频")
    print("  - 'combined': 组合测试 (默认)")
    print()
    print("📁 截图文件夹结构:")
    print("  U-disk0/gpiotest/d68tos68_0_2/")
    print("  ├── gpio0/")
    print("  │   ├── D68_GPIO0_S68GPIO0-8_30Hz_*.png (固定频率)")
    print("  │   └── sweep/")
    print("  │       └── D68_GPIO0_S68GPIO0-8_SWEEP_300000Hz_*.png (扫频)")
    print("  └── ...")
