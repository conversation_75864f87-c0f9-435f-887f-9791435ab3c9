# -*- coding: utf-8 -*-
"""
GPIO Oscilloscope Auto Screenshot Test

基于test_gpio_case5_7.py模板，使用示波器自动截图功能测试GPIO信号。
配置数字通道触发，产生方波信号，并在不同频率下自动截图。
此版本新增全link测试
"""
import logging
import time
import pytest
import os
import tkinter as tk
from tkinter import messagebox

# 测试配置
TEST_CONFIG = {
    'active_links': [0],                    # 默认使用Link0
    'q68_source_gpio': 0,                  # Q68源GPIO
    's68_target_gpios': [0, 1, 2, 3, 4, 5, 6 , 7 ,8],          # S68目标GPIO列表
    'signal_id': 11,                        # GPIO信号ID
    'observation_time': 1,                  # 每个频率的观察时间(秒)
    
    # 示波器配置
    'oscilloscope_config': {
        'enable_screenshot': True,          # 是否启用示波器截图功能
        'use_fixed_frequencies': True,      # True=使用固定频率, False=使用扫频
        'digital_trigger_channel': 1,       # 数字通道D1作为触发源
        'waveform_type': 'SQUARE',          # 方波
        # 固定频率模式配置
        'frequency_list': [30, 1000, 10000, 50000, 100000],  # 固定频率列表 (Hz)
        'timebase_list': ['20ms', '1ms', '100us', '20us', '10us'],  # 对应的时基
        # 扫频模式配置 (保留原有功能)
        'frequency_range': {
            'start': 300000,                # 300kHz
            'end': 420000,                  # 420kHz
            'step': 10000                   # 10kHz步进
        },
        'sweep_timebase': '1us',            # 扫频模式的时基
        'amplitude': 1.8,                  # 1.8Vpp
        'offset': 0.9,                      # 900mVdc偏移
        'screenshot_folder': 'U-disk0/gpiotest/q68tos68_0_8',  # 截图保存文件夹
        'persistence_mode': 'INFinite',     # 余晖模式 (该机型只支持INFinite)
        'probe_wait_time': 5,              # 第一次截图前等待插探头时间(秒)
        'freq_observation_time': 3,         # 每个频率的观察时间(秒)
    }
}


class GPIO_Q68_S68_AutoTester:
    """Q68到S68 GPIO自动遍历测试器"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.get_link_status = devices['get_link_status']
        self.oscilloscope = devices.get('oscilloscope')

        # 获取top模块的函数
        self.power_off = devices.get('power_off')
        self.power_on = devices.get('power_on')
        self.setup_communication = devices.get('setup_communication')

    def full_system_reinit(self, link_id, q68_gpio, s68_gpio):
        """完整的系统重新初始化 - 模拟断电重新开机流程"""
        print(f"\n🔄 开始完整系统重新初始化...")
        print(f"   目标: Link{link_id} - Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio}")

        try:
            # 步骤1: 断电
            print(f"  🔌 步骤1: 系统断电...")
            if self.power_off:
                self.power_off()
                print(f"    - 断电完成")
            else:
                print(f"    - 警告: power_off函数不可用，跳过断电")

            # 等待断电稳定
            print(f"  ⏳ 等待3秒断电稳定...")
            time.sleep(3)

            # 步骤2: 重新上电
            print(f"  ⚡ 步骤2: 系统重新上电...")
            if self.power_on:
                self.power_on()
                print(f"    - 上电完成")
            else:
                print(f"    - 警告: power_on函数不可用，跳过上电")

            # 等待上电稳定
            print(f"  ⏳ 等待5秒上电稳定...")
            time.sleep(5)

            # 步骤3: 重新建立通信
            print(f"  📡 步骤3: 重新建立Q68-S68通信...")
            if self.setup_communication:
                self.setup_communication()
                print(f"    - 通信建立完成")
            else:
                print(f"    - 警告: setup_communication函数不可用，跳过通信建立")

            # 等待通信稳定
            print(f"  ⏳ 等待2秒通信稳定...")
            time.sleep(2)

            # 步骤4: 重新配置指定Link
            print(f"  🔗 步骤4: 重新配置Link{link_id}...")
            link_status = self.configure_links([link_id])
            print(f"    - Link{link_id}配置完成: {link_status}")

            print(f"✅ 系统重新初始化完成，准备测试Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio}")
            return True

        except Exception as e:
            print(f"❌ 系统重新初始化失败: {e}")
            return False

    def show_gpio_confirmation_dialog(self, link_id, q68_gpio):
        """显示Q68 GPIO测试确认对话框 (会同时测试所有S68 GPIO 0-8)"""
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            root.attributes('-topmost', True)  # 置顶显示

            # 显示确认对话框
            title = f"Q68 GPIO测试确认 - Link{link_id}"
            message = f"""即将测试:
Link{link_id} - Q68 GPIO{q68_gpio} → S68 GPIO0-8 (同时测试所有S68 GPIO)

⚠️ 注意：每个Q68 GPIO测试前会执行完整的系统重新初始化
包括：断电 → 重新上电 → 重建通信 → 重新配置Link

预计耗时：约15-20秒 (初始化) + 测试时间

是否继续测试此Q68 GPIO?

[是] = 继续测试
[否] = 跳过此Q68 GPIO
[取消] = 停止所有测试"""

            result = messagebox.askyesnocancel(
                title=title,
                message=message,
                icon='question'
            )

            root.destroy()  # 销毁窗口

            # 返回结果：True=继续, False=跳过, None=取消全部
            return result

        except Exception as e:
            print(f"    - 弹窗显示失败: {e}")
            print(f"    - 自动继续测试...")
            return True  # 如果弹窗失败，默认继续

    def show_all_links_gpio_confirmation_dialog(self, q68_gpio):
        """显示全Link Q68 GPIO测试确认对话框 (会同时测试所有Links上的所有S68 GPIO 0-8)"""
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            root.attributes('-topmost', True)  # 置顶显示

            # 显示确认对话框
            title = f"全Link Q68 GPIO测试确认"
            message = f"""即将测试:
所有Links(0-3) - Q68 GPIO{q68_gpio} → S68 GPIO0-8 (同时测试所有Links上的所有S68 GPIO)

⚠️ 注意：每个Q68 GPIO测试前会执行完整的系统重新初始化
包括：断电 → 重新上电 → 重建通信 → 重新配置所有Links

预计耗时：约20-25秒 (初始化) + 测试时间

是否继续测试此Q68 GPIO?

[是] = 继续测试
[否] = 跳过此Q68 GPIO
[取消] = 停止所有测试"""

            result = messagebox.askyesnocancel(
                title=title,
                message=message,
                icon='question'
            )

            root.destroy()  # 销毁窗口

            # 返回结果：True=继续, False=跳过, None=取消全部
            return result

        except Exception as e:
            print(f"    - 弹窗显示失败: {e}")
            print(f"    - 自动继续测试...")
            return True  # 如果弹窗失败，默认继续

    def full_system_reinit_all_links(self, q68_gpio, s68_gpio):
        """完整的系统重新初始化 - 模拟断电重新开机流程 (针对所有Links)"""
        print(f"\n🔄 开始完整系统重新初始化...")
        print(f"   目标: 所有Links(0-3) - Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio}")

        try:
            # 步骤1: 断电
            print(f"  🔌 步骤1: 系统断电...")
            if self.power_off:
                self.power_off()
                print(f"    - 断电完成")
            else:
                print(f"    - 警告: power_off函数不可用，跳过断电")

            # 等待断电稳定
            print(f"  ⏳ 等待3秒断电稳定...")
            time.sleep(3)

            # 步骤2: 重新上电
            print(f"  ⚡ 步骤2: 系统重新上电...")
            if self.power_on:
                self.power_on()
                print(f"    - 上电完成")
            else:
                print(f"    - 警告: power_on函数不可用，跳过上电")

            # 等待上电稳定
            print(f"  ⏳ 等待5秒上电稳定...")
            time.sleep(5)

            # 步骤3: 重新建立通信
            print(f"  📡 步骤3: 重新建立Q68-S68通信...")
            if self.setup_communication:
                self.setup_communication()
                print(f"    - 通信建立完成")
            else:
                print(f"    - 警告: setup_communication函数不可用，跳过通信建立")

            # 等待通信稳定
            print(f"  ⏳ 等待2秒通信稳定...")
            time.sleep(2)

            # 步骤4: 重新配置所有Links
            print(f"  🔗 步骤4: 重新配置所有Links(0-3)...")
            all_links = [0, 1, 2, 3]
            link_status = self.configure_links(all_links)
            print(f"    - 所有Links配置完成: {link_status}")

            print(f"✅ 系统重新初始化完成，准备测试Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio}")
            return True

        except Exception as e:
            print(f"❌ 系统重新初始化失败: {e}")
            return False

    def oscilloscope_frequency_sweep_screenshot(self, link_id, q68_gpio):
        """示波器频率扫描截图功能 - 支持固定频率和扫频两种模式"""
        osc_config = TEST_CONFIG['oscilloscope_config']

        if not osc_config['enable_screenshot'] or self.oscilloscope is None:
            if not osc_config['enable_screenshot']:
                print("  - 示波器自动截图已禁用")
            elif self.oscilloscope is None:
                print("  - 示波器不可用")
            return

        try:
            mode = "固定频率" if osc_config['use_fixed_frequencies'] else "扫频"
            print(f"  - 开始示波器{mode}截图...")

            # 设置数字通道触发
            trigger_source = self.oscilloscope.Set_Digital_Trigger(digital_channel=osc_config['digital_trigger_channel'])
            print(f"    * 数字触发设置: {trigger_source.strip()}")

            # 设置余晖模式
            persistence_time = self.oscilloscope.Set_Display_Persistence(time=osc_config['persistence_mode'])
            print(f"    * 余晖模式设置: {persistence_time.strip()}")

            # 创建截图保存目录
            screenshot_folder = osc_config['screenshot_folder']
            os.makedirs(screenshot_folder, exist_ok=True)

            # 设置初始波形参数
            waveform = osc_config['waveform_type']
            amplitude = osc_config['amplitude']
            offset = osc_config['offset']

            if osc_config['use_fixed_frequencies']:
                # 固定频率模式
                frequency_list = osc_config['frequency_list']
                timebase_list = osc_config['timebase_list']

                print(f"    * 设置初始波形参数...")
                self.oscilloscope.Set_Wavegen_Basic(
                    waveform=waveform,
                    frequency=frequency_list[0] + 50000,  # 临时频率
                    amplitude=amplitude,
                    offset=offset,
                    output_state='OFF',  # 初始不开启输出
                    load=50
                )
                print(f"      - 波形类型: {waveform}, 幅值: {amplitude}Vpp, 偏移: {offset}Vdc")

                # 第一次截图前等待插探头
                probe_wait_time = osc_config['probe_wait_time']
                print(f"    * 等待{probe_wait_time}秒供您插探头...")
                time.sleep(probe_wait_time)

                print(f"    * 开始固定频率测试: {len(frequency_list)}个频率点")

                for i, (freq_hz, timebase) in enumerate(zip(frequency_list, timebase_list)):
                    freq_display = f"{freq_hz}Hz" if freq_hz < 1000 else f"{freq_hz/1000:.0f}kHz"
                    print(f"      -> 测试频率: {freq_display}, 时基: {timebase} [{i+1}/{len(frequency_list)}]")

                    # 设置时基
                    current_timebase = self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
                    print(f"        - 时基设置: {timebase} ({current_timebase:.2e}s/div)")

                    # 第一个频率的特殊处理
                    if i == 0:
                        print(f"        - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 临时频率输出已开启")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=max(freq_hz-10000, 10))
                        print(f"        - 准备切换到目标频率")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率切换完成: {freq_display}")
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)
                    else:
                        # 非第一个频率的正常处理流程
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 输出已开启")
                        time.sleep(0.5)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率设置完成: {freq_display}")
                        time.sleep(0.3)
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)

                    # 等待信号稳定和余晖累积
                    print(f"        - 等待{osc_config['freq_observation_time']}秒进行余晖观察...")
                    time.sleep(osc_config['freq_observation_time'])

                    # 自动截图
                    timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒
                    screenshot_filename = f"{i+1:02d}_Link{link_id}_Q68GPIO{q68_gpio}_{freq_display}_{timebase}_{timestamp}.png"
                    screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

                    self.oscilloscope.Save_Image(
                        filepath=screenshot_path,
                        image_format="PNG",
                        invert="OFF",      # 正常色彩
                        menu="MOF"         # 隐藏菜单
                    )
                    print(f"        - 截图已保存: {screenshot_filename}")
                    time.sleep(1)

                print(f"    * 固定频率测试完成，共测试 {len(frequency_list)} 个频率点")

            else:
                # 扫频模式 (保留原有功能)
                freq_config = osc_config['frequency_range']
                timebase = osc_config['sweep_timebase']

                # 设置时基
                current_timebase = self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
                print(f"    * 时基设置: {timebase} ({current_timebase:.2e}s/div)")

                print(f"    * 设置初始波形参数...")
                target_first_freq = freq_config['start']
                temp_freq = target_first_freq + 50000  # 临时频率

                self.oscilloscope.Set_Wavegen_Basic(
                    waveform=waveform,
                    frequency=temp_freq,  # 先设置临时频率
                    amplitude=amplitude,
                    offset=offset,
                    output_state='OFF',  # 初始不开启输出
                    load=50
                )
                print(f"      - 波形类型: {waveform}, 幅值: {amplitude}Vpp, 偏移: {offset}Vdc")
                print(f"      - 临时频率: {temp_freq/1000}kHz (为第一个目标频率{target_first_freq/1000}kHz做准备)")

                # 第一次截图前等待插探头
                probe_wait_time = osc_config['probe_wait_time']
                print(f"    * 等待{probe_wait_time}秒供您插探头...")
                time.sleep(probe_wait_time)

                # 频率扫描
                frequency_list = range(freq_config['start'], freq_config['end'] + freq_config['step'], freq_config['step'])
                print(f"    * 开始频率扫描: {freq_config['start']/1000}-{freq_config['end']/1000}kHz, 步进{freq_config['step']/1000}kHz")

                for i, freq_hz in enumerate(frequency_list):
                    freq_khz = freq_hz / 1000
                    print(f"      -> 测试频率: {freq_khz}kHz [{i+1}/{len(frequency_list)}]")

                    # 第一个频率的特殊处理
                    if i == 0:
                        print(f"        - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 临时频率输出已开启")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz-10000)
                        print(f"        - 准备切换到目标频率")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率切换完成: {freq_khz}kHz")
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)
                    else:
                        # 非第一个频率的正常处理流程
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 输出已开启")
                        time.sleep(0.5)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率设置完成: {freq_khz}kHz")
                        time.sleep(0.3)
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)

                    # 等待信号稳定和余晖累积
                    print(f"        - 等待{osc_config['freq_observation_time']}秒进行余晖观察...")
                    time.sleep(osc_config['freq_observation_time'])

                    # 自动截图
                    timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒
                    screenshot_filename = f"{i+1:02d}_Link{link_id}_Q68GPIO{q68_gpio}_{freq_khz}kHz_{timestamp}.png"
                    screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

                    self.oscilloscope.Save_Image(
                        filepath=screenshot_path,
                        image_format="PNG",
                        invert="OFF",      # 正常色彩
                        menu="MOF"         # 隐藏菜单
                    )
                    print(f"        - 截图已保存: {screenshot_filename}")
                    time.sleep(1)

                print(f"    * 频率扫描完成，共测试 {len(frequency_list)} 个频率点")

        except Exception as e:
            print(f"    - 示波器截图失败: {e}")
            print(f"    - 继续原有测试流程...")

    def test_single_q68_gpio_to_all_s68_gpios(self, link_id, q68_gpio, s68_gpio_range, signal_id, duration=3):
        """测试单个Q68 GPIO到所有S68 GPIO的信号传输"""
        print(f"\n🔗 测试 Link{link_id}: Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")

        try:
            # 步骤1: 配置Link
            print(f"  步骤1: 配置Link{link_id}...")
            link_status = self.configure_links([link_id])
            if not link_status:
                print(f"    ❌ Link{link_id}配置失败")
                return False
            print(f"    ✅ Link{link_id}配置成功")

            # 步骤2: 配置Q68发送端
            print(f"  步骤2: 配置Q68 GPIO{q68_gpio}为发送端...")

            # 检查是否为GPIO15/16，需要特殊配置
            if q68_gpio in [15, 16]:
                print(f"    ⚠️  检测到Q68 GPIO{q68_gpio} (I2C1引脚)，应用特殊配置...")
                i2c_bus_config = 1
                print(f"    - 自动设置 i2c_bus = 1 (用于GPIO{q68_gpio})")

                # 设置地址转换 (使用i2c_bus=1)
                self.q68_remote.S68_AddrTrans(
                    link=link_id,
                    q68_iic_addr=0x73,
                    s68_iic_addr=0x40,
                    s68_retrans_addr=self.s68_res_dev[link_id],
                    sensor_addr=0x24,
                    sensor_retrans_addr=0x24 + link_id,
                    i2c_bus=i2c_bus_config
                )
                print(f"    - Link{link_id} 地址转换设置: i2c_bus={i2c_bus_config}")
            else:
                i2c_bus_config = 0
                print(f"    - 使用标准GPIO{q68_gpio}配置 (i2c_bus = 0)")

                # 设置地址转换 (使用i2c_bus=0)
                self.q68_remote.S68_AddrTrans(
                    link=link_id,
                    q68_iic_addr=0x73,
                    s68_iic_addr=0x40,
                    s68_retrans_addr=self.s68_res_dev[link_id],
                    sensor_addr=0x24,
                    sensor_retrans_addr=0x24 + link_id,
                    i2c_bus=i2c_bus_config
                )
                print(f"    - Link{link_id} 地址转换设置: i2c_bus={i2c_bus_config}")

            # Q68 GPIO配置
            self.q68.MFNSet(gpio=q68_gpio, mfn=0)
            self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id)
            print(f"    ✅ Q68 GPIO{q68_gpio} 发送端配置完成 (信号ID: {signal_id})")

            # 步骤3: 配置所有S68 GPIO为接收端
            print(f"  步骤3: 配置S68 GPIO{s68_gpio_range}为接收端...")

            # 设置设备地址
            target_addr = self.s68_res_dev[link_id]
            self.q68_remote.dongle.devAddr = target_addr
            print(f"    - 设备地址已设置为: 0x{target_addr:02X}")

            # 等待一小段时间确保地址设置生效
            time.sleep(0.1)

            for s68_gpio in s68_gpio_range:
                print(f"    - 配置S68 GPIO{s68_gpio}...")

                # 添加错误处理机制
                try:
                    # 设置MFN为0 (GPIO功能)
                    self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                    print(f"      ✅ S68 GPIO{s68_gpio} MFN设置为0 (GPIO功能)")

                    # 设置远程接收
                    self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
                    print(f"      ✅ S68 GPIO{s68_gpio} 远程接收设置完成 (信号ID: {signal_id})")

                except Exception as e:
                    print(f"      ❌ S68 GPIO{s68_gpio} 配置失败: {e}")
                    # 继续配置其他GPIO，不中断整个测试

            print(f"    ✅ 所有S68 GPIO{s68_gpio_range} 接收端配置完成")

            # 步骤4: 等待和验证 + 示波器自动截图
            print(f"  步骤4: 等待{duration}秒进行信号传输...")

            # 示波器频率扫描截图功能 (可选)
            self.oscilloscope_frequency_sweep_screenshot(link_id, q68_gpio)

            # 如果没有示波器或截图禁用，则执行原有的等待逻辑
            osc_config = TEST_CONFIG['oscilloscope_config']
            if not osc_config['enable_screenshot'] or self.oscilloscope is None:
                time.sleep(duration)

            # 步骤5: 验证结果
            print(f"  步骤5: 验证Link{link_id}状态...")
            final_status = self.get_link_status()
            print(f"    - 最终Link状态: {final_status}")

            print(f"✅ 测试完成: Link{link_id} Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")
            return True

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

    def test_single_q68_gpio_to_all_links_all_s68_gpios(self, q68_gpio, s68_gpio_range, all_links, signal_id, duration=3):
        """测试单个Q68 GPIO到所有Links上所有S68 GPIO的信号传输"""
        print(f"\n🔗 测试 所有Links{all_links}: Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")

        try:
            # 步骤1: 配置所有Links
            print(f"  步骤1: 配置所有Links{all_links}...")
            link_status = self.configure_links(all_links)
            if not link_status:
                print(f"    ❌ Links配置失败")
                return False
            print(f"    ✅ 所有Links配置成功")

            # 步骤2: 配置Q68发送端
            print(f"  步骤2: 配置Q68 GPIO{q68_gpio}为发送端...")

            # 检查是否为GPIO15/16，需要特殊配置
            if q68_gpio in [15, 16]:
                print(f"    ⚠️  检测到Q68 GPIO{q68_gpio} (I2C1引脚)，应用特殊配置...")
                i2c_bus_config = 1
                print(f"    - 自动设置 i2c_bus = 1 (用于GPIO{q68_gpio})")
            else:
                i2c_bus_config = 0
                print(f"    - 使用标准GPIO{q68_gpio}配置 (i2c_bus = 0)")

            # 为所有Links设置地址转换
            print(f"  步骤2.5: 为所有Links设置地址转换...")
            for link_id in all_links:
                if link_id < len(self.s68_res_dev):
                    self.q68_remote.S68_AddrTrans(
                        link=link_id,
                        q68_iic_addr=0x73,
                        s68_iic_addr=0x40,
                        s68_retrans_addr=self.s68_res_dev[link_id],
                        sensor_addr=0x24,
                        sensor_retrans_addr=0x24 + link_id,
                        i2c_bus=i2c_bus_config
                    )
                    print(f"    - Link{link_id} 地址转换设置: i2c_bus={i2c_bus_config}")

            # Q68 GPIO配置
            self.q68.MFNSet(gpio=q68_gpio, mfn=0)
            # 使用第一个Link作为发送Link
            primary_link = all_links[0]
            self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id, link_id=primary_link, dly_comp_en=0)
            print(f"    ✅ Q68 GPIO{q68_gpio} 发送端配置完成 (信号ID: {signal_id}, 主Link: {primary_link})")

            # 步骤3: 配置所有Links上的所有S68 GPIO为接收端
            print(f"  步骤3: 配置所有Links上的S68 GPIO{s68_gpio_range}为接收端...")

            for link_id in all_links:
                if link_id < len(self.s68_res_dev):
                    target_addr = self.s68_res_dev[link_id]
                    print(f"    - 配置Link{link_id} S68 (地址: 0x{target_addr:02X})...")

                    # 设置设备地址
                    self.q68_remote.dongle.devAddr = target_addr
                    print(f"      * 设备地址已设置为: 0x{target_addr:02X}")

                    # 等待一小段时间确保地址设置生效
                    time.sleep(0.1)

                    for s68_gpio in s68_gpio_range:
                        print(f"      * 配置GPIO{s68_gpio}...")

                        # 添加错误处理机制
                        try:
                            # 设置MFN为0 (GPIO功能)
                            self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                            print(f"        ✅ Link{link_id} S68 GPIO{s68_gpio} MFN设置为0 (GPIO功能)")

                            # 设置远程接收
                            self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
                            print(f"        ✅ Link{link_id} S68 GPIO{s68_gpio} 远程接收设置完成 (信号ID: {signal_id})")

                        except Exception as e:
                            print(f"        ❌ Link{link_id} S68 GPIO{s68_gpio} 配置失败: {e}")
                            # 继续配置其他GPIO，不中断整个测试
                else:
                    print(f"    - 警告: Link{link_id} 超出s68_res_dev范围，跳过")

            print(f"    ✅ 所有Links上的S68 GPIO{s68_gpio_range} 接收端配置完成")

            # 步骤4: 等待和验证 + 示波器自动截图
            print(f"  步骤4: 等待{duration}秒进行信号传输...")

            # 示波器频率扫描截图功能 (可选) - 使用第一个Link进行截图
            self.oscilloscope_frequency_sweep_screenshot(all_links[0], q68_gpio)

            # 如果没有示波器或截图禁用，则执行原有的等待逻辑
            osc_config = TEST_CONFIG['oscilloscope_config']
            if not osc_config['enable_screenshot'] or self.oscilloscope is None:
                time.sleep(duration)

            # 步骤5: 验证结果
            print(f"  步骤5: 验证所有Links状态...")
            final_status = self.get_link_status()
            print(f"    - 最终Links状态: {final_status}")

            print(f"✅ 测试完成: 所有Links{all_links} Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")
            return True

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

    def test_single_link_all_q68_gpios(self, q68_gpio_range=None, s68_gpio_range=None, link_id=0, enable_dialog=True):
        """自动遍历单个Link的所有Q68 GPIO进行测试 (每个Q68 GPIO同时测试所有S68 GPIO)

        Args:
            q68_gpio_range: Q68 GPIO范围，默认[0,1,2,3,4,5,6,7,8,9,10,11,12,14,15,16]
            s68_gpio_range: S68 GPIO范围，默认[0,1,2,3,4,5,6,7,8]
            link_id: 测试的Link ID，默认0
            enable_dialog: 是否启用确认对话框
        """
        if q68_gpio_range is None:
            # 默认测试Q68 GPIO 0-16，排除GPIO13 (GPIO13通常用于特殊用途)
            q68_gpio_range = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16]

        if s68_gpio_range is None:
            s68_gpio_range = [0, 1, 2, 3, 4, 5, 6, 7, 8]

        print(f"\n{'='*80}")
        print(f"开始单Link Q68→S68 GPIO自动遍历测试:")
        print(f"  - Link: {link_id}")
        print(f"  - Q68 GPIOs: {q68_gpio_range}")
        print(f"  - S68 GPIOs: {s68_gpio_range} (每个Q68 GPIO同时测试所有S68 GPIO)")
        print(f"  - 确认对话框: {'启用' if enable_dialog else '禁用'}")

        # 计算总测试数量 (每个Q68 GPIO测试一次，同时配置所有S68 GPIO)
        total_tests = len(q68_gpio_range)
        print(f"  - 总测试数量: {len(q68_gpio_range)} Q68 GPIOs × 1 (同时测试所有S68 GPIO) = {total_tests} 个测试")
        print(f"{'='*80}")

        results = {}
        current_test = 0
        skipped_tests = []
        failed_tests = []

        print(f"\n🔗 开始测试 Link{link_id}")
        print(f"{'='*60}")

        for q68_gpio in q68_gpio_range:
            current_test += 1
            test_key = f"Link{link_id}_Q68GPIO{q68_gpio}_S68GPIO{s68_gpio_range}"

            print(f"\n📍 测试进度: {current_test}/{total_tests}")
            print(f"当前测试: Link{link_id} - Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")

            # 显示确认对话框 (如果启用)
            if enable_dialog:
                dialog_result = self.show_gpio_confirmation_dialog(link_id, q68_gpio)

                if dialog_result is None:  # 用户点击取消
                    print(f"❌ 用户取消测试，停止所有后续测试")
                    results[test_key] = "cancelled"
                    return {
                        'results': results,
                        'summary': {
                            'total': current_test - 1,
                            'completed': len([r for r in results.values() if r == True]),
                            'failed': len([r for r in results.values() if r == False]),
                            'skipped': len(skipped_tests),
                            'cancelled': True
                        }
                    }

                elif dialog_result is False:  # 用户选择跳过
                    print(f"⏭️  跳过测试: {test_key}")
                    skipped_tests.append(test_key)
                    results[test_key] = "skipped"
                    continue

            # 执行完整的系统重新初始化
            print(f"🔄 执行完整系统重新初始化 (模拟断电重新开机)...")
            reinit_success = self.full_system_reinit(link_id, q68_gpio, f"GPIO{s68_gpio_range}")

            if not reinit_success:
                print(f"❌ 系统重新初始化失败，跳过此Q68 GPIO测试")
                results[test_key] = False
                failed_tests.append(test_key)
                continue

            # 执行测试
            try:
                print(f"🚀 开始执行Q68 GPIO{q68_gpio}测试...")
                result = self.test_single_q68_gpio_to_all_s68_gpios(
                    link_id=link_id,
                    q68_gpio=q68_gpio,
                    s68_gpio_range=s68_gpio_range,
                    signal_id=11 + link_id,  # 每个Link使用不同的信号ID
                    duration=2  # 缩短单个测试时间
                )

                results[test_key] = result

                if result:
                    print(f"✅ 测试通过: {test_key}")
                else:
                    print(f"❌ 测试失败: {test_key}")
                    failed_tests.append(test_key)

            except Exception as e:
                print(f"💥 测试异常: {test_key} - {e}")
                results[test_key] = False
                failed_tests.append(test_key)

            # 测试间隔
            if current_test < total_tests:
                print(f"⏳ 等待1秒后进行下一个测试...")
                time.sleep(1)

        # 最终总结
        print(f"\n{'='*80}")
        print(f"🎯 单Link Q68→S68 GPIO自动遍历测试完成!")

        completed_tests = [r for r in results.values() if r in [True, False]]
        passed_tests = [r for r in results.values() if r == True]

        summary = {
            'total': total_tests,
            'completed': len(completed_tests),
            'passed': len(passed_tests),
            'failed': len(failed_tests),
            'skipped': len(skipped_tests),
            'cancelled': False
        }

        print(f"📈 测试统计:")
        print(f"  - 总计划测试: {summary['total']}")
        print(f"  - 实际完成: {summary['completed']}")
        print(f"  - 测试通过: {summary['passed']}")
        print(f"  - 测试失败: {summary['failed']}")
        print(f"  - 跳过测试: {summary['skipped']}")

        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test}")

        if skipped_tests:
            print(f"\n⏭️  跳过的测试:")
            for test in skipped_tests:
                print(f"  - {test}")

        print(f"{'='*80}")

        return {
            'results': results,
            'summary': summary,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests
        }

    def test_all_links_all_q68_gpios(self, q68_gpio_range=None, s68_gpio_range=None, enable_dialog=True):
        """自动遍历所有Links的所有Q68 GPIO进行测试 (每个Q68 GPIO同时测试所有Links上的所有S68 GPIO)

        Args:
            q68_gpio_range: Q68 GPIO范围，默认[0,1,2,3,4,5,6,7,8,9,10,11,12,14,15,16]
            s68_gpio_range: S68 GPIO范围，默认[0,1,2,3,4,5,6,7,8]
            enable_dialog: 是否启用确认对话框
        """
        if q68_gpio_range is None:
            # 默认测试Q68 GPIO 0-16，排除GPIO13 (GPIO13通常用于特殊用途)
            q68_gpio_range = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16]

        if s68_gpio_range is None:
            s68_gpio_range = [0, 1, 2, 3, 4, 5, 6, 7, 8]

        all_links = [0, 1, 2, 3]  # 所有4个Link

        print(f"\n{'='*80}")
        print(f"开始全Link Q68→S68 GPIO自动遍历测试:")
        print(f"  - Links: {all_links} (同时测试所有4个Link)")
        print(f"  - Q68 GPIOs: {q68_gpio_range}")
        print(f"  - S68 GPIOs: {s68_gpio_range} (每个Q68 GPIO同时测试所有Links上的所有S68 GPIO)")
        print(f"  - 确认对话框: {'启用' if enable_dialog else '禁用'}")

        # 计算总测试数量 (每个Q68 GPIO测试一次，同时配置所有Links上的所有S68 GPIO)
        total_tests = len(q68_gpio_range)
        total_gpio_configs = len(q68_gpio_range) * len(all_links) * len(s68_gpio_range)
        print(f"  - 总测试数量: {len(q68_gpio_range)} Q68 GPIOs × 1 (同时测试所有Links) = {total_tests} 个测试")
        print(f"  - 总GPIO配置数: {len(q68_gpio_range)} Q68 × {len(all_links)} Links × {len(s68_gpio_range)} S68 = {total_gpio_configs} 个配置")
        print(f"{'='*80}")

        results = {}
        current_test = 0
        skipped_tests = []
        failed_tests = []

        print(f"\n🔗 开始测试所有Links {all_links}")
        print(f"{'='*60}")

        for q68_gpio in q68_gpio_range:
            current_test += 1
            test_key = f"AllLinks_Q68GPIO{q68_gpio}_S68GPIO{s68_gpio_range}"

            print(f"\n📍 测试进度: {current_test}/{total_tests}")
            print(f"当前测试: 所有Links - Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")

            # 显示确认对话框 (如果启用)
            if enable_dialog:
                dialog_result = self.show_all_links_gpio_confirmation_dialog(q68_gpio)

                if dialog_result is None:  # 用户点击取消
                    print(f"❌ 用户取消测试，停止所有后续测试")
                    results[test_key] = "cancelled"
                    return {
                        'results': results,
                        'summary': {
                            'total': current_test - 1,
                            'completed': len([r for r in results.values() if r == True]),
                            'failed': len([r for r in results.values() if r == False]),
                            'skipped': len(skipped_tests),
                            'cancelled': True
                        }
                    }

                elif dialog_result is False:  # 用户选择跳过
                    print(f"⏭️  跳过测试: {test_key}")
                    skipped_tests.append(test_key)
                    results[test_key] = "skipped"
                    continue

            # 执行完整的系统重新初始化
            print(f"🔄 执行完整系统重新初始化 (模拟断电重新开机)...")
            reinit_success = self.full_system_reinit_all_links(q68_gpio, f"GPIO{s68_gpio_range}")

            if not reinit_success:
                print(f"❌ 系统重新初始化失败，跳过此Q68 GPIO测试")
                results[test_key] = False
                failed_tests.append(test_key)
                continue

            # 执行测试
            try:
                print(f"🚀 开始执行Q68 GPIO{q68_gpio}全Link测试...")
                result = self.test_single_q68_gpio_to_all_links_all_s68_gpios(
                    q68_gpio=q68_gpio,
                    s68_gpio_range=s68_gpio_range,
                    all_links=all_links,
                    signal_id=11,  # 使用固定信号ID
                    duration=2  # 缩短单个测试时间
                )

                results[test_key] = result

                if result:
                    print(f"✅ 测试通过: {test_key}")
                else:
                    print(f"❌ 测试失败: {test_key}")
                    failed_tests.append(test_key)

            except Exception as e:
                print(f"💥 测试异常: {test_key} - {e}")
                results[test_key] = False
                failed_tests.append(test_key)

            # 测试间隔
            if current_test < total_tests:
                print(f"⏳ 等待1秒后进行下一个测试...")
                time.sleep(1)

        # 最终总结
        print(f"\n{'='*80}")
        print(f"🎯 全Link Q68→S68 GPIO自动遍历测试完成!")

        completed_tests = [r for r in results.values() if r in [True, False]]
        passed_tests = [r for r in results.values() if r == True]

        summary = {
            'total': total_tests,
            'completed': len(completed_tests),
            'passed': len(passed_tests),
            'failed': len(failed_tests),
            'skipped': len(skipped_tests),
            'cancelled': False
        }

        print(f"📈 测试统计:")
        print(f"  - 总计划测试: {summary['total']}")
        print(f"  - 实际完成: {summary['completed']}")
        print(f"  - 测试通过: {summary['passed']}")
        print(f"  - 测试失败: {summary['failed']}")
        print(f"  - 跳过测试: {summary['skipped']}")

        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test}")

        if skipped_tests:
            print(f"\n⏭️  跳过的测试:")
            for test in skipped_tests:
                print(f"  - {test}")

        print(f"{'='*80}")

        return {
            'results': results,
            'summary': summary,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests
        }


@pytest.mark.fast
def test_gpio_q68_s68_auto_traverse_all_links(devices):
    """自动遍历测试所有Links的所有Q68 GPIO (每个Q68 GPIO同时测试所有Links上的所有S68 GPIO 0-8)"""
    tester = GPIO_Q68_S68_AutoTester(devices)

    print(f"\n🚀 开始全Link Q68→S68 GPIO自动遍历测试")
    print(f"📱 每个Q68 GPIO测试前会弹窗确认")
    print(f"🔄 每个Q68 GPIO会同时测试所有Links(0-3)上的所有S68 GPIO 0-8")

    # 测试所有Links的所有Q68 GPIO
    result = tester.test_all_links_all_q68_gpios(
        q68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16],  # 排除GPIO13
        s68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8],
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ 全Link Q68→S68 GPIO自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_gpio_q68_s68_auto_traverse_single_link(devices):
    """自动遍历测试单个Link的所有Q68 GPIO (每个Q68 GPIO同时测试所有S68 GPIO 0-8)"""
    tester = GPIO_Q68_S68_AutoTester(devices)

    print(f"\n🚀 开始单Link Q68→S68 GPIO自动遍历测试")
    print(f"📱 每个Q68 GPIO测试前会弹窗确认")
    print(f"🔄 每个Q68 GPIO会同时测试所有S68 GPIO 0-8")

    # 测试Link0的所有Q68 GPIO
    result = tester.test_single_link_all_q68_gpios(
        q68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16],  # 排除GPIO13
        s68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8],
        link_id=0,  # 测试Link0
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ 单Link Q68→S68 GPIO自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


# @pytest.mark.slow
# def test_gpio_q68_s68_auto_traverse_no_dialog(devices):
#     """自动遍历测试单个Link的所有Q68 GPIO (无确认对话框，快速模式)"""
#     tester = GPIO_Q68_S68_AutoTester(devices)

#     print(f"\n🚀 开始单Link Q68→S68 GPIO快速自动遍历测试")
#     print(f"⚡ 快速模式：无确认对话框，自动执行所有测试")
#     print(f"🔄 每个Q68 GPIO会同时测试所有S68 GPIO 0-8")

#     # 测试Link0的所有Q68 GPIO (无对话框)
#     result = tester.test_single_link_all_q68_gpios(
#         q68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16],  # 排除GPIO13
#         s68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8],
#         link_id=0,  # 测试Link0
#         enable_dialog=False  # 禁用确认对话框
#     )

#     # 验证至少有一些测试通过
#     summary = result['summary']
#     assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

#     print(f"✅ 单Link Q68→S68 GPIO快速自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


# @pytest.mark.fast
# def test_gpio_oscilloscope_auto_screenshot(devices):
#     """
#     GPIO示波器自动截图测试
    
#     测试步骤:
#     1. 初始化示波器并配置数字触发、余晖模式和时基
#     2. 配置Q68 GPIO为发送端
#     3. 配置S68 GPIOs为接收端
#     4. 在指定频率范围内扫描，每个频率点:
#        - 清除前一频率的扫描并重新开始采集
#        - 设置新的波形参数
#        - 等待余晖累积
#        - 自动截图保存
#     5. 验证链路状态
#     """
#     print(f"\n[GPIO示波器测试] 开始执行: 自动截图功能测试")
#     print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")
#     print(f"频率范围: {TEST_CONFIG['oscilloscope_config']['frequency_range']['start']/1000}kHz - {TEST_CONFIG['oscilloscope_config']['frequency_range']['end']/1000}kHz")
    
#     # 获取设备对象和配置函数
#     q68 = devices['q68']
#     q68_remote = devices['q68_remote']
#     s68_res_dev = devices['s68_res_dev']
#     configure_links = devices['configure_links']
#     oscilloscope = devices['oscilloscope']  # 从devices获取示波器对象

#     # ---------------------------------------------------------------------
#     # 步骤0: 检查示波器可用性并配置
#     # ---------------------------------------------------------------------
#     print(f"\n步骤0: 检查示波器可用性并配置...")

#     if oscilloscope is None:
#         print("  - 示波器不可用，跳过测试")
#         pytest.skip("示波器不可用，跳过测试")

#     try:
#         print("  - 示波器已初始化")

#         # 设置数字通道触发
#         trigger_channel = TEST_CONFIG['oscilloscope_config']['digital_trigger_channel']
#         trigger_source = oscilloscope.Set_Digital_Trigger(digital_channel=trigger_channel)
#         print(f"  - 数字触发设置完成: {trigger_source}")

#         # 设置余晖模式
#         persistence_mode = TEST_CONFIG['oscilloscope_config']['persistence_mode']
#         persistence_time = oscilloscope.Set_Display_Persistence(time=persistence_mode)
#         print(f"  - 余晖模式设置完成: {persistence_time.strip()}")

#         # 设置时基 (使用扫频模式的时基作为默认值)
#         timebase_scale = TEST_CONFIG['oscilloscope_config']['sweep_timebase']
#         current_timebase = oscilloscope.Set_Timebase_Scale(timebase_scale=timebase_scale)
#         print(f"  - 时基设置完成: {timebase_scale} ({current_timebase:.2e}s/div)")

#         # 设置初始波形参数 (使用一个临时频率，为第一个目标频率做准备)
#         waveform = TEST_CONFIG['oscilloscope_config']['waveform_type']
#         amplitude = TEST_CONFIG['oscilloscope_config']['amplitude']
#         offset = TEST_CONFIG['oscilloscope_config']['offset']
#         target_first_freq = TEST_CONFIG['oscilloscope_config']['frequency_range']['start']
#         temp_freq = target_first_freq + 50000  # 临时频率：比第一个目标频率高50kHz

#         print(f"  - 设置初始波形参数...")
#         basic_wave_status, _ = oscilloscope.Set_Wavegen_Basic(
#             waveform=waveform,
#             frequency=temp_freq,  # 先设置临时频率
#             amplitude=amplitude,
#             offset=offset,
#             output_state='OFF',  # 初始不开启输出
#             load=50
#         )
#         print(f"    * 波形类型: {waveform}")
#         print(f"    * 幅值: {amplitude}Vpp")
#         print(f"    * 偏移: {offset}Vdc")
#         print(f"    * 临时频率: {temp_freq/1000}kHz (为第一个目标频率{target_first_freq/1000}kHz做准备)")
#         print(f"    * 基本波形状态: {basic_wave_status.strip()}")

#         # 创建截图保存目录
#         screenshot_folder = TEST_CONFIG['oscilloscope_config']['screenshot_folder']
#         os.makedirs(screenshot_folder, exist_ok=True)
#         print(f"  - 截图保存目录: {screenshot_folder}")

#     except Exception as e:
#         print(f"  - 示波器配置失败: {e}")
#         pytest.skip("示波器配置失败，跳过测试")
    
#     # ---------------------------------------------------------------------
#     # 步骤1: 配置测试专用Links
#     # ---------------------------------------------------------------------
#     print(f"\n步骤1: 配置测试专用Links {TEST_CONFIG['active_links']}")
#     link_status = configure_links(TEST_CONFIG['active_links'])
#     print(f"  - Links配置完成: {link_status}")
    
#     # 检查GPIO15/16特殊配置
#     q68_source_gpio = TEST_CONFIG['q68_source_gpio']
    
#     # 根据GPIO类型确定i2c_bus配置
#     if q68_source_gpio in [15, 16]:
#         print(f"\n⚠️  检测到Q68 GPIO{q68_source_gpio} (I2C1引脚)")
#         print("=" * 60)
#         print("🔌 硬件连接提示:")
#         print("   请确保已连接 Q68 GPIO 11/12 (SDA1/SCL1)")
#         print("   - GPIO 11: SDA1 (I2C1数据线)")
#         print("   - GPIO 12: SCL1 (I2C1时钟线)")
#         print("=" * 60)
#         i2c_bus_config = 1
#         print(f"✅ 自动设置 i2c_bus = 1 (用于GPIO{q68_source_gpio})")
#     else:
#         i2c_bus_config = 0
#         print(f"\n使用标准GPIO{q68_source_gpio}配置 (i2c_bus = 0)")
    
#     # 设置地址转换
#     print(f"\n步骤1.5: 设置激活Links的地址转换...")
#     for link in TEST_CONFIG['active_links']:
#         if link < len(s68_res_dev):
#             q68_remote.S68_AddrTrans(
#                 link=link,
#                 q68_iic_addr=0x73,
#                 s68_iic_addr=0x40,
#                 s68_retrans_addr=s68_res_dev[link],
#                 sensor_addr=0x24,
#                 sensor_retrans_addr=0x24 + link,
#                 i2c_bus=i2c_bus_config
#             )
#             print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40 (i2c_bus={i2c_bus_config})")
#     print("  - 地址转换设置完成")
    
#     # ---------------------------------------------------------------------
#     # 步骤2: 配置Q68 GPIO为发送端
#     # ---------------------------------------------------------------------
#     print(f"\n步骤2: 配置 Q68 GPIO-{q68_source_gpio} 为发送端 (Signal ID: {TEST_CONFIG['signal_id']})...")
#     print(f"    - I2C总线配置: {i2c_bus_config}")
    
#     q68.MFNSet(gpio=q68_source_gpio, mfn=0)
    
#     # 使用第一个激活的Link作为发送Link
#     primary_link = TEST_CONFIG['active_links'][0]
#     q68.GPIORemoteTx(gpio=q68_source_gpio, tx_id=TEST_CONFIG['signal_id'],
#                      link_id=primary_link, dly_comp_en=0)
#     print(f"    - Q68 GPIO{q68_source_gpio} 配置完成，使用Link{primary_link}")
    
#     # ---------------------------------------------------------------------
#     # 步骤3: 配置S68 GPIOs为接收端
#     # ---------------------------------------------------------------------
#     print(f"\n步骤3: 配置激活Links上的S68 GPIOs {TEST_CONFIG['s68_target_gpios']} 为接收端...")
#     for link in TEST_CONFIG['active_links']:
#         if link < len(s68_res_dev):
#             target_addr = s68_res_dev[link]
#             print(f"    - 配置 S68 Link-{link} (地址: 0x{target_addr:02X})...")
            
#             # 设置设备地址
#             q68_remote.dongle.devAddr = target_addr
#             print(f"      * 设备地址已设置为: 0x{target_addr:02X}")
            
#             time.sleep(0.1)
            
#             for s68_pin in TEST_CONFIG['s68_target_gpios']:
#                 print(f"      * 配置GPIO{s68_pin}...")
                
#                 try:
#                     q68_remote.M2CMFNSet(gpio=s68_pin, mfn=0)
#                     print(f"        - GPIO{s68_pin} MFN设置为0 (GPIO功能)")
                    
#                     q68_remote.M2CGPIORemoteRx(gpio=s68_pin, rx_id=TEST_CONFIG['signal_id'])
#                     print(f"        - GPIO{s68_pin} 远程接收设置完成 (信号ID: {TEST_CONFIG['signal_id']})")
#                 except Exception as e:
#                     print(f"        - 错误: GPIO{s68_pin} 配置失败: {e}")
#         else:
#             print(f"    - 警告: Link{link} 超出s68_res_dev范围，跳过")
#     print("    - 激活Links上的S68 GPIO 配置完成。")
    
#     # ---------------------------------------------------------------------
#     # 步骤4: 频率扫描和自动截图
#     # ---------------------------------------------------------------------
#     freq_config = TEST_CONFIG['oscilloscope_config']['frequency_range']

#     print(f"\n步骤4: 开始频率扫描和自动截图...")
#     print(f"    - 波形参数已在初始化时设定，现在只改变频率")

#     frequency_list = range(freq_config['start'], freq_config['end'] + freq_config['step'], freq_config['step'])

#     for i, freq_hz in enumerate(frequency_list):
#         freq_khz = freq_hz / 1000
#         print(f"\n  -> 测试频率: {freq_khz}kHz ({freq_hz}Hz) [{i+1}/{len(frequency_list)}]")

#         try:
#             # 第一个频率的特殊处理：需要先开启输出，然后进行频率切换
#             if i == 0:
#                 print(f"    - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")

#                 # 开启输出
#                 output_on_status = oscilloscope.Set_Wavegen_Output(state='ON', load=50)
#                 print(f"    - 临时频率输出已开启: {output_on_status.strip()}")
#                 time.sleep(0.3)

#                 # 准备切换频率
#                 oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz-10000)
#                 print(f"    - 准备切换到目标频率")
#                 time.sleep(0.3)

#                 # 切换到目标频率
#                 oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
#                 print(f"    - 频率切换完成: {freq_khz}kHz")
#                 # time.sleep(0.3)

#                 # 清除扫描并重新开始采集
#                 oscilloscope.Clear_Display_Waveform()
#                 print(f"    - 已清除扫描，重新开始采集目标频率")
#                 time.sleep(0.5)

#             else:
#                 # 非第一个频率的正常处理流程
#                 # 清除之前的扫描并重新开始采集

#                 # 开启输出
#                 output_on_status = oscilloscope.Set_Wavegen_Output(state='ON', load=50)
#                 print(f"    - 输出已开启: {output_on_status.strip()}")
#                 time.sleep(0.5)

#                 # 设置新频率
#                 oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
#                 print(f"    - 频率设置完成: {freq_khz}kHz")
#                 time.sleep(0.3)

#                 oscilloscope.Clear_Display_Waveform()
#                 print(f"    - 已清除扫描，重新开始采集目标频率")
#                 time.sleep(0.5)

#             # 等待信号稳定和余晖累积
#             print(f"    - 等待{TEST_CONFIG['observation_time']}秒进行余晖观察...")
#             time.sleep(TEST_CONFIG['observation_time'])

#             # 自动截图
#             gpio_num = TEST_CONFIG['q68_source_gpio']
#             timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒，不包含年份
#             screenshot_filename = f"{i+1:02d}_GPIO{gpio_num}_{freq_khz}kHz_{timestamp}.png"
#             screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

#             # 使用正确的截图参数：PNG格式，正常色彩，隐藏菜单
#             oscilloscope.Save_Image(
#                 filepath=screenshot_path,
#                 image_format="PNG",
#                 invert="OFF",      # 正常色彩
#                 menu="MOF"         # 隐藏菜单
#             )
#             print(f"    - 截图已保存: {screenshot_filename} (PNG格式，隐藏菜单)")

#             # 短暂延时确保截图完成
#             time.sleep(2)

#         except Exception as e:
#             print(f"    - 错误: 频率{freq_khz}kHz测试失败: {e}")
#             continue
    
#     print(f"\n  - 频率扫描完成，共测试 {len(frequency_list)} 个频率点")
    
#     # ---------------------------------------------------------------------
#     # 步骤5: 验证链路状态
#     # ---------------------------------------------------------------------
#     print(f"\n步骤5: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
#     link_status_funcs = [
#         q68.c2m.rd_test_fsm_status1_link0,
#         q68.c2m.rd_test_fsm_status1_link1,
#         q68.c2m.rd_test_fsm_status2_link2,
#         q68.c2m.rd_test_fsm_status2_link3,
#     ]
    
#     active_statuses = {}
#     for link_id in TEST_CONFIG['active_links']:
#         if 0 <= link_id <= 3:
#             status = link_status_funcs[link_id]()
#             active_statuses[f'link{link_id}'] = status
#             print(f"    - Link{link_id} 状态: {status}")
    
#     # 验证激活Links的状态
#     failed_links = [link for link, status in active_statuses.items() if status != 5]
#     assert len(failed_links) == 0, \
#         f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"
    
#     print(f"\n==> [GPIO示波器测试] 测试通过!")
#     print(f"    - GPIO信号路径: Q68 GPIO{TEST_CONFIG['q68_source_gpio']} -> S68 GPIOs{TEST_CONFIG['s68_target_gpios']}")
#     print(f"    - 激活Links: {TEST_CONFIG['active_links']}")
#     print(f"    - 频率范围: {freq_config['start']/1000}-{freq_config['end']/1000}kHz")
#     print(f"    - 截图保存位置: {screenshot_folder}")
#     print(f"    - 链路状态正常: {active_statuses}")


if __name__ == "__main__":
    """
    使用说明:

    1. 原有的示波器自动截图测试:
       pytest test_gpio_case5_7auto_fix.py::test_gpio_oscilloscope_auto_screenshot -v -s

    2. 🆕 全Link自动遍历测试 (带确认对话框) - 同时测试所有4个Links:
       pytest test_gpio_case5_7auto_fix.py::test_gpio_q68_s68_auto_traverse_all_links -v -s

    3. 🆕 单Link自动遍历测试 (带确认对话框):
       pytest test_gpio_case5_7auto_fix.py::test_gpio_q68_s68_auto_traverse_single_link -v -s

    4. 🆕 快速自动遍历测试 (无确认对话框):
       pytest test_gpio_case5_7auto_fix.py::test_gpio_q68_s68_auto_traverse_no_dialog -v -s

    5. 运行所有快速测试:
       pytest test_gpio_case5_7auto_fix.py -m fast -v -s

    6. 运行所有慢速测试 (包括自动遍历):
       pytest test_gpio_case5_7auto_fix.py -m slow -v -s

    🔥 新功能特点:
    - 🆕 全Link自动遍历: 同时测试所有4个Links的Q68 GPIO0-16到S68 GPIO0-8
    - 🆕 单Link自动遍历: 测试指定单个Link的所有GPIO组合
    - 弹窗确认每个GPIO测试，可选择跳过或取消
    - 每个GPIO测试前完整系统重新初始化 (断电→重新上电→重建通信)
    - 固定频率测试: 30Hz, 1kHz, 10kHz, 50kHz, 100kHz
    - 对应时基: 20ms, 1ms, 100us, 10us, 10us
    - 详细的测试进度和统计信息
    - 智能错误处理和测试总结
    - 支持示波器自动截图功能

    📊 测试配置:
    - 修改TEST_CONFIG中的参数来调整测试设置
    - frequency_list: 固定频率列表 [30, 1000, 10000, 50000, 100000] Hz
    - timebase_list: 对应时基 ['20ms', '1ms', '100us', '10us', '10us']
    - amplitude/offset: 调整波形参数
    - screenshot_folder: 修改截图保存路径
    - persistence_mode: 调整余晖模式 (该机型只支持INFinite)

    🔧 硬件要求:
    - Siglent SDS5034X示波器通过USB连接
    - Q68和S68硬件正确连接
    - 如使用GPIO15/16需要连接I2C1引脚
    - 支持完整的电源控制 (断电/重新上电)
    """
    print("Q68→S68 GPIO自动遍历测试用例")
    print("请使用pytest运行测试")
