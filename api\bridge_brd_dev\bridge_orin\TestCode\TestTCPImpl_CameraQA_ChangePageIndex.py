#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import HWImpl
import time

if __name__ == '__main__':
    # 使用第一个设备
    HWImpl.hw_open(ip_addr="*************")

    ret = HWImpl.hw_waithw_ok(100)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")
    
    ret = HWImpl.hw_show_form_index(2)
    if ret == 0:
        print("hw_show_form_index ok")
    else:
        print("hw_show_form_index timeout")
    
    time.sleep(1)
    ret = HWImpl.hw_show_form_index(3)
    if ret == 0:
        print("hw_show_form_index ok")
    else:
        print("hw_show_form_index timeout")
    
    time.sleep(1)
    ret = HWImpl.hw_show_form_index(4)
    if ret == 0:
        print("hw_show_form_index ok")
    else:
        print("hw_show_form_index timeout")
    
    time.sleep(1)
    ret = HWImpl.hw_show_form_index(5)
    if ret == 0:
        print("hw_show_form_index ok")
    else:
        print("hw_show_form_index timeout")
        
    time.sleep(1)
    ret = HWImpl.hw_show_form_index(12)
    if ret == 0:
        print("hw_show_form_index ok")
    else:
        print("hw_show_form_index timeout")
    
    time.sleep(2)
    
    ret = HWImpl.hw_show_form_index(1)
    if ret == 0:
        print("hw_show_form_index ok")
    else:
        print("hw_show_form_index timeout")
    
    time.sleep(2)
    # 关闭设备
    HWImpl.hw_close()
