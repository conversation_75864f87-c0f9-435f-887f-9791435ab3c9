from QRegisterAccess import *

# 选择编号为0的MCU设备
selectDevice(deviceIndex=0)

# 选择当前操作的I2C设备信息为，bus=0, devAddr=0x29
selectI2CDevice(0, 0x73)

reg_ana_0x1001 = M65Q68I2CRead_normal(0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')

reg_ana_0x1001 = M66S68I2CRead_normal(0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')


M65Q68I2CWrite_normal(0x1001, 0x00)
M66S68I2CWrite_normal(0x1001, 0x00)


# 选择当前操作的I2C设备信息为，bus=1, devAddr=0x31
selectI2CDevice(1, 0x31)
reg_ana_0x1001 = M65Q68I2CRead_normal(0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')

reg_ana_0x1001 = M66S68I2CRead_normal(0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')


M65Q68I2CWrite_normal(0x1001, 0x00)
M66S68I2CWrite_normal(0x1001, 0x00)
