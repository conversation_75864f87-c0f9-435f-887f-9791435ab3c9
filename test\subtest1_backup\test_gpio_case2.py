# -*- coding: utf-8 -*-
"""GPIO Case-2 ‑ Duty-cycle sweep

验证不同占空比下帧同步输出的稳定性。
"""

import logging
import sys
import os
import time
import pytest

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(project_root)

# 测试配置 - 可以手动修改
TEST_CONFIG = {
    'active_links': [0,],  
    'duty_cycles': [1, 2, 3, 4, 5, 6, 7, 8, 9],  # 占空比值 (0-9)
    'signal_id': 11,               # GPIO信号ID
    'target_gpio': 0,              # 目标GPIO
    'step_duration': 20,            # 每个占空比持续时间(秒)
}

@pytest.mark.fast
def test_gpio_duty_cycle(devices):
    print(f"[CASE-2] 开始执行: 帧同步占空比 sweep")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")

    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']

    # 配置测试专用Links
    print(f"\n步骤0: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")

    # 设置地址转换 (关键步骤！)
    print(f"\n步骤0.5: 设置激活Links的地址转换...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            # 设置地址转换：转译地址 -> 实际设备地址
            q68_remote.S68_AddrTrans(
                link=link,
                q68_iic_addr=0x73,                    # Q68地址
                s68_iic_addr=0x40,                    # S68实际地址
                s68_retrans_addr=s68_res_dev[link],   # S68转译地址 (0x20, 0x21, 0x22, 0x23)
                sensor_addr=0x24,                     # sensor地址 (如果需要)
                sensor_retrans_addr=0x24 + link      # sensor转译地址
            )
            print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40")
    print("  - 地址转换设置完成")

    # S68 侧配置 - 只配置激活的Links
    print(f"\n步骤1: 配置激活Links上的S68 GPIO{TEST_CONFIG['target_gpio']}接收帧同步信号...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            q68_remote.dongle.devAddr = s68_res_dev[link]
            q68_remote.M2CGPIORemoteRx(gpio=TEST_CONFIG['target_gpio'], rx_id=TEST_CONFIG['signal_id'])
            print(f"  - Link{link} S68 GPIO{TEST_CONFIG['target_gpio']} 配置完成")

    # 依次测试不同占空比
    print(f"\n步骤2: 开始占空比扫描测试...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    for duty in TEST_CONFIG['duty_cycles']:
        print(f"  -> 测试占空比: {duty}")
        q68.FrameSyncOutConifg(i=0, per_div=0x0B, duty_cycle=duty,
                                period=17361, fs_tx_id=TEST_CONFIG['signal_id'],
                                auto_fs=1, outen=1)
        time.sleep(TEST_CONFIG['step_duration'])

        # 只检查激活Links的状态
        active_statuses = {}
        for link_id in TEST_CONFIG['active_links']:
            if 0 <= link_id <= 3:
                status = link_status_funcs[link_id]()
                active_statuses[f'link{link_id}'] = status

        print(f"    占空比{duty} - 激活Links状态: {active_statuses}")

        # 验证激活Links的状态
        failed_links = [link for link, status in active_statuses.items() if status != 5]
        assert len(failed_links) == 0, \
            f"占空比{duty}测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"

    print(f"\n==> [CASE-2] 占空比扫描测试通过: 所有占空比在Links {TEST_CONFIG['active_links']} 上工作正常")