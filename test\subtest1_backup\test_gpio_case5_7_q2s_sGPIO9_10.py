# -*- coding: utf-8 -*-
"""
灵活的GPIO9/10测试用例
展示如何在测试用例内部灵活配置不同的Link

特点:
1. 测试用例内部可以动态选择Link
2. 不需要修改top.py的全局配置
3. 支持单Link和多Link测试
4. 最大化模块灵活性

注意引脚上不用接上拉，也不能接探头，跑完再接
"""


import time
import pytest


class GPIO9_10_FlexibleTester:
    """GPIO9/10灵活测试器"""
    
    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_I2C2remote = devices['q68_I2C2remote']
        self.configure_links = devices['configure_links']
        self.get_link_status = devices['get_link_status']
    
    def test_single_link(self, link_id, signal_id=11, q68_gpio=0, s68_gpios=[9, 10], duration=3):
        """测试单个Link的GPIO9/10功能
        
        Args:
            link_id: 要测试的Link ID (0-3)
            signal_id: GPIO信号ID
            q68_gpio: Q68发送GPIO
            s68_gpios: S68接收GPIO列表
            duration: 测试持续时间
        """
        print(f"\n{'='*50}")
        print(f"测试单个Link: Link{link_id}")
        print(f"配置: 信号ID={signal_id}, Q68_GPIO{q68_gpio} → S68_GPIO{s68_gpios}")
        print(f"{'='*50}")
        
        # 步骤1: 配置指定Link
        print(f"\n步骤1: 配置Link{link_id}")
        link_status = self.configure_links([link_id])
        initial_status = link_status.get(f'link{link_id}', 0)
        print(f"  - Link{link_id} 初始状态: {initial_status}")
        
        # 步骤2: 配置Q68发送端
        print(f"\n步骤2: 配置Q68 GPIO{q68_gpio}为发送端")
        self.q68.MFNSet(gpio=q68_gpio, mfn=0)
        self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id, link_id=link_id, dly_comp_en=0)
        print(f"  - Q68 GPIO{q68_gpio} 配置完成 (信号ID={signal_id}, Link={link_id})")
        
        # 步骤3: 配置S68接收端
        print(f"\n步骤3: 配置S68 GPIO{s68_gpios}为接收端")
        self.q68_I2C2remote.dongle.devAddr = 0x40
        
        for gpio_pin in s68_gpios:
            # 引脚功能切换
            self.q68_I2C2remote.M2CMFNSet(gpio=gpio_pin, mfn=0)
            # 远程接收配置
            self.q68_I2C2remote.M2CGPIORemoteRx(gpio=gpio_pin, rx_id=signal_id)
            print(f"  - GPIO{gpio_pin}: I2C功能 → GPIO功能, 接收信号ID={signal_id}")
        
        # 步骤4: 等待和验证
        print(f"\n步骤4: 等待{duration}秒进行信号传输...")
        time.sleep(duration)
        
        # 步骤5: 验证结果
        final_status = self.get_link_status(link_id)
        print(f"\n步骤5: 验证结果")
        print(f"  - Link{link_id} 最终状态: {final_status}")
        
        # 断言验证
        assert final_status == 5, f"Link{link_id} 测试失败，状态: {final_status} (期望: 5)"
        
        print(f"✅ Link{link_id} GPIO9/10测试通过!")
        return True
    
    def test_multiple_links(self, link_configs):
        """测试多个Link的GPIO9/10功能
        
        Args:
            link_configs: Link配置列表，每个配置包含link_id, signal_id等
        """
        print(f"\n{'='*60}")
        print(f"测试多个Links: {[cfg['link_id'] for cfg in link_configs]}")
        print(f"{'='*60}")
        
        results = {}
        
        for i, config in enumerate(link_configs):
            print(f"\n--- 测试配置 {i+1}/{len(link_configs)} ---")
            
            try:
                result = self.test_single_link(
                    link_id=config['link_id'],
                    signal_id=config.get('signal_id', 11 + config['link_id']),
                    q68_gpio=config.get('q68_gpio', config['link_id']),
                    s68_gpios=config.get('s68_gpios', [9, 10]),
                    duration=config.get('duration', 3)
                )
                results[f"link{config['link_id']}"] = result
                
            except Exception as e:
                print(f"❌ Link{config['link_id']} 测试失败: {e}")
                results[f"link{config['link_id']}"] = False
                # 继续测试其他Link，不中断
        
        # 总结结果
        print(f"\n{'='*60}")
        print("多Link测试总结:")
        for link, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  - {link}: {status}")
        print(f"{'='*60}")
        
        return results


@pytest.mark.fast
def test_gpio9_10_link0(devices):
    """测试Link0的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=0, signal_id=11)


@pytest.mark.fast  
def test_gpio9_10_link1(devices):
    """测试Link1的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=1, signal_id=12)


@pytest.mark.fast
def test_gpio9_10_link2(devices):
    """测试Link2的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=2, signal_id=13)


@pytest.mark.fast
def test_gpio9_10_link3(devices):
    """测试Link3的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=3, signal_id=14)


@pytest.mark.slow
def test_gpio9_10_all_links(devices):
    """测试所有Link的GPIO9/10 (如果硬件支持)"""
    tester = GPIO9_10_FlexibleTester(devices)
    
    # 定义所有Link的测试配置
    link_configs = [
        {'link_id': 0, 'signal_id': 11, 'q68_gpio': 0},
        {'link_id': 1, 'signal_id': 12, 'q68_gpio': 1},
        {'link_id': 2, 'signal_id': 13, 'q68_gpio': 2},
        {'link_id': 3, 'signal_id': 14, 'q68_gpio': 3},
    ]
    
    results = tester.test_multiple_links(link_configs)
    
    # 验证至少有一个Link测试通过
    passed_links = [link for link, result in results.items() if result]
    assert len(passed_links) > 0, f"所有Link测试都失败了: {results}"
    
    print(f"✅ 多Link测试完成，通过的Links: {passed_links}")


@pytest.mark.parametrize("link_id", [0, 1, 2, 3])
def test_gpio9_10_parametrized(devices, link_id):
    """参数化测试 - 可以选择性运行特定Link"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(
        link_id=link_id, 
        signal_id=10 + link_id,
        duration=2  # 缩短测试时间
    )


if __name__ == "__main__":
    """
    使用说明:
    
    1. 测试单个Link:
       pytest test_gpio9_10_flexible.py::test_gpio9_10_link0 -v -s
    
    2. 测试所有Link:
       pytest test_gpio9_10_flexible.py::test_gpio9_10_all_links -v -s
    
    3. 参数化测试特定Link:
       pytest test_gpio9_10_flexible.py::test_gpio9_10_parametrized[0] -v -s
    
    4. 运行所有快速测试:
       pytest test_gpio9_10_flexible.py -m fast -v -s
    """
    print("GPIO9/10灵活测试用例")
    print("请使用pytest运行测试")
