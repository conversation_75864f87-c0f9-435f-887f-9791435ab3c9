# -*- coding: utf-8 -*-
"""
GPIO配置工具函数

提供GPIO特殊配置的通用函数，包括GPIO13、GPIO15/16等特殊引脚的处理
"""
import time


def check_and_configure_special_gpio(gpio_pin, test_config=None):
    """
    检查并配置特殊GPIO引脚
    
    Args:
        gpio_pin: GPIO引脚号
        test_config: 测试配置字典 (可选)
        
    Returns:
        dict: 配置结果
        {
            'gpio_type': 'standard'|'gpio13'|'gpio15_16',
            'i2c_bus': 0|1,
            'special_config_needed': bool,
            'user_prompt_shown': bool,
            'config_message': str
        }
    """
    result = {
        'gpio_type': 'standard',
        'i2c_bus': 0,
        'special_config_needed': False,
        'user_prompt_shown': False,
        'config_message': ''
    }
    
    if gpio_pin == 13:
        # GPIO13特殊配置
        result['gpio_type'] = 'gpio13'
        result['i2c_bus'] = 0  # GPIO13使用默认I2C总线
        result['special_config_needed'] = True
        result['config_message'] = f"检测到GPIO13，需要特殊寄存器配置"
        
    elif gpio_pin in [15, 16]:
        # GPIO15/16特殊配置
        result['gpio_type'] = 'gpio15_16'
        result['i2c_bus'] = 1  # GPIO15/16需要使用I2C1总线
        result['special_config_needed'] = True
        result['user_prompt_shown'] = True
        result['config_message'] = f"检测到GPIO{gpio_pin} (I2C1引脚)，需要连接GPIO 11/12"
        
        # 显示硬件连接提示
        print(f"\n⚠️  检测到Q68 GPIO{gpio_pin} (I2C1引脚)")
        print("=" * 60)
        print("🔌 硬件连接提示:")
        print("   请确保已连接 Q68 GPIO 11/12 (SDA1/SCL1)")
        print("   - GPIO 11: SDA1 (I2C1数据线)")
        print("   - GPIO 12: SCL1 (I2C1时钟线)")
        print("=" * 60)
        print(f"✅ 自动设置 i2c_bus = 1 (用于GPIO{gpio_pin})")
        
    else:
        # 标准GPIO配置
        result['gpio_type'] = 'standard'
        result['i2c_bus'] = 0
        result['config_message'] = f"使用标准GPIO{gpio_pin}配置"
    
    # 更新测试配置 (如果提供)
    if test_config is not None:
        test_config['i2c_bus'] = result['i2c_bus']
    
    return result


def configure_q68_gpio_with_special_handling(q68, gpio_pin, signal_id, link_id):
    """
    配置Q68 GPIO，自动处理特殊引脚
    
    Args:
        q68: Q68设备对象
        gpio_pin: GPIO引脚号
        signal_id: 信号ID
        link_id: Link ID
        
    Returns:
        bool: 配置是否成功
    """
    try:
        print(f"配置Q68 GPIO{gpio_pin}...")
        
        # 检查特殊GPIO配置
        config_result = check_and_configure_special_gpio(gpio_pin)
        print(f"  - {config_result['config_message']}")
        
        # GPIO13特殊处理
        if config_result['gpio_type'] == 'gpio13':
            # 导入GPIO13工具函数 (如果可用)
            try:
                from gpio13_utils import configure_q68_gpio13_for_remote_tx
                success = configure_q68_gpio13_for_remote_tx(q68, signal_id, link_id)
                if success:
                    print(f"  - GPIO13特殊配置成功")
                    return True
                else:
                    print(f"  - GPIO13特殊配置失败，使用标准方法")
            except ImportError:
                print(f"  - gpio13_utils不可用，使用内联配置")
                
                # 内联GPIO13配置
                q68.M65Q68I2CLocalWrite(regAddr=0x4479, value=0x00)  # disable i2c sel
                q68.M65Q68I2CLocalWrite(regAddr=0x440d, value=0xff)  # GPIO控制
                q68.M65Q68I2CLocalWrite(regAddr=0x4420, value=0x02)  # 初始输出低
                time.sleep(0.1)
        
        # 标准GPIO配置
        print(f"  - 设置GPIO{gpio_pin}为GPIO功能...")
        q68.MFNSet(gpio=gpio_pin, mfn=0)
        
        print(f"  - 配置GPIO{gpio_pin}为远程发送...")
        q68.GPIORemoteTx(gpio=gpio_pin, tx_id=signal_id, link_id=link_id, dly_comp_en=0)
        
        print(f"  - GPIO{gpio_pin}配置完成")
        return True
        
    except Exception as e:
        print(f"  - 错误: GPIO{gpio_pin}配置失败: {e}")
        return False


def setup_address_translation_with_gpio_config(q68_remote, active_links, s68_res_dev, gpio_pin):
    """
    根据GPIO类型设置地址转换
    
    Args:
        q68_remote: Q68远程对象
        active_links: 激活的Links列表
        s68_res_dev: S68设备地址列表
        gpio_pin: GPIO引脚号
        
    Returns:
        bool: 设置是否成功
    """
    try:
        # 检查GPIO配置
        config_result = check_and_configure_special_gpio(gpio_pin)
        i2c_bus_config = config_result['i2c_bus']
        
        print(f"\n设置激活Links的地址转换 (i2c_bus={i2c_bus_config})...")
        
        for link in active_links:
            if link < len(s68_res_dev):
                # 设置地址转换：转译地址 -> 实际设备地址
                q68_remote.S68_AddrTrans(
                    link=link,
                    q68_iic_addr=0x73,                    # Q68地址
                    s68_iic_addr=0x40,                    # S68实际地址
                    s68_retrans_addr=s68_res_dev[link],   # S68转译地址 (0x20, 0x21, 0x22, 0x23)
                    sensor_addr=0x24,                     # sensor地址 (如果需要)
                    sensor_retrans_addr=0x24 + link,      # sensor转译地址
                    i2c_bus=i2c_bus_config                # 根据GPIO类型动态设置
                )
                print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40 (i2c_bus={i2c_bus_config})")
        
        print("  - 地址转换设置完成")
        return True
    
        
    except Exception as e:
        print(f"  - 错误: 地址转换设置失败: {e}")
        return False


def get_gpio_config_summary(gpio_pin):
    """
    获取GPIO配置摘要信息
    
    Args:
        gpio_pin: GPIO引脚号
        
    Returns:
        str: 配置摘要
    """
    config_result = check_and_configure_special_gpio(gpio_pin)
    
    if config_result['gpio_type'] == 'gpio13':
        return f"GPIO{gpio_pin} (特殊引脚，需要寄存器配置，i2c_bus={config_result['i2c_bus']})"
    elif config_result['gpio_type'] == 'gpio15_16':
        return f"GPIO{gpio_pin} (I2C1引脚，需要连接GPIO11/12，i2c_bus={config_result['i2c_bus']})"
    else:
        return f"GPIO{gpio_pin} (标准引脚，i2c_bus={config_result['i2c_bus']})"


# 使用示例
if __name__ == "__main__":
    """
    使用示例:
    
    # 检查GPIO配置
    config = check_and_configure_special_gpio(15)
    print(f"GPIO15配置: {config}")
    
    # 配置Q68 GPIO
    success = configure_q68_gpio_with_special_handling(q68, 13, signal_id=11, link_id=0)
    
    # 设置地址转换
    setup_address_translation_with_gpio_config(q68_remote, [0, 1], s68_res_dev, 15)
    
    # 获取配置摘要
    summary = get_gpio_config_summary(16)
    print(summary)
    """
    print("GPIO配置工具函数模块")
    print("支持GPIO13、GPIO15/16等特殊引脚的自动配置")
