# -*- coding: utf-8 -*-
"""
GPIO Case-6: Bidirectional Signal Transmission Path - D68 Chip Version

基于test_gpio_case6_8_s2q_auto2.py模板，适配D68芯片测试。
支持双向信号测试：
1. S2D方向：S68 GPIO → D68 Q68 GPIO（串行测试，一个Link一个Link）
2. D2S方向：D68 Q68 GPIO → S68 GPIO（并行测试，所有Links同时）

主要修改：
1. 导入模块改为M65D68_Common_Fuction_A0
2. MFN设置函数改为MFNSet_D68
3. 添加D68特定初始化代码
4. 仅使用Link0和Link2进行测试
5. 使用独立设备初始化，包含完整地址转换
6. 新增D2S方向测试功能
"""
import logging
import time
import pytest
import os
import sys
import tkinter as tk
from tkinter import messagebox
from conftest import get_universal_oscilloscope_screenshot,power_on


# 禁用pyvisa的DEBUG日志，减少冗余输出
logging.getLogger('pyvisa').setLevel(logging.WARNING)

# 添加D68模块路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65d68_a0'))  # D68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'))  # S68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # 示波器模块路径

# D68芯片专用导入
try:
    from Common_d.M65D68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    print("✅ D68模块导入成功")
except ImportError as e:
    print(f"⚠️ D68模块导入失败: {e}")
    print("🔄 尝试使用Q68模块作为兼容...")
    try:
        from Common.M65Q68_Common_Fuction_A0 import *
        from Common_var.M66S68_Common_Fuction_A0 import *
        print("✅ 使用Q68模块作为D68兼容")
    except ImportError as e2:
        print(f"❌ 所有模块导入失败: {e2}")
        # 定义空类以避免错误
        class M65Q68_A0: pass
        class M65Q68_A0_Remote_M66S68: pass

# D68设备初始化配置
D68_DEVICE_CONFIG = {
    'q68_iic_addr': 0x73,
    's68_iic_dev': [0x40, 0x40, 0x40, 0x40],
    's68_res_dev': [0x20, 0x21, 0x22, 0x23],
    's68_res_sensor_dev': [0x24, 0x25, 0x26, 0x27],
    'active_links': [0, 2],  # D68仅支持Link0和Link2
    'RATE':         [2, 2, 2, 2],
    'RATE_final':   [2, 2, 2, 2],
    'BCRATE':       [0, 0, 0, 0]  
}

# 全局停止标志
GLOBAL_STOP_FLAG = False

# 测试配置 - D68版本
TEST_CONFIG = {
    'active_links': [0, 2],                 # D68版本仅使用Link0和Link2
    'signal_id': 11,                        # GPIO信号ID
    'observation_time': 1,                  # 观察时间(秒)
    's68_source_gpio': 8,                   # S68源GPIO (用于截图文件命名，每次测试都设置同一个)

    # 示波器自动截图配置
    'oscilloscope_config': {
        'enable_screenshot': True,          # 是否启用自动截图
        'test_mode': 'combined',            # 测试模式: 'fixed'=仅固定频率, 'sweep'=仅扫频, 'combined'=组合测试
        'trigger_source': 'D1',            # 触发源: C1-C4(模拟), D0-D15(数字), EX, EX5, LINE
        'waveform_type': 'SQUARE',          # 方波
        # 固定频率模式配置
        'frequency_list': [30, 1000, 10000, 50000, 100000],  # 固定频率列表 (Hz)
        'timebase_list': ['20ms', '1ms', '100us', '10us', '5us'],   # 对应的时基 - 优化100kHz时基
        # 'frequency_list': [30,1000 ],  # 固定频率列表 (Hz)
        # 'timebase_list': ['20ms','1ms' ],   # 对应的时基 - 优化100kHz时基
        # 扫频模式配置
        # 'frequency_range': {
        #     'start': 1210000,                    # 1210kHz
        #     'end': 1210000,                      # 1240kHz
        #     'step': 10000                       # 10kHz步进
        # },
        # 'sweep_timebase': '500ns',              # 扫频模式的时基
        'frequency_range': {
            'start': 450000,                    # 1210kHz
            'end': 470000,                      # 1240kHz
            'step': 10000                       # 10kHz步进
        },
        'sweep_timebase': '2us',              # 扫频模式的时基
        'amplitude': 1.8,                      # 1.8Vpp
        'offset': 0.9,                          # 900mVdc偏移
        'screenshot_folder_base': 'U-disk0/gpiotest/s68tod68_0_2',  # D68版本截图保存基础文件夹
        'screenshot_folder_base_d2s': 'U-disk0/gpiotest/d68tos68_0_2',  # D2S版本截图保存基础文件夹
        'persistence_mode': 'INFinite',         # 余晖模式 (该机型唯一选项)
        'probe_wait_time': 1,                  # 第一次截图前等待插探头时间(秒)
        'freq_observation_time': 1.3,             # 每个频率的观察时间(秒)
    },

    # 动态生成configurations，使用统一的S68 GPIO变量
    'configurations': None  # 将在运行时动态生成
}


def d68_complete_initialization():
    """D68完整初始化函数 -  (上电->初始化D68 Q68设备->初始化D68 Q68远程设备->D68特定初始化->设置链路速率->地址转换)"""

    config = D68_DEVICE_CONFIG
    q68 = M65Q68_A0(dongle='stm32', id=0, bus='i2c')  # 初始化D68 Q68设备
    q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')  # 初始化D68 Q68远程设备
    q68.c2m.wr_test_glb_ctrl0_fields(key=0x5c)
    q68.c2m.wr_test_tx_link_data_inv_fields(tx_polar_sel=0x6)
    q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel=0x6)  # D68特定初始化
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])
    q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=config['BCRATE'][0], bc_rate1=config['BCRATE'][1], bc_rate2=config['BCRATE'][2], bc_rate3=config['BCRATE'][3])
    q68.Q68_C3_6G_Init(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])  # 设置链路速率
    for link in config['active_links']: 
        q68_remote.S68_AddrTrans(link=link, q68_iic_addr=0x73, s68_iic_addr=0x40, s68_retrans_addr=config['s68_res_dev'][link], sensor_addr=0x24, sensor_retrans_addr=0x24 + link)  # 地址转换
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])
    q68.Q68_C3_6G_Init(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])  # 设置链路速率
    try: from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X; oscilloscope = SiglentSDS5034X()
    except: oscilloscope = None
    return {'q68': q68, 'q68_remote': q68_remote, 's68_res_dev': config['s68_res_dev'], 'oscilloscope': oscilloscope, 'configure_links': config['active_links']}


class GPIO_S68_D68_AutoTester:
    """S68到D68的GPIO自动测试器"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def show_confirmation_dialog(self, s68_gpio, current_test, total_tests, current_link=None):
        """显示确认对话框 - 5秒后自动选择是（使用线程安全的messagebox）"""
        global GLOBAL_STOP_FLAG

        # 如果已经设置了全局停止标志，直接返回None
        if GLOBAL_STOP_FLAG:
            return None

        import threading

        result = [True]  # 默认为True

        def show_dialog():
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                root.attributes('-topmost', True)  # 置顶
                root.lift()  # 提升到最前面
                root.focus_force()  # 强制获取焦点

                link_info = f"Link{current_link} " if current_link is not None else ""
                message = f"测试 S68 GPIO{s68_gpio} → D68?\n{link_info}进度: {current_test}/{total_tests}\n\n5秒后自动开始..."

                # 使用简单的messagebox，更稳定
                from tkinter import messagebox
                dialog_result = messagebox.askyesnocancel("GPIO测试确认", message, parent=root)

                # 设置结果
                result[0] = dialog_result

                # 如果用户点击取消，设置全局停止标志
                if dialog_result is None:
                    global GLOBAL_STOP_FLAG
                    GLOBAL_STOP_FLAG = True

                root.destroy()
            except:
                pass

        # 在新线程中显示对话框
        dialog_thread = threading.Thread(target=show_dialog, daemon=True)
        dialog_thread.start()

        # 等待5秒或用户操作
        dialog_thread.join(timeout=5.0)

        # 如果用户点击了取消，设置全局停止标志
        if result[0] is None:
            GLOBAL_STOP_FLAG = True

        return result[0]

    def create_all_screenshot_folders(self):
        """预创建所有截图文件夹"""
        import os
        s2d_base = TEST_CONFIG['oscilloscope_config']['screenshot_folder_base']
        d2s_base = TEST_CONFIG['oscilloscope_config']['screenshot_folder_base_d2s']
        active_links = TEST_CONFIG['active_links']

        # S2D文件夹
        for link in active_links:
            for gpio in range(9):
                os.makedirs(f"{s2d_base}/link{link}_gpio{gpio}", exist_ok=True)

        # D2S文件夹
        all_links_str = ''.join(map(str, active_links))
        for gpio in range(11):
            os.makedirs(f"{d2s_base}/link{all_links_str}_gpio{gpio}", exist_ok=True)
            for link in active_links:
                os.makedirs(f"{d2s_base}/link{link}_gpio{gpio}", exist_ok=True)

    def test_single_s68_gpio_single_link_d68(self, s68_gpio, target_link):
        """测试单个S68 GPIO到所有Q68 GPIO - D68版本（单个Link）"""
        try:
            signal_id = TEST_CONFIG['signal_id']
            if target_link < len(self.s68_res_dev):
                self.q68_remote.dongle.devAddr = self.s68_res_dev[target_link]
                self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                self.q68_remote.M2CGPIORemoteTx(gpio=s68_gpio, tx_id=signal_id)
                for q68_gpio in range(11):
                    self.q68.MFNSet_D68(gpio=q68_gpio, mfn=0)
                    self.q68.GPIORemoteRx(gpio=q68_gpio, rx_id=signal_id)
            self.oscilloscope_screenshot_universal(s68_gpio, [target_link], TEST_CONFIG['oscilloscope_config']['test_mode'], direction='s2d')
            return True
        except Exception as e:
            print(f"❌ Link{target_link}测试失败: {e}")
            return False


    def full_system_reinit_d68(self):
        """完整系统重新初始化 - 从断电重新上电开始"""
        try:
            print(f"🔄 清理旧连接...")
            # 清理旧的设备连接
            try:
                if hasattr(self, 'q68') and self.q68: del self.q68
                if hasattr(self, 'q68_remote') and self.q68_remote: del self.q68_remote
            except: pass

            print(f"🔄 断电重新上电...")
            power_on()
            devices = d68_complete_initialization()
            self.q68 = devices['q68']
            self.q68_remote = devices['q68_remote']
            self.s68_res_dev = devices['s68_res_dev']
            self.oscilloscope = devices['oscilloscope']
            self.configure_links = devices['configure_links']
            print(f"✅ 重新上电初始化完成")
            return True
        except Exception as e:
            print(f"❌ 重新上电初始化失败: {e}")
            return False


    def oscilloscope_screenshot_universal(self, gpio_num, active_links=None, test_mode='combined', direction='s2d'):
        """统一的示波器截图方法 - 支持S2D和D2S方向"""
        osc_config = TEST_CONFIG['oscilloscope_config'].copy()
        if direction == 'd2s':
            osc_config['screenshot_folder_base'] = TEST_CONFIG['oscilloscope_config']['screenshot_folder_base_d2s']
            # D2S all link模式：动态生成link格式
            target_links = active_links or TEST_CONFIG['active_links']
            if len(target_links) > 1:
                target_links = [''.join(map(str, target_links))]
        else:
            target_links = active_links or TEST_CONFIG['active_links']

        return get_universal_oscilloscope_screenshot(self.oscilloscope).execute_screenshot(
            gpio_num=gpio_num,
            active_links=target_links,
            test_mode=test_mode,
            osc_config=osc_config
        )

    def test_single_d68_gpio_all_link_s68(self, d68_gpio, link_id=0):
        """测试单个D68 GPIO到所有S68 GPIO - D2S版本（所有Links并行）"""
        try:
            signal_id = TEST_CONFIG['signal_id']
            active_links = TEST_CONFIG['active_links']

            # S68端配置（接收端）- 同时配置所有活跃Links的所有GPIO
            for link in active_links:
                if link < len(self.s68_res_dev):
                    print(f"    📡 配置S68 Link{link} (地址: 0x{self.s68_res_dev[link]:02X})")
                    self.q68_remote.dongle.devAddr = self.s68_res_dev[link]
                    time.sleep(0.1)  # 地址切换后稳定时间

                    for gpio_n in range(9):  # S68 GPIO 0-8
                        self.q68_remote.M2CMFNSet(gpio=gpio_n, mfn=0)
                        self.q68_remote.M2CGPIORemoteRx(gpio=gpio_n, rx_id=signal_id)
                        time.sleep(0.01)  # GPIO配置间隔

            # D68端配置（发送端）- 单个GPIO发送
            self.q68.MFNSet_D68(gpio=d68_gpio, mfn=0)
            self.q68.GPIORemoteTx(gpio=d68_gpio, tx_id=signal_id, link_id=link_id)

            # 示波器测试
            self.oscilloscope_screenshot_universal(d68_gpio, TEST_CONFIG['active_links'], TEST_CONFIG['oscilloscope_config']['test_mode'], direction='d2s')
            return True
        except Exception as e:
            print(f"❌ D68 GPIO{d68_gpio}测试失败: {e}")
            return False
    
    def test_all_s68_gpios_by_link_d68(self, s68_gpio_range, enable_dialog=True):
        """按Link遍历测试所有S68 GPIO - D68版本 (先完成一个Link的所有GPIO，再测试下一个Link)"""
        print(f"\n{'='*80}")
        print(f"🚀 开始S68→D68 GPIO按Link遍历测试")
        print(f"📱 测试范围: S68 GPIO {s68_gpio_range}")
        print(f"🔗 活跃Links: {TEST_CONFIG['active_links']}")
        print(f"💬 确认对话框: {'启用' if enable_dialog else '禁用'}")
        print(f"🎯 测试策略: 先完成Link0所有GPIO，再测试Link2所有GPIO")
        print(f"{'='*80}")

        all_results = {}
        failed_tests = []
        skipped_tests = []
        active_links = TEST_CONFIG['active_links']
        total_tests = len(active_links) * len(s68_gpio_range)
        current_test_count = 0

        # 外层循环：按Link遍历
        for link_index, current_link in enumerate(active_links, 1):
            print(f"\n{'='*80}")
            print(f"🔗 开始测试Link{current_link} ({link_index}/{len(active_links)})")
            print(f"📊 将测试S68 GPIO: {s68_gpio_range}")
            print(f"{'='*80}")

            link_results = {}

            # 内层循环：当前Link的所有GPIO
            for gpio_index, s68_gpio in enumerate(s68_gpio_range, 1):
                # 检查全局停止标志
                global GLOBAL_STOP_FLAG
                if GLOBAL_STOP_FLAG:
                    print(f"🛑 检测到全局停止标志，终止所有测试")
                    return {
                        'results': all_results,
                        'summary': {
                            'total': current_test_count,
                            'completed': len([r for r in all_results.values() if r in [True, False]]),
                            'failed': len(failed_tests),
                            'skipped': len(skipped_tests),
                            'cancelled': True
                        }
                    }

                current_test_count += 1
                test_key = f"Link{current_link}_S68_GPIO{s68_gpio}"

                print(f"\n📍 测试进度: {current_test_count}/{total_tests}")
                print(f"   当前测试: {test_key}")
                print(f"   Link{current_link} GPIO进度: {gpio_index}/{len(s68_gpio_range)}")

                # 弹窗确认 (如果启用) - 传入当前Link信息
                if enable_dialog:
                    dialog_result = self.show_confirmation_dialog(s68_gpio, current_test_count, total_tests, current_link)

                    if dialog_result is None:  # 用户选择取消
                        print(f"❌ 用户取消测试，停止所有后续测试")
                        all_results[test_key] = "cancelled"
                        return {
                            'results': all_results,
                            'summary': {
                                'total': current_test_count - 1,
                                'completed': len([r for r in all_results.values() if r in [True, False]]),
                                'failed': len(failed_tests),
                                'skipped': len(skipped_tests),
                                'cancelled': True
                            }
                        }

                    elif dialog_result is False:  # 用户选择跳过
                        print(f"⏭️  跳过测试: {test_key}")
                        skipped_tests.append(test_key)
                        all_results[test_key] = "skipped"
                        continue

                # 执行完整的系统重新初始化
                reinit_success = self.full_system_reinit_d68()

                if not reinit_success:
                    print(f"❌ 系统重新初始化失败，跳过此S68 GPIO测试")
                    all_results[test_key] = False
                    failed_tests.append(test_key)
                    continue

                # 执行测试 - 修改为只测试当前Link
                try:
                    print(f"🚀 开始执行S68 GPIO{s68_gpio}测试 (仅Link{current_link})...")
                    result = self.test_single_s68_gpio_single_link_d68(s68_gpio, current_link)

                    all_results[test_key] = result
                    link_results[test_key] = result

                    if result:
                        print(f"✅ 测试通过: {test_key}")
                    else:
                        print(f"❌ 测试失败: {test_key}")
                        failed_tests.append(test_key)

                except Exception as e:
                    print(f"💥 测试异常: {test_key} - {e}")
                    all_results[test_key] = False
                    link_results[test_key] = False
                    failed_tests.append(test_key)

                # 测试间隔
                if gpio_index < len(s68_gpio_range):
                    print(f"⏳ 等待1秒后进行下一个GPIO测试...")
                    time.sleep(1)

            # Link测试完成总结
            print(f"\n{'='*60}")
            print(f"📊 Link{current_link} 测试总结:")
            link_passed = [r for r in link_results.values() if r == True]
            link_failed = [r for r in link_results.values() if r == False]
            print(f"   ✅ 成功: {len(link_passed)}/{len(s68_gpio_range)}")
            print(f"   ❌ 失败: {len(link_failed)}/{len(s68_gpio_range)}")
            print(f"   📈 成功率: {len(link_passed)/len(s68_gpio_range)*100:.1f}%")
            print(f"{'='*60}")

            # Link间隔
            if link_index < len(active_links):
                print(f"⏳ 等待2秒后测试下一个Link...")
                time.sleep(2)

        # 最终总结
        print(f"\n{'='*80}")
        print(f"🎯 S68→D68 GPIO按Link遍历测试完成!")

        completed_tests = [r for r in all_results.values() if r in [True, False]]
        passed_tests = [r for r in all_results.values() if r == True]

        summary = {
            'total': total_tests,
            'completed': len(completed_tests),
            'passed': len(passed_tests),
            'failed': len(failed_tests),
            'skipped': len(skipped_tests),
            'cancelled': False
        }

        print(f"📈 测试统计:")
        print(f"  - 总计划测试: {summary['total']}")
        print(f"  - 实际完成: {summary['completed']}")
        print(f"  - 测试通过: {summary['passed']}")
        print(f"  - 测试失败: {summary['failed']}")
        print(f"  - 跳过测试: {summary['skipped']}")

        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test}")

        if skipped_tests:
            print(f"\n⏭️  跳过的测试:")
            for test in skipped_tests:
                print(f"  - {test}")

        print(f"{'='*80}")

        return {
            'results': all_results,
            'summary': summary,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests
        }

    def test_all_d68_gpios_all_link_s68(self, d68_gpio_range, enable_dialog=True):
        """测试所有D68 GPIO - D2S版本（所有Links并行测试）"""
        all_results = {}
        failed_tests = []
        skipped_tests = []
        total_tests = len(d68_gpio_range)
        current_test_count = 0

        for gpio_index, d68_gpio in enumerate(d68_gpio_range, 1):
            # 检查全局停止标志
            global GLOBAL_STOP_FLAG
            if GLOBAL_STOP_FLAG:
                print(f"🛑 检测到全局停止标志，终止所有测试")
                return {
                    'results': all_results,
                    'summary': {
                        'total': current_test_count,
                        'completed': len([r for r in all_results.values() if r in [True, False]]),
                        'failed': len(failed_tests),
                        'skipped': len(skipped_tests),
                        'cancelled': True
                    }
                }

            current_test_count += 1
            test_key = f"D68_GPIO{d68_gpio}_AllLinks"

            if enable_dialog:
                dialog_result = self.show_confirmation_dialog_d2s(d68_gpio, current_test_count, total_tests)
                if dialog_result is None:
                    all_results[test_key] = "cancelled"
                    return {
                        'results': all_results,
                        'summary': {
                            'total': current_test_count - 1,
                            'completed': len([r for r in all_results.values() if r in [True, False]]),
                            'failed': len(failed_tests),
                            'skipped': len(skipped_tests),
                            'cancelled': True
                        }
                    }
                elif dialog_result is False:
                    skipped_tests.append(test_key)
                    all_results[test_key] = "skipped"
                    continue

            if not self.full_system_reinit_d68():
                all_results[test_key] = False
                failed_tests.append(test_key)
                continue

            try:
                result = self.test_single_d68_gpio_all_link_s68(d68_gpio, link_id=0)
                all_results[test_key] = result
                if not result:
                    failed_tests.append(test_key)
            except Exception as e:
                print(f"❌ 测试异常: {test_key} - {e}")
                all_results[test_key] = False
                failed_tests.append(test_key)

            if gpio_index < len(d68_gpio_range):
                time.sleep(1)

        completed_tests = [r for r in all_results.values() if r in [True, False]]
        passed_tests = [r for r in all_results.values() if r == True]
        summary = {
            'total': total_tests,
            'completed': len(completed_tests),
            'passed': len(passed_tests),
            'failed': len(failed_tests),
            'skipped': len(skipped_tests),
            'cancelled': False
        }

        return {
            'results': all_results,
            'summary': summary,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests
        }

    def show_confirmation_dialog_d2s(self, d68_gpio, current_test, total_tests):
        """显示D2S确认对话框 - 5秒后自动选择是（使用线程安全的messagebox）"""
        global GLOBAL_STOP_FLAG

        # 如果已经设置了全局停止标志，直接返回None
        if GLOBAL_STOP_FLAG:
            return None

        import threading

        result = [True]  # 默认为True

        def show_dialog():
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                root.attributes('-topmost', True)  # 置顶
                root.lift()  # 提升到最前面
                root.focus_force()  # 强制获取焦点

                message = f"测试 D68 GPIO{d68_gpio} → S68?\n进度: {current_test}/{total_tests}\n\n5秒后自动开始..."

                # 使用简单的messagebox，更稳定
                from tkinter import messagebox
                dialog_result = messagebox.askyesnocancel("D2S GPIO测试确认", message, parent=root)

                # 设置结果
                result[0] = dialog_result

                # 如果用户点击取消，设置全局停止标志
                if dialog_result is None:
                    global GLOBAL_STOP_FLAG
                    GLOBAL_STOP_FLAG = True

                root.destroy()
            except:
                pass

        # 在新线程中显示对话框
        dialog_thread = threading.Thread(target=show_dialog, daemon=True)
        dialog_thread.start()

        # 等待5秒或用户操作
        dialog_thread.join(timeout=5.0)

        # 如果用户点击了取消，设置全局停止标志
        if result[0] is None:
            GLOBAL_STOP_FLAG = True

        return result[0]


@pytest.mark.fast
def test_gpio_s68_d68_auto_traverse_by_link():
    """按Link遍历测试所有S68 GPIO - D68版本 (先完成Link0所有GPIO，再测试Link2所有GPIO)"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False  # 重置全局停止标志

    print(f"\n🚀 开始S68→D68 GPIO按Link遍历测试")
    print(f"📱 每个S68 GPIO测试前会弹窗确认")
    print(f"🔄 每个S68 GPIO会同时测试所有Q68 GPIO 0-14")
    print(f"🔗 仅使用Link0和Link2 (D68限制)")
    print(f"🎯 测试策略: 先完成Link0所有GPIO，再测试Link2所有GPIO")

    # 使用初始化函数
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    # 使用新的按Link遍历方法测试所有S68 GPIO
    result = tester.test_all_s68_gpios_by_link_d68(
        s68_gpio_range=[0,1,2,3,4,5,6, 7, 8],
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ S68→D68 GPIO自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_gpio_d68_s68_auto_traverse_all_links():
    """D68→S68 GPIO自动遍历测试 - D2S版本（所有Links并行测试）"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False  # 重置全局停止标志

    print(f"\n🚀 开始D68→S68 GPIO自动遍历测试")
    print(f"📱 每个D68 GPIO测试前会弹窗确认")
    print(f"🔄 每个D68 GPIO会同时测试所有S68 GPIO 0-8")
    print(f"🔗 仅使用Link0和Link2 (D68限制)")
    print(f"🎯 测试策略: 所有Links并行测试")

    # 使用初始化函数
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    # 使用新的D2S方法测试所有D68 GPIO
    result = tester.test_all_d68_gpios_all_link_s68(
        d68_gpio_range=[0,1,2,3,4,5,6,7,8,9,10],  # D68 GPIO 0-10
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ D68→S68 GPIO自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_create_all_folders():
    """预创建所有截图文件夹 - 方便复制到U盘"""
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)
    tester.create_all_screenshot_folders()
    print("✅ 所有文件夹创建完成，可以复制到U盘了")


if __name__ == "__main__":
    """
    使用说明:

    🆕 D68芯片GPIO双向测试功能:

    1. S2D方向测试 (S68→D68，串行测试):
       pytest test_gpio_case6_8_s2d_auto3.py::test_gpio_s68_d68_auto_traverse_by_link -v -s

    2. D2S方向测试 (D68→S68，并行测试):
       pytest test_gpio_case6_8_s2d_auto3.py::test_gpio_d68_s68_auto_traverse_all_links -v -s


    🔥 D68版本特点:
    - 基于test_gpio_case6_8_s2q_auto2.py适配D68芯片
    - 支持双向信号测试：S2D（串行）和D2S（并行）
    - 仅使用Link0和Link2 (D68硬件限制)
    - 使用MFNSet_D68方法替代MFNSet
    - 使用d68_complete_initialization()紧凑初始化函数
    - 包含完整的地址转换设置
    - 弹窗确认每个GPIO测试，可选择跳过或取消
    - 每个GPIO测试前完整系统重新初始化
    - 固定频率测试: 30Hz, 1kHz, 10kHz, 50kHz, 100kHz
    - 扫频测试: 1210kHz-1240kHz, 10kHz步进

    📊 测试策略差异:
    - S2D: 串行测试（一个Link一个Link）
    - D2S: 并行测试（所有Links同时）

    📁 截图文件夹结构:
    S2D: U-disk0/gpiotest/s68tod68_0_2/link{id}_gpio{n}/
    D2S: U-disk0/gpiotest/d68tos68_0_2/gpio{n}/

    - 完全兼容原有代码流程，针对D68芯片优化
    """
    print("D68芯片GPIO双向测试用例 (S2D + D2S)")
    print("请使用pytest运行测试")
