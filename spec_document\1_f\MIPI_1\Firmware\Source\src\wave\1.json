{"signal": [{"name": "clk (Byte Clock)", "wave": "p........................................"}, {}, {"name": "Clock Lane", "wave": "=.32.p...............p.4.=", "data": ["LP-11 (Stop)", "HS-REQ", "HS-CLOCK (Preamble)", "HS-CLOCK", "HS-CLOCK (Postamble)", "LP-11 (Stop)"], "period": 0.5}, {"name": "Data Lane", "wave": "===.324.5............6.=", "data": ["LP-11", "HS-REQ", "SoT", "Packet Data", "EoT", "LP-11"]}, {}, {"name": "Logical Packet", "wave": "x........2....3........x..", "data": ["Packet Header", "Payload"]}], "head": {"text": "MIPI D-PHY High-Speed (HS) Burst Timing Diagram", "tick": 0}, "foot": {"text": "Key Principle: Clock Lane must be 'First-In, Last-Out' of High-Speed mode.", "tock": -5}, "config": {"hscale": 2}}