#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from adb.AdbFunction_V2 import AdbFunction

class ADBImpl:
    def __init__(self):
        self.adbFuncation = AdbFunction()

    def __del__(self):
        pass
        # 开启指定的连接设备
    def usbdev_open(self, port_name):
        self.adbFuncation.adb_open(port_name)
    
    # 关闭指令的连接设备
    def usbdev_close(self):
        self.adbFuncation.adb_close()

    def usbdev_get_sn(self):
        return self.adbFuncation.get_sn()
    
    # 获取连接的设备列表
    def usbdev_scan(self):
        return self.adbFuncation.get_connected_devices()
    
    # mode:0:r8v8 1:r8v16
    def usbdev_i2c_set(self, mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):
        return self.adbFuncation.i2c_set(mode, bus, dev_addr, reg_addr, reg_val)
    
    # mode 0:r8v8 1:r16v8
    # return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
    def usbdev_i2c_get(self, mode: int, bus: int, dev_addr: int, reg_addr: int):
        return self.adbFuncation.i2c_get(mode, bus, dev_addr, reg_addr)

    # bus:需要设置的总线
    # speed：需要设置的频率(单位Kbps)
    def usbdev_i2c_setbusspeed(self, bus: int, speed: int):
        return 0

    