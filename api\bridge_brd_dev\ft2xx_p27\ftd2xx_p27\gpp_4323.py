#coding:utf-8
#File:Keysight_86100D.py
#Description:
#Author:ys
#Copyright:XY-Technology International Co.,Ltd
#History
#Version:0.00
#Date:2021.06.04
#Change: 
import time
import serial



class GPP_4323():
    def __init__(self, port='com9'):
        self.ser = serial.Serial(port, 115200, 8, 'N', 1)
        self.ser.write(':REMOTE 1\r')  # remote serial communication format
        
    def IDN_Inqury(self):
        self.ser.write('*IDN?\r')
        time.sleep(0.5)
        sert = ''
        while self.ser.inWaiting()>0:
            sert += self.ser.read(1)
        print sert 
    def VoltageSet(self, channel, voltage):
        
        self.ser.write('VSET'+str(channel)+':'+str(voltage)+'\r')
        
    def PowerOn(self,channel, power):
        'channel: 1/2/3/4'
        'power: ON/OFF'
        if power=='ON':
            self.ser.write(':OUTPut'+str(channel)+':STATe ON\r')
        else:
           self.ser.write(':ALLOUTOFF\r') 
           
    def PowerOff(self,channel,):
        'channel: 1/2/3/4'
        'power: OFF'
        self.ser.write(':OUTPut'+str(channel)+':STATe OFF\r')
        
        
           
    def MeasureCurrent(self,channel):
        self.ser.write(':MEASure'+str(channel)+':CURRent?\r')
        # sert = ''
        # while self.ser.inWaiting()>0:
        #     sert += self.ser.read(6)
        result=self.ser.read(7)
        return float(result)
        
    
    

    
# M = GPP_4323()
# print M.MeasureCurrent(2)
# M.IDN_Inqury()
# # M.VoltageSet(1, 1.8)
# M.PowerOn(4, 'ON')
# M.DTErrCnt_Inquiry(channel=0)
# M.DTErrCnt_Inquiry_All_Channel()
# M.IDN_Inqury()



