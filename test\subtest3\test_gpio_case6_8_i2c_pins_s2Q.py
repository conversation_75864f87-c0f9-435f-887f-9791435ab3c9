#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO I2C引脚测试 - S68→Q68方向 (GPIO 9和10专用)
专门测试I2C引脚GPIO 9和10
需要注意iic bus选择

"""

import logging
import time
import pytest
import os
import sys
import tkinter as tk
from tkinter import messagebox

# Add parent directory to path to import conftest
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
try:
    from conftest import get_universal_oscilloscope_screenshot, power_on
    print("✅ Successfully imported from conftest")
except ImportError as e:
    print(f"❌ Failed to import from conftest: {e}")
    # Define fallback functions if import fails
    def get_universal_oscilloscope_screenshot(oscilloscope):
        print("⚠️ Using fallback oscilloscope screenshot function")
        class FallbackScreenshot:
            def __init__(self, osc): self.osc = osc
            def execute_screenshot(self, **kwargs): return True
        return FallbackScreenshot(oscilloscope)

    def power_on():
        print("⚠️ Using fallback power_on function")
        return True


# 禁用pyvisa的DEBUG日志，减少冗余输出
logging.getLogger('pyvisa').setLevel(logging.WARNING)

# 添加D68模块路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65q68_a0'))  # D68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'))  # S68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # 示波器模块路径

# Q68芯片导入
try:
    from Common.M65Q68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    print("✅ D68模块导入成功")
except ImportError as e:
    print(f"⚠️ D68模块导入失败: {e}")
    class M65Q68_A0: pass
    class M65Q68_A0_Remote_M66S68: pass

# 全局停止标志
GLOBAL_STOP_FLAG = False

# 设备初始化标志 - 确保设备只初始化一次
_devices_initialized = False
_global_devices = {}

def cleanup_devices():
    """清理设备连接和线程"""
    try:
        import gc
        gc.collect()  # 强制垃圾回收
        print("🧹 清理设备连接和线程")
        time.sleep(0.5)  # 等待清理完成
    except Exception as e:
        print(f"⚠️ 清理设备时出现异常: {e}")

# I2C引脚专用配置
I2C_GPIO_CONFIG = {
    'signal_id': 11,
    'active_links': [0, 1,2,3],  # D68限制只能使用Link0和Link2
    'gpio_range': [9, 10],   # 只测试I2C引脚
    
    # I2C2设备配置
    'i2c2_dongle_id': b'\x00:\x00&41Q\x024590',
    'i2c2_device_addr': 0x40,
    
    # # 示波器配置
    # 'oscilloscope_config': {
    #     'screenshot_folder_base': 'U-disk0/gpiotest/s68toq68_i2c',  # S68→Q68 I2C测试截图文件夹
    #     'test_mode': 'combined',
    #     'frequency_list': [30, 1000, 10000, 50000, 100000],  # 固定频率列表 (Hz)
    #     'timebase_list': ['20ms', '1ms', '100us', '10us', '5us'],   # 对应的时基 - 优化100kHz时基
    #     'frequency_range': {
    #         'start': 1220000,                    # 1210kHz
    #         'end': 1240000,                      # 1240kHz
    #         'step': 10000                       # 10kHz步进
    #     },
    #     'sweep_timebase': '500ns',              # 扫频模式的时基
    #     'trigger_source': 'D0',
    #     'persistence_mode': 'INFinite',
    #     'waveform_type': 'SQUARE',
    #     'amplitude': 1.8,
    #     'offset': 0.9,
    #     'freq_observation_time': 1,
    #     'enable_screenshot': True,
    # }

    # FOT CODE TEST SAVE TIME:
    'oscilloscope_config': {
        'screenshot_folder_base': 'U-disk0/gpiotest/s68toq68_i2c',  # S68→Q68 I2C测试截图文件夹
        'test_mode': 'fixed',
        'frequency_list': [30],  # 固定频率列表 (Hz)
        'timebase_list': ['20ms'],   # 对应的时基 - 优化100kHz时基
        'frequency_range': {
            'start': 1220000,                    # 1210kHz
            'end': 1240000,                      # 1240kHz
            'step': 10000                       # 10kHz步进
        },
        'sweep_timebase': '500ns',              # 扫频模式的时基
        'trigger_source': 'D0',
        'persistence_mode': 'INFinite',
        'waveform_type': 'SQUARE',
        'amplitude': 1.8,
        'offset': 0.9,
        'freq_observation_time': 1,
        'enable_screenshot': True,
    }
}

 

# D68设备初始化配置
D68_I2C_DEVICE_CONFIG = {
    'q68_iic_addr': 0x73,
    's68_iic_dev': [0x40, 0x40, 0x40, 0x40],
    's68_res_dev': [0x20, 0x21, 0x22, 0x23],
    's68_res_sensor_dev': [0x24, 0x25, 0x26, 0x27],
    'active_links': [0, 1, 2, 3],  # D68仅支持Link0和Link2
    'RATE':         [2, 2, 2, 2],
    'RATE_final':   [2, 2, 2, 2],
    'BCRATE':       [0, 0, 0, 0]  
}

def cleanup_devices():
    try:
        import gc
        gc.collect()  # 强制垃圾回收
        print("🧹 清理设备连接和线程")
    except: pass

def d68_i2c_initialization():
    """D68 I2C引脚专用初始化函数 - 使用flag确保只初始化一次"""
    global _devices_initialized, _global_devices

    # 如果设备已经初始化，直接返回缓存的设备
    if _devices_initialized:
        print("🔄 设备已初始化，复用现有设备")
        return _global_devices

    cleanup_devices()
    print("🔄 开始D68 I2C引脚初始化...")
    power_on()
    config = D68_I2C_DEVICE_CONFIG

    # 初始化4个设备（只初始化一次）
    q68 = M65Q68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual')
    print("✅ q68 init")
    q68_remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00Q\x00$41Q\x197997', dongle='stm32', id=0, bus='i2c', optype='manual')
    print("✅ q68_remote init")
    q68_I2C2 = M65Q68_A0(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual')  # G9PH DS board, bus_chan=1 for I2C1 (SDA1, SCL1)
    print("✅ q68_I2C2 init")
    q68_I2C2remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', optype='manual')
    print("✅ q68_I2C2remote init")
    
    # # D68特定初始化
    # q68_I2C2.c2m.wr_test_glb_ctrl0_fields(key=0x5c)
    # q68_I2C2.c2m.wr_test_tx_link_data_inv_fields(tx_polar_sel=0x6)
    # q68_I2C2.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel=0x6)
    
    # 设置链路速率
    q68_I2C2.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])
    q68_I2C2.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=config['BCRATE'][0], bc_rate1=config['BCRATE'][1], bc_rate2=config['BCRATE'][2], bc_rate3=config['BCRATE'][3])
    q68_I2C2.Q68_C3_6G_Init(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])
    
    # 地址转换
    for link in config['active_links']:
        q68_I2C2remote.S68_AddrTrans(link=link, q68_iic_addr=0x73, s68_iic_addr=0x40, s68_retrans_addr=config['s68_res_dev'][link], sensor_addr=0x24, sensor_retrans_addr=0x24 + link,i2c_bus=1)
    
    # 最终链路速率设置
    q68_I2C2.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])
    q68_I2C2.Q68_C3_6G_Init(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])
    
    # 初始化示波器
    try:
        from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
        oscilloscope = SiglentSDS5034X()
    except:
        oscilloscope = None

    # 保存设备到全局缓存
    _global_devices = {
        'q68': q68,
        'q68_remote': q68_remote,
        'q68_I2C2': q68_I2C2,
        'q68_I2C2remote': q68_I2C2remote,
        's68_res_dev': config['s68_res_dev'],
        'oscilloscope': oscilloscope,
        'configure_links': config['active_links']
    }

    # 设置初始化标志
    _devices_initialized = True
    print("✅ 设备初始化完成，已缓存到全局变量")

    return _global_devices

class GPIO_I2C_Tester:
    """I2C引脚GPIO专用测试器"""


    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.q68_I2C2 = devices['q68_I2C2']
        self.q68_I2C2remote = devices['q68_I2C2remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def create_all_i2c_screenshot_folders(self):
        """预创建所有I2C截图文件夹 - 按Link和GPIO分类"""
        import os
        screenshot_base = I2C_GPIO_CONFIG['oscilloscope_config']['screenshot_folder_base']
        active_links = I2C_GPIO_CONFIG['active_links']
        gpio_range = I2C_GPIO_CONFIG['gpio_range']

        print(f"🔄 创建I2C截图文件夹...")
        print(f"📁 基础路径: {screenshot_base}")
        print(f"🔗 活跃Links: {active_links}")
        print(f"🔌 GPIO范围: {gpio_range}")

        # 按Link和GPIO分类创建文件夹
        for current_link in active_links:
            for gpio_num in gpio_range:
                folder_path = f"{screenshot_base}/link{current_link}_gpio{gpio_num}"
                os.makedirs(folder_path, exist_ok=True)
                print(f"✅ 创建文件夹: {folder_path}")

        print(f"✅ 所有I2C截图文件夹创建完成")

    def show_i2c_confirmation_dialog(self, gpio_num, current_test=0, total_tests=1):
        """I2C引脚专用确认对话框"""
        global GLOBAL_STOP_FLAG
        if GLOBAL_STOP_FLAG: return None
        
        import threading
        result = [True]
        
        def show_dialog():
            try:
                root = tk.Tk()
                root.withdraw()
                root.attributes('-topmost', True)
                
                special_msg = f"\n⚠️ GPIO[9,10]是I2C引脚，需要:\n1. 去掉上拉电阻\n2. 配置完成后再接探头\n3. 等待5秒配置时间"
                message = f"测试 S68 I2C GPIO{gpio_num}?\n进度: {current_test}/{total_tests}{special_msg}\n\n10秒后自动开始..."
                
                result[0] = messagebox.askyesnocancel("I2C GPIO测试", message, parent=root)
                if result[0] is None: 
                    global GLOBAL_STOP_FLAG
                    GLOBAL_STOP_FLAG = True
                root.destroy()
            except: pass
        
        thread = threading.Thread(target=show_dialog, daemon=True)
        thread.start()
        thread.join(timeout=10.0)  # I2C引脚等待10秒
        if result[0] is None: GLOBAL_STOP_FLAG = True
        return result[0]

        
    def test_single_S68_i2c_gpio(self, S68_gpio, current_link=None):
        """测试单个S68 GPIO到Q68 I2C引脚 - 专用于GPIO 9和10"""
        try:
            if current_link is None:
                active_links = I2C_GPIO_CONFIG['active_links']
                current_link = active_links[0]
            s68_iic_dev = self.s68_res_dev[current_link]
            self.q68_I2C2remote.dongle.devAddr = s68_iic_dev
            # self.q68_I2C2remote.M2CMFNSet(gpio=S68_gpio, mfn=0)
            # self.q68_I2C2remote.M2CGPIORemoteTx(gpio=S68_gpio, tx_id=11)
            self.q68_I2C2remote.M2CMFNSet(gpio=9, mfn=0)
            self.q68_I2C2remote.M2CGPIORemoteTx(gpio=9, tx_id=11)
            for gpio_n in range(7): 
                self.q68_I2C2.MFNSet_Q68E(gpio=gpio_n, mfn=0)
                self.q68_I2C2.GPIORemoteRx(gpio=gpio_n, rx_id=11)     
                                    
            self.q68_I2C2.MFNSet_Q68E(gpio=9, mfn=0)
            self.q68_I2C2.GPIORemoteRx(gpio=9, rx_id=11)
            self.q68_I2C2.MFNSet_Q68E(gpio=10, mfn=0)
            self.q68_I2C2.GPIORemoteRx(gpio=10, rx_id=11)
                # q68_i2c2.Log_FaultHandler()
            print('done')
            # 示波器测试
            screenshot_result = self.oscilloscope_screenshot_i2c(S68_gpio, current_link)
            print(f"    📷 示波器截图: {'✅' if screenshot_result else '❌'}")
            return screenshot_result
        except Exception as e:
            print(f"❌ D68 GPIO{S68_gpio}→S68 I2C测试失败: {e}")
            return False

    def oscilloscope_screenshot_i2c(self, s68_gpio, current_link):
        """I2C引脚专用示波器截图 - 按Link和GPIO分类保存"""
        if not self.oscilloscope:
            print(f"    ⚠️ 示波器未连接，跳过截图")
            return True

        try:
            print(f"    📷 开始I2C截图: GPIO{s68_gpio}, Link{current_link}")

            # 获取配置并确保enable_screenshot为True
            osc_config = I2C_GPIO_CONFIG['oscilloscope_config'].copy()
            osc_config['enable_screenshot'] = True

            print(f"    📷 截图配置: 基础路径={osc_config['screenshot_folder_base']}, 模式={osc_config['test_mode']}")

            # 使用通用截图方法，参考其他测试文件的成功模式
            result = get_universal_oscilloscope_screenshot(self.oscilloscope).execute_screenshot(
                gpio_num=s68_gpio,
                active_links=[current_link],  # 传递单个Link作为列表
                test_mode=osc_config['test_mode'],
                osc_config=osc_config
            )
            print(f"    📷 Link{current_link} GPIO{s68_gpio} I2C截图结果: {'✅' if result else '❌'}")
            return result
        except Exception as e:
            print(f"    ❌ I2C示波器截图异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def test_all_S68_to_i2c_pins(self, S68_gpio_range, enable_dialog=True):
        """测试所有S68 GPIO到Q68 I2C引脚 - 按Link遍历"""
        all_results = {}

        for current_link in I2C_GPIO_CONFIG['active_links']:
            for S68_gpio in S68_gpio_range:
                global GLOBAL_STOP_FLAG
                if GLOBAL_STOP_FLAG: break

                test_key = f"Link{current_link}_S68_GPIO{S68_gpio}_to_Q68_I2C"

                if enable_dialog:
                    dialog_result = self.show_i2c_confirmation_dialog(S68_gpio, 0, 1)
                    if dialog_result is None: break
                    elif dialog_result is False: continue

                # 不需要重新初始化设备，复用全局设备
                print(f"🔄 复用全局设备，无需重新初始化...")

                try:
                    result = self.test_single_S68_i2c_gpio(S68_gpio, current_link)
                    all_results[test_key] = result
                    print(f"{'✅' if result else '❌'} Link{current_link} S68 GPIO{S68_gpio}→Q68 I2C测试")
                except Exception:
                    all_results[test_key] = False

                time.sleep(1)
            if GLOBAL_STOP_FLAG: break

        completed = [r for r in all_results.values() if r in [True, False]]
        passed = [r for r in all_results.values() if r == True]
        return {
            'results': all_results,
            'summary': {'total': len(all_results), 'completed': len(completed), 'passed': len(passed)}
        }

@pytest.mark.fast
def test_gpio_S68_to_Q68E_i2c_pins():
    """D68→S68 I2C引脚测试 - 专门测试GPIO 9和10"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    print(f"\n🚀 开始S68→Q68 I2C引脚测试")
    print(f"🔌 测试I2C引脚: GPIO 9和10")
    print(f"🔗 使用Links: {I2C_GPIO_CONFIG['active_links']}")

    devices = d68_i2c_initialization()
    tester = GPIO_I2C_Tester(devices)

    # 测试所有S68 GPIO到Q68E I2C引脚[0,1,2,3,4,5,6,7,8,9,10]
    result = tester.test_all_S68_to_i2c_pins([9,10], enable_dialog=True)

    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何I2C引脚测试"
    print(f"✅ I2C引脚测试完成，通过率: {summary['passed']}/{summary['completed']}")

@pytest.mark.fast
def test_create_i2c_folders():
    """预创建所有I2C截图文件夹 - 方便复制到U盘"""
    devices = d68_i2c_initialization()
    tester = GPIO_I2C_Tester(devices)
    tester.create_all_i2c_screenshot_folders()
    print("✅ 所有I2C文件夹创建完成，可以复制到U盘了")

if __name__ == "__main__":
    """
    🔌 D68→S68 I2C引脚测试用例

    专门测试I2C引脚GPIO 9和10，使用独立的I2C2设备配置

    🚀 使用方法:
    pytest test/subtest2/test_gpio_case6_8_i2c_pins_d2s.py::test_gpio_d68_to_s68_i2c_pins -v -s

    🔧 特点:
    - 独立的I2C2设备配置
    - 专用的I2C引脚确认对话框
    - 10秒等待时间，5秒配置稳定时间
    - 专用截图文件夹: U-disk0/gpiotest/d68tos68_i2c_0_2/
    """
    print("I2C引脚GPIO测试用例 (D68→S68)")
    print("请使用pytest运行测试")
