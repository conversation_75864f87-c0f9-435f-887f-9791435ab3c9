# -*- coding: utf-8 -*-
"""
GPIO Case-5: Q68 GPIO15/16 to S68 GPIOs Test

测试Q68 GPIO15/16 (I2C1引脚) 向S68发送信号的功能。
GPIO15和GPIO16是I2C1的SDA和SCL引脚，需要特殊配置。
"""
import logging
import time
import pytest
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from gpio_config_utils import check_and_configure_special_gpio, setup_address_translation_with_gpio_config
    GPIO_UTILS_AVAILABLE = True
except ImportError:
    print("警告: gpio_config_utils模块未找到，将使用内联配置")
    GPIO_UTILS_AVAILABLE = False

# 测试配置 - 可以手动修改
TEST_CONFIG = {
    'active_links': [0],           # 默认使用Link0
    'q68_source_gpio': 15,         # Q68源GPIO (15或16)
    's68_target_gpios': [0, 1, 2, 3, 4, 5, 6],  # S68目标GPIO列表
    'signal_id': 11,               # GPIO信号ID
    'observation_time': 10,        # 观察时间(秒)
    'i2c_bus': 0                   # 将根据GPIO自动设置
}

@pytest.mark.fast
def test_gpio15_16_q68_to_s68(devices):
    """
    测试Q68 GPIO15/16向S68发送信号
    
    GPIO15/16是I2C1的SDA/SCL引脚，需要：
    1. 设置i2c_bus=1
    2. 提示用户连接GPIO 11/12 (SDA1/SCL1)
    3. 正确配置地址转换
    """
    print(f"\n[GPIO15/16测试] 开始执行: Q68 GPIO{TEST_CONFIG['q68_source_gpio']} -> S68")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")

    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']

    # ---------------------------------------------------------------------
    # 0. Configure specific Links for this test
    # ---------------------------------------------------------------------
    print(f"\n步骤0: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")

    # ---------------------------------------------------------------------
    # 1. GPIO15/16特殊配置检查
    # ---------------------------------------------------------------------
    q68_source_gpio = TEST_CONFIG['q68_source_gpio']
    
    if GPIO_UTILS_AVAILABLE:
        # 使用工具函数检查和配置
        config_result = check_and_configure_special_gpio(q68_source_gpio, TEST_CONFIG)
        print(f"  - GPIO配置结果: {config_result['config_message']}")
        i2c_bus_config = config_result['i2c_bus']
    else:
        # 内联配置
        if q68_source_gpio in [15, 16]:
            print(f"\n⚠️  检测到Q68 GPIO{q68_source_gpio} (I2C1引脚)")
            print("=" * 60)
            print("🔌 硬件连接提示:")
            print("   请确保已连接 Q68 GPIO 11/12 (SDA1/SCL1)")
            print("   - GPIO 11: SDA1 (I2C1数据线)")
            print("   - GPIO 12: SCL1 (I2C1时钟线)")
            print("=" * 60)
            
            i2c_bus_config = 1
            TEST_CONFIG['i2c_bus'] = 1
            print(f"✅ 自动设置 i2c_bus = 1 (用于GPIO{q68_source_gpio})")
            
            print("\n请确认硬件连接后按Enter继续...")
            input("按Enter继续测试...")
        else:
            i2c_bus_config = 0
            TEST_CONFIG['i2c_bus'] = 0
            print(f"\n使用标准GPIO{q68_source_gpio}配置 (i2c_bus = 0)")

    # ---------------------------------------------------------------------
    # 2. 设置地址转换
    # ---------------------------------------------------------------------
    if GPIO_UTILS_AVAILABLE:
        # 使用工具函数设置地址转换
        success = setup_address_translation_with_gpio_config(
            q68_remote, 
            TEST_CONFIG['active_links'], 
            s68_res_dev, 
            q68_source_gpio
        )
        if not success:
            print("地址转换设置失败，使用备用方法")
            GPIO_UTILS_AVAILABLE = False
    
    if not GPIO_UTILS_AVAILABLE:
        # 内联地址转换设置
        print(f"\n步骤2: 设置激活Links的地址转换 (i2c_bus={i2c_bus_config})...")
        for link in TEST_CONFIG['active_links']:
            if link < len(s68_res_dev):
                q68_remote.S68_AddrTrans(
                    link=link,
                    q68_iic_addr=0x73,
                    s68_iic_addr=0x40,
                    s68_retrans_addr=s68_res_dev[link],
                    sensor_addr=0x24,
                    sensor_retrans_addr=0x24 + link,
                    i2c_bus=i2c_bus_config
                )
                print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40 (i2c_bus={i2c_bus_config})")
        print("  - 地址转换设置完成")

    # ---------------------------------------------------------------------
    # 3. Q68 Side Configuration (Transmitter)
    # ---------------------------------------------------------------------
    print(f"\n步骤3: 配置 Q68 GPIO-{q68_source_gpio} 为发送端 (Signal ID: {TEST_CONFIG['signal_id']})...")
    print(f"    - I2C总线配置: {i2c_bus_config}")
    
    q68.MFNSet(gpio=q68_source_gpio, mfn=0)

    # 使用第一个激活的Link作为发送Link
    primary_link = TEST_CONFIG['active_links'][0]
    q68.GPIORemoteTx(gpio=q68_source_gpio, tx_id=TEST_CONFIG['signal_id'],
                     link_id=primary_link, dly_comp_en=0)
    print(f"    - Q68 GPIO{q68_source_gpio} 配置完成，使用Link{primary_link}")

    # ---------------------------------------------------------------------
    # 4. S68 Side Configuration (Receivers)
    # ---------------------------------------------------------------------
    print(f"\n步骤4: 配置激活Links上的S68 GPIOs {TEST_CONFIG['s68_target_gpios']} 为接收端...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            target_addr = s68_res_dev[link]
            print(f"    - 配置 S68 Link-{link} (地址: 0x{target_addr:02X})...")

            # 设置设备地址
            q68_remote.dongle.devAddr = target_addr
            print(f"      * 设备地址已设置为: 0x{target_addr:02X}")

            # 等待一小段时间确保地址设置生效
            time.sleep(0.1)

            for s68_pin in TEST_CONFIG['s68_target_gpios']:
                print(f"      * 配置GPIO{s68_pin}...")

                # 设置MFN为0 (GPIO功能)
                try:
                    q68_remote.M2CMFNSet(gpio=s68_pin, mfn=0)
                    print(f"        - GPIO{s68_pin} MFN设置为0 (GPIO功能)")
                except Exception as e:
                    print(f"        - 错误: GPIO{s68_pin} MFN设置失败: {e}")

                # 设置远程接收
                try:
                    q68_remote.M2CGPIORemoteRx(gpio=s68_pin, rx_id=TEST_CONFIG['signal_id'])
                    print(f"        - GPIO{s68_pin} 远程接收设置完成 (信号ID: {TEST_CONFIG['signal_id']})")
                except Exception as e:
                    print(f"        - 错误: GPIO{s68_pin} 远程接收设置失败: {e}")

        else:
            print(f"    - 警告: Link{link} 超出s68_res_dev范围，跳过")
    print("    - 激活Links上的S68 GPIO 配置完成。")

    # ---------------------------------------------------------------------
    # 5. Observation and Verification
    # ---------------------------------------------------------------------
    print(f"\n步骤5: 等待{TEST_CONFIG['observation_time']}秒以便观察信号...")
    time.sleep(TEST_CONFIG['observation_time'])

    # 只检查激活Links的状态
    print(f"\n步骤6: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    active_statuses = {}
    for link_id in TEST_CONFIG['active_links']:
        if 0 <= link_id <= 3:
            status = link_status_funcs[link_id]()
            active_statuses[f'link{link_id}'] = status
            print(f"    - Link{link_id} 状态: {status}")

    # 验证激活Links的状态
    failed_links = [link for link, status in active_statuses.items() if status != 5]
    assert len(failed_links) == 0, \
        f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"

    print(f"\n==> [GPIO15/16测试] 测试通过: Q68 GPIO{q68_source_gpio} -> S68 GPIOs{TEST_CONFIG['s68_target_gpios']} 信号路径已配置到Links {TEST_CONFIG['active_links']}，链路状态正常。")


@pytest.mark.parametrize("gpio_pin", [15, 16])
def test_gpio15_16_parametrized(devices, gpio_pin):
    """参数化测试 - 分别测试GPIO15和GPIO16"""
    # 临时修改配置
    original_gpio = TEST_CONFIG['q68_source_gpio']
    TEST_CONFIG['q68_source_gpio'] = gpio_pin
    
    try:
        test_gpio15_16_q68_to_s68(devices)
        print(f"✅ GPIO{gpio_pin} 测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['q68_source_gpio'] = original_gpio


@pytest.mark.parametrize("link_combination", [
    [0],           # 只测试Link0
    [1],           # 只测试Link1
    [0, 1],        # 测试Link0和Link1
])
def test_gpio15_16_different_links(devices, link_combination):
    """参数化测试 - 测试不同的Link组合"""
    # 临时修改配置
    original_links = TEST_CONFIG['active_links']
    TEST_CONFIG['active_links'] = link_combination
    
    try:
        test_gpio15_16_q68_to_s68(devices)
        print(f"✅ Link组合 {link_combination} 测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['active_links'] = original_links


if __name__ == "__main__":
    """
    使用说明:
    
    1. 测试GPIO15:
       pytest test_gpio_case5_7_GPIO15_16.py::test_gpio15_16_q68_to_s68 -v -s
    
    2. 参数化测试GPIO15和GPIO16:
       pytest test_gpio_case5_7_GPIO15_16.py::test_gpio15_16_parametrized -v -s
    
    3. 测试不同Link组合:
       pytest test_gpio_case5_7_GPIO15_16.py::test_gpio15_16_different_links -v -s
    
    4. 手动修改TEST_CONFIG中的q68_source_gpio来测试特定GPIO
    """
    print("GPIO15/16 (I2C1引脚) 测试用例")
    print("请使用pytest运行测试")
