import QRegisterAccess
import time

# ft4222 (loop = 1024, size = 1024 * 5)
#     ~0.57Mbytes/s for 40MHz SPI clock
#     ~0.34Mbytes/s for 10MHz SPI clock
#     ~0.26Mbytes/s for  5MHz SPI clock

if QRegisterAccess.findMeritechDACSpiChan():
    addr=0x15
    value=0x0505
    # QRegisterAccess.spiReadDACReg(addr)
    # QRegisterAccess.spiWriteDACReg(addr, value)
    QRegisterAccess.spiReadDACReg(addr)
    QRegisterAccess.spiWriteDACReg(addr, value)
    print ('done')