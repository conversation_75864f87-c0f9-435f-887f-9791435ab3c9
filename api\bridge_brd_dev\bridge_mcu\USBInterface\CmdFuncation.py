#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 用于交互中的指令响应

from USBInterface import USBImpl
from USBInterface import USBDataDeal
from threading import Event

res_nodata_event = Event()
global res_nodata_data
res_0000_event = Event()
global res_0000_data
res_0002_event = Event()
global res_0002_data
res_0004_event = Event()
global res_0004_data

###########################################################################
# 处理需要发送的信息


# 响应指令【无携带数据】
def send_cmd_respond_nodata(cmd_id, result):
    data = bytearray(12)
    # 帧头
    data[0] = 0xAA
    data[1] = 0xAA

    # 回复指令ID: 0xFFFE
    data[2] = 0xFF
    data[3] = 0xFE

    # 回复对应的指令
    data[4] = cmd_id >> 8 & 0xFF
    data[5] = cmd_id & 0xFF
    data[6] = result

    # 数据长度
    data[7] = 0x00
    data[8] = 0x00

    # CRC
    data[9] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8]

    # 帧尾
    data[10] = 0xA5
    data[11] = 0xA5

    USBDataDeal.usb_send_buff(data)


# ID:0x0000: 获取设备SN号
def send_cmd_get_sn():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 设置引脚指令
    data[3] = 0x00  # 设置引脚指令

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度
    data[6] = data[2]^data[3]^data[4]^data[5]

    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾

    ret = USBDataDeal.usb_send_buff(data)
    if ret != 0:
        print("send_cmd_get_sn usb_send_buff error")
        return -1, -1

    # 等待响应
    res_0000_event.clear()
    if not res_0000_event.wait(0.1):
        print("send_cmd_get_sn respond error")
        return -1, -1

    if res_0000_data[2] != 0:
        return -1, -1
    else:
        return -res_0000_data[2], res_0000_data[3:21]


# ID:0x0001: 设置指定输出引脚的值
def send_cmd_set_gpio(pin_num: int, pin_val: int):
    data = bytearray(11)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 设置引脚指令
    data[3] = 0x01  # 设置引脚指令

    data[4] = 0x00  # 数据长度
    data[5] = 0x02  # 数据长度
    data[6] = pin_num
    data[7] = pin_val
    data[8] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7]

    data[9] = 0xA5  # 帧尾
    data[10] = 0xA5  # 帧尾

    ret = USBDataDeal.usb_send_buff(data)
    if ret != 0:
        print("send_cmd_set_gpio usb_send_buff error")
        return -1

    # 等待响应
    res_nodata_event.clear()
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_gpio respond error")
        return -1

    return -res_nodata_data[2]


# ID:0x0002: 获取指定输入引脚的值
def send_cmd_get_gpio(pin_num: int):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 设置引脚指令
    data[3] = 0x02  # 设置引脚指令

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = pin_num
    data[7] = data[2]^data[3]^data[4]^data[5]^data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    ret = USBDataDeal.usb_send_buff(data)
    if ret != 0:
        print("send_cmd_get_gpio usb_send_buff error")
        return -1, -1

    # 等待响应
    res_0002_event.clear()
    if not res_0002_event.wait(0.1):
        print("send_cmd_get_gpio respond error")
        return -1, -1

    if res_0002_data[2] != 0:
        return -1, -1
    else:
        return -res_0002_data[2], res_0002_data[5]


# ID:0x0003:设置I2C寄存器的值
# mode:0:r8v8 1:r16v8
def send_cmd_i2c_set(mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):
    data = bytearray(16)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 设置引脚指令
    data[3] = 0x03  # 设置引脚指令

    data[4] = 0x00  # 数据长度
    data[5] = 0x07  # 数据长度
    data[6] = mode
    data[7] = bus
    data[8] = dev_addr
    data[9] = reg_addr >> 8 & 0xFF
    data[10] = reg_addr & 0xFF
    data[11] = reg_val >> 8 & 0xFF
    data[12] = reg_val & 0xFF

    data[13] = (data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9]
                ^ data[10] ^ data[11] ^ data[12])

    data[14] = 0xA5  # 帧尾
    data[15] = 0xA5  # 帧尾

    ret = USBDataDeal.usb_send_buff(data)
    if ret != 0:
        print("send_cmd_i2c_set usb_send_buff error")
        return -1

    # 等待响应
    res_nodata_event.clear()
    if not res_nodata_event.wait(0.1):
        print("send_cmd_i2c_set respond error")
        return -1

    return -res_nodata_data[2]


# ID:0x0004:获取I2C寄存器的值
# return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
def send_cmd_i2c_get(mode: int, bus: int, dev_addr: int, reg_addr: int):
    data = bytearray(16)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 设置引脚指令
    data[3] = 0x04  # 设置引脚指令

    data[4] = 0x00  # 数据长度
    data[5] = 0x05  # 数据长度
    data[6] = mode
    data[7] = bus
    data[8] = dev_addr
    data[9] = reg_addr >> 8 & 0xFF
    data[10] = reg_addr & 0xFF

    data[11] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10]

    data[12] = 0xA5  # 帧尾
    data[13] = 0xA5  # 帧尾

    ret = USBDataDeal.usb_send_buff(data)
    if ret != 0:
        print("send_cmd_i2c_get usb_send_buff error")
        return -1, -1

    # 等待响应
    res_0004_event.clear()
    if not res_0004_event.wait(0.1):
        print("send_cmd_i2c_get respond error")
        return -1, -1

    if res_0004_data[2] != 0:
        return -1, -1
    else:
        return -res_0004_data[2], (res_0004_data[5] << 8) +  res_0004_data[6]

# ID:0x0005: 设置数据类型，0:真实数据，1:模拟数据
def send_cmd_set_mode(mode: int):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 设置引脚指令
    data[3] = 0x05  # 设置引脚指令

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = mode
    data[7] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    ret = USBDataDeal.usb_send_buff(data)
    if ret != 0:
        print("send_cmd_set_gpio usb_send_buff error")
        return -1

    # 等待响应
    res_nodata_event.clear()
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_gpio respond error")
        return -1

    return -res_nodata_data[2]


# ID:0x0006: 设置I2C速率，  bus：需要设置的总线，speed:需要设置的频率（kbps）
def send_cmd_i2c_setbusspeed(bus: int, speed: int):
    data = bytearray(12)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 设置引脚指令
    data[3] = 0x06  # 设置引脚指令

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = bus
    data[7] = speed >> 8 & 0xFF
    data[8] = speed & 0xFF
    data[9] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8]

    data[10] = 0xA5  # 帧尾
    data[11] = 0xA5  # 帧尾

    ret = USBDataDeal.usb_send_buff(data)
    if ret != 0:
        print("send_cmd_i2c_setbusspeed usb_send_buff error")
        return -1

    # 等待响应
    res_nodata_event.clear()
    if not res_nodata_event.wait(0.1):
        print("send_cmd_i2c_setbusspeed respond error")
        return -1

    return -res_nodata_data[2]
###########################################################################


###########################################################################
# 处理接收到的信息

# 处理回应数据的子信息
def respond_cmd_nodata_fun(cmd_buff: bytearray, cmd_len: int):
    global res_nodata_data
    res_nodata_data = cmd_buff[4:7]
    res_nodata_event.set()


# get sn
def respond_cmd_0000_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0000_data
    res_0000_data = cmd_buff[4:25]
    res_0000_event.set()


# get gpio
def respond_cmd_0002_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0002_data
    res_0002_data = cmd_buff[4:10]
    res_0002_event.set()


# get i2c
def respond_cmd_0004_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0004_data
    res_0004_data = cmd_buff[4:11]
    res_0004_event.set()


# 处理回应过来的子指令
function_cmd_respond_map = {
    0x0000: respond_cmd_0000_fun,       # get sn
    0x0001: respond_cmd_nodata_fun,     # set gpio
    0x0002: respond_cmd_0002_fun,       # get gpio
    0x0003: respond_cmd_nodata_fun,     # set i2c
    0x0004: respond_cmd_0004_fun,       # get i2c
    0x0005: respond_cmd_nodata_fun,     # set data mode
    # 添加更多的函数映射
}


# 处理对方发过来的指令：ID=0x1001
def receive_cmd_1001_fun(cmd_buff: bytearray, cmd_len: int):
    if cmd_buff[6] == 0:
        print("cmd_1001_fun ic init success")
    else:
        print("cmd_1001_fun ic init error")
    send_cmd_respond_nodata(0x1001, 0)


# 处理MCU对指令的响应
def receive_cmd_ffff_fun(cmd_buff: bytearray, cmd_len: int):
    target_function = function_cmd_respond_map.get(cmd_buff[4]<<8 | cmd_buff[5])
    target_function(cmd_buff, cmd_len)


# 处理发送过来的指令
function_cmd_map = {
    0x1001: receive_cmd_1001_fun,  # MCU发送过来的指令
    0xFFFF: receive_cmd_ffff_fun,  # MCU对指令的响应
    # 添加更多的函数映射
}


# 按照指令ID, 分发到对应的函数处理
def publish_cmd(cmd_buff: bytearray, cmd_len: int):
    target_function = function_cmd_map.get(cmd_buff[2]<<8 | cmd_buff[3])
    target_function(cmd_buff, cmd_len)
###########################################################################
