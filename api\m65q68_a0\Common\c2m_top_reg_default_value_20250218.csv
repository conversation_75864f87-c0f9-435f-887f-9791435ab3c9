address,default,name
0x00000001,0x00000000,c2m_sys_cfg_dev_info0
0x00000002,0x00000000,c2m_sys_cfg_dev_info1
0x00000003,0x00000000,c2m_sys_cfg_dev
0x00000004,0x00000007,c2m_sys_cfg_i2c_uart
0x00000005,0x00000009,c2m_sys_cfg_link_ctrl0
0x00000006,0x00000000,c2m_sys_cfg_link_ctrl1
0x00000007,0x00000000,c2m_sys_cfg_link_ctrl2
0x00000008,0x00000000,c2m_sys_cfg_link_ctrl3
0x00000009,0x0000000f,c2m_sys_cfg_link_ctrl4
0x0000000a,0x00000024,c2m_sys_cfg_uart_link_ctrl
0x0000000b,0x00000000,c2m_sys_cfg_spi_link_ctrl
0x0000000c,0x0000000f,c2m_sys_cfg_gpio_link_ctrl
0x0000000d,0x00000000,c2m_sys_cfg_mipi_ctrl
0x0000000e,0x00000000,c2m_sys_cfg_video_ctrl
0x0000000f,0x00000001,c2m_sys_cfg_vg_glb_en
0x00000010,0x00000000,c2m_sys_cfg_power_save
0x00000011,0x00000000,c2m_sys_cfg_cfg_block
0x00000060,0x00000000,c2m_sys_cfg_status0
0x00000080,0x00000001,c2m_rcc_clock_ctrl0[0]
0x00000081,0x00000001,c2m_rcc_clock_ctrl0[1]
0x00000082,0x00000001,c2m_rcc_clock_ctrl0[2]
0x00000083,0x00000001,c2m_rcc_clock_ctrl0[3]
0x00000084,0x00000001,c2m_rcc_clock_ctrl1[0]
0x00000085,0x00000001,c2m_rcc_clock_ctrl1[1]
0x00000086,0x00000001,c2m_rcc_clock_ctrl1[2]
0x00000087,0x00000001,c2m_rcc_clock_ctrl1[3]
0x00000088,0x0000003f,c2m_rcc_clock_ctrl2
0x00000089,0x00000000,c2m_rcc_clock_ctrl3
0x0000008a,0x00000001,c2m_rcc_clock_ctrl4
0x0000008b,0x00000018,c2m_rcc_clock_ctrl5
0x0000008c,0x00000000,c2m_rcc_clock_ctrl6[0]
0x0000008d,0x00000000,c2m_rcc_clock_ctrl6[1]
0x0000008e,0x00000000,c2m_rcc_clock_ctrl6[2]
0x0000008f,0x00000000,c2m_rcc_clock_ctrl6[3]
0x00000090,0x00000000,c2m_rcc_clock_ctrl7[0]
0x00000091,0x00000004,c2m_rcc_clock_ctrl7[1]
0x00000092,0x0000000c,c2m_rcc_clock_ctrl7[2]
0x00000093,0x00000014,c2m_rcc_clock_ctrl7[3]
0x00000094,0x00000000,c2m_rcc_clock_ctrl8[0]
0x00000095,0x00000000,c2m_rcc_clock_ctrl8[1]
0x00000096,0x00000000,c2m_rcc_clock_ctrl8[2]
0x00000097,0x00000000,c2m_rcc_clock_ctrl9[0]
0x00000098,0x00000000,c2m_rcc_clock_ctrl9[1]
0x00000099,0x00000000,c2m_rcc_clock_ctrl9[2]
0x0000009a,0x00000000,c2m_rcc_clock_ctrl10
0x0000009b,0x00000001,c2m_rcc_reset_ctrl0
0x0000009c,0x00000001,c2m_rcc_reset_ctrl1
0x0000009d,0x00000000,c2m_rcc_reset_ctrl2
0x0000009e,0x0000000f,c2m_rcc_reset_ctrl3
0x0000009f,0x0000000f,c2m_rcc_reset_ctrl4
0x000000a0,0x00000001,c2m_rcc_reset_ctrl5
0x000000a4,0x00000000,c2m_rcc_dphy_inv_ctrl[0]
0x000000a5,0x00000000,c2m_rcc_dphy_inv_ctrl[1]
0x000000a6,0x00000000,c2m_rcc_dphy_inv_ctrl[2]
0x000000a7,0x00000000,c2m_rcc_dphy_inv_ctrl[3]
0x000000a8,0x00000000,c2m_rcc_dphy_inv_ctrl[4]
0x000000a9,0x00000000,c2m_rcc_dphy_inv_ctrl[5]
0x000000aa,0x00000000,c2m_rcc_dphy_inv_ctrl[6]
0x000000ab,0x00000000,c2m_rcc_dphy_inv_ctrl[7]
0x000000ac,0x00000000,c2m_rcc_dphy_inv_ctrl[8]
0x000000ad,0x00000000,c2m_rcc_dphy_inv_ctrl[9]
0x000000ae,0x00000000,c2m_rcc_dphy_inv_ctrl[10]
0x000000af,0x00000000,c2m_rcc_dphy_inv_ctrl[11]
0x000000b0,0x00000000,c2m_rcc_vg_inv_ctrl[0]
0x000000b1,0x00000000,c2m_rcc_vg_inv_ctrl[1]
0x000000b6,0x00000000,c2m_rcc_pll_vg_cken
0x000000b7,0x00000000,c2m_rcc_pll_status
0x000000b8,0x00000000,c2m_rcc_debug_clk_mux
0x000000b9,0x00000000,c2m_rcc_xtal_status
0x000000ba,0x00000000,c2m_rcc_spi_mst_ctrl0
0x000000bb,0x0000007f,c2m_rcc_spi_mst_ctrl1
0x00000140,0x00000027,c2m_i2c0_ctrl0
0x00000141,0x00000055,c2m_i2c0_ctrl1
0x00000142,0x00000077,c2m_i2c0_ctrl2
0x00000143,0x00000007,c2m_i2c0_ctrl3
0x00000144,0x00000000,c2m_i2c0_ctrl4[0]
0x00000145,0x00000000,c2m_i2c0_ctrl4[1]
0x00000146,0x00000000,c2m_i2c0_ctrl4[2]
0x00000147,0x00000000,c2m_i2c0_ctrl4[3]
0x00000148,0x00000000,c2m_i2c0_ctrl4[4]
0x00000149,0x0000007f,c2m_i2c0_ctrl5[0]
0x0000014a,0x0000007f,c2m_i2c0_ctrl5[1]
0x0000014b,0x0000007f,c2m_i2c0_ctrl5[2]
0x0000014c,0x0000007f,c2m_i2c0_ctrl5[3]
0x0000014d,0x0000007f,c2m_i2c0_ctrl5[4]
0x0000014e,0x00000003,c2m_i2c0_ctrl6[0]
0x0000014f,0x00000003,c2m_i2c0_ctrl6[1]
0x00000150,0x00000003,c2m_i2c0_ctrl6[2]
0x00000151,0x00000003,c2m_i2c0_ctrl6[3]
0x00000152,0x00000000,c2m_i2c0_pkt_cnt0[0]
0x00000153,0x00000000,c2m_i2c0_pkt_cnt0[1]
0x00000154,0x00000000,c2m_i2c0_pkt_cnt0[2]
0x00000155,0x00000000,c2m_i2c0_pkt_cnt0[3]
0x00000156,0x00000000,c2m_i2c0_pkt_cnt0[4]
0x00000157,0x00000000,c2m_i2c0_pkt_cnt1[0]
0x00000158,0x00000000,c2m_i2c0_pkt_cnt1[1]
0x00000159,0x00000000,c2m_i2c0_pkt_cnt1[2]
0x0000015a,0x00000000,c2m_i2c0_pkt_cnt1[3]
0x0000015b,0x00000000,c2m_i2c0_pkt_cnt1[4]
0x0000015c,0x00000000,c2m_i2c0_dev_addr_tran0[0]
0x0000015d,0x00000000,c2m_i2c0_dev_addr_tran0[1]
0x0000015e,0x00000000,c2m_i2c0_dev_addr_tran0[2]
0x0000015f,0x00000000,c2m_i2c0_dev_addr_tran0[3]
0x00000160,0x00000000,c2m_i2c0_dev_addr_tran1[0]
0x00000161,0x00000000,c2m_i2c0_dev_addr_tran1[1]
0x00000162,0x00000000,c2m_i2c0_dev_addr_tran1[2]
0x00000163,0x00000000,c2m_i2c0_dev_addr_tran1[3]
0x00000164,0x00000000,c2m_i2c0_dev_addr_tran2[0]
0x00000165,0x00000000,c2m_i2c0_dev_addr_tran2[1]
0x00000166,0x00000000,c2m_i2c0_dev_addr_tran2[2]
0x00000167,0x00000000,c2m_i2c0_dev_addr_tran2[3]
0x00000168,0x00000000,c2m_i2c0_dev_addr_tran3[0]
0x00000169,0x00000000,c2m_i2c0_dev_addr_tran3[1]
0x0000016a,0x00000000,c2m_i2c0_dev_addr_tran3[2]
0x0000016b,0x00000000,c2m_i2c0_dev_addr_tran3[3]
0x0000016c,0x0000007f,c2m_i2c0_bypassed_dev_addr[0]
0x0000016d,0x0000007f,c2m_i2c0_bypassed_dev_addr[1]
0x0000016e,0x0000007f,c2m_i2c0_bypassed_dev_addr[2]
0x0000016f,0x0000007f,c2m_i2c0_bypassed_dev_addr[3]
0x00000170,0x00000000,c2m_i2c0_bypassed_dev_addr_mask[0]
0x00000171,0x00000000,c2m_i2c0_bypassed_dev_addr_mask[1]
0x00000172,0x00000000,c2m_i2c0_bypassed_dev_addr_mask[2]
0x00000173,0x00000000,c2m_i2c0_bypassed_dev_addr_mask[3]
0x00000180,0x00000027,c2m_i2c1_ctrl0
0x00000181,0x00000055,c2m_i2c1_ctrl1
0x00000182,0x00000077,c2m_i2c1_ctrl2
0x00000183,0x00000007,c2m_i2c1_ctrl3
0x00000184,0x00000000,c2m_i2c1_ctrl4[0]
0x00000185,0x00000000,c2m_i2c1_ctrl4[1]
0x00000186,0x00000000,c2m_i2c1_ctrl4[2]
0x00000187,0x00000000,c2m_i2c1_ctrl4[3]
0x00000188,0x00000000,c2m_i2c1_ctrl4[4]
0x00000189,0x0000007f,c2m_i2c1_ctrl5[0]
0x0000018a,0x0000007f,c2m_i2c1_ctrl5[1]
0x0000018b,0x0000007f,c2m_i2c1_ctrl5[2]
0x0000018c,0x0000007f,c2m_i2c1_ctrl5[3]
0x0000018d,0x0000007f,c2m_i2c1_ctrl5[4]
0x0000018e,0x00000003,c2m_i2c1_ctrl6[0]
0x0000018f,0x00000003,c2m_i2c1_ctrl6[1]
0x00000190,0x00000003,c2m_i2c1_ctrl6[2]
0x00000191,0x00000003,c2m_i2c1_ctrl6[3]
0x00000192,0x00000000,c2m_i2c1_pkt_cnt0[0]
0x00000193,0x00000000,c2m_i2c1_pkt_cnt0[1]
0x00000194,0x00000000,c2m_i2c1_pkt_cnt0[2]
0x00000195,0x00000000,c2m_i2c1_pkt_cnt0[3]
0x00000196,0x00000000,c2m_i2c1_pkt_cnt0[4]
0x00000197,0x00000000,c2m_i2c1_pkt_cnt1[0]
0x00000198,0x00000000,c2m_i2c1_pkt_cnt1[1]
0x00000199,0x00000000,c2m_i2c1_pkt_cnt1[2]
0x0000019a,0x00000000,c2m_i2c1_pkt_cnt1[3]
0x0000019b,0x00000000,c2m_i2c1_pkt_cnt1[4]
0x0000019c,0x00000000,c2m_i2c1_dev_addr_tran0[0]
0x0000019d,0x00000000,c2m_i2c1_dev_addr_tran0[1]
0x0000019e,0x00000000,c2m_i2c1_dev_addr_tran0[2]
0x0000019f,0x00000000,c2m_i2c1_dev_addr_tran0[3]
0x000001a0,0x00000000,c2m_i2c1_dev_addr_tran1[0]
0x000001a1,0x00000000,c2m_i2c1_dev_addr_tran1[1]
0x000001a2,0x00000000,c2m_i2c1_dev_addr_tran1[2]
0x000001a3,0x00000000,c2m_i2c1_dev_addr_tran1[3]
0x000001a4,0x00000000,c2m_i2c1_dev_addr_tran2[0]
0x000001a5,0x00000000,c2m_i2c1_dev_addr_tran2[1]
0x000001a6,0x00000000,c2m_i2c1_dev_addr_tran2[2]
0x000001a7,0x00000000,c2m_i2c1_dev_addr_tran2[3]
0x000001a8,0x00000000,c2m_i2c1_dev_addr_tran3[0]
0x000001a9,0x00000000,c2m_i2c1_dev_addr_tran3[1]
0x000001aa,0x00000000,c2m_i2c1_dev_addr_tran3[2]
0x000001ab,0x00000000,c2m_i2c1_dev_addr_tran3[3]
0x000001ac,0x0000007f,c2m_i2c1_bypassed_dev_addr[0]
0x000001ad,0x0000007f,c2m_i2c1_bypassed_dev_addr[1]
0x000001ae,0x0000007f,c2m_i2c1_bypassed_dev_addr[2]
0x000001af,0x0000007f,c2m_i2c1_bypassed_dev_addr[3]
0x000001b0,0x00000000,c2m_i2c1_bypassed_dev_addr_mask[0]
0x000001b1,0x00000000,c2m_i2c1_bypassed_dev_addr_mask[1]
0x000001b2,0x00000000,c2m_i2c1_bypassed_dev_addr_mask[2]
0x000001b3,0x00000000,c2m_i2c1_bypassed_dev_addr_mask[3]
0x000001c0,0x00000027,c2m_i2c2_ctrl0
0x000001c1,0x00000055,c2m_i2c2_ctrl1
0x000001c2,0x00000077,c2m_i2c2_ctrl2
0x000001c3,0x00000007,c2m_i2c2_ctrl3
0x000001c4,0x00000000,c2m_i2c2_ctrl4[0]
0x000001c5,0x00000000,c2m_i2c2_ctrl4[1]
0x000001c6,0x00000000,c2m_i2c2_ctrl4[2]
0x000001c7,0x00000000,c2m_i2c2_ctrl4[3]
0x000001c8,0x00000000,c2m_i2c2_ctrl4[4]
0x000001c9,0x0000007f,c2m_i2c2_ctrl5[0]
0x000001ca,0x0000007f,c2m_i2c2_ctrl5[1]
0x000001cb,0x0000007f,c2m_i2c2_ctrl5[2]
0x000001cc,0x0000007f,c2m_i2c2_ctrl5[3]
0x000001cd,0x0000007f,c2m_i2c2_ctrl5[4]
0x000001ce,0x00000003,c2m_i2c2_ctrl6[0]
0x000001cf,0x00000003,c2m_i2c2_ctrl6[1]
0x000001d0,0x00000003,c2m_i2c2_ctrl6[2]
0x000001d1,0x00000003,c2m_i2c2_ctrl6[3]
0x000001d2,0x00000000,c2m_i2c2_pkt_cnt0[0]
0x000001d3,0x00000000,c2m_i2c2_pkt_cnt0[1]
0x000001d4,0x00000000,c2m_i2c2_pkt_cnt0[2]
0x000001d5,0x00000000,c2m_i2c2_pkt_cnt0[3]
0x000001d6,0x00000000,c2m_i2c2_pkt_cnt0[4]
0x000001d7,0x00000000,c2m_i2c2_pkt_cnt1[0]
0x000001d8,0x00000000,c2m_i2c2_pkt_cnt1[1]
0x000001d9,0x00000000,c2m_i2c2_pkt_cnt1[2]
0x000001da,0x00000000,c2m_i2c2_pkt_cnt1[3]
0x000001db,0x00000000,c2m_i2c2_pkt_cnt1[4]
0x000001dc,0x00000000,c2m_i2c2_dev_addr_tran0[0]
0x000001dd,0x00000000,c2m_i2c2_dev_addr_tran0[1]
0x000001de,0x00000000,c2m_i2c2_dev_addr_tran0[2]
0x000001df,0x00000000,c2m_i2c2_dev_addr_tran0[3]
0x000001e0,0x00000000,c2m_i2c2_dev_addr_tran1[0]
0x000001e1,0x00000000,c2m_i2c2_dev_addr_tran1[1]
0x000001e2,0x00000000,c2m_i2c2_dev_addr_tran1[2]
0x000001e3,0x00000000,c2m_i2c2_dev_addr_tran1[3]
0x000001e4,0x00000000,c2m_i2c2_dev_addr_tran2[0]
0x000001e5,0x00000000,c2m_i2c2_dev_addr_tran2[1]
0x000001e6,0x00000000,c2m_i2c2_dev_addr_tran2[2]
0x000001e7,0x00000000,c2m_i2c2_dev_addr_tran2[3]
0x000001e8,0x00000000,c2m_i2c2_dev_addr_tran3[0]
0x000001e9,0x00000000,c2m_i2c2_dev_addr_tran3[1]
0x000001ea,0x00000000,c2m_i2c2_dev_addr_tran3[2]
0x000001eb,0x00000000,c2m_i2c2_dev_addr_tran3[3]
0x000001ec,0x0000007f,c2m_i2c2_bypassed_dev_addr[0]
0x000001ed,0x0000007f,c2m_i2c2_bypassed_dev_addr[1]
0x000001ee,0x0000007f,c2m_i2c2_bypassed_dev_addr[2]
0x000001ef,0x0000007f,c2m_i2c2_bypassed_dev_addr[3]
0x000001f0,0x00000000,c2m_i2c2_bypassed_dev_addr_mask[0]
0x000001f1,0x00000000,c2m_i2c2_bypassed_dev_addr_mask[1]
0x000001f2,0x00000000,c2m_i2c2_bypassed_dev_addr_mask[2]
0x000001f3,0x00000000,c2m_i2c2_bypassed_dev_addr_mask[3]
0x00000200,0x00000008,c2m_uart0_ctrl0
0x00000201,0x0000003c,c2m_uart0_ctrl1
0x00000202,0x00000012,c2m_uart0_ck_ctrl0
0x00000203,0x000000df,c2m_uart0_ck_ctrl1
0x00000208,0x00000000,c2m_uart0_rt_pkt_counter0
0x00000209,0x00000000,c2m_uart0_rt_pkt_counter1
0x0000020a,0x00000000,c2m_uart0_tr_pkt_counter0
0x0000020b,0x00000000,c2m_uart0_tr_pkt_counter1
0x00000220,0x00000008,c2m_uart1_ctrl0
0x00000221,0x0000003c,c2m_uart1_ctrl1
0x00000222,0x00000012,c2m_uart1_ck_ctrl0
0x00000223,0x000000df,c2m_uart1_ck_ctrl1
0x00000228,0x00000000,c2m_uart1_rt_pkt_counter0
0x00000229,0x00000000,c2m_uart1_rt_pkt_counter1
0x0000022a,0x00000000,c2m_uart1_tr_pkt_counter0
0x0000022b,0x00000000,c2m_uart1_tr_pkt_counter1
0x00000240,0x00000008,c2m_uart2_ctrl0
0x00000241,0x0000003c,c2m_uart2_ctrl1
0x00000242,0x00000012,c2m_uart2_ck_ctrl0
0x00000243,0x000000df,c2m_uart2_ck_ctrl1
0x00000248,0x00000000,c2m_uart2_rt_pkt_counter0
0x00000249,0x00000000,c2m_uart2_rt_pkt_counter1
0x0000024a,0x00000000,c2m_uart2_tr_pkt_counter0
0x0000024b,0x00000000,c2m_uart2_tr_pkt_counter1
0x00000300,0x00000000,c2m_spi0_ctrl0
0x00000301,0x00000000,c2m_spi0_ctrl1
0x00000302,0x00000000,c2m_spi0_ctrl2
0x00000303,0x00000000,c2m_spi0_ctrl3
0x00000304,0x00000000,c2m_spi0_ctrl4
0x00000305,0x00000000,c2m_spi0_ctrl5
0x00000306,0x00000000,c2m_spi0_ctrl6
0x00000400,0x00000000,c2m_fusa_cfgcrc_ctrl0
0x00000401,0x00000000,c2m_fusa_cfgcrc_ctrl1
0x00000402,0x00000000,c2m_fusa_cfgcrc_status0
0x00000403,0x00000000,c2m_fusa_cfgcrc_status1
0x00000404,0x00000000,c2m_fusa_cfgcrc_status2
0x00000405,0x00000001,c2m_fusa_uv_ov_en
0x00000406,0x00000000,c2m_fusa_uv_ov_status[0]
0x00000407,0x00000000,c2m_fusa_uv_ov_status[1]
0x00000408,0x00000000,c2m_fusa_uv_ov_status[2]
0x00000409,0x00000000,c2m_fusa_uv_ov_status[3]
0x0000040a,0x00000000,c2m_fusa_uv_ov_status[4]
0x0000040b,0x00000000,c2m_fusa_uv_ov_status[5]
0x0000040c,0x00000000,c2m_fusa_uv_ov_status[6]
0x0000040d,0x00000000,c2m_fusa_uv_ov_status[7]
0x00000500,0x000000ff,c2m_rx_router_link_ctrl_pipe_enable
0x00000501,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[0]
0x00000502,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[1]
0x00000503,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[2]
0x00000504,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[3]
0x00000505,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[4]
0x00000506,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[5]
0x00000507,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[6]
0x00000508,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl1[7]
0x00000509,0x00000000,c2m_rx_router_link_ctrl_pipe0_map_stream
0x0000050a,0x00000001,c2m_rx_router_link_ctrl_pipe1_map_stream
0x0000050b,0x00000002,c2m_rx_router_link_ctrl_pipe2_map_stream
0x0000050c,0x00000003,c2m_rx_router_link_ctrl_pipe3_map_stream
0x0000050d,0x00000000,c2m_rx_router_link_ctrl_pipe4_map_stream
0x0000050e,0x00000001,c2m_rx_router_link_ctrl_pipe5_map_stream
0x0000050f,0x00000002,c2m_rx_router_link_ctrl_pipe6_map_stream
0x00000510,0x00000003,c2m_rx_router_link_ctrl_pipe7_map_stream
0x00000511,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[0]
0x00000512,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[1]
0x00000513,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[2]
0x00000514,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[3]
0x00000515,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[4]
0x00000516,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[5]
0x00000517,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[6]
0x00000518,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl3[7]
0x00000519,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[0]
0x0000051a,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[1]
0x0000051b,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[2]
0x0000051c,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[3]
0x0000051d,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[4]
0x0000051e,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[5]
0x0000051f,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[6]
0x00000520,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl4[7]
0x00000521,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[0]
0x00000522,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[1]
0x00000523,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[2]
0x00000524,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[3]
0x00000525,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[4]
0x00000526,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[5]
0x00000527,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[6]
0x00000528,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl5[7]
0x00000529,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[0]
0x0000052a,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[1]
0x0000052b,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[2]
0x0000052c,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[3]
0x0000052d,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[4]
0x0000052e,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[5]
0x0000052f,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[6]
0x00000530,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl6[7]
0x00000531,0x00000000,c2m_rx_router_link_ctrl_pipe0_map_mdi
0x00000532,0x00000000,c2m_rx_router_link_ctrl_pipe1_map_mdi
0x00000533,0x00000000,c2m_rx_router_link_ctrl_pipe2_map_mdi
0x00000534,0x00000000,c2m_rx_router_link_ctrl_pipe3_map_mdi
0x00000535,0x00000001,c2m_rx_router_link_ctrl_pipe4_map_mdi
0x00000536,0x00000001,c2m_rx_router_link_ctrl_pipe5_map_mdi
0x00000537,0x00000001,c2m_rx_router_link_ctrl_pipe6_map_mdi
0x00000538,0x00000001,c2m_rx_router_link_ctrl_pipe7_map_mdi
0x00000539,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[0]
0x0000053a,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[1]
0x0000053b,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[2]
0x0000053c,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[3]
0x0000053d,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[4]
0x0000053e,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[5]
0x0000053f,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[6]
0x00000540,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl8[7]
0x00000541,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[0]
0x00000542,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[1]
0x00000543,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[2]
0x00000544,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[3]
0x00000545,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[4]
0x00000546,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[5]
0x00000547,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[6]
0x00000548,0x00000020,c2m_rx_router_link_ctrl_pipe_ctrl9[7]
0x00000569,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[0]
0x0000056a,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[1]
0x0000056b,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[2]
0x0000056c,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[3]
0x0000056d,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[4]
0x0000056e,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[5]
0x0000056f,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[6]
0x00000570,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl17[7]
0x00000571,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl19[0]
0x00000572,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl19[1]
0x00000573,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl19[2]
0x00000574,0x00000000,c2m_rx_router_link_ctrl_pipe_ctrl19[3]
0x00000575,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl20[0]
0x00000576,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl20[1]
0x00000577,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl20[2]
0x00000578,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl20[3]
0x00000579,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl21[0]
0x0000057a,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl21[1]
0x0000057b,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl21[2]
0x0000057c,0x00000002,c2m_rx_router_link_ctrl_pipe_ctrl21[3]
0x0000057d,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl22[0]
0x0000057e,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl22[1]
0x0000057f,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl22[2]
0x00000580,0x00000003,c2m_rx_router_link_ctrl_pipe_ctrl22[3]
0x00000581,0x00000004,c2m_rx_router_link_ctrl_pipe_ctrl23[0]
0x00000582,0x00000004,c2m_rx_router_link_ctrl_pipe_ctrl23[1]
0x00000583,0x00000004,c2m_rx_router_link_ctrl_pipe_ctrl23[2]
0x00000584,0x00000004,c2m_rx_router_link_ctrl_pipe_ctrl23[3]
0x00000585,0x00000005,c2m_rx_router_link_ctrl_pipe_ctrl24[0]
0x00000586,0x00000005,c2m_rx_router_link_ctrl_pipe_ctrl24[1]
0x00000587,0x00000005,c2m_rx_router_link_ctrl_pipe_ctrl24[2]
0x00000588,0x00000005,c2m_rx_router_link_ctrl_pipe_ctrl24[3]
0x00000589,0x00000006,c2m_rx_router_link_ctrl_pipe_ctrl25[0]
0x0000058a,0x00000006,c2m_rx_router_link_ctrl_pipe_ctrl25[1]
0x0000058b,0x00000006,c2m_rx_router_link_ctrl_pipe_ctrl25[2]
0x0000058c,0x00000006,c2m_rx_router_link_ctrl_pipe_ctrl25[3]
0x0000058d,0x00000007,c2m_rx_router_link_ctrl_pipe_ctrl26[0]
0x0000058e,0x00000007,c2m_rx_router_link_ctrl_pipe_ctrl26[1]
0x0000058f,0x00000007,c2m_rx_router_link_ctrl_pipe_ctrl26[2]
0x00000590,0x00000007,c2m_rx_router_link_ctrl_pipe_ctrl26[3]
0x00000591,0x00000008,c2m_rx_router_link_ctrl_pipe_ctrl27[0]
0x00000592,0x00000008,c2m_rx_router_link_ctrl_pipe_ctrl27[1]
0x00000593,0x00000008,c2m_rx_router_link_ctrl_pipe_ctrl27[2]
0x00000594,0x00000008,c2m_rx_router_link_ctrl_pipe_ctrl27[3]
0x00000595,0x00000009,c2m_rx_router_link_ctrl_pipe_ctrl28[0]
0x00000596,0x00000009,c2m_rx_router_link_ctrl_pipe_ctrl28[1]
0x00000597,0x00000009,c2m_rx_router_link_ctrl_pipe_ctrl28[2]
0x00000598,0x00000009,c2m_rx_router_link_ctrl_pipe_ctrl28[3]
0x00000599,0x0000000a,c2m_rx_router_link_ctrl_pipe_ctrl29[0]
0x0000059a,0x0000000a,c2m_rx_router_link_ctrl_pipe_ctrl29[1]
0x0000059b,0x0000000a,c2m_rx_router_link_ctrl_pipe_ctrl29[2]
0x0000059c,0x0000000a,c2m_rx_router_link_ctrl_pipe_ctrl29[3]
0x0000059d,0x0000000b,c2m_rx_router_link_ctrl_pipe_ctrl30[0]
0x0000059e,0x0000000b,c2m_rx_router_link_ctrl_pipe_ctrl30[1]
0x0000059f,0x0000000b,c2m_rx_router_link_ctrl_pipe_ctrl30[2]
0x000005a0,0x0000000b,c2m_rx_router_link_ctrl_pipe_ctrl30[3]
0x000005a1,0x0000000c,c2m_rx_router_link_ctrl_pipe_ctrl31[0]
0x000005a2,0x0000000c,c2m_rx_router_link_ctrl_pipe_ctrl31[1]
0x000005a3,0x0000000c,c2m_rx_router_link_ctrl_pipe_ctrl31[2]
0x000005a4,0x0000000c,c2m_rx_router_link_ctrl_pipe_ctrl31[3]
0x000005a5,0x0000000d,c2m_rx_router_link_ctrl_pipe_ctrl32[0]
0x000005a6,0x0000000d,c2m_rx_router_link_ctrl_pipe_ctrl32[1]
0x000005a7,0x0000000d,c2m_rx_router_link_ctrl_pipe_ctrl32[2]
0x000005a8,0x0000000d,c2m_rx_router_link_ctrl_pipe_ctrl32[3]
0x000005a9,0x0000000e,c2m_rx_router_link_ctrl_pipe_ctrl33[0]
0x000005aa,0x0000000e,c2m_rx_router_link_ctrl_pipe_ctrl33[1]
0x000005ab,0x0000000e,c2m_rx_router_link_ctrl_pipe_ctrl33[2]
0x000005ac,0x0000000e,c2m_rx_router_link_ctrl_pipe_ctrl33[3]
0x000005ad,0x0000000f,c2m_rx_router_link_ctrl_pipe_ctrl34[0]
0x000005ae,0x0000000f,c2m_rx_router_link_ctrl_pipe_ctrl34[1]
0x000005af,0x0000000f,c2m_rx_router_link_ctrl_pipe_ctrl34[2]
0x000005b0,0x0000000f,c2m_rx_router_link_ctrl_pipe_ctrl34[3]
0x000005b1,0x00000001,c2m_rx_router_link_ctrl_pipe_ctrl35
0x000005b2,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[0]
0x000005b3,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[1]
0x000005b4,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[2]
0x000005b5,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[3]
0x000005b6,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[4]
0x000005b7,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[5]
0x000005b8,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[6]
0x000005b9,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_a[7]
0x000005ba,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[0]
0x000005bb,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[1]
0x000005bc,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[2]
0x000005bd,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[3]
0x000005be,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[4]
0x000005bf,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[5]
0x000005c0,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[6]
0x000005c1,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_a[7]
0x000005c2,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[0]
0x000005c3,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[1]
0x000005c4,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[2]
0x000005c5,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[3]
0x000005c6,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[4]
0x000005c7,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[5]
0x000005c8,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[6]
0x000005c9,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_a[7]
0x000005ca,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[0]
0x000005cb,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[1]
0x000005cc,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[2]
0x000005cd,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[3]
0x000005ce,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[4]
0x000005cf,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[5]
0x000005d0,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[6]
0x000005d1,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_a[7]
0x000005d2,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[0]
0x000005d3,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[1]
0x000005d4,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[2]
0x000005d5,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[3]
0x000005d6,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[4]
0x000005d7,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[5]
0x000005d8,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[6]
0x000005d9,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_b[7]
0x000005da,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[0]
0x000005db,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[1]
0x000005dc,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[2]
0x000005dd,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[3]
0x000005de,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[4]
0x000005df,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[5]
0x000005e0,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[6]
0x000005e1,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_b[7]
0x000005e2,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[0]
0x000005e3,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[1]
0x000005e4,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[2]
0x000005e5,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[3]
0x000005e6,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[4]
0x000005e7,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[5]
0x000005e8,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[6]
0x000005e9,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_b[7]
0x000005ea,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[0]
0x000005eb,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[1]
0x000005ec,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[2]
0x000005ed,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[3]
0x000005ee,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[4]
0x000005ef,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[5]
0x000005f0,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[6]
0x000005f1,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_b[7]
0x000005f2,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[0]
0x000005f3,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[1]
0x000005f4,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[2]
0x000005f5,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[3]
0x000005f6,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[4]
0x000005f7,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[5]
0x000005f8,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[6]
0x000005f9,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_c[7]
0x000005fa,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[0]
0x000005fb,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[1]
0x000005fc,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[2]
0x000005fd,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[3]
0x000005fe,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[4]
0x000005ff,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[5]
0x00000600,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[6]
0x00000601,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_c[7]
0x00000602,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[0]
0x00000603,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[1]
0x00000604,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[2]
0x00000605,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[3]
0x00000606,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[4]
0x00000607,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[5]
0x00000608,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[6]
0x00000609,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_c[7]
0x0000060a,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[0]
0x0000060b,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[1]
0x0000060c,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[2]
0x0000060d,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[3]
0x0000060e,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[4]
0x0000060f,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[5]
0x00000610,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[6]
0x00000611,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_c[7]
0x00000612,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[0]
0x00000613,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[1]
0x00000614,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[2]
0x00000615,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[3]
0x00000616,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[4]
0x00000617,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[5]
0x00000618,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[6]
0x00000619,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_d[7]
0x0000061a,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[0]
0x0000061b,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[1]
0x0000061c,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[2]
0x0000061d,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[3]
0x0000061e,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[4]
0x0000061f,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[5]
0x00000620,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[6]
0x00000621,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_d[7]
0x00000622,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[0]
0x00000623,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[1]
0x00000624,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[2]
0x00000625,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[3]
0x00000626,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[4]
0x00000627,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[5]
0x00000628,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[6]
0x00000629,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_d[7]
0x0000062a,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[0]
0x0000062b,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[1]
0x0000062c,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[2]
0x0000062d,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[3]
0x0000062e,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[4]
0x0000062f,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[5]
0x00000630,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[6]
0x00000631,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_d[7]
0x00000632,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[0]
0x00000633,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[1]
0x00000634,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[2]
0x00000635,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[3]
0x00000636,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[4]
0x00000637,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[5]
0x00000638,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[6]
0x00000639,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_e[7]
0x0000063a,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[0]
0x0000063b,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[1]
0x0000063c,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[2]
0x0000063d,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[3]
0x0000063e,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[4]
0x0000063f,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[5]
0x00000640,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[6]
0x00000641,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_e[7]
0x00000642,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[0]
0x00000643,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[1]
0x00000644,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[2]
0x00000645,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[3]
0x00000646,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[4]
0x00000647,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[5]
0x00000648,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[6]
0x00000649,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_e[7]
0x0000064a,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[0]
0x0000064b,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[1]
0x0000064c,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[2]
0x0000064d,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[3]
0x0000064e,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[4]
0x0000064f,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[5]
0x00000650,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[6]
0x00000651,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_e[7]
0x00000652,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[0]
0x00000653,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[1]
0x00000654,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[2]
0x00000655,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[3]
0x00000656,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[4]
0x00000657,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[5]
0x00000658,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[6]
0x00000659,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_f[7]
0x0000065a,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[0]
0x0000065b,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[1]
0x0000065c,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[2]
0x0000065d,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[3]
0x0000065e,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[4]
0x0000065f,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[5]
0x00000660,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[6]
0x00000661,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_f[7]
0x00000662,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[0]
0x00000663,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[1]
0x00000664,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[2]
0x00000665,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[3]
0x00000666,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[4]
0x00000667,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[5]
0x00000668,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[6]
0x00000669,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_f[7]
0x0000066a,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[0]
0x0000066b,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[1]
0x0000066c,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[2]
0x0000066d,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[3]
0x0000066e,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[4]
0x0000066f,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[5]
0x00000670,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[6]
0x00000671,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_f[7]
0x00000672,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[0]
0x00000673,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[1]
0x00000674,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[2]
0x00000675,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[3]
0x00000676,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[4]
0x00000677,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[5]
0x00000678,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[6]
0x00000679,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_g[7]
0x0000067a,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[0]
0x0000067b,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[1]
0x0000067c,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[2]
0x0000067d,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[3]
0x0000067e,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[4]
0x0000067f,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[5]
0x00000680,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[6]
0x00000681,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_g[7]
0x00000682,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[0]
0x00000683,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[1]
0x00000684,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[2]
0x00000685,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[3]
0x00000686,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[4]
0x00000687,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[5]
0x00000688,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[6]
0x00000689,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_g[7]
0x0000068a,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[0]
0x0000068b,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[1]
0x0000068c,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[2]
0x0000068d,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[3]
0x0000068e,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[4]
0x0000068f,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[5]
0x00000690,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[6]
0x00000691,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_g[7]
0x00000692,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[0]
0x00000693,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[1]
0x00000694,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[2]
0x00000695,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[3]
0x00000696,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[4]
0x00000697,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[5]
0x00000698,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[6]
0x00000699,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_src_h[7]
0x0000069a,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[0]
0x0000069b,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[1]
0x0000069c,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[2]
0x0000069d,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[3]
0x0000069e,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[4]
0x0000069f,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[5]
0x000006a0,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[6]
0x000006a1,0x00000000,c2m_rx_router_link_ctrl_pipex_dt_remap_dest_h[7]
0x000006a2,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[0]
0x000006a3,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[1]
0x000006a4,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[2]
0x000006a5,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[3]
0x000006a6,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[4]
0x000006a7,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[5]
0x000006a8,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[6]
0x000006a9,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_src_h[7]
0x000006aa,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[0]
0x000006ab,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[1]
0x000006ac,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[2]
0x000006ad,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[3]
0x000006ae,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[4]
0x000006af,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[5]
0x000006b0,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[6]
0x000006b1,0x00000000,c2m_rx_router_link_ctrl_pipex_vcid_remap_dest_h[7]
0x000006b2,0x00000000,c2m_rx_router_link_ctrl_auto_err_proc_mask
0x000006b3,0x00000000,c2m_rx_router_link_ctrl_auto_reset_mask1
0x000006b4,0x000000ff,c2m_rx_router_link_ctrl_auto_reset_mask2
0x000006b5,0x000000ff,c2m_rx_router_link_ctrl_auto_reset_mask3
0x000006b6,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[0]
0x000006b7,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[1]
0x000006b8,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[2]
0x000006b9,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[3]
0x000006ba,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[4]
0x000006bb,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[5]
0x000006bc,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[6]
0x000006bd,0x00000080,c2m_rx_router_link_ctrl_dummy_data_filler[7]
0x000006be,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[0]
0x000006bf,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[1]
0x000006c0,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[2]
0x000006c1,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[3]
0x000006c2,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[4]
0x000006c3,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[5]
0x000006c4,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[6]
0x000006c5,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_a[7]
0x000006c6,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[0]
0x000006c7,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[1]
0x000006c8,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[2]
0x000006c9,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[3]
0x000006ca,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[4]
0x000006cb,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[5]
0x000006cc,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[6]
0x000006cd,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_b[7]
0x000006ce,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[0]
0x000006cf,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[1]
0x000006d0,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[2]
0x000006d1,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[3]
0x000006d2,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[4]
0x000006d3,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[5]
0x000006d4,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[6]
0x000006d5,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_c[7]
0x000006d6,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[0]
0x000006d7,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[1]
0x000006d8,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[2]
0x000006d9,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[3]
0x000006da,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[4]
0x000006db,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[5]
0x000006dc,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[6]
0x000006dd,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_vcid_d[7]
0x000006de,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[0]
0x000006df,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[1]
0x000006e0,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[2]
0x000006e1,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[3]
0x000006e2,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[4]
0x000006e3,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[5]
0x000006e4,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[6]
0x000006e5,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_a[7]
0x000006e6,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[0]
0x000006e7,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[1]
0x000006e8,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[2]
0x000006e9,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[3]
0x000006ea,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[4]
0x000006eb,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[5]
0x000006ec,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[6]
0x000006ed,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_b[7]
0x000006ee,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[0]
0x000006ef,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[1]
0x000006f0,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[2]
0x000006f1,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[3]
0x000006f2,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[4]
0x000006f3,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[5]
0x000006f4,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[6]
0x000006f5,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_c[7]
0x000006f6,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[0]
0x000006f7,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[1]
0x000006f8,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[2]
0x000006f9,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[3]
0x000006fa,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[4]
0x000006fb,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[5]
0x000006fc,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[6]
0x000006fd,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_dt_d[7]
0x000006fe,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[0]
0x000006ff,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[1]
0x00000700,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[2]
0x00000701,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[3]
0x00000702,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[4]
0x00000703,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[5]
0x00000704,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[6]
0x00000705,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_a[7]
0x00000706,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[0]
0x00000707,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[1]
0x00000708,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[2]
0x00000709,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[3]
0x0000070a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[4]
0x0000070b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[5]
0x0000070c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[6]
0x0000070d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_b[7]
0x0000070e,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[0]
0x0000070f,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[1]
0x00000710,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[2]
0x00000711,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[3]
0x00000712,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[4]
0x00000713,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[5]
0x00000714,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[6]
0x00000715,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_c[7]
0x00000716,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[0]
0x00000717,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[1]
0x00000718,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[2]
0x00000719,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[3]
0x0000071a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[4]
0x0000071b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[5]
0x0000071c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[6]
0x0000071d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_7_0_d[7]
0x0000071e,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[0]
0x0000071f,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[1]
0x00000720,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[2]
0x00000721,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[3]
0x00000722,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[4]
0x00000723,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[5]
0x00000724,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[6]
0x00000725,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_a[7]
0x00000726,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[0]
0x00000727,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[1]
0x00000728,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[2]
0x00000729,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[3]
0x0000072a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[4]
0x0000072b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[5]
0x0000072c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[6]
0x0000072d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_b[7]
0x0000072e,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[0]
0x0000072f,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[1]
0x00000730,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[2]
0x00000731,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[3]
0x00000732,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[4]
0x00000733,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[5]
0x00000734,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[6]
0x00000735,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_c[7]
0x00000736,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[0]
0x00000737,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[1]
0x00000738,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[2]
0x00000739,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[3]
0x0000073a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[4]
0x0000073b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[5]
0x0000073c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[6]
0x0000073d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_start_15_8_d[7]
0x0000073e,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[0]
0x0000073f,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[1]
0x00000740,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[2]
0x00000741,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[3]
0x00000742,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[4]
0x00000743,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[5]
0x00000744,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[6]
0x00000745,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_a[7]
0x00000746,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[0]
0x00000747,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[1]
0x00000748,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[2]
0x00000749,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[3]
0x0000074a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[4]
0x0000074b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[5]
0x0000074c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[6]
0x0000074d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_b[7]
0x0000074e,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[0]
0x0000074f,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[1]
0x00000750,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[2]
0x00000751,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[3]
0x00000752,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[4]
0x00000753,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[5]
0x00000754,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[6]
0x00000755,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_c[7]
0x00000756,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[0]
0x00000757,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[1]
0x00000758,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[2]
0x00000759,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[3]
0x0000075a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[4]
0x0000075b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[5]
0x0000075c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[6]
0x0000075d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_7_0_d[7]
0x0000075e,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[0]
0x0000075f,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[1]
0x00000760,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[2]
0x00000761,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[3]
0x00000762,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[4]
0x00000763,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[5]
0x00000764,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[6]
0x00000765,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_a[7]
0x00000766,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[0]
0x00000767,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[1]
0x00000768,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[2]
0x00000769,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[3]
0x0000076a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[4]
0x0000076b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[5]
0x0000076c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[6]
0x0000076d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_b[7]
0x0000076e,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[0]
0x0000076f,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[1]
0x00000770,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[2]
0x00000771,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[3]
0x00000772,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[4]
0x00000773,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[5]
0x00000774,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[6]
0x00000775,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_c[7]
0x00000776,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[0]
0x00000777,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[1]
0x00000778,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[2]
0x00000779,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[3]
0x0000077a,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[4]
0x0000077b,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[5]
0x0000077c,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[6]
0x0000077d,0x00000000,c2m_rx_router_link_ctrl_pipex_trim_line_end_15_8_d[7]
0x0000077e,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[0]
0x0000077f,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[1]
0x00000780,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[2]
0x00000781,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[3]
0x00000782,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[4]
0x00000783,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[5]
0x00000784,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[6]
0x00000785,0x00000000,c2m_rx_router_link_ctrl_pipex_err_trigger_th_7_0[7]
0x00000786,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[0]
0x00000787,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[1]
0x00000788,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[2]
0x00000789,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[3]
0x0000078a,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[4]
0x0000078b,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[5]
0x0000078c,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[6]
0x0000078d,0x0000000d,c2m_rx_router_link_ctrl_pipex_err_trigger_th_15_8[7]
0x0000078e,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_7_0[0]
0x0000078f,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_7_0[1]
0x00000790,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_7_0[2]
0x00000791,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_7_0[3]
0x00000792,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_15_8[0]
0x00000793,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_15_8[1]
0x00000794,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_15_8[2]
0x00000795,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_delta_15_8[3]
0x00000796,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_th_7_0[0]
0x00000797,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_th_7_0[1]
0x00000798,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_th_7_0[2]
0x00000799,0x00000000,c2m_rx_router_link_ctrl_mdi_err_trigger_th_7_0[3]
0x0000079a,0x0000000d,c2m_rx_router_link_ctrl_mdi_err_trigger_th_15_8[0]
0x0000079b,0x0000000d,c2m_rx_router_link_ctrl_mdi_err_trigger_th_15_8[1]
0x0000079c,0x0000000d,c2m_rx_router_link_ctrl_mdi_err_trigger_th_15_8[2]
0x0000079d,0x0000000d,c2m_rx_router_link_ctrl_mdi_err_trigger_th_15_8[3]
0x0000079e,0x00000001,c2m_rx_router_link_ctrl_mdi_err_trigger_sel[0]
0x0000079f,0x00000001,c2m_rx_router_link_ctrl_mdi_err_trigger_sel[1]
0x000007a0,0x00000001,c2m_rx_router_link_ctrl_mdi_err_trigger_sel[2]
0x000007a1,0x00000001,c2m_rx_router_link_ctrl_mdi_err_trigger_sel[3]
0x000007a2,0x00000002,c2m_rx_router_link_ctrl_csix_all_pipe_err_trig_rst_mask[0]
0x000007a3,0x00000002,c2m_rx_router_link_ctrl_csix_all_pipe_err_trig_rst_mask[1]
0x000007a4,0x00000002,c2m_rx_router_link_ctrl_csix_all_pipe_err_trig_rst_mask[2]
0x000007a5,0x00000002,c2m_rx_router_link_ctrl_csix_all_pipe_err_trig_rst_mask[3]
0x000007a6,0x00000000,c2m_rx_router_link_ctrl_video_lock_trig_discard_mask[0]
0x000007a7,0x00000000,c2m_rx_router_link_ctrl_video_lock_trig_discard_mask[1]
0x000007a8,0x00000000,c2m_rx_router_link_ctrl_video_lock_trig_discard_mask[2]
0x000007a9,0x00000000,c2m_rx_router_link_ctrl_video_lock_trig_discard_mask[3]
0x000007c0,0x00000000,c2m_rx_router_mdi_ctrl_mdi_ctrl0[0]
0x000007c1,0x00000000,c2m_rx_router_mdi_ctrl_mdi_ctrl0[1]
0x000007c2,0x00000000,c2m_rx_router_mdi_ctrl_mdi_ctrl0[2]
0x000007c3,0x00000000,c2m_rx_router_mdi_ctrl_mdi_ctrl0[3]
0x000007c8,0x000000e4,c2m_rx_router_mdi_ctrl_mdi_ctrl2
0x000007c9,0x0000000c,c2m_rx_router_mdi_ctrl_mdi_ctrl3[0]
0x000007ca,0x0000000c,c2m_rx_router_mdi_ctrl_mdi_ctrl3[1]
0x000007cb,0x0000000c,c2m_rx_router_mdi_ctrl_mdi_ctrl3[2]
0x000007cc,0x0000000c,c2m_rx_router_mdi_ctrl_mdi_ctrl3[3]
0x000007cd,0x0000000f,c2m_rx_router_mdi_ctrl_mdi_stream_en
0x00000800,0x00000000,c2m_rx_router_status_line_status0[0]
0x00000801,0x00000000,c2m_rx_router_status_line_status0[1]
0x00000802,0x00000000,c2m_rx_router_status_line_status0[2]
0x00000803,0x00000000,c2m_rx_router_status_line_status0[3]
0x00000804,0x00000000,c2m_rx_router_status_line_status0[4]
0x00000805,0x00000000,c2m_rx_router_status_line_status0[5]
0x00000806,0x00000000,c2m_rx_router_status_line_status0[6]
0x00000807,0x00000000,c2m_rx_router_status_line_status0[7]
0x00000808,0x00000000,c2m_rx_router_status_line_status1[0]
0x00000809,0x00000000,c2m_rx_router_status_line_status1[1]
0x0000080a,0x00000000,c2m_rx_router_status_line_status1[2]
0x0000080b,0x00000000,c2m_rx_router_status_line_status1[3]
0x0000080c,0x00000000,c2m_rx_router_status_line_status1[4]
0x0000080d,0x00000000,c2m_rx_router_status_line_status1[5]
0x0000080e,0x00000000,c2m_rx_router_status_line_status1[6]
0x0000080f,0x00000000,c2m_rx_router_status_line_status1[7]
0x00000810,0x00000066,c2m_rx_router_status_line_status2[0]
0x00000811,0x00000066,c2m_rx_router_status_line_status2[1]
0x00000812,0x00000066,c2m_rx_router_status_line_status2[2]
0x00000813,0x00000066,c2m_rx_router_status_line_status2[3]
0x00000814,0x00000066,c2m_rx_router_status_line_status2[4]
0x00000815,0x00000066,c2m_rx_router_status_line_status2[5]
0x00000816,0x00000066,c2m_rx_router_status_line_status2[6]
0x00000817,0x00000066,c2m_rx_router_status_line_status2[7]
0x00000818,0x00000000,c2m_rx_router_status_line_status3[0]
0x00000819,0x00000000,c2m_rx_router_status_line_status3[1]
0x0000081a,0x00000000,c2m_rx_router_status_line_status3[2]
0x0000081b,0x00000000,c2m_rx_router_status_line_status3[3]
0x0000081c,0x00000000,c2m_rx_router_status_line_status3[4]
0x0000081d,0x00000000,c2m_rx_router_status_line_status3[5]
0x0000081e,0x00000000,c2m_rx_router_status_line_status3[6]
0x0000081f,0x00000000,c2m_rx_router_status_line_status3[7]
0x00000820,0x00000000,c2m_rx_router_status_line_status4[0]
0x00000821,0x00000000,c2m_rx_router_status_line_status4[1]
0x00000822,0x00000000,c2m_rx_router_status_line_status4[2]
0x00000823,0x00000000,c2m_rx_router_status_line_status4[3]
0x00000824,0x00000000,c2m_rx_router_status_line_status4[4]
0x00000825,0x00000000,c2m_rx_router_status_line_status4[5]
0x00000826,0x00000000,c2m_rx_router_status_line_status4[6]
0x00000827,0x00000000,c2m_rx_router_status_line_status4[7]
0x00000828,0x00000000,c2m_rx_router_status_line_remap_status[0]
0x00000829,0x00000000,c2m_rx_router_status_line_remap_status[1]
0x0000082a,0x00000000,c2m_rx_router_status_line_remap_status[2]
0x0000082b,0x00000000,c2m_rx_router_status_line_remap_status[3]
0x0000082c,0x00000000,c2m_rx_router_status_line_remap_status[4]
0x0000082d,0x00000000,c2m_rx_router_status_line_remap_status[5]
0x0000082e,0x00000000,c2m_rx_router_status_line_remap_status[6]
0x0000082f,0x00000000,c2m_rx_router_status_line_remap_status[7]
0x00000830,0x00000000,c2m_rx_router_status_memory_ecc_status
0x00000831,0x00000004,c2m_rx_router_status_mdi_status0[0]
0x00000832,0x00000004,c2m_rx_router_status_mdi_status0[1]
0x00000833,0x00000004,c2m_rx_router_status_mdi_status0[2]
0x00000834,0x00000004,c2m_rx_router_status_mdi_status0[3]
0x00000835,0x00000000,c2m_rx_router_status_frame_cnt_low8[0]
0x00000836,0x00000000,c2m_rx_router_status_frame_cnt_low8[1]
0x00000837,0x00000000,c2m_rx_router_status_frame_cnt_low8[2]
0x00000838,0x00000000,c2m_rx_router_status_frame_cnt_low8[3]
0x00000839,0x00000000,c2m_rx_router_status_frame_cnt_low8[4]
0x0000083a,0x00000000,c2m_rx_router_status_frame_cnt_low8[5]
0x0000083b,0x00000000,c2m_rx_router_status_frame_cnt_low8[6]
0x0000083c,0x00000000,c2m_rx_router_status_frame_cnt_low8[7]
0x0000083d,0x00000000,c2m_rx_router_status_frame_cnt_low8[8]
0x0000083e,0x00000000,c2m_rx_router_status_frame_cnt_low8[9]
0x0000083f,0x00000000,c2m_rx_router_status_frame_cnt_low8[10]
0x00000840,0x00000000,c2m_rx_router_status_frame_cnt_low8[11]
0x00000841,0x00000000,c2m_rx_router_status_frame_cnt_low8[12]
0x00000842,0x00000000,c2m_rx_router_status_frame_cnt_low8[13]
0x00000843,0x00000000,c2m_rx_router_status_frame_cnt_low8[14]
0x00000844,0x00000000,c2m_rx_router_status_frame_cnt_low8[15]
0x00000845,0x00000000,c2m_rx_router_status_frame_cnt_low8[16]
0x00000846,0x00000000,c2m_rx_router_status_frame_cnt_low8[17]
0x00000847,0x00000000,c2m_rx_router_status_frame_cnt_low8[18]
0x00000848,0x00000000,c2m_rx_router_status_frame_cnt_low8[19]
0x00000849,0x00000000,c2m_rx_router_status_frame_cnt_low8[20]
0x0000084a,0x00000000,c2m_rx_router_status_frame_cnt_low8[21]
0x0000084b,0x00000000,c2m_rx_router_status_frame_cnt_low8[22]
0x0000084c,0x00000000,c2m_rx_router_status_frame_cnt_low8[23]
0x0000084d,0x00000000,c2m_rx_router_status_frame_cnt_low8[24]
0x0000084e,0x00000000,c2m_rx_router_status_frame_cnt_low8[25]
0x0000084f,0x00000000,c2m_rx_router_status_frame_cnt_low8[26]
0x00000850,0x00000000,c2m_rx_router_status_frame_cnt_low8[27]
0x00000851,0x00000000,c2m_rx_router_status_frame_cnt_low8[28]
0x00000852,0x00000000,c2m_rx_router_status_frame_cnt_low8[29]
0x00000853,0x00000000,c2m_rx_router_status_frame_cnt_low8[30]
0x00000854,0x00000000,c2m_rx_router_status_frame_cnt_low8[31]
0x00000855,0x00000000,c2m_rx_router_status_frame_cnt_high8[0]
0x00000856,0x00000000,c2m_rx_router_status_frame_cnt_high8[1]
0x00000857,0x00000000,c2m_rx_router_status_frame_cnt_high8[2]
0x00000858,0x00000000,c2m_rx_router_status_frame_cnt_high8[3]
0x00000859,0x00000000,c2m_rx_router_status_frame_cnt_high8[4]
0x0000085a,0x00000000,c2m_rx_router_status_frame_cnt_high8[5]
0x0000085b,0x00000000,c2m_rx_router_status_frame_cnt_high8[6]
0x0000085c,0x00000000,c2m_rx_router_status_frame_cnt_high8[7]
0x0000085d,0x00000000,c2m_rx_router_status_frame_cnt_high8[8]
0x0000085e,0x00000000,c2m_rx_router_status_frame_cnt_high8[9]
0x0000085f,0x00000000,c2m_rx_router_status_frame_cnt_high8[10]
0x00000860,0x00000000,c2m_rx_router_status_frame_cnt_high8[11]
0x00000861,0x00000000,c2m_rx_router_status_frame_cnt_high8[12]
0x00000862,0x00000000,c2m_rx_router_status_frame_cnt_high8[13]
0x00000863,0x00000000,c2m_rx_router_status_frame_cnt_high8[14]
0x00000864,0x00000000,c2m_rx_router_status_frame_cnt_high8[15]
0x00000865,0x00000000,c2m_rx_router_status_frame_cnt_high8[16]
0x00000866,0x00000000,c2m_rx_router_status_frame_cnt_high8[17]
0x00000867,0x00000000,c2m_rx_router_status_frame_cnt_high8[18]
0x00000868,0x00000000,c2m_rx_router_status_frame_cnt_high8[19]
0x00000869,0x00000000,c2m_rx_router_status_frame_cnt_high8[20]
0x0000086a,0x00000000,c2m_rx_router_status_frame_cnt_high8[21]
0x0000086b,0x00000000,c2m_rx_router_status_frame_cnt_high8[22]
0x0000086c,0x00000000,c2m_rx_router_status_frame_cnt_high8[23]
0x0000086d,0x00000000,c2m_rx_router_status_frame_cnt_high8[24]
0x0000086e,0x00000000,c2m_rx_router_status_frame_cnt_high8[25]
0x0000086f,0x00000000,c2m_rx_router_status_frame_cnt_high8[26]
0x00000870,0x00000000,c2m_rx_router_status_frame_cnt_high8[27]
0x00000871,0x00000000,c2m_rx_router_status_frame_cnt_high8[28]
0x00000872,0x00000000,c2m_rx_router_status_frame_cnt_high8[29]
0x00000873,0x00000000,c2m_rx_router_status_frame_cnt_high8[30]
0x00000874,0x00000000,c2m_rx_router_status_frame_cnt_high8[31]
0x00000875,0x00000000,c2m_rx_router_status_line_cnt_low8[0]
0x00000876,0x00000000,c2m_rx_router_status_line_cnt_low8[1]
0x00000877,0x00000000,c2m_rx_router_status_line_cnt_low8[2]
0x00000878,0x00000000,c2m_rx_router_status_line_cnt_low8[3]
0x00000879,0x00000000,c2m_rx_router_status_line_cnt_low8[4]
0x0000087a,0x00000000,c2m_rx_router_status_line_cnt_low8[5]
0x0000087b,0x00000000,c2m_rx_router_status_line_cnt_low8[6]
0x0000087c,0x00000000,c2m_rx_router_status_line_cnt_low8[7]
0x0000087d,0x00000000,c2m_rx_router_status_line_cnt_low8[8]
0x0000087e,0x00000000,c2m_rx_router_status_line_cnt_low8[9]
0x0000087f,0x00000000,c2m_rx_router_status_line_cnt_low8[10]
0x00000880,0x00000000,c2m_rx_router_status_line_cnt_low8[11]
0x00000881,0x00000000,c2m_rx_router_status_line_cnt_low8[12]
0x00000882,0x00000000,c2m_rx_router_status_line_cnt_low8[13]
0x00000883,0x00000000,c2m_rx_router_status_line_cnt_low8[14]
0x00000884,0x00000000,c2m_rx_router_status_line_cnt_low8[15]
0x00000885,0x00000000,c2m_rx_router_status_line_cnt_low8[16]
0x00000886,0x00000000,c2m_rx_router_status_line_cnt_low8[17]
0x00000887,0x00000000,c2m_rx_router_status_line_cnt_low8[18]
0x00000888,0x00000000,c2m_rx_router_status_line_cnt_low8[19]
0x00000889,0x00000000,c2m_rx_router_status_line_cnt_low8[20]
0x0000088a,0x00000000,c2m_rx_router_status_line_cnt_low8[21]
0x0000088b,0x00000000,c2m_rx_router_status_line_cnt_low8[22]
0x0000088c,0x00000000,c2m_rx_router_status_line_cnt_low8[23]
0x0000088d,0x00000000,c2m_rx_router_status_line_cnt_low8[24]
0x0000088e,0x00000000,c2m_rx_router_status_line_cnt_low8[25]
0x0000088f,0x00000000,c2m_rx_router_status_line_cnt_low8[26]
0x00000890,0x00000000,c2m_rx_router_status_line_cnt_low8[27]
0x00000891,0x00000000,c2m_rx_router_status_line_cnt_low8[28]
0x00000892,0x00000000,c2m_rx_router_status_line_cnt_low8[29]
0x00000893,0x00000000,c2m_rx_router_status_line_cnt_low8[30]
0x00000894,0x00000000,c2m_rx_router_status_line_cnt_low8[31]
0x00000895,0x00000000,c2m_rx_router_status_line_cnt_high8[0]
0x00000896,0x00000000,c2m_rx_router_status_line_cnt_high8[1]
0x00000897,0x00000000,c2m_rx_router_status_line_cnt_high8[2]
0x00000898,0x00000000,c2m_rx_router_status_line_cnt_high8[3]
0x00000899,0x00000000,c2m_rx_router_status_line_cnt_high8[4]
0x0000089a,0x00000000,c2m_rx_router_status_line_cnt_high8[5]
0x0000089b,0x00000000,c2m_rx_router_status_line_cnt_high8[6]
0x0000089c,0x00000000,c2m_rx_router_status_line_cnt_high8[7]
0x0000089d,0x00000000,c2m_rx_router_status_line_cnt_high8[8]
0x0000089e,0x00000000,c2m_rx_router_status_line_cnt_high8[9]
0x0000089f,0x00000000,c2m_rx_router_status_line_cnt_high8[10]
0x000008a0,0x00000000,c2m_rx_router_status_line_cnt_high8[11]
0x000008a1,0x00000000,c2m_rx_router_status_line_cnt_high8[12]
0x000008a2,0x00000000,c2m_rx_router_status_line_cnt_high8[13]
0x000008a3,0x00000000,c2m_rx_router_status_line_cnt_high8[14]
0x000008a4,0x00000000,c2m_rx_router_status_line_cnt_high8[15]
0x000008a5,0x00000000,c2m_rx_router_status_line_cnt_high8[16]
0x000008a6,0x00000000,c2m_rx_router_status_line_cnt_high8[17]
0x000008a7,0x00000000,c2m_rx_router_status_line_cnt_high8[18]
0x000008a8,0x00000000,c2m_rx_router_status_line_cnt_high8[19]
0x000008a9,0x00000000,c2m_rx_router_status_line_cnt_high8[20]
0x000008aa,0x00000000,c2m_rx_router_status_line_cnt_high8[21]
0x000008ab,0x00000000,c2m_rx_router_status_line_cnt_high8[22]
0x000008ac,0x00000000,c2m_rx_router_status_line_cnt_high8[23]
0x000008ad,0x00000000,c2m_rx_router_status_line_cnt_high8[24]
0x000008ae,0x00000000,c2m_rx_router_status_line_cnt_high8[25]
0x000008af,0x00000000,c2m_rx_router_status_line_cnt_high8[26]
0x000008b0,0x00000000,c2m_rx_router_status_line_cnt_high8[27]
0x000008b1,0x00000000,c2m_rx_router_status_line_cnt_high8[28]
0x000008b2,0x00000000,c2m_rx_router_status_line_cnt_high8[29]
0x000008b3,0x00000000,c2m_rx_router_status_line_cnt_high8[30]
0x000008b4,0x00000000,c2m_rx_router_status_line_cnt_high8[31]
0x000008b5,0x00000000,c2m_rx_router_status_router_in_fifo_status
0x00000900,0x00000000,c2m_vc0_config0
0x00000901,0x00000005,c2m_vc0_config1
0x00000902,0x00000000,c2m_vc0_pattern
0x00000903,0x00000000,c2m_vc0_status
0x00000904,0x00000000,c2m_vc0_prbs0
0x00000905,0x00000000,c2m_vc0_prbs1
0x00000906,0x00000000,c2m_vc0_prbs2
0x00000907,0x00000000,c2m_vc0_prbs3
0x00000908,0x00000000,c2m_vc0_res_hlen[0]
0x00000909,0x00000000,c2m_vc0_res_hlen[1]
0x0000090a,0x00000000,c2m_vc0_res_hlen[2]
0x0000090b,0x00000000,c2m_vc0_res_hlen[3]
0x0000090c,0x00000000,c2m_vc0_res_hlen[4]
0x0000090d,0x00000000,c2m_vc0_res_hlen[5]
0x0000090e,0x00000000,c2m_vc0_res_hlen[6]
0x0000090f,0x00000000,c2m_vc0_res_hlen[7]
0x00000910,0x00000000,c2m_vc0_res_vlen[0]
0x00000911,0x00000000,c2m_vc0_res_vlen[1]
0x00000912,0x00000000,c2m_vc0_res_vlen[2]
0x00000913,0x00000000,c2m_vc0_res_vlen[3]
0x00000914,0x00000000,c2m_vc0_res_vlen[4]
0x00000915,0x00000000,c2m_vc0_res_vlen[5]
0x00000916,0x00000000,c2m_vc0_res_vlen[6]
0x00000917,0x00000000,c2m_vc0_res_vlen[7]
0x00000918,0x00000000,c2m_vc0_err_hpos[0]
0x00000919,0x00000000,c2m_vc0_err_hpos[1]
0x0000091a,0x00000000,c2m_vc0_err_hpos[2]
0x0000091b,0x00000000,c2m_vc0_err_hpos[3]
0x0000091c,0x00000000,c2m_vc0_err_hpos[4]
0x0000091d,0x00000000,c2m_vc0_err_hpos[5]
0x0000091e,0x00000000,c2m_vc0_err_hpos[6]
0x0000091f,0x00000000,c2m_vc0_err_hpos[7]
0x00000920,0x00000000,c2m_vc0_err_vpos[0]
0x00000921,0x00000000,c2m_vc0_err_vpos[1]
0x00000922,0x00000000,c2m_vc0_err_vpos[2]
0x00000923,0x00000000,c2m_vc0_err_vpos[3]
0x00000924,0x00000000,c2m_vc0_err_vpos[4]
0x00000925,0x00000000,c2m_vc0_err_vpos[5]
0x00000926,0x00000000,c2m_vc0_err_vpos[6]
0x00000927,0x00000000,c2m_vc0_err_vpos[7]
0x00000928,0x00000000,c2m_vc0_err_fpos[0]
0x00000929,0x00000000,c2m_vc0_err_fpos[1]
0x0000092a,0x00000000,c2m_vc0_err_fpos[2]
0x0000092b,0x00000000,c2m_vc0_err_fpos[3]
0x0000092c,0x00000000,c2m_vc0_err_fpos[4]
0x0000092d,0x00000000,c2m_vc0_err_fpos[5]
0x0000092e,0x00000000,c2m_vc0_err_fpos[6]
0x0000092f,0x00000000,c2m_vc0_err_fpos[7]
0x00000930,0x00000000,c2m_vc0_mdi_evt[0]
0x00000931,0x00000000,c2m_vc0_mdi_evt[1]
0x00000932,0x00000000,c2m_vc0_mdi_evt[2]
0x00000933,0x00000000,c2m_vc0_mdi_evt[3]
0x00000950,0x00000000,c2m_vc1_config0
0x00000951,0x00000005,c2m_vc1_config1
0x00000952,0x00000000,c2m_vc1_pattern
0x00000953,0x00000000,c2m_vc1_status
0x00000954,0x00000000,c2m_vc1_prbs0
0x00000955,0x00000000,c2m_vc1_prbs1
0x00000956,0x00000000,c2m_vc1_prbs2
0x00000957,0x00000000,c2m_vc1_prbs3
0x00000958,0x00000000,c2m_vc1_res_hlen[0]
0x00000959,0x00000000,c2m_vc1_res_hlen[1]
0x0000095a,0x00000000,c2m_vc1_res_hlen[2]
0x0000095b,0x00000000,c2m_vc1_res_hlen[3]
0x0000095c,0x00000000,c2m_vc1_res_hlen[4]
0x0000095d,0x00000000,c2m_vc1_res_hlen[5]
0x0000095e,0x00000000,c2m_vc1_res_hlen[6]
0x0000095f,0x00000000,c2m_vc1_res_hlen[7]
0x00000960,0x00000000,c2m_vc1_res_vlen[0]
0x00000961,0x00000000,c2m_vc1_res_vlen[1]
0x00000962,0x00000000,c2m_vc1_res_vlen[2]
0x00000963,0x00000000,c2m_vc1_res_vlen[3]
0x00000964,0x00000000,c2m_vc1_res_vlen[4]
0x00000965,0x00000000,c2m_vc1_res_vlen[5]
0x00000966,0x00000000,c2m_vc1_res_vlen[6]
0x00000967,0x00000000,c2m_vc1_res_vlen[7]
0x00000968,0x00000000,c2m_vc1_err_hpos[0]
0x00000969,0x00000000,c2m_vc1_err_hpos[1]
0x0000096a,0x00000000,c2m_vc1_err_hpos[2]
0x0000096b,0x00000000,c2m_vc1_err_hpos[3]
0x0000096c,0x00000000,c2m_vc1_err_hpos[4]
0x0000096d,0x00000000,c2m_vc1_err_hpos[5]
0x0000096e,0x00000000,c2m_vc1_err_hpos[6]
0x0000096f,0x00000000,c2m_vc1_err_hpos[7]
0x00000970,0x00000000,c2m_vc1_err_vpos[0]
0x00000971,0x00000000,c2m_vc1_err_vpos[1]
0x00000972,0x00000000,c2m_vc1_err_vpos[2]
0x00000973,0x00000000,c2m_vc1_err_vpos[3]
0x00000974,0x00000000,c2m_vc1_err_vpos[4]
0x00000975,0x00000000,c2m_vc1_err_vpos[5]
0x00000976,0x00000000,c2m_vc1_err_vpos[6]
0x00000977,0x00000000,c2m_vc1_err_vpos[7]
0x00000978,0x00000000,c2m_vc1_err_fpos[0]
0x00000979,0x00000000,c2m_vc1_err_fpos[1]
0x0000097a,0x00000000,c2m_vc1_err_fpos[2]
0x0000097b,0x00000000,c2m_vc1_err_fpos[3]
0x0000097c,0x00000000,c2m_vc1_err_fpos[4]
0x0000097d,0x00000000,c2m_vc1_err_fpos[5]
0x0000097e,0x00000000,c2m_vc1_err_fpos[6]
0x0000097f,0x00000000,c2m_vc1_err_fpos[7]
0x00000980,0x00000000,c2m_vc1_mdi_evt[0]
0x00000981,0x00000000,c2m_vc1_mdi_evt[1]
0x00000982,0x00000000,c2m_vc1_mdi_evt[2]
0x00000983,0x00000000,c2m_vc1_mdi_evt[3]
0x00001000,0x00000000,c2m_vc2_config0
0x00001001,0x00000005,c2m_vc2_config1
0x00001002,0x00000000,c2m_vc2_pattern
0x00001003,0x00000000,c2m_vc2_status
0x00001004,0x00000000,c2m_vc2_prbs0
0x00001005,0x00000000,c2m_vc2_prbs1
0x00001006,0x00000000,c2m_vc2_prbs2
0x00001007,0x00000000,c2m_vc2_prbs3
0x00001008,0x00000000,c2m_vc2_res_hlen[0]
0x00001009,0x00000000,c2m_vc2_res_hlen[1]
0x0000100a,0x00000000,c2m_vc2_res_hlen[2]
0x0000100b,0x00000000,c2m_vc2_res_hlen[3]
0x0000100c,0x00000000,c2m_vc2_res_hlen[4]
0x0000100d,0x00000000,c2m_vc2_res_hlen[5]
0x0000100e,0x00000000,c2m_vc2_res_hlen[6]
0x0000100f,0x00000000,c2m_vc2_res_hlen[7]
0x00001010,0x00000000,c2m_vc2_res_vlen[0]
0x00001011,0x00000000,c2m_vc2_res_vlen[1]
0x00001012,0x00000000,c2m_vc2_res_vlen[2]
0x00001013,0x00000000,c2m_vc2_res_vlen[3]
0x00001014,0x00000000,c2m_vc2_res_vlen[4]
0x00001015,0x00000000,c2m_vc2_res_vlen[5]
0x00001016,0x00000000,c2m_vc2_res_vlen[6]
0x00001017,0x00000000,c2m_vc2_res_vlen[7]
0x00001018,0x00000000,c2m_vc2_err_hpos[0]
0x00001019,0x00000000,c2m_vc2_err_hpos[1]
0x0000101a,0x00000000,c2m_vc2_err_hpos[2]
0x0000101b,0x00000000,c2m_vc2_err_hpos[3]
0x0000101c,0x00000000,c2m_vc2_err_hpos[4]
0x0000101d,0x00000000,c2m_vc2_err_hpos[5]
0x0000101e,0x00000000,c2m_vc2_err_hpos[6]
0x0000101f,0x00000000,c2m_vc2_err_hpos[7]
0x00001020,0x00000000,c2m_vc2_err_vpos[0]
0x00001021,0x00000000,c2m_vc2_err_vpos[1]
0x00001022,0x00000000,c2m_vc2_err_vpos[2]
0x00001023,0x00000000,c2m_vc2_err_vpos[3]
0x00001024,0x00000000,c2m_vc2_err_vpos[4]
0x00001025,0x00000000,c2m_vc2_err_vpos[5]
0x00001026,0x00000000,c2m_vc2_err_vpos[6]
0x00001027,0x00000000,c2m_vc2_err_vpos[7]
0x00001028,0x00000000,c2m_vc2_err_fpos[0]
0x00001029,0x00000000,c2m_vc2_err_fpos[1]
0x0000102a,0x00000000,c2m_vc2_err_fpos[2]
0x0000102b,0x00000000,c2m_vc2_err_fpos[3]
0x0000102c,0x00000000,c2m_vc2_err_fpos[4]
0x0000102d,0x00000000,c2m_vc2_err_fpos[5]
0x0000102e,0x00000000,c2m_vc2_err_fpos[6]
0x0000102f,0x00000000,c2m_vc2_err_fpos[7]
0x00001030,0x00000000,c2m_vc2_mdi_evt[0]
0x00001031,0x00000000,c2m_vc2_mdi_evt[1]
0x00001032,0x00000000,c2m_vc2_mdi_evt[2]
0x00001033,0x00000000,c2m_vc2_mdi_evt[3]
0x00001050,0x00000000,c2m_vc3_config0
0x00001051,0x00000005,c2m_vc3_config1
0x00001052,0x00000000,c2m_vc3_pattern
0x00001053,0x00000000,c2m_vc3_status
0x00001054,0x00000000,c2m_vc3_prbs0
0x00001055,0x00000000,c2m_vc3_prbs1
0x00001056,0x00000000,c2m_vc3_prbs2
0x00001057,0x00000000,c2m_vc3_prbs3
0x00001058,0x00000000,c2m_vc3_res_hlen[0]
0x00001059,0x00000000,c2m_vc3_res_hlen[1]
0x0000105a,0x00000000,c2m_vc3_res_hlen[2]
0x0000105b,0x00000000,c2m_vc3_res_hlen[3]
0x0000105c,0x00000000,c2m_vc3_res_hlen[4]
0x0000105d,0x00000000,c2m_vc3_res_hlen[5]
0x0000105e,0x00000000,c2m_vc3_res_hlen[6]
0x0000105f,0x00000000,c2m_vc3_res_hlen[7]
0x00001060,0x00000000,c2m_vc3_res_vlen[0]
0x00001061,0x00000000,c2m_vc3_res_vlen[1]
0x00001062,0x00000000,c2m_vc3_res_vlen[2]
0x00001063,0x00000000,c2m_vc3_res_vlen[3]
0x00001064,0x00000000,c2m_vc3_res_vlen[4]
0x00001065,0x00000000,c2m_vc3_res_vlen[5]
0x00001066,0x00000000,c2m_vc3_res_vlen[6]
0x00001067,0x00000000,c2m_vc3_res_vlen[7]
0x00001068,0x00000000,c2m_vc3_err_hpos[0]
0x00001069,0x00000000,c2m_vc3_err_hpos[1]
0x0000106a,0x00000000,c2m_vc3_err_hpos[2]
0x0000106b,0x00000000,c2m_vc3_err_hpos[3]
0x0000106c,0x00000000,c2m_vc3_err_hpos[4]
0x0000106d,0x00000000,c2m_vc3_err_hpos[5]
0x0000106e,0x00000000,c2m_vc3_err_hpos[6]
0x0000106f,0x00000000,c2m_vc3_err_hpos[7]
0x00001070,0x00000000,c2m_vc3_err_vpos[0]
0x00001071,0x00000000,c2m_vc3_err_vpos[1]
0x00001072,0x00000000,c2m_vc3_err_vpos[2]
0x00001073,0x00000000,c2m_vc3_err_vpos[3]
0x00001074,0x00000000,c2m_vc3_err_vpos[4]
0x00001075,0x00000000,c2m_vc3_err_vpos[5]
0x00001076,0x00000000,c2m_vc3_err_vpos[6]
0x00001077,0x00000000,c2m_vc3_err_vpos[7]
0x00001078,0x00000000,c2m_vc3_err_fpos[0]
0x00001079,0x00000000,c2m_vc3_err_fpos[1]
0x0000107a,0x00000000,c2m_vc3_err_fpos[2]
0x0000107b,0x00000000,c2m_vc3_err_fpos[3]
0x0000107c,0x00000000,c2m_vc3_err_fpos[4]
0x0000107d,0x00000000,c2m_vc3_err_fpos[5]
0x0000107e,0x00000000,c2m_vc3_err_fpos[6]
0x0000107f,0x00000000,c2m_vc3_err_fpos[7]
0x00001080,0x00000000,c2m_vc3_mdi_evt[0]
0x00001081,0x00000000,c2m_vc3_mdi_evt[1]
0x00001082,0x00000000,c2m_vc3_mdi_evt[2]
0x00001083,0x00000000,c2m_vc3_mdi_evt[3]
0x00001100,0x00000000,c2m_gpios_ctrl0
0x00001101,0x00000000,c2m_gpios_ctrl1
0x00001110,0x00000000,c2m_gpios_gpio_a[0]
0x00001114,0x00000000,c2m_gpios_gpio_a[1]
0x00001118,0x00000000,c2m_gpios_gpio_a[2]
0x0000111c,0x00000000,c2m_gpios_gpio_a[3]
0x00001120,0x00000000,c2m_gpios_gpio_a[4]
0x00001124,0x00000000,c2m_gpios_gpio_a[5]
0x00001128,0x00000000,c2m_gpios_gpio_a[6]
0x0000112c,0x00000000,c2m_gpios_gpio_a[7]
0x00001130,0x00000000,c2m_gpios_gpio_a[8]
0x00001134,0x00000000,c2m_gpios_gpio_a[9]
0x00001138,0x00000000,c2m_gpios_gpio_a[10]
0x0000113c,0x00000000,c2m_gpios_gpio_a[11]
0x00001140,0x00000000,c2m_gpios_gpio_a[12]
0x00001144,0x00000000,c2m_gpios_gpio_a[13]
0x00001148,0x00000000,c2m_gpios_gpio_a[14]
0x0000114c,0x00000000,c2m_gpios_gpio_a[15]
0x00001150,0x00000000,c2m_gpios_gpio_a[16]
0x00001154,0x00000000,c2m_gpios_gpio_a[17]
0x00001158,0x00000000,c2m_gpios_gpio_a[18]
0x0000115c,0x00000000,c2m_gpios_gpio_a[19]
0x00001160,0x00000000,c2m_gpios_gpio_a[20]
0x00001164,0x00000000,c2m_gpios_gpio_a[21]
0x00001168,0x00000000,c2m_gpios_gpio_a[22]
0x0000116c,0x00000000,c2m_gpios_gpio_a[23]
0x00001170,0x00000000,c2m_gpios_gpio_a[24]
0x00001174,0x00000000,c2m_gpios_gpio_a[25]
0x00001178,0x00000000,c2m_gpios_gpio_a[26]
0x0000117c,0x00000000,c2m_gpios_gpio_a[27]
0x00001180,0x00000004,c2m_gpios_gpio_a[28]
0x00001184,0x00000004,c2m_gpios_gpio_a[29]
0x00001188,0x00000004,c2m_gpios_gpio_a[30]
0x0000118c,0x00000004,c2m_gpios_gpio_a[31]
0x00001111,0x00000000,c2m_gpios_gpio_b[0]
0x00001115,0x00000001,c2m_gpios_gpio_b[1]
0x00001119,0x00000002,c2m_gpios_gpio_b[2]
0x0000111d,0x00000003,c2m_gpios_gpio_b[3]
0x00001121,0x00000004,c2m_gpios_gpio_b[4]
0x00001125,0x00000005,c2m_gpios_gpio_b[5]
0x00001129,0x00000006,c2m_gpios_gpio_b[6]
0x0000112d,0x00000007,c2m_gpios_gpio_b[7]
0x00001131,0x00000008,c2m_gpios_gpio_b[8]
0x00001135,0x00000009,c2m_gpios_gpio_b[9]
0x00001139,0x0000000a,c2m_gpios_gpio_b[10]
0x0000113d,0x0000000b,c2m_gpios_gpio_b[11]
0x00001141,0x0000000c,c2m_gpios_gpio_b[12]
0x00001145,0x0000000d,c2m_gpios_gpio_b[13]
0x00001149,0x0000000e,c2m_gpios_gpio_b[14]
0x0000114d,0x0000000f,c2m_gpios_gpio_b[15]
0x00001151,0x00000010,c2m_gpios_gpio_b[16]
0x00001155,0x00000011,c2m_gpios_gpio_b[17]
0x00001159,0x00000012,c2m_gpios_gpio_b[18]
0x0000115d,0x00000013,c2m_gpios_gpio_b[19]
0x00001161,0x00000014,c2m_gpios_gpio_b[20]
0x00001165,0x00000015,c2m_gpios_gpio_b[21]
0x00001169,0x00000016,c2m_gpios_gpio_b[22]
0x0000116d,0x00000017,c2m_gpios_gpio_b[23]
0x00001171,0x00000018,c2m_gpios_gpio_b[24]
0x00001175,0x00000019,c2m_gpios_gpio_b[25]
0x00001179,0x0000001a,c2m_gpios_gpio_b[26]
0x0000117d,0x0000001b,c2m_gpios_gpio_b[27]
0x00001181,0x0000001c,c2m_gpios_gpio_b[28]
0x00001185,0x0000001d,c2m_gpios_gpio_b[29]
0x00001189,0x0000001e,c2m_gpios_gpio_b[30]
0x0000118d,0x0000001f,c2m_gpios_gpio_b[31]
0x00001112,0x00000000,c2m_gpios_gpio_c[0]
0x00001116,0x00000001,c2m_gpios_gpio_c[1]
0x0000111a,0x00000002,c2m_gpios_gpio_c[2]
0x0000111e,0x00000003,c2m_gpios_gpio_c[3]
0x00001122,0x00000004,c2m_gpios_gpio_c[4]
0x00001126,0x00000005,c2m_gpios_gpio_c[5]
0x0000112a,0x00000006,c2m_gpios_gpio_c[6]
0x0000112e,0x00000007,c2m_gpios_gpio_c[7]
0x00001132,0x00000008,c2m_gpios_gpio_c[8]
0x00001136,0x00000009,c2m_gpios_gpio_c[9]
0x0000113a,0x0000000a,c2m_gpios_gpio_c[10]
0x0000113e,0x0000000b,c2m_gpios_gpio_c[11]
0x00001142,0x0000000c,c2m_gpios_gpio_c[12]
0x00001146,0x0000000d,c2m_gpios_gpio_c[13]
0x0000114a,0x0000000e,c2m_gpios_gpio_c[14]
0x0000114e,0x0000000f,c2m_gpios_gpio_c[15]
0x00001152,0x00000010,c2m_gpios_gpio_c[16]
0x00001156,0x00000011,c2m_gpios_gpio_c[17]
0x0000115a,0x00000012,c2m_gpios_gpio_c[18]
0x0000115e,0x00000013,c2m_gpios_gpio_c[19]
0x00001162,0x00000014,c2m_gpios_gpio_c[20]
0x00001166,0x00000015,c2m_gpios_gpio_c[21]
0x0000116a,0x00000016,c2m_gpios_gpio_c[22]
0x0000116e,0x00000017,c2m_gpios_gpio_c[23]
0x00001172,0x00000018,c2m_gpios_gpio_c[24]
0x00001176,0x00000019,c2m_gpios_gpio_c[25]
0x0000117a,0x0000001a,c2m_gpios_gpio_c[26]
0x0000117e,0x0000001b,c2m_gpios_gpio_c[27]
0x00001182,0x0000001c,c2m_gpios_gpio_c[28]
0x00001186,0x0000001d,c2m_gpios_gpio_c[29]
0x0000118a,0x0000001e,c2m_gpios_gpio_c[30]
0x0000118e,0x0000001f,c2m_gpios_gpio_c[31]
0x00001113,0x0000000e,c2m_gpios_gpio_d[0]
0x00001117,0x0000000e,c2m_gpios_gpio_d[1]
0x0000111b,0x0000000e,c2m_gpios_gpio_d[2]
0x0000111f,0x0000000e,c2m_gpios_gpio_d[3]
0x00001123,0x0000000e,c2m_gpios_gpio_d[4]
0x00001127,0x0000000e,c2m_gpios_gpio_d[5]
0x0000112b,0x0000000e,c2m_gpios_gpio_d[6]
0x0000112f,0x0000000e,c2m_gpios_gpio_d[7]
0x00001133,0x0000000e,c2m_gpios_gpio_d[8]
0x00001137,0x0000000e,c2m_gpios_gpio_d[9]
0x0000113b,0x0000000e,c2m_gpios_gpio_d[10]
0x0000113f,0x0000000e,c2m_gpios_gpio_d[11]
0x00001143,0x0000000e,c2m_gpios_gpio_d[12]
0x00001147,0x0000000e,c2m_gpios_gpio_d[13]
0x0000114b,0x0000000e,c2m_gpios_gpio_d[14]
0x0000114f,0x0000000e,c2m_gpios_gpio_d[15]
0x00001153,0x0000000e,c2m_gpios_gpio_d[16]
0x00001157,0x0000000e,c2m_gpios_gpio_d[17]
0x0000115b,0x0000000e,c2m_gpios_gpio_d[18]
0x0000115f,0x0000000e,c2m_gpios_gpio_d[19]
0x00001163,0x0000000e,c2m_gpios_gpio_d[20]
0x00001167,0x0000000e,c2m_gpios_gpio_d[21]
0x0000116b,0x0000000e,c2m_gpios_gpio_d[22]
0x0000116f,0x0000000e,c2m_gpios_gpio_d[23]
0x00001173,0x0000000e,c2m_gpios_gpio_d[24]
0x00001177,0x0000000e,c2m_gpios_gpio_d[25]
0x0000117b,0x0000000e,c2m_gpios_gpio_d[26]
0x0000117f,0x0000000e,c2m_gpios_gpio_d[27]
0x00001183,0x0000000e,c2m_gpios_gpio_d[28]
0x00001187,0x0000000e,c2m_gpios_gpio_d[29]
0x0000118b,0x0000000e,c2m_gpios_gpio_d[30]
0x0000118f,0x0000000e,c2m_gpios_gpio_d[31]
0x00001800,0x00000000,c2m_framesync_fsync_gen0
0x00001801,0x00000004,c2m_framesync_fsync_gen1[0]
0x00001802,0x00000004,c2m_framesync_fsync_gen1[1]
0x00001803,0x00000004,c2m_framesync_fsync_gen1[2]
0x00001804,0x00000035,c2m_framesync_fsync_gen2[0]
0x00001805,0x00000035,c2m_framesync_fsync_gen2[1]
0x00001806,0x00000035,c2m_framesync_fsync_gen2[2]
0x00001807,0x00000087,c2m_framesync_fsync_gen3[0]
0x00001808,0x00000087,c2m_framesync_fsync_gen3[1]
0x00001809,0x00000087,c2m_framesync_fsync_gen3[2]
0x0000180a,0x0000000c,c2m_framesync_fsync_gen4[0]
0x0000180b,0x0000000c,c2m_framesync_fsync_gen4[1]
0x0000180c,0x0000000c,c2m_framesync_fsync_gen4[2]
0x0000180d,0x00000080,c2m_framesync_fsync_gen5[0]
0x0000180e,0x00000080,c2m_framesync_fsync_gen5[1]
0x0000180f,0x00000080,c2m_framesync_fsync_gen5[2]
0x00001810,0x00000000,c2m_framesync_fsync_gen6[0]
0x00001811,0x00000000,c2m_framesync_fsync_gen6[1]
0x00001812,0x00000000,c2m_framesync_fsync_gen6[2]
0x00001813,0x00000000,c2m_framesync_fsync_gen7[0]
0x00001814,0x00000000,c2m_framesync_fsync_gen7[1]
0x00001815,0x00000000,c2m_framesync_fsync_gen7[2]
0x00001816,0x00000000,c2m_framesync_fsync_gen8[0]
0x00001817,0x00000000,c2m_framesync_fsync_gen8[1]
0x00001818,0x00000000,c2m_framesync_fsync_gen8[2]
0x00001819,0x00000000,c2m_framesync_fsync_diff0[0]
0x0000181a,0x00000000,c2m_framesync_fsync_diff0[1]
0x0000181b,0x000000ff,c2m_framesync_fsync_diff1[0]
0x0000181c,0x000000ff,c2m_framesync_fsync_diff1[1]
0x0000181d,0x00000000,c2m_framesync_fsync_diff2[0]
0x0000181e,0x00000000,c2m_framesync_fsync_diff2[1]
0x0000181f,0x00000000,c2m_framesync_fsync_diff3[0]
0x00001820,0x00000000,c2m_framesync_fsync_diff3[1]
0x00001821,0x00000000,c2m_framesync_fsync_diff4[0]
0x00001822,0x00000000,c2m_framesync_fsync_diff4[1]
0x00001823,0x00000000,c2m_framesync_fsync_diff5[0]
0x00001824,0x00000000,c2m_framesync_fsync_diff5[1]
0x00001825,0x00000000,c2m_framesync_fsync_diff6[0]
0x00001826,0x00000000,c2m_framesync_fsync_diff6[1]
0x00001827,0x00000000,c2m_framesync_fsync_diff7[0]
0x00001828,0x00000000,c2m_framesync_fsync_diff7[1]
0x00001829,0x00000000,c2m_framesync_fsync_ctrl0[0]
0x0000182a,0x00000000,c2m_framesync_fsync_ctrl0[1]
0x0000182b,0x00000000,c2m_framesync_fsync_ctrl1[0]
0x0000182c,0x00000000,c2m_framesync_fsync_ctrl1[1]
0x0000182d,0x00000000,c2m_framesync_fsync_ctrl1[2]
0x00001900,0x00000024,c2m_fh_tx_en_ctrl0[0]
0x00001903,0x00000024,c2m_fh_tx_en_ctrl0[1]
0x00001906,0x00000024,c2m_fh_tx_en_ctrl0[2]
0x00001909,0x00000024,c2m_fh_tx_en_ctrl0[3]
0x00001901,0x000000e0,c2m_fh_tx_en_ctrl1[0]
0x00001904,0x000000e0,c2m_fh_tx_en_ctrl1[1]
0x00001907,0x000000e0,c2m_fh_tx_en_ctrl1[2]
0x0000190a,0x000000e0,c2m_fh_tx_en_ctrl1[3]
0x00001902,0x00000050,c2m_fh_tx_en_ctrl2[0]
0x00001905,0x00000050,c2m_fh_tx_en_ctrl2[1]
0x00001908,0x00000050,c2m_fh_tx_en_ctrl2[2]
0x0000190b,0x00000050,c2m_fh_tx_en_ctrl2[3]
0x00001910,0x0000008c,c2m_fh_rx_en_ctrl0[0]
0x00001918,0x0000008c,c2m_fh_rx_en_ctrl0[1]
0x00001920,0x0000008c,c2m_fh_rx_en_ctrl0[2]
0x00001928,0x0000008c,c2m_fh_rx_en_ctrl0[3]
0x00001911,0x00000013,c2m_fh_rx_en_ctrl1[0]
0x00001919,0x00000013,c2m_fh_rx_en_ctrl1[1]
0x00001921,0x00000013,c2m_fh_rx_en_ctrl1[2]
0x00001929,0x00000013,c2m_fh_rx_en_ctrl1[3]
0x00001912,0x0000000c,c2m_fh_rx_en_ctrl2[0]
0x0000191a,0x0000000c,c2m_fh_rx_en_ctrl2[1]
0x00001922,0x0000000c,c2m_fh_rx_en_ctrl2[2]
0x0000192a,0x0000000c,c2m_fh_rx_en_ctrl2[3]
0x00001913,0x00000000,c2m_fh_rx_en_ctrl3[0]
0x0000191b,0x00000000,c2m_fh_rx_en_ctrl3[1]
0x00001923,0x00000000,c2m_fh_rx_en_ctrl3[2]
0x0000192b,0x00000000,c2m_fh_rx_en_ctrl3[3]
0x00001914,0x00000000,c2m_fh_rx_en_ctrl4[0]
0x0000191c,0x00000000,c2m_fh_rx_en_ctrl4[1]
0x00001924,0x00000000,c2m_fh_rx_en_ctrl4[2]
0x0000192c,0x00000000,c2m_fh_rx_en_ctrl4[3]
0x00001915,0x00000000,c2m_fh_rx_en_ctrl5[0]
0x0000191d,0x00000000,c2m_fh_rx_en_ctrl5[1]
0x00001925,0x00000000,c2m_fh_rx_en_ctrl5[2]
0x0000192d,0x00000000,c2m_fh_rx_en_ctrl5[3]
0x00001916,0x00000000,c2m_fh_rx_en_ctrl6[0]
0x0000191e,0x00000000,c2m_fh_rx_en_ctrl6[1]
0x00001926,0x00000000,c2m_fh_rx_en_ctrl6[2]
0x0000192e,0x00000000,c2m_fh_rx_en_ctrl6[3]
0x00001917,0x00000000,c2m_fh_rx_en_ctrl7[0]
0x0000191f,0x00000000,c2m_fh_rx_en_ctrl7[1]
0x00001927,0x00000000,c2m_fh_rx_en_ctrl7[2]
0x0000192f,0x00000000,c2m_fh_rx_en_ctrl7[3]
0x00001950,0x00000000,c2m_fh_rx_en_ctrl8[0]
0x00001951,0x00000000,c2m_fh_rx_en_ctrl8[1]
0x00001952,0x00000000,c2m_fh_rx_en_ctrl8[2]
0x00001953,0x00000000,c2m_fh_rx_en_ctrl8[3]
0x00001954,0x0000002c,c2m_fh_rx_en_ctrl9[0]
0x00001955,0x0000002c,c2m_fh_rx_en_ctrl9[1]
0x00001956,0x0000002c,c2m_fh_rx_en_ctrl9[2]
0x00001957,0x0000002c,c2m_fh_rx_en_ctrl9[3]
0x00001958,0x00000000,c2m_fh_lf_en_ctrl0
0x00001960,0x00000080,c2m_fh_i2c_en_ctrl0[0]
0x00001962,0x00000080,c2m_fh_i2c_en_ctrl0[1]
0x00001964,0x00000080,c2m_fh_i2c_en_ctrl0[2]
0x00001961,0x00000001,c2m_fh_i2c_en_ctrl1[0]
0x00001963,0x00000001,c2m_fh_i2c_en_ctrl1[1]
0x00001965,0x00000001,c2m_fh_i2c_en_ctrl1[2]
0x00001968,0x00000000,c2m_fh_uart_en_ctrl0[0]
0x0000196a,0x00000000,c2m_fh_uart_en_ctrl0[1]
0x0000196c,0x00000000,c2m_fh_uart_en_ctrl0[2]
0x00001969,0x00000000,c2m_fh_uart_en_ctrl1[0]
0x0000196b,0x00000000,c2m_fh_uart_en_ctrl1[1]
0x0000196d,0x00000000,c2m_fh_uart_en_ctrl1[2]
0x00001970,0x00000000,c2m_fh_spi_en_ctrl0
0x00001978,0x00000000,c2m_fh_gpio_en_ctrl0[0]
0x00001979,0x00000000,c2m_fh_gpio_en_ctrl0[1]
0x0000197a,0x00000000,c2m_fh_gpio_en_ctrl0[2]
0x0000197b,0x00000000,c2m_fh_gpio_en_ctrl0[3]
0x00001980,0x0000003a,c2m_fh_rx_router_en_ctrl0[0]
0x00001984,0x0000003a,c2m_fh_rx_router_en_ctrl0[1]
0x00001988,0x0000003a,c2m_fh_rx_router_en_ctrl0[2]
0x0000198c,0x0000003a,c2m_fh_rx_router_en_ctrl0[3]
0x00001990,0x0000003a,c2m_fh_rx_router_en_ctrl0[4]
0x00001994,0x0000003a,c2m_fh_rx_router_en_ctrl0[5]
0x00001998,0x0000003a,c2m_fh_rx_router_en_ctrl0[6]
0x0000199c,0x0000003a,c2m_fh_rx_router_en_ctrl0[7]
0x00001981,0x00000050,c2m_fh_rx_router_en_ctrl1[0]
0x00001985,0x00000050,c2m_fh_rx_router_en_ctrl1[1]
0x00001989,0x00000050,c2m_fh_rx_router_en_ctrl1[2]
0x0000198d,0x00000050,c2m_fh_rx_router_en_ctrl1[3]
0x00001991,0x00000050,c2m_fh_rx_router_en_ctrl1[4]
0x00001995,0x00000050,c2m_fh_rx_router_en_ctrl1[5]
0x00001999,0x00000050,c2m_fh_rx_router_en_ctrl1[6]
0x0000199d,0x00000050,c2m_fh_rx_router_en_ctrl1[7]
0x000019a0,0x00000001,c2m_fh_frame_sync_en_ctrl0
0x000019a1,0x00000007,c2m_fh_mipi_tx_en_ctrl0
0x000019a2,0x00000001,c2m_fh_pll_en_ctrl0
0x000019a4,0x00000000,c2m_fh_vm_en_ov_ctrl0[0]
0x000019a5,0x00000000,c2m_fh_vm_en_ov_ctrl0[1]
0x000019a6,0x00000000,c2m_fh_vm_en_ov_ctrl0[2]
0x000019a7,0x0000000e,c2m_fh_vm_en_ov_ctrl0[3]
0x000019a8,0x00000000,c2m_fh_vm_en_uv_ctrl0[0]
0x000019a9,0x00000000,c2m_fh_vm_en_uv_ctrl0[1]
0x000019aa,0x00000000,c2m_fh_vm_en_uv_ctrl0[2]
0x000019ab,0x0000000e,c2m_fh_vm_en_uv_ctrl0[3]
0x000019ac,0x0000000f,c2m_fh_bist_en_ctrl0
0x000019ad,0x00000000,c2m_fh_cfg_crc_en_ctrl0
0x000019ae,0x00000000,c2m_fh_rem_event_en_ctrl0
0x000019af,0x00000000,c2m_fh_rem_event_en_ctrl1
0x000019b0,0x00000000,c2m_fh_clr_ctrl0
0x000019b1,0x00000000,c2m_fh_clr_ctrl1
0x000019b2,0x00000000,c2m_fh_clr_ctrl2
0x000019b3,0x00000000,c2m_fh_clr_ctrl3
0x000019b4,0x00000000,c2m_fh_clr_ctrl4
0x000019c0,0x00000000,c2m_fh_l0_status0
0x000019c1,0x00000000,c2m_fh_l0_status1
0x000019c2,0x00000000,c2m_fh_l0_status2
0x000019c3,0x00000000,c2m_fh_l0_status3
0x000019c4,0x00000000,c2m_fh_l0_status4
0x000019c5,0x00000000,c2m_fh_err_status
0x00001a00,0x00000000,c2m_fh_tx_status0[0]
0x00001a03,0x00000000,c2m_fh_tx_status0[1]
0x00001a06,0x00000000,c2m_fh_tx_status0[2]
0x00001a09,0x00000000,c2m_fh_tx_status0[3]
0x00001a01,0x00000000,c2m_fh_tx_status1[0]
0x00001a04,0x00000000,c2m_fh_tx_status1[1]
0x00001a07,0x00000000,c2m_fh_tx_status1[2]
0x00001a0a,0x00000000,c2m_fh_tx_status1[3]
0x00001a02,0x00000000,c2m_fh_tx_status2[0]
0x00001a05,0x00000000,c2m_fh_tx_status2[1]
0x00001a08,0x00000000,c2m_fh_tx_status2[2]
0x00001a0b,0x00000000,c2m_fh_tx_status2[3]
0x00001a10,0x00000000,c2m_fh_rx_status0[0]
0x00001a18,0x00000000,c2m_fh_rx_status0[1]
0x00001a20,0x00000000,c2m_fh_rx_status0[2]
0x00001a28,0x00000000,c2m_fh_rx_status0[3]
0x00001a11,0x00000000,c2m_fh_rx_status1[0]
0x00001a19,0x00000000,c2m_fh_rx_status1[1]
0x00001a21,0x00000000,c2m_fh_rx_status1[2]
0x00001a29,0x00000000,c2m_fh_rx_status1[3]
0x00001a12,0x00000000,c2m_fh_rx_status2[0]
0x00001a1a,0x00000000,c2m_fh_rx_status2[1]
0x00001a22,0x00000000,c2m_fh_rx_status2[2]
0x00001a2a,0x00000000,c2m_fh_rx_status2[3]
0x00001a13,0x00000000,c2m_fh_rx_status3[0]
0x00001a1b,0x00000000,c2m_fh_rx_status3[1]
0x00001a23,0x00000000,c2m_fh_rx_status3[2]
0x00001a2b,0x00000000,c2m_fh_rx_status3[3]
0x00001a14,0x00000000,c2m_fh_rx_status4[0]
0x00001a1c,0x00000000,c2m_fh_rx_status4[1]
0x00001a24,0x00000000,c2m_fh_rx_status4[2]
0x00001a2c,0x00000000,c2m_fh_rx_status4[3]
0x00001a15,0x00000000,c2m_fh_rx_status5[0]
0x00001a1d,0x00000000,c2m_fh_rx_status5[1]
0x00001a25,0x00000000,c2m_fh_rx_status5[2]
0x00001a2d,0x00000000,c2m_fh_rx_status5[3]
0x00001a16,0x00000000,c2m_fh_rx_status6[0]
0x00001a1e,0x00000000,c2m_fh_rx_status6[1]
0x00001a26,0x00000000,c2m_fh_rx_status6[2]
0x00001a2e,0x00000000,c2m_fh_rx_status6[3]
0x00001a17,0x00000000,c2m_fh_rx_status7[0]
0x00001a1f,0x00000000,c2m_fh_rx_status7[1]
0x00001a27,0x00000000,c2m_fh_rx_status7[2]
0x00001a2f,0x00000000,c2m_fh_rx_status7[3]
0x00001a50,0x00000000,c2m_fh_rx_status8[0]
0x00001a51,0x00000000,c2m_fh_rx_status8[1]
0x00001a52,0x00000000,c2m_fh_rx_status8[2]
0x00001a53,0x00000000,c2m_fh_rx_status8[3]
0x00001a54,0x00000000,c2m_fh_rx_status9[0]
0x00001a55,0x00000000,c2m_fh_rx_status9[1]
0x00001a56,0x00000000,c2m_fh_rx_status9[2]
0x00001a57,0x00000000,c2m_fh_rx_status9[3]
0x00001a58,0x00000000,c2m_fh_lf_status0
0x00001a60,0x00000000,c2m_fh_i2c_status0[0]
0x00001a62,0x00000000,c2m_fh_i2c_status0[1]
0x00001a64,0x00000000,c2m_fh_i2c_status0[2]
0x00001a61,0x00000000,c2m_fh_i2c_status1[0]
0x00001a63,0x00000000,c2m_fh_i2c_status1[1]
0x00001a65,0x00000000,c2m_fh_i2c_status1[2]
0x00001a68,0x00000000,c2m_fh_uart_status0[0]
0x00001a6a,0x00000000,c2m_fh_uart_status0[1]
0x00001a6c,0x00000000,c2m_fh_uart_status0[2]
0x00001a69,0x00000000,c2m_fh_uart_status1[0]
0x00001a6b,0x00000000,c2m_fh_uart_status1[1]
0x00001a6d,0x00000000,c2m_fh_uart_status1[2]
0x00001a70,0x00000000,c2m_fh_spi_status0
0x00001a78,0x00000000,c2m_fh_gpio_status0[0]
0x00001a79,0x00000000,c2m_fh_gpio_status0[1]
0x00001a7a,0x00000000,c2m_fh_gpio_status0[2]
0x00001a7b,0x00000000,c2m_fh_gpio_status0[3]
0x00001a80,0x00000000,c2m_fh_rx_router_status0[0]
0x00001a82,0x00000000,c2m_fh_rx_router_status0[1]
0x00001a84,0x00000000,c2m_fh_rx_router_status0[2]
0x00001a86,0x00000000,c2m_fh_rx_router_status0[3]
0x00001a88,0x00000000,c2m_fh_rx_router_status0[4]
0x00001a8a,0x00000000,c2m_fh_rx_router_status0[5]
0x00001a8c,0x00000000,c2m_fh_rx_router_status0[6]
0x00001a8e,0x00000000,c2m_fh_rx_router_status0[7]
0x00001a81,0x00000000,c2m_fh_rx_router_status1[0]
0x00001a83,0x00000000,c2m_fh_rx_router_status1[1]
0x00001a85,0x00000000,c2m_fh_rx_router_status1[2]
0x00001a87,0x00000000,c2m_fh_rx_router_status1[3]
0x00001a89,0x00000000,c2m_fh_rx_router_status1[4]
0x00001a8b,0x00000000,c2m_fh_rx_router_status1[5]
0x00001a8d,0x00000000,c2m_fh_rx_router_status1[6]
0x00001a8f,0x00000000,c2m_fh_rx_router_status1[7]
0x00001aa0,0x00000000,c2m_fh_frame_sync_status0
0x00001aa1,0x00000000,c2m_fh_mipi_tx_status0
0x00001aa2,0x00000000,c2m_fh_pll_status0
0x00001aa4,0x00000000,c2m_fh_vm_ov_status0[0]
0x00001aa5,0x00000000,c2m_fh_vm_ov_status0[1]
0x00001aa6,0x00000000,c2m_fh_vm_ov_status0[2]
0x00001aa7,0x00000000,c2m_fh_vm_ov_status0[3]
0x00001aa8,0x00000000,c2m_fh_vm_uv_status0[0]
0x00001aa9,0x00000000,c2m_fh_vm_uv_status0[1]
0x00001aaa,0x00000000,c2m_fh_vm_uv_status0[2]
0x00001aab,0x00000000,c2m_fh_vm_uv_status0[3]
0x00001aac,0x00000000,c2m_fh_bist_status0
0x00001aad,0x00000000,c2m_fh_cfg_crc_status0
0x00001aae,0x00000000,c2m_fh_rem_event_status0
0x00001aaf,0x000000f0,c2m_fh_rem_event_status1
0x00001ab0,0x00000000,c2m_fh_ctrl0
0x00001ab1,0x00000000,c2m_fh_ctrl1
0x00001ac0,0x0000000f,c2m_fh_lock_ctrl0
0x00001ac1,0x00000000,c2m_fh_lock_ctrl1
0x00001ac2,0x00000000,c2m_fh_lock_status0
0x00001ac3,0x00000000,c2m_fh_lock_status1
0x00001f00,0x000000ff,c2m_lf_ctrl0
0x00001f01,0x00000000,c2m_lf_ctrl1
0x00001f02,0x00000000,c2m_lf_ctrl2
0x00001f08,0x00000000,c2m_lf_status0
0x00001f09,0x00000000,c2m_lf_status1
0x00001f0a,0x00000000,c2m_lf_status2
0x00001f0b,0x00000000,c2m_lf_status3
0x00001f80,0x00000019,c2m_link_init_ctrl0
0x00001f81,0x00000019,c2m_link_init_ctrl1
0x00001f82,0x00000019,c2m_link_init_ctrl2
0x00001f83,0x000000d8,c2m_link_init_ctrl3
0x00001f84,0x00000090,c2m_link_init_ctrl4
0x00001f85,0x000000c4,c2m_link_init_ctrl5
0x00001f86,0x00000009,c2m_link_init_ctrl6
0x00001f87,0x00000019,c2m_link_init_ctrl7
0x00001f88,0x00000019,c2m_link_init_ctrl8
0x00001f89,0x00000019,c2m_link_init_ctrl9
0x00001f8a,0x00000019,c2m_link_init_ctrl10
0x00001f8b,0x00000019,c2m_link_init_ctrl11
0x00001f8c,0x00000003,c2m_link_init_ctrl12
0x00002000,0x00000000,c2m_tx_link0_phy_phy_ctrl0
0x00002001,0x00000080,c2m_tx_link0_phy_sync_ctrl0
0x00002002,0x00000000,c2m_tx_link0_phy_sync_ctrl1
0x00002003,0x00000000,c2m_tx_link0_phy_sync_ctrl2
0x00002004,0x00000000,c2m_tx_link0_phy_comp_ctrl0
0x00002005,0x00000000,c2m_tx_link0_phy_comp_ctrl1
0x00002006,0x00000000,c2m_tx_link0_phy_comp_ctrl2
0x00002007,0x00000001,c2m_tx_link0_phy_fec_ctrl0
0x00002008,0x00000002,c2m_tx_link0_phy_psch_ctrl0
0x00002009,0x00000000,c2m_tx_link0_phy_ipg_ctrl0
0x0000200a,0x00000040,c2m_tx_link0_phy_ipg_ctrl1
0x0000200b,0x0000007f,c2m_tx_link0_phy_idle_ctrl0
0x0000200c,0x0000000c,c2m_tx_link0_phy_errinj_ctrl0
0x0000200d,0x00000000,c2m_tx_link0_phy_errinj_ctrl1
0x0000200e,0x00000081,c2m_tx_link0_phy_ana_ctrl0
0x0000200f,0x00000000,c2m_tx_link0_phy_ana_ctrl1
0x00002010,0x00000000,c2m_tx_link0_phy_ana_ctrl2
0x00002011,0x00000001,c2m_tx_link0_phy_ana_ctrl3
0x00002012,0x0000007f,c2m_tx_link0_phy_ana_ctrl4
0x00002013,0x00000001,c2m_tx_link0_phy_ana_ctrl5
0x00002014,0x00000064,c2m_tx_link0_phy_ana_ctrl6
0x00002015,0x00000019,c2m_tx_link0_phy_ana_ctrl7
0x00002016,0x0000005a,c2m_tx_link0_phy_ana_ctrl8
0x00002017,0x0000003c,c2m_tx_link0_phy_ana_ctrl9
0x000020f0,0x00000000,c2m_tx_link0_psch_ctrl0
0x000020f1,0x00000000,c2m_tx_link0_psch_ctrl1
0x000020f2,0x00000001,c2m_tx_link0_psch_ctrl2
0x000020f3,0x00000001,c2m_tx_link0_psch_ctrl3
0x000020f4,0x00000000,c2m_tx_link0_psch_status0
0x00002020,0x00000003,c2m_tx_link0_i2c0_ctrl0
0x00002021,0x0000003c,c2m_tx_link0_i2c0_ctrl1
0x00002022,0x00000010,c2m_tx_link0_i2c0_ctrl2
0x00002023,0x000000a0,c2m_tx_link0_i2c0_ctrl3
0x00002024,0x00000001,c2m_tx_link0_i2c0_ctrl4
0x00002025,0x00000000,c2m_tx_link0_i2c0_ctrl5
0x00002026,0x00000000,c2m_tx_link0_i2c0_ctrl6
0x00002027,0x00000000,c2m_tx_link0_i2c0_ctrl7
0x00002028,0x00000000,c2m_tx_link0_i2c0_status0
0x00002029,0x00000000,c2m_tx_link0_i2c0_status1
0x0000202a,0x00000000,c2m_tx_link0_i2c0_status2
0x00002030,0x00000003,c2m_tx_link0_i2c1_ctrl0
0x00002031,0x0000003c,c2m_tx_link0_i2c1_ctrl1
0x00002032,0x00000015,c2m_tx_link0_i2c1_ctrl2
0x00002033,0x000000a0,c2m_tx_link0_i2c1_ctrl3
0x00002034,0x00000001,c2m_tx_link0_i2c1_ctrl4
0x00002035,0x00000000,c2m_tx_link0_i2c1_ctrl5
0x00002036,0x00000000,c2m_tx_link0_i2c1_ctrl6
0x00002037,0x00000000,c2m_tx_link0_i2c1_ctrl7
0x00002038,0x00000000,c2m_tx_link0_i2c1_status0
0x00002039,0x00000000,c2m_tx_link0_i2c1_status1
0x0000203a,0x00000000,c2m_tx_link0_i2c1_status2
0x00002040,0x00000003,c2m_tx_link0_i2c2_ctrl0
0x00002041,0x0000003c,c2m_tx_link0_i2c2_ctrl1
0x00002042,0x0000001a,c2m_tx_link0_i2c2_ctrl2
0x00002043,0x000000a0,c2m_tx_link0_i2c2_ctrl3
0x00002044,0x00000001,c2m_tx_link0_i2c2_ctrl4
0x00002045,0x00000000,c2m_tx_link0_i2c2_ctrl5
0x00002046,0x00000000,c2m_tx_link0_i2c2_ctrl6
0x00002047,0x00000000,c2m_tx_link0_i2c2_ctrl7
0x00002048,0x00000000,c2m_tx_link0_i2c2_status0
0x00002049,0x00000000,c2m_tx_link0_i2c2_status1
0x0000204a,0x00000000,c2m_tx_link0_i2c2_status2
0x00002050,0x00000003,c2m_tx_link0_i2c_ack_ctrl0
0x00002051,0x00000000,c2m_tx_link0_i2c_ack_ctrl1
0x00002052,0x00000000,c2m_tx_link0_i2c_ack_ctrl2
0x00002053,0x00000000,c2m_tx_link0_i2c_ack_ctrl3
0x00002054,0x00000000,c2m_tx_link0_i2c_ack_status0
0x00002060,0x00000003,c2m_tx_link0_uart0_ctrl0
0x00002061,0x0000003c,c2m_tx_link0_uart0_ctrl1
0x00002062,0x00000000,c2m_tx_link0_uart0_ctrl2
0x00002063,0x000000a0,c2m_tx_link0_uart0_ctrl3
0x00002064,0x00000001,c2m_tx_link0_uart0_ctrl4
0x00002065,0x00000000,c2m_tx_link0_uart0_ctrl5
0x00002066,0x00000000,c2m_tx_link0_uart0_ctrl6
0x00002067,0x00000000,c2m_tx_link0_uart0_ctrl7
0x00002068,0x00000000,c2m_tx_link0_uart0_status0
0x00002069,0x00000000,c2m_tx_link0_uart0_status1
0x0000206a,0x00000000,c2m_tx_link0_uart0_status2
0x00002070,0x00000003,c2m_tx_link0_uart1_ctrl0
0x00002071,0x0000003c,c2m_tx_link0_uart1_ctrl1
0x00002072,0x00000005,c2m_tx_link0_uart1_ctrl2
0x00002073,0x000000a0,c2m_tx_link0_uart1_ctrl3
0x00002074,0x00000001,c2m_tx_link0_uart1_ctrl4
0x00002075,0x00000000,c2m_tx_link0_uart1_ctrl5
0x00002076,0x00000000,c2m_tx_link0_uart1_ctrl6
0x00002077,0x00000000,c2m_tx_link0_uart1_ctrl7
0x00002078,0x00000000,c2m_tx_link0_uart1_status0
0x00002079,0x00000000,c2m_tx_link0_uart1_status1
0x0000207a,0x00000000,c2m_tx_link0_uart1_status2
0x00002080,0x00000003,c2m_tx_link0_uart2_ctrl0
0x00002081,0x0000003c,c2m_tx_link0_uart2_ctrl1
0x00002082,0x0000000a,c2m_tx_link0_uart2_ctrl2
0x00002083,0x000000a0,c2m_tx_link0_uart2_ctrl3
0x00002084,0x00000001,c2m_tx_link0_uart2_ctrl4
0x00002085,0x00000000,c2m_tx_link0_uart2_ctrl5
0x00002086,0x00000000,c2m_tx_link0_uart2_ctrl6
0x00002087,0x00000000,c2m_tx_link0_uart2_ctrl7
0x00002088,0x00000000,c2m_tx_link0_uart2_status0
0x00002089,0x00000000,c2m_tx_link0_uart2_status1
0x0000208a,0x00000000,c2m_tx_link0_uart2_status2
0x00002090,0x00000003,c2m_tx_link0_uart_ack_ctrl0
0x00002091,0x00000000,c2m_tx_link0_uart_ack_ctrl1
0x00002092,0x00000000,c2m_tx_link0_uart_ack_ctrl2
0x00002093,0x00000000,c2m_tx_link0_uart_ack_ctrl3
0x00002094,0x00000000,c2m_tx_link0_uart_ack_status0
0x000020a0,0x00000003,c2m_tx_link0_spi_ctrl0
0x000020a1,0x0000003c,c2m_tx_link0_spi_ctrl1
0x000020a2,0x00000000,c2m_tx_link0_spi_ctrl2
0x000020a3,0x000000a0,c2m_tx_link0_spi_ctrl3
0x000020a4,0x00000001,c2m_tx_link0_spi_ctrl4
0x000020a5,0x00000000,c2m_tx_link0_spi_ctrl5
0x000020a6,0x00000000,c2m_tx_link0_spi_ctrl6
0x000020a7,0x00000000,c2m_tx_link0_spi_ctrl7
0x000020a8,0x00000000,c2m_tx_link0_spi_status0
0x000020a9,0x00000000,c2m_tx_link0_spi_status1
0x000020aa,0x00000000,c2m_tx_link0_spi_status2
0x000020b0,0x00000003,c2m_tx_link0_spi_ack_ctrl0
0x000020b1,0x00000000,c2m_tx_link0_spi_ack_ctrl1
0x000020b2,0x00000000,c2m_tx_link0_spi_ack_ctrl2
0x000020b3,0x00000000,c2m_tx_link0_spi_ack_ctrl3
0x000020b4,0x00000000,c2m_tx_link0_spi_ack_status0
0x000020c0,0x00000003,c2m_tx_link0_gpio_ctrl0
0x000020c1,0x0000003c,c2m_tx_link0_gpio_ctrl1
0x000020c2,0x00000000,c2m_tx_link0_gpio_ctrl2
0x000020c3,0x000000a0,c2m_tx_link0_gpio_ctrl3
0x000020c4,0x00000001,c2m_tx_link0_gpio_ctrl4
0x000020c5,0x00000000,c2m_tx_link0_gpio_ctrl5
0x000020c6,0x00000000,c2m_tx_link0_gpio_ctrl6
0x000020c7,0x00000000,c2m_tx_link0_gpio_ctrl7
0x000020c8,0x00000000,c2m_tx_link0_gpio_status0
0x000020c9,0x00000000,c2m_tx_link0_gpio_status1
0x000020ca,0x00000000,c2m_tx_link0_gpio_status2
0x000020d0,0x00000003,c2m_tx_link0_gpio_ack_ctrl0
0x000020d1,0x00000000,c2m_tx_link0_gpio_ack_ctrl1
0x000020d2,0x00000000,c2m_tx_link0_gpio_ack_ctrl2
0x000020d3,0x00000000,c2m_tx_link0_gpio_ack_ctrl3
0x000020d4,0x00000000,c2m_tx_link0_gpio_ack_status0
0x000020e0,0x00000003,c2m_tx_link0_fs_ctrl0
0x000020e1,0x0000003c,c2m_tx_link0_fs_ctrl1
0x000020e2,0x00000004,c2m_tx_link0_fs_ctrl2
0x000020e3,0x000000a0,c2m_tx_link0_fs_ctrl3
0x000020e4,0x00000001,c2m_tx_link0_fs_ctrl4
0x000020e5,0x00000000,c2m_tx_link0_fs_ctrl5
0x000020e6,0x00000000,c2m_tx_link0_fs_ctrl6
0x000020e7,0x00000000,c2m_tx_link0_fs_ctrl7
0x000020e8,0x00000000,c2m_tx_link0_fs_status0
0x000020e9,0x00000000,c2m_tx_link0_fs_status1
0x000020ea,0x00000000,c2m_tx_link0_fs_status2
0x00002100,0x00000000,c2m_rx_link0_phy_phy_ctrl0
0x00002101,0x00000008,c2m_rx_link0_phy_sync_ctrl0
0x00002102,0x00000000,c2m_rx_link0_phy_sync_ctrl1
0x00002103,0x00000020,c2m_rx_link0_phy_sync_ctrl2
0x00002104,0x00000001,c2m_rx_link0_phy_sync_ctrl3
0x00002105,0x00000000,c2m_rx_link0_phy_sync_ctrl4
0x00002106,0x00000002,c2m_rx_link0_phy_fec_ctrl0
0x00002107,0x00000004,c2m_rx_link0_phy_fec_ctrl1
0x00002108,0x00000001,c2m_rx_link0_phy_fec_ctrl2
0x00002109,0x00000010,c2m_rx_link0_phy_idle_ctrl0
0x0000210a,0x00000003,c2m_rx_link0_phy_dec_ctrl0
0x0000210b,0x00000040,c2m_rx_link0_phy_dec_ctrl1
0x0000210c,0x00000001,c2m_rx_link0_phy_dec_ctrl2
0x0000210d,0x00000001,c2m_rx_link0_phy_dec_ctrl3
0x0000210e,0x000000a0,c2m_rx_link0_phy_fault_ctrl0
0x0000210f,0x00000064,c2m_rx_link0_phy_fault_ctrl1
0x00002110,0x000000fe,c2m_rx_link0_phy_ana_ctrl0
0x00002111,0x00000000,c2m_rx_link0_phy_ana_ctrl1
0x00002112,0x00000000,c2m_rx_link0_phy_ana_ctrl2
0x00002118,0x0000007d,c2m_rx_link0_phy_ana_ctrl8
0x00002119,0x00000094,c2m_rx_link0_phy_ana_ctrl9
0x0000211a,0x00000011,c2m_rx_link0_phy_ana_ctrl10
0x0000211b,0x00000090,c2m_rx_link0_phy_ana_ctrl11
0x0000211c,0x000000fa,c2m_rx_link0_phy_ana_ctrl12
0x0000211d,0x00000080,c2m_rx_link0_phy_ana_ctrl13
0x0000211e,0x00000080,c2m_rx_link0_phy_ana_ctrl14
0x00002120,0x00000000,c2m_rx_link0_phy_ana_ctrl16
0x00002121,0x00000000,c2m_rx_link0_phy_ana_ctrl17
0x00002122,0x00000003,c2m_rx_link0_phy_ana_ctrl18
0x00002123,0x00000000,c2m_rx_link0_phy_err_cnt_clr
0x00002124,0x00000000,c2m_rx_link0_phy_err_cnt_auto_clr_en
0x00002125,0x00000001,c2m_rx_link0_phy_dec_rd_err_thrd
0x00002126,0x00000000,c2m_rx_link0_phy_dec_rd_err_cnt
0x00002127,0x00000001,c2m_rx_link0_phy_dec_code_err_thrd
0x00002128,0x00000000,c2m_rx_link0_phy_dec_code_err_cnt
0x00002129,0x00000001,c2m_rx_link0_phy_dec_err_thrd
0x0000212a,0x00000000,c2m_rx_link0_phy_dec_err_cnt
0x0000212b,0x00000001,c2m_rx_link0_phy_sync_word_err_thrd
0x0000212c,0x00000000,c2m_rx_link0_phy_sync_word_err_cnt
0x0000212d,0x00000001,c2m_rx_link0_phy_fec_correct_err_thrd
0x0000212e,0x00000000,c2m_rx_link0_phy_fec_correct_err_cnt
0x0000212f,0x00000001,c2m_rx_link0_phy_fec_uncorrect_err_thrd
0x00002130,0x00000000,c2m_rx_link0_phy_fec_uncorrect_err_cnt
0x00002140,0x00000007,c2m_rx_link0_pp_i2c_ctrl0
0x00002141,0x000000ff,c2m_rx_link0_pp_i2c_ctrl1
0x00002142,0x00000003,c2m_rx_link0_pp_uart_ctrl0
0x00002143,0x000000ff,c2m_rx_link0_pp_uart_ctrl1
0x00002144,0x00000003,c2m_rx_link0_pp_spi_ctrl0
0x00002145,0x00000003,c2m_rx_link0_pp_gpio_ctrl0
0x00002146,0x00000000,c2m_rx_link0_pp_timeout_ctrl0
0x00002147,0x000000ff,c2m_rx_link0_pp_timeout_ctrl1
0x00002148,0x000000ff,c2m_rx_link0_pp_timeout_ctrl2
0x00002149,0x00000000,c2m_rx_link0_pp_video_ctrl0
0x0000214a,0x000000ff,c2m_rx_link0_pp_video_ctrl1
0x0000214b,0x000000ff,c2m_rx_link0_pp_video_ctrl2
0x0000214c,0x00000003,c2m_rx_link0_pp_video_ctrl3
0x0000214d,0x00000043,c2m_rx_link0_pp_video_ctrl4[0]
0x0000214e,0x00000034,c2m_rx_link0_pp_video_ctrl4[1]
0x0000214f,0x00000097,c2m_rx_link0_pp_video_ctrl4[2]
0x00002150,0x00000003,c2m_rx_link0_pp_video_ctrl4[3]
0x00002151,0x00000035,c2m_rx_link0_pp_video_ctrl5
0x00002152,0x00000000,c2m_rx_link0_pp_video_status0
0x00002153,0x00000000,c2m_rx_link0_pp_video_status1
0x00002154,0x00000000,c2m_rx_link0_pp_video_status2
0x00002155,0x00000000,c2m_rx_link0_pp_video_status3
0x00002156,0x00000000,c2m_rx_link0_pp_video_status4
0x00002200,0x00000000,c2m_tx_link1_phy_phy_ctrl0
0x00002201,0x00000080,c2m_tx_link1_phy_sync_ctrl0
0x00002202,0x00000000,c2m_tx_link1_phy_sync_ctrl1
0x00002203,0x00000000,c2m_tx_link1_phy_sync_ctrl2
0x00002204,0x00000000,c2m_tx_link1_phy_comp_ctrl0
0x00002205,0x00000000,c2m_tx_link1_phy_comp_ctrl1
0x00002206,0x00000000,c2m_tx_link1_phy_comp_ctrl2
0x00002207,0x00000001,c2m_tx_link1_phy_fec_ctrl0
0x00002208,0x00000002,c2m_tx_link1_phy_psch_ctrl0
0x00002209,0x00000000,c2m_tx_link1_phy_ipg_ctrl0
0x0000220a,0x00000040,c2m_tx_link1_phy_ipg_ctrl1
0x0000220b,0x0000007f,c2m_tx_link1_phy_idle_ctrl0
0x0000220c,0x0000000c,c2m_tx_link1_phy_errinj_ctrl0
0x0000220d,0x00000000,c2m_tx_link1_phy_errinj_ctrl1
0x0000220e,0x00000081,c2m_tx_link1_phy_ana_ctrl0
0x0000220f,0x00000000,c2m_tx_link1_phy_ana_ctrl1
0x00002210,0x00000000,c2m_tx_link1_phy_ana_ctrl2
0x00002211,0x00000001,c2m_tx_link1_phy_ana_ctrl3
0x00002212,0x0000007f,c2m_tx_link1_phy_ana_ctrl4
0x00002213,0x00000001,c2m_tx_link1_phy_ana_ctrl5
0x00002214,0x00000064,c2m_tx_link1_phy_ana_ctrl6
0x00002215,0x00000019,c2m_tx_link1_phy_ana_ctrl7
0x00002216,0x0000005a,c2m_tx_link1_phy_ana_ctrl8
0x00002217,0x0000003c,c2m_tx_link1_phy_ana_ctrl9
0x000022f0,0x00000000,c2m_tx_link1_psch_ctrl0
0x000022f1,0x00000000,c2m_tx_link1_psch_ctrl1
0x000022f2,0x00000001,c2m_tx_link1_psch_ctrl2
0x000022f3,0x00000001,c2m_tx_link1_psch_ctrl3
0x000022f4,0x00000000,c2m_tx_link1_psch_status0
0x00002220,0x00000003,c2m_tx_link1_i2c0_ctrl0
0x00002221,0x0000003c,c2m_tx_link1_i2c0_ctrl1
0x00002222,0x00000010,c2m_tx_link1_i2c0_ctrl2
0x00002223,0x000000a0,c2m_tx_link1_i2c0_ctrl3
0x00002224,0x00000001,c2m_tx_link1_i2c0_ctrl4
0x00002225,0x00000000,c2m_tx_link1_i2c0_ctrl5
0x00002226,0x00000000,c2m_tx_link1_i2c0_ctrl6
0x00002227,0x00000000,c2m_tx_link1_i2c0_ctrl7
0x00002228,0x00000000,c2m_tx_link1_i2c0_status0
0x00002229,0x00000000,c2m_tx_link1_i2c0_status1
0x0000222a,0x00000000,c2m_tx_link1_i2c0_status2
0x00002230,0x00000003,c2m_tx_link1_i2c1_ctrl0
0x00002231,0x0000003c,c2m_tx_link1_i2c1_ctrl1
0x00002232,0x00000015,c2m_tx_link1_i2c1_ctrl2
0x00002233,0x000000a0,c2m_tx_link1_i2c1_ctrl3
0x00002234,0x00000001,c2m_tx_link1_i2c1_ctrl4
0x00002235,0x00000000,c2m_tx_link1_i2c1_ctrl5
0x00002236,0x00000000,c2m_tx_link1_i2c1_ctrl6
0x00002237,0x00000000,c2m_tx_link1_i2c1_ctrl7
0x00002238,0x00000000,c2m_tx_link1_i2c1_status0
0x00002239,0x00000000,c2m_tx_link1_i2c1_status1
0x0000223a,0x00000000,c2m_tx_link1_i2c1_status2
0x00002240,0x00000003,c2m_tx_link1_i2c2_ctrl0
0x00002241,0x0000003c,c2m_tx_link1_i2c2_ctrl1
0x00002242,0x0000001a,c2m_tx_link1_i2c2_ctrl2
0x00002243,0x000000a0,c2m_tx_link1_i2c2_ctrl3
0x00002244,0x00000001,c2m_tx_link1_i2c2_ctrl4
0x00002245,0x00000000,c2m_tx_link1_i2c2_ctrl5
0x00002246,0x00000000,c2m_tx_link1_i2c2_ctrl6
0x00002247,0x00000000,c2m_tx_link1_i2c2_ctrl7
0x00002248,0x00000000,c2m_tx_link1_i2c2_status0
0x00002249,0x00000000,c2m_tx_link1_i2c2_status1
0x0000224a,0x00000000,c2m_tx_link1_i2c2_status2
0x00002250,0x00000003,c2m_tx_link1_i2c_ack_ctrl0
0x00002251,0x00000000,c2m_tx_link1_i2c_ack_ctrl1
0x00002252,0x00000000,c2m_tx_link1_i2c_ack_ctrl2
0x00002253,0x00000000,c2m_tx_link1_i2c_ack_ctrl3
0x00002254,0x00000000,c2m_tx_link1_i2c_ack_status0
0x00002260,0x00000003,c2m_tx_link1_uart0_ctrl0
0x00002261,0x0000003c,c2m_tx_link1_uart0_ctrl1
0x00002262,0x00000000,c2m_tx_link1_uart0_ctrl2
0x00002263,0x000000a0,c2m_tx_link1_uart0_ctrl3
0x00002264,0x00000001,c2m_tx_link1_uart0_ctrl4
0x00002265,0x00000000,c2m_tx_link1_uart0_ctrl5
0x00002266,0x00000000,c2m_tx_link1_uart0_ctrl6
0x00002267,0x00000000,c2m_tx_link1_uart0_ctrl7
0x00002268,0x00000000,c2m_tx_link1_uart0_status0
0x00002269,0x00000000,c2m_tx_link1_uart0_status1
0x0000226a,0x00000000,c2m_tx_link1_uart0_status2
0x00002270,0x00000003,c2m_tx_link1_uart1_ctrl0
0x00002271,0x0000003c,c2m_tx_link1_uart1_ctrl1
0x00002272,0x00000005,c2m_tx_link1_uart1_ctrl2
0x00002273,0x000000a0,c2m_tx_link1_uart1_ctrl3
0x00002274,0x00000001,c2m_tx_link1_uart1_ctrl4
0x00002275,0x00000000,c2m_tx_link1_uart1_ctrl5
0x00002276,0x00000000,c2m_tx_link1_uart1_ctrl6
0x00002277,0x00000000,c2m_tx_link1_uart1_ctrl7
0x00002278,0x00000000,c2m_tx_link1_uart1_status0
0x00002279,0x00000000,c2m_tx_link1_uart1_status1
0x0000227a,0x00000000,c2m_tx_link1_uart1_status2
0x00002280,0x00000003,c2m_tx_link1_uart2_ctrl0
0x00002281,0x0000003c,c2m_tx_link1_uart2_ctrl1
0x00002282,0x0000000a,c2m_tx_link1_uart2_ctrl2
0x00002283,0x000000a0,c2m_tx_link1_uart2_ctrl3
0x00002284,0x00000001,c2m_tx_link1_uart2_ctrl4
0x00002285,0x00000000,c2m_tx_link1_uart2_ctrl5
0x00002286,0x00000000,c2m_tx_link1_uart2_ctrl6
0x00002287,0x00000000,c2m_tx_link1_uart2_ctrl7
0x00002288,0x00000000,c2m_tx_link1_uart2_status0
0x00002289,0x00000000,c2m_tx_link1_uart2_status1
0x0000228a,0x00000000,c2m_tx_link1_uart2_status2
0x00002290,0x00000003,c2m_tx_link1_uart_ack_ctrl0
0x00002291,0x00000000,c2m_tx_link1_uart_ack_ctrl1
0x00002292,0x00000000,c2m_tx_link1_uart_ack_ctrl2
0x00002293,0x00000000,c2m_tx_link1_uart_ack_ctrl3
0x00002294,0x00000000,c2m_tx_link1_uart_ack_status0
0x000022a0,0x00000003,c2m_tx_link1_spi_ctrl0
0x000022a1,0x0000003c,c2m_tx_link1_spi_ctrl1
0x000022a2,0x00000000,c2m_tx_link1_spi_ctrl2
0x000022a3,0x000000a0,c2m_tx_link1_spi_ctrl3
0x000022a4,0x00000001,c2m_tx_link1_spi_ctrl4
0x000022a5,0x00000000,c2m_tx_link1_spi_ctrl5
0x000022a6,0x00000000,c2m_tx_link1_spi_ctrl6
0x000022a7,0x00000000,c2m_tx_link1_spi_ctrl7
0x000022a8,0x00000000,c2m_tx_link1_spi_status0
0x000022a9,0x00000000,c2m_tx_link1_spi_status1
0x000022aa,0x00000000,c2m_tx_link1_spi_status2
0x000022b0,0x00000003,c2m_tx_link1_spi_ack_ctrl0
0x000022b1,0x00000000,c2m_tx_link1_spi_ack_ctrl1
0x000022b2,0x00000000,c2m_tx_link1_spi_ack_ctrl2
0x000022b3,0x00000000,c2m_tx_link1_spi_ack_ctrl3
0x000022b4,0x00000000,c2m_tx_link1_spi_ack_status0
0x000022c0,0x00000003,c2m_tx_link1_gpio_ctrl0
0x000022c1,0x0000003c,c2m_tx_link1_gpio_ctrl1
0x000022c2,0x00000000,c2m_tx_link1_gpio_ctrl2
0x000022c3,0x000000a0,c2m_tx_link1_gpio_ctrl3
0x000022c4,0x00000001,c2m_tx_link1_gpio_ctrl4
0x000022c5,0x00000000,c2m_tx_link1_gpio_ctrl5
0x000022c6,0x00000000,c2m_tx_link1_gpio_ctrl6
0x000022c7,0x00000000,c2m_tx_link1_gpio_ctrl7
0x000022c8,0x00000000,c2m_tx_link1_gpio_status0
0x000022c9,0x00000000,c2m_tx_link1_gpio_status1
0x000022ca,0x00000000,c2m_tx_link1_gpio_status2
0x000022d0,0x00000003,c2m_tx_link1_gpio_ack_ctrl0
0x000022d1,0x00000000,c2m_tx_link1_gpio_ack_ctrl1
0x000022d2,0x00000000,c2m_tx_link1_gpio_ack_ctrl2
0x000022d3,0x00000000,c2m_tx_link1_gpio_ack_ctrl3
0x000022d4,0x00000000,c2m_tx_link1_gpio_ack_status0
0x000022e0,0x00000003,c2m_tx_link1_fs_ctrl0
0x000022e1,0x0000003c,c2m_tx_link1_fs_ctrl1
0x000022e2,0x00000004,c2m_tx_link1_fs_ctrl2
0x000022e3,0x000000a0,c2m_tx_link1_fs_ctrl3
0x000022e4,0x00000001,c2m_tx_link1_fs_ctrl4
0x000022e5,0x00000000,c2m_tx_link1_fs_ctrl5
0x000022e6,0x00000000,c2m_tx_link1_fs_ctrl6
0x000022e7,0x00000000,c2m_tx_link1_fs_ctrl7
0x000022e8,0x00000000,c2m_tx_link1_fs_status0
0x000022e9,0x00000000,c2m_tx_link1_fs_status1
0x000022ea,0x00000000,c2m_tx_link1_fs_status2
0x00002300,0x00000000,c2m_rx_link1_phy_phy_ctrl0
0x00002301,0x00000008,c2m_rx_link1_phy_sync_ctrl0
0x00002302,0x00000000,c2m_rx_link1_phy_sync_ctrl1
0x00002303,0x00000020,c2m_rx_link1_phy_sync_ctrl2
0x00002304,0x00000001,c2m_rx_link1_phy_sync_ctrl3
0x00002305,0x00000000,c2m_rx_link1_phy_sync_ctrl4
0x00002306,0x00000002,c2m_rx_link1_phy_fec_ctrl0
0x00002307,0x00000004,c2m_rx_link1_phy_fec_ctrl1
0x00002308,0x00000001,c2m_rx_link1_phy_fec_ctrl2
0x00002309,0x00000010,c2m_rx_link1_phy_idle_ctrl0
0x0000230a,0x00000003,c2m_rx_link1_phy_dec_ctrl0
0x0000230b,0x00000040,c2m_rx_link1_phy_dec_ctrl1
0x0000230c,0x00000001,c2m_rx_link1_phy_dec_ctrl2
0x0000230d,0x00000001,c2m_rx_link1_phy_dec_ctrl3
0x0000230e,0x000000a0,c2m_rx_link1_phy_fault_ctrl0
0x0000230f,0x00000064,c2m_rx_link1_phy_fault_ctrl1
0x00002310,0x000000fe,c2m_rx_link1_phy_ana_ctrl0
0x00002311,0x00000000,c2m_rx_link1_phy_ana_ctrl1
0x00002312,0x00000000,c2m_rx_link1_phy_ana_ctrl2
0x00002318,0x0000007d,c2m_rx_link1_phy_ana_ctrl8
0x00002319,0x00000094,c2m_rx_link1_phy_ana_ctrl9
0x0000231a,0x00000011,c2m_rx_link1_phy_ana_ctrl10
0x0000231b,0x00000090,c2m_rx_link1_phy_ana_ctrl11
0x0000231c,0x000000fa,c2m_rx_link1_phy_ana_ctrl12
0x0000231d,0x00000080,c2m_rx_link1_phy_ana_ctrl13
0x0000231e,0x00000080,c2m_rx_link1_phy_ana_ctrl14
0x00002320,0x00000000,c2m_rx_link1_phy_ana_ctrl16
0x00002321,0x00000000,c2m_rx_link1_phy_ana_ctrl17
0x00002322,0x00000003,c2m_rx_link1_phy_ana_ctrl18
0x00002323,0x00000000,c2m_rx_link1_phy_err_cnt_clr
0x00002324,0x00000000,c2m_rx_link1_phy_err_cnt_auto_clr_en
0x00002325,0x00000001,c2m_rx_link1_phy_dec_rd_err_thrd
0x00002326,0x00000000,c2m_rx_link1_phy_dec_rd_err_cnt
0x00002327,0x00000001,c2m_rx_link1_phy_dec_code_err_thrd
0x00002328,0x00000000,c2m_rx_link1_phy_dec_code_err_cnt
0x00002329,0x00000001,c2m_rx_link1_phy_dec_err_thrd
0x0000232a,0x00000000,c2m_rx_link1_phy_dec_err_cnt
0x0000232b,0x00000001,c2m_rx_link1_phy_sync_word_err_thrd
0x0000232c,0x00000000,c2m_rx_link1_phy_sync_word_err_cnt
0x0000232d,0x00000001,c2m_rx_link1_phy_fec_correct_err_thrd
0x0000232e,0x00000000,c2m_rx_link1_phy_fec_correct_err_cnt
0x0000232f,0x00000001,c2m_rx_link1_phy_fec_uncorrect_err_thrd
0x00002330,0x00000000,c2m_rx_link1_phy_fec_uncorrect_err_cnt
0x00002340,0x00000007,c2m_rx_link1_pp_i2c_ctrl0
0x00002341,0x000000ff,c2m_rx_link1_pp_i2c_ctrl1
0x00002342,0x00000003,c2m_rx_link1_pp_uart_ctrl0
0x00002343,0x000000ff,c2m_rx_link1_pp_uart_ctrl1
0x00002344,0x00000003,c2m_rx_link1_pp_spi_ctrl0
0x00002345,0x00000003,c2m_rx_link1_pp_gpio_ctrl0
0x00002346,0x00000000,c2m_rx_link1_pp_timeout_ctrl0
0x00002347,0x000000ff,c2m_rx_link1_pp_timeout_ctrl1
0x00002348,0x000000ff,c2m_rx_link1_pp_timeout_ctrl2
0x00002349,0x00000000,c2m_rx_link1_pp_video_ctrl0
0x0000234a,0x000000ff,c2m_rx_link1_pp_video_ctrl1
0x0000234b,0x000000ff,c2m_rx_link1_pp_video_ctrl2
0x0000234c,0x00000003,c2m_rx_link1_pp_video_ctrl3
0x0000234d,0x00000043,c2m_rx_link1_pp_video_ctrl4[0]
0x0000234e,0x00000034,c2m_rx_link1_pp_video_ctrl4[1]
0x0000234f,0x00000097,c2m_rx_link1_pp_video_ctrl4[2]
0x00002350,0x00000003,c2m_rx_link1_pp_video_ctrl4[3]
0x00002351,0x00000035,c2m_rx_link1_pp_video_ctrl5
0x00002352,0x00000000,c2m_rx_link1_pp_video_status0
0x00002353,0x00000000,c2m_rx_link1_pp_video_status1
0x00002354,0x00000000,c2m_rx_link1_pp_video_status2
0x00002355,0x00000000,c2m_rx_link1_pp_video_status3
0x00002356,0x00000000,c2m_rx_link1_pp_video_status4
0x00002400,0x00000000,c2m_tx_link2_phy_phy_ctrl0
0x00002401,0x00000080,c2m_tx_link2_phy_sync_ctrl0
0x00002402,0x00000000,c2m_tx_link2_phy_sync_ctrl1
0x00002403,0x00000000,c2m_tx_link2_phy_sync_ctrl2
0x00002404,0x00000000,c2m_tx_link2_phy_comp_ctrl0
0x00002405,0x00000000,c2m_tx_link2_phy_comp_ctrl1
0x00002406,0x00000000,c2m_tx_link2_phy_comp_ctrl2
0x00002407,0x00000001,c2m_tx_link2_phy_fec_ctrl0
0x00002408,0x00000002,c2m_tx_link2_phy_psch_ctrl0
0x00002409,0x00000000,c2m_tx_link2_phy_ipg_ctrl0
0x0000240a,0x00000040,c2m_tx_link2_phy_ipg_ctrl1
0x0000240b,0x0000007f,c2m_tx_link2_phy_idle_ctrl0
0x0000240c,0x0000000c,c2m_tx_link2_phy_errinj_ctrl0
0x0000240d,0x00000000,c2m_tx_link2_phy_errinj_ctrl1
0x0000240e,0x00000081,c2m_tx_link2_phy_ana_ctrl0
0x0000240f,0x00000000,c2m_tx_link2_phy_ana_ctrl1
0x00002410,0x00000000,c2m_tx_link2_phy_ana_ctrl2
0x00002411,0x00000001,c2m_tx_link2_phy_ana_ctrl3
0x00002412,0x0000007f,c2m_tx_link2_phy_ana_ctrl4
0x00002413,0x00000001,c2m_tx_link2_phy_ana_ctrl5
0x00002414,0x00000064,c2m_tx_link2_phy_ana_ctrl6
0x00002415,0x00000019,c2m_tx_link2_phy_ana_ctrl7
0x00002416,0x0000005a,c2m_tx_link2_phy_ana_ctrl8
0x00002417,0x0000003c,c2m_tx_link2_phy_ana_ctrl9
0x000024f0,0x00000000,c2m_tx_link2_psch_ctrl0
0x000024f1,0x00000000,c2m_tx_link2_psch_ctrl1
0x000024f2,0x00000001,c2m_tx_link2_psch_ctrl2
0x000024f3,0x00000001,c2m_tx_link2_psch_ctrl3
0x000024f4,0x00000000,c2m_tx_link2_psch_status0
0x00002420,0x00000003,c2m_tx_link2_i2c0_ctrl0
0x00002421,0x0000003c,c2m_tx_link2_i2c0_ctrl1
0x00002422,0x00000010,c2m_tx_link2_i2c0_ctrl2
0x00002423,0x000000a0,c2m_tx_link2_i2c0_ctrl3
0x00002424,0x00000001,c2m_tx_link2_i2c0_ctrl4
0x00002425,0x00000000,c2m_tx_link2_i2c0_ctrl5
0x00002426,0x00000000,c2m_tx_link2_i2c0_ctrl6
0x00002427,0x00000000,c2m_tx_link2_i2c0_ctrl7
0x00002428,0x00000000,c2m_tx_link2_i2c0_status0
0x00002429,0x00000000,c2m_tx_link2_i2c0_status1
0x0000242a,0x00000000,c2m_tx_link2_i2c0_status2
0x00002430,0x00000003,c2m_tx_link2_i2c1_ctrl0
0x00002431,0x0000003c,c2m_tx_link2_i2c1_ctrl1
0x00002432,0x00000015,c2m_tx_link2_i2c1_ctrl2
0x00002433,0x000000a0,c2m_tx_link2_i2c1_ctrl3
0x00002434,0x00000001,c2m_tx_link2_i2c1_ctrl4
0x00002435,0x00000000,c2m_tx_link2_i2c1_ctrl5
0x00002436,0x00000000,c2m_tx_link2_i2c1_ctrl6
0x00002437,0x00000000,c2m_tx_link2_i2c1_ctrl7
0x00002438,0x00000000,c2m_tx_link2_i2c1_status0
0x00002439,0x00000000,c2m_tx_link2_i2c1_status1
0x0000243a,0x00000000,c2m_tx_link2_i2c1_status2
0x00002440,0x00000003,c2m_tx_link2_i2c2_ctrl0
0x00002441,0x0000003c,c2m_tx_link2_i2c2_ctrl1
0x00002442,0x0000001a,c2m_tx_link2_i2c2_ctrl2
0x00002443,0x000000a0,c2m_tx_link2_i2c2_ctrl3
0x00002444,0x00000001,c2m_tx_link2_i2c2_ctrl4
0x00002445,0x00000000,c2m_tx_link2_i2c2_ctrl5
0x00002446,0x00000000,c2m_tx_link2_i2c2_ctrl6
0x00002447,0x00000000,c2m_tx_link2_i2c2_ctrl7
0x00002448,0x00000000,c2m_tx_link2_i2c2_status0
0x00002449,0x00000000,c2m_tx_link2_i2c2_status1
0x0000244a,0x00000000,c2m_tx_link2_i2c2_status2
0x00002450,0x00000003,c2m_tx_link2_i2c_ack_ctrl0
0x00002451,0x00000000,c2m_tx_link2_i2c_ack_ctrl1
0x00002452,0x00000000,c2m_tx_link2_i2c_ack_ctrl2
0x00002453,0x00000000,c2m_tx_link2_i2c_ack_ctrl3
0x00002454,0x00000000,c2m_tx_link2_i2c_ack_status0
0x00002460,0x00000003,c2m_tx_link2_uart0_ctrl0
0x00002461,0x0000003c,c2m_tx_link2_uart0_ctrl1
0x00002462,0x00000000,c2m_tx_link2_uart0_ctrl2
0x00002463,0x000000a0,c2m_tx_link2_uart0_ctrl3
0x00002464,0x00000001,c2m_tx_link2_uart0_ctrl4
0x00002465,0x00000000,c2m_tx_link2_uart0_ctrl5
0x00002466,0x00000000,c2m_tx_link2_uart0_ctrl6
0x00002467,0x00000000,c2m_tx_link2_uart0_ctrl7
0x00002468,0x00000000,c2m_tx_link2_uart0_status0
0x00002469,0x00000000,c2m_tx_link2_uart0_status1
0x0000246a,0x00000000,c2m_tx_link2_uart0_status2
0x00002470,0x00000003,c2m_tx_link2_uart1_ctrl0
0x00002471,0x0000003c,c2m_tx_link2_uart1_ctrl1
0x00002472,0x00000005,c2m_tx_link2_uart1_ctrl2
0x00002473,0x000000a0,c2m_tx_link2_uart1_ctrl3
0x00002474,0x00000001,c2m_tx_link2_uart1_ctrl4
0x00002475,0x00000000,c2m_tx_link2_uart1_ctrl5
0x00002476,0x00000000,c2m_tx_link2_uart1_ctrl6
0x00002477,0x00000000,c2m_tx_link2_uart1_ctrl7
0x00002478,0x00000000,c2m_tx_link2_uart1_status0
0x00002479,0x00000000,c2m_tx_link2_uart1_status1
0x0000247a,0x00000000,c2m_tx_link2_uart1_status2
0x00002480,0x00000003,c2m_tx_link2_uart2_ctrl0
0x00002481,0x0000003c,c2m_tx_link2_uart2_ctrl1
0x00002482,0x0000000a,c2m_tx_link2_uart2_ctrl2
0x00002483,0x000000a0,c2m_tx_link2_uart2_ctrl3
0x00002484,0x00000001,c2m_tx_link2_uart2_ctrl4
0x00002485,0x00000000,c2m_tx_link2_uart2_ctrl5
0x00002486,0x00000000,c2m_tx_link2_uart2_ctrl6
0x00002487,0x00000000,c2m_tx_link2_uart2_ctrl7
0x00002488,0x00000000,c2m_tx_link2_uart2_status0
0x00002489,0x00000000,c2m_tx_link2_uart2_status1
0x0000248a,0x00000000,c2m_tx_link2_uart2_status2
0x00002490,0x00000003,c2m_tx_link2_uart_ack_ctrl0
0x00002491,0x00000000,c2m_tx_link2_uart_ack_ctrl1
0x00002492,0x00000000,c2m_tx_link2_uart_ack_ctrl2
0x00002493,0x00000000,c2m_tx_link2_uart_ack_ctrl3
0x00002494,0x00000000,c2m_tx_link2_uart_ack_status0
0x000024a0,0x00000003,c2m_tx_link2_spi_ctrl0
0x000024a1,0x0000003c,c2m_tx_link2_spi_ctrl1
0x000024a2,0x00000000,c2m_tx_link2_spi_ctrl2
0x000024a3,0x000000a0,c2m_tx_link2_spi_ctrl3
0x000024a4,0x00000001,c2m_tx_link2_spi_ctrl4
0x000024a5,0x00000000,c2m_tx_link2_spi_ctrl5
0x000024a6,0x00000000,c2m_tx_link2_spi_ctrl6
0x000024a7,0x00000000,c2m_tx_link2_spi_ctrl7
0x000024a8,0x00000000,c2m_tx_link2_spi_status0
0x000024a9,0x00000000,c2m_tx_link2_spi_status1
0x000024aa,0x00000000,c2m_tx_link2_spi_status2
0x000024b0,0x00000003,c2m_tx_link2_spi_ack_ctrl0
0x000024b1,0x00000000,c2m_tx_link2_spi_ack_ctrl1
0x000024b2,0x00000000,c2m_tx_link2_spi_ack_ctrl2
0x000024b3,0x00000000,c2m_tx_link2_spi_ack_ctrl3
0x000024b4,0x00000000,c2m_tx_link2_spi_ack_status0
0x000024c0,0x00000003,c2m_tx_link2_gpio_ctrl0
0x000024c1,0x0000003c,c2m_tx_link2_gpio_ctrl1
0x000024c2,0x00000000,c2m_tx_link2_gpio_ctrl2
0x000024c3,0x000000a0,c2m_tx_link2_gpio_ctrl3
0x000024c4,0x00000001,c2m_tx_link2_gpio_ctrl4
0x000024c5,0x00000000,c2m_tx_link2_gpio_ctrl5
0x000024c6,0x00000000,c2m_tx_link2_gpio_ctrl6
0x000024c7,0x00000000,c2m_tx_link2_gpio_ctrl7
0x000024c8,0x00000000,c2m_tx_link2_gpio_status0
0x000024c9,0x00000000,c2m_tx_link2_gpio_status1
0x000024ca,0x00000000,c2m_tx_link2_gpio_status2
0x000024d0,0x00000003,c2m_tx_link2_gpio_ack_ctrl0
0x000024d1,0x00000000,c2m_tx_link2_gpio_ack_ctrl1
0x000024d2,0x00000000,c2m_tx_link2_gpio_ack_ctrl2
0x000024d3,0x00000000,c2m_tx_link2_gpio_ack_ctrl3
0x000024d4,0x00000000,c2m_tx_link2_gpio_ack_status0
0x000024e0,0x00000003,c2m_tx_link2_fs_ctrl0
0x000024e1,0x0000003c,c2m_tx_link2_fs_ctrl1
0x000024e2,0x00000004,c2m_tx_link2_fs_ctrl2
0x000024e3,0x000000a0,c2m_tx_link2_fs_ctrl3
0x000024e4,0x00000001,c2m_tx_link2_fs_ctrl4
0x000024e5,0x00000000,c2m_tx_link2_fs_ctrl5
0x000024e6,0x00000000,c2m_tx_link2_fs_ctrl6
0x000024e7,0x00000000,c2m_tx_link2_fs_ctrl7
0x000024e8,0x00000000,c2m_tx_link2_fs_status0
0x000024e9,0x00000000,c2m_tx_link2_fs_status1
0x000024ea,0x00000000,c2m_tx_link2_fs_status2
0x00002500,0x00000000,c2m_rx_link2_phy_phy_ctrl0
0x00002501,0x00000008,c2m_rx_link2_phy_sync_ctrl0
0x00002502,0x00000000,c2m_rx_link2_phy_sync_ctrl1
0x00002503,0x00000020,c2m_rx_link2_phy_sync_ctrl2
0x00002504,0x00000001,c2m_rx_link2_phy_sync_ctrl3
0x00002505,0x00000000,c2m_rx_link2_phy_sync_ctrl4
0x00002506,0x00000002,c2m_rx_link2_phy_fec_ctrl0
0x00002507,0x00000004,c2m_rx_link2_phy_fec_ctrl1
0x00002508,0x00000001,c2m_rx_link2_phy_fec_ctrl2
0x00002509,0x00000010,c2m_rx_link2_phy_idle_ctrl0
0x0000250a,0x00000003,c2m_rx_link2_phy_dec_ctrl0
0x0000250b,0x00000040,c2m_rx_link2_phy_dec_ctrl1
0x0000250c,0x00000001,c2m_rx_link2_phy_dec_ctrl2
0x0000250d,0x00000001,c2m_rx_link2_phy_dec_ctrl3
0x0000250e,0x000000a0,c2m_rx_link2_phy_fault_ctrl0
0x0000250f,0x00000064,c2m_rx_link2_phy_fault_ctrl1
0x00002510,0x000000fe,c2m_rx_link2_phy_ana_ctrl0
0x00002511,0x00000000,c2m_rx_link2_phy_ana_ctrl1
0x00002512,0x00000000,c2m_rx_link2_phy_ana_ctrl2
0x00002518,0x0000007d,c2m_rx_link2_phy_ana_ctrl8
0x00002519,0x00000094,c2m_rx_link2_phy_ana_ctrl9
0x0000251a,0x00000011,c2m_rx_link2_phy_ana_ctrl10
0x0000251b,0x00000090,c2m_rx_link2_phy_ana_ctrl11
0x0000251c,0x000000fa,c2m_rx_link2_phy_ana_ctrl12
0x0000251d,0x00000080,c2m_rx_link2_phy_ana_ctrl13
0x0000251e,0x00000080,c2m_rx_link2_phy_ana_ctrl14
0x00002520,0x00000000,c2m_rx_link2_phy_ana_ctrl16
0x00002521,0x00000000,c2m_rx_link2_phy_ana_ctrl17
0x00002522,0x00000003,c2m_rx_link2_phy_ana_ctrl18
0x00002523,0x00000000,c2m_rx_link2_phy_err_cnt_clr
0x00002524,0x00000000,c2m_rx_link2_phy_err_cnt_auto_clr_en
0x00002525,0x00000001,c2m_rx_link2_phy_dec_rd_err_thrd
0x00002526,0x00000000,c2m_rx_link2_phy_dec_rd_err_cnt
0x00002527,0x00000001,c2m_rx_link2_phy_dec_code_err_thrd
0x00002528,0x00000000,c2m_rx_link2_phy_dec_code_err_cnt
0x00002529,0x00000001,c2m_rx_link2_phy_dec_err_thrd
0x0000252a,0x00000000,c2m_rx_link2_phy_dec_err_cnt
0x0000252b,0x00000001,c2m_rx_link2_phy_sync_word_err_thrd
0x0000252c,0x00000000,c2m_rx_link2_phy_sync_word_err_cnt
0x0000252d,0x00000001,c2m_rx_link2_phy_fec_correct_err_thrd
0x0000252e,0x00000000,c2m_rx_link2_phy_fec_correct_err_cnt
0x0000252f,0x00000001,c2m_rx_link2_phy_fec_uncorrect_err_thrd
0x00002530,0x00000000,c2m_rx_link2_phy_fec_uncorrect_err_cnt
0x00002540,0x00000007,c2m_rx_link2_pp_i2c_ctrl0
0x00002541,0x000000ff,c2m_rx_link2_pp_i2c_ctrl1
0x00002542,0x00000003,c2m_rx_link2_pp_uart_ctrl0
0x00002543,0x000000ff,c2m_rx_link2_pp_uart_ctrl1
0x00002544,0x00000003,c2m_rx_link2_pp_spi_ctrl0
0x00002545,0x00000003,c2m_rx_link2_pp_gpio_ctrl0
0x00002546,0x00000000,c2m_rx_link2_pp_timeout_ctrl0
0x00002547,0x000000ff,c2m_rx_link2_pp_timeout_ctrl1
0x00002548,0x000000ff,c2m_rx_link2_pp_timeout_ctrl2
0x00002549,0x00000000,c2m_rx_link2_pp_video_ctrl0
0x0000254a,0x000000ff,c2m_rx_link2_pp_video_ctrl1
0x0000254b,0x000000ff,c2m_rx_link2_pp_video_ctrl2
0x0000254c,0x00000003,c2m_rx_link2_pp_video_ctrl3
0x0000254d,0x00000043,c2m_rx_link2_pp_video_ctrl4[0]
0x0000254e,0x00000034,c2m_rx_link2_pp_video_ctrl4[1]
0x0000254f,0x00000097,c2m_rx_link2_pp_video_ctrl4[2]
0x00002550,0x00000003,c2m_rx_link2_pp_video_ctrl4[3]
0x00002551,0x00000035,c2m_rx_link2_pp_video_ctrl5
0x00002552,0x00000000,c2m_rx_link2_pp_video_status0
0x00002553,0x00000000,c2m_rx_link2_pp_video_status1
0x00002554,0x00000000,c2m_rx_link2_pp_video_status2
0x00002555,0x00000000,c2m_rx_link2_pp_video_status3
0x00002556,0x00000000,c2m_rx_link2_pp_video_status4
0x00002600,0x00000000,c2m_tx_link3_phy_phy_ctrl0
0x00002601,0x00000080,c2m_tx_link3_phy_sync_ctrl0
0x00002602,0x00000000,c2m_tx_link3_phy_sync_ctrl1
0x00002603,0x00000000,c2m_tx_link3_phy_sync_ctrl2
0x00002604,0x00000000,c2m_tx_link3_phy_comp_ctrl0
0x00002605,0x00000000,c2m_tx_link3_phy_comp_ctrl1
0x00002606,0x00000000,c2m_tx_link3_phy_comp_ctrl2
0x00002607,0x00000001,c2m_tx_link3_phy_fec_ctrl0
0x00002608,0x00000002,c2m_tx_link3_phy_psch_ctrl0
0x00002609,0x00000000,c2m_tx_link3_phy_ipg_ctrl0
0x0000260a,0x00000040,c2m_tx_link3_phy_ipg_ctrl1
0x0000260b,0x0000007f,c2m_tx_link3_phy_idle_ctrl0
0x0000260c,0x0000000c,c2m_tx_link3_phy_errinj_ctrl0
0x0000260d,0x00000000,c2m_tx_link3_phy_errinj_ctrl1
0x0000260e,0x00000081,c2m_tx_link3_phy_ana_ctrl0
0x0000260f,0x00000000,c2m_tx_link3_phy_ana_ctrl1
0x00002610,0x00000000,c2m_tx_link3_phy_ana_ctrl2
0x00002611,0x00000001,c2m_tx_link3_phy_ana_ctrl3
0x00002612,0x0000007f,c2m_tx_link3_phy_ana_ctrl4
0x00002613,0x00000001,c2m_tx_link3_phy_ana_ctrl5
0x00002614,0x00000064,c2m_tx_link3_phy_ana_ctrl6
0x00002615,0x00000019,c2m_tx_link3_phy_ana_ctrl7
0x00002616,0x0000005a,c2m_tx_link3_phy_ana_ctrl8
0x00002617,0x0000003c,c2m_tx_link3_phy_ana_ctrl9
0x000026f0,0x00000000,c2m_tx_link3_psch_ctrl0
0x000026f1,0x00000000,c2m_tx_link3_psch_ctrl1
0x000026f2,0x00000001,c2m_tx_link3_psch_ctrl2
0x000026f3,0x00000001,c2m_tx_link3_psch_ctrl3
0x000026f4,0x00000000,c2m_tx_link3_psch_status0
0x00002620,0x00000003,c2m_tx_link3_i2c0_ctrl0
0x00002621,0x0000003c,c2m_tx_link3_i2c0_ctrl1
0x00002622,0x00000010,c2m_tx_link3_i2c0_ctrl2
0x00002623,0x000000a0,c2m_tx_link3_i2c0_ctrl3
0x00002624,0x00000001,c2m_tx_link3_i2c0_ctrl4
0x00002625,0x00000000,c2m_tx_link3_i2c0_ctrl5
0x00002626,0x00000000,c2m_tx_link3_i2c0_ctrl6
0x00002627,0x00000000,c2m_tx_link3_i2c0_ctrl7
0x00002628,0x00000000,c2m_tx_link3_i2c0_status0
0x00002629,0x00000000,c2m_tx_link3_i2c0_status1
0x0000262a,0x00000000,c2m_tx_link3_i2c0_status2
0x00002630,0x00000003,c2m_tx_link3_i2c1_ctrl0
0x00002631,0x0000003c,c2m_tx_link3_i2c1_ctrl1
0x00002632,0x00000015,c2m_tx_link3_i2c1_ctrl2
0x00002633,0x000000a0,c2m_tx_link3_i2c1_ctrl3
0x00002634,0x00000001,c2m_tx_link3_i2c1_ctrl4
0x00002635,0x00000000,c2m_tx_link3_i2c1_ctrl5
0x00002636,0x00000000,c2m_tx_link3_i2c1_ctrl6
0x00002637,0x00000000,c2m_tx_link3_i2c1_ctrl7
0x00002638,0x00000000,c2m_tx_link3_i2c1_status0
0x00002639,0x00000000,c2m_tx_link3_i2c1_status1
0x0000263a,0x00000000,c2m_tx_link3_i2c1_status2
0x00002640,0x00000003,c2m_tx_link3_i2c2_ctrl0
0x00002641,0x0000003c,c2m_tx_link3_i2c2_ctrl1
0x00002642,0x0000001a,c2m_tx_link3_i2c2_ctrl2
0x00002643,0x000000a0,c2m_tx_link3_i2c2_ctrl3
0x00002644,0x00000001,c2m_tx_link3_i2c2_ctrl4
0x00002645,0x00000000,c2m_tx_link3_i2c2_ctrl5
0x00002646,0x00000000,c2m_tx_link3_i2c2_ctrl6
0x00002647,0x00000000,c2m_tx_link3_i2c2_ctrl7
0x00002648,0x00000000,c2m_tx_link3_i2c2_status0
0x00002649,0x00000000,c2m_tx_link3_i2c2_status1
0x0000264a,0x00000000,c2m_tx_link3_i2c2_status2
0x00002650,0x00000003,c2m_tx_link3_i2c_ack_ctrl0
0x00002651,0x00000000,c2m_tx_link3_i2c_ack_ctrl1
0x00002652,0x00000000,c2m_tx_link3_i2c_ack_ctrl2
0x00002653,0x00000000,c2m_tx_link3_i2c_ack_ctrl3
0x00002654,0x00000000,c2m_tx_link3_i2c_ack_status0
0x00002660,0x00000003,c2m_tx_link3_uart0_ctrl0
0x00002661,0x0000003c,c2m_tx_link3_uart0_ctrl1
0x00002662,0x00000000,c2m_tx_link3_uart0_ctrl2
0x00002663,0x000000a0,c2m_tx_link3_uart0_ctrl3
0x00002664,0x00000001,c2m_tx_link3_uart0_ctrl4
0x00002665,0x00000000,c2m_tx_link3_uart0_ctrl5
0x00002666,0x00000000,c2m_tx_link3_uart0_ctrl6
0x00002667,0x00000000,c2m_tx_link3_uart0_ctrl7
0x00002668,0x00000000,c2m_tx_link3_uart0_status0
0x00002669,0x00000000,c2m_tx_link3_uart0_status1
0x0000266a,0x00000000,c2m_tx_link3_uart0_status2
0x00002670,0x00000003,c2m_tx_link3_uart1_ctrl0
0x00002671,0x0000003c,c2m_tx_link3_uart1_ctrl1
0x00002672,0x00000005,c2m_tx_link3_uart1_ctrl2
0x00002673,0x000000a0,c2m_tx_link3_uart1_ctrl3
0x00002674,0x00000001,c2m_tx_link3_uart1_ctrl4
0x00002675,0x00000000,c2m_tx_link3_uart1_ctrl5
0x00002676,0x00000000,c2m_tx_link3_uart1_ctrl6
0x00002677,0x00000000,c2m_tx_link3_uart1_ctrl7
0x00002678,0x00000000,c2m_tx_link3_uart1_status0
0x00002679,0x00000000,c2m_tx_link3_uart1_status1
0x0000267a,0x00000000,c2m_tx_link3_uart1_status2
0x00002680,0x00000003,c2m_tx_link3_uart2_ctrl0
0x00002681,0x0000003c,c2m_tx_link3_uart2_ctrl1
0x00002682,0x0000000a,c2m_tx_link3_uart2_ctrl2
0x00002683,0x000000a0,c2m_tx_link3_uart2_ctrl3
0x00002684,0x00000001,c2m_tx_link3_uart2_ctrl4
0x00002685,0x00000000,c2m_tx_link3_uart2_ctrl5
0x00002686,0x00000000,c2m_tx_link3_uart2_ctrl6
0x00002687,0x00000000,c2m_tx_link3_uart2_ctrl7
0x00002688,0x00000000,c2m_tx_link3_uart2_status0
0x00002689,0x00000000,c2m_tx_link3_uart2_status1
0x0000268a,0x00000000,c2m_tx_link3_uart2_status2
0x00002690,0x00000003,c2m_tx_link3_uart_ack_ctrl0
0x00002691,0x00000000,c2m_tx_link3_uart_ack_ctrl1
0x00002692,0x00000000,c2m_tx_link3_uart_ack_ctrl2
0x00002693,0x00000000,c2m_tx_link3_uart_ack_ctrl3
0x00002694,0x00000000,c2m_tx_link3_uart_ack_status0
0x000026a0,0x00000003,c2m_tx_link3_spi_ctrl0
0x000026a1,0x0000003c,c2m_tx_link3_spi_ctrl1
0x000026a2,0x00000000,c2m_tx_link3_spi_ctrl2
0x000026a3,0x000000a0,c2m_tx_link3_spi_ctrl3
0x000026a4,0x00000001,c2m_tx_link3_spi_ctrl4
0x000026a5,0x00000000,c2m_tx_link3_spi_ctrl5
0x000026a6,0x00000000,c2m_tx_link3_spi_ctrl6
0x000026a7,0x00000000,c2m_tx_link3_spi_ctrl7
0x000026a8,0x00000000,c2m_tx_link3_spi_status0
0x000026a9,0x00000000,c2m_tx_link3_spi_status1
0x000026aa,0x00000000,c2m_tx_link3_spi_status2
0x000026b0,0x00000003,c2m_tx_link3_spi_ack_ctrl0
0x000026b1,0x00000000,c2m_tx_link3_spi_ack_ctrl1
0x000026b2,0x00000000,c2m_tx_link3_spi_ack_ctrl2
0x000026b3,0x00000000,c2m_tx_link3_spi_ack_ctrl3
0x000026b4,0x00000000,c2m_tx_link3_spi_ack_status0
0x000026c0,0x00000003,c2m_tx_link3_gpio_ctrl0
0x000026c1,0x0000003c,c2m_tx_link3_gpio_ctrl1
0x000026c2,0x00000000,c2m_tx_link3_gpio_ctrl2
0x000026c3,0x000000a0,c2m_tx_link3_gpio_ctrl3
0x000026c4,0x00000001,c2m_tx_link3_gpio_ctrl4
0x000026c5,0x00000000,c2m_tx_link3_gpio_ctrl5
0x000026c6,0x00000000,c2m_tx_link3_gpio_ctrl6
0x000026c7,0x00000000,c2m_tx_link3_gpio_ctrl7
0x000026c8,0x00000000,c2m_tx_link3_gpio_status0
0x000026c9,0x00000000,c2m_tx_link3_gpio_status1
0x000026ca,0x00000000,c2m_tx_link3_gpio_status2
0x000026d0,0x00000003,c2m_tx_link3_gpio_ack_ctrl0
0x000026d1,0x00000000,c2m_tx_link3_gpio_ack_ctrl1
0x000026d2,0x00000000,c2m_tx_link3_gpio_ack_ctrl2
0x000026d3,0x00000000,c2m_tx_link3_gpio_ack_ctrl3
0x000026d4,0x00000000,c2m_tx_link3_gpio_ack_status0
0x000026e0,0x00000003,c2m_tx_link3_fs_ctrl0
0x000026e1,0x0000003c,c2m_tx_link3_fs_ctrl1
0x000026e2,0x00000004,c2m_tx_link3_fs_ctrl2
0x000026e3,0x000000a0,c2m_tx_link3_fs_ctrl3
0x000026e4,0x00000001,c2m_tx_link3_fs_ctrl4
0x000026e5,0x00000000,c2m_tx_link3_fs_ctrl5
0x000026e6,0x00000000,c2m_tx_link3_fs_ctrl6
0x000026e7,0x00000000,c2m_tx_link3_fs_ctrl7
0x000026e8,0x00000000,c2m_tx_link3_fs_status0
0x000026e9,0x00000000,c2m_tx_link3_fs_status1
0x000026ea,0x00000000,c2m_tx_link3_fs_status2
0x00002700,0x00000000,c2m_rx_link3_phy_phy_ctrl0
0x00002701,0x00000008,c2m_rx_link3_phy_sync_ctrl0
0x00002702,0x00000000,c2m_rx_link3_phy_sync_ctrl1
0x00002703,0x00000020,c2m_rx_link3_phy_sync_ctrl2
0x00002704,0x00000001,c2m_rx_link3_phy_sync_ctrl3
0x00002705,0x00000000,c2m_rx_link3_phy_sync_ctrl4
0x00002706,0x00000002,c2m_rx_link3_phy_fec_ctrl0
0x00002707,0x00000004,c2m_rx_link3_phy_fec_ctrl1
0x00002708,0x00000001,c2m_rx_link3_phy_fec_ctrl2
0x00002709,0x00000010,c2m_rx_link3_phy_idle_ctrl0
0x0000270a,0x00000003,c2m_rx_link3_phy_dec_ctrl0
0x0000270b,0x00000040,c2m_rx_link3_phy_dec_ctrl1
0x0000270c,0x00000001,c2m_rx_link3_phy_dec_ctrl2
0x0000270d,0x00000001,c2m_rx_link3_phy_dec_ctrl3
0x0000270e,0x000000a0,c2m_rx_link3_phy_fault_ctrl0
0x0000270f,0x00000064,c2m_rx_link3_phy_fault_ctrl1
0x00002710,0x000000fe,c2m_rx_link3_phy_ana_ctrl0
0x00002711,0x00000000,c2m_rx_link3_phy_ana_ctrl1
0x00002712,0x00000000,c2m_rx_link3_phy_ana_ctrl2
0x00002718,0x0000007d,c2m_rx_link3_phy_ana_ctrl8
0x00002719,0x00000094,c2m_rx_link3_phy_ana_ctrl9
0x0000271a,0x00000011,c2m_rx_link3_phy_ana_ctrl10
0x0000271b,0x00000090,c2m_rx_link3_phy_ana_ctrl11
0x0000271c,0x000000fa,c2m_rx_link3_phy_ana_ctrl12
0x0000271d,0x00000080,c2m_rx_link3_phy_ana_ctrl13
0x0000271e,0x00000080,c2m_rx_link3_phy_ana_ctrl14
0x00002720,0x00000000,c2m_rx_link3_phy_ana_ctrl16
0x00002721,0x00000000,c2m_rx_link3_phy_ana_ctrl17
0x00002722,0x00000003,c2m_rx_link3_phy_ana_ctrl18
0x00002723,0x00000000,c2m_rx_link3_phy_err_cnt_clr
0x00002724,0x00000000,c2m_rx_link3_phy_err_cnt_auto_clr_en
0x00002725,0x00000001,c2m_rx_link3_phy_dec_rd_err_thrd
0x00002726,0x00000000,c2m_rx_link3_phy_dec_rd_err_cnt
0x00002727,0x00000001,c2m_rx_link3_phy_dec_code_err_thrd
0x00002728,0x00000000,c2m_rx_link3_phy_dec_code_err_cnt
0x00002729,0x00000001,c2m_rx_link3_phy_dec_err_thrd
0x0000272a,0x00000000,c2m_rx_link3_phy_dec_err_cnt
0x0000272b,0x00000001,c2m_rx_link3_phy_sync_word_err_thrd
0x0000272c,0x00000000,c2m_rx_link3_phy_sync_word_err_cnt
0x0000272d,0x00000001,c2m_rx_link3_phy_fec_correct_err_thrd
0x0000272e,0x00000000,c2m_rx_link3_phy_fec_correct_err_cnt
0x0000272f,0x00000001,c2m_rx_link3_phy_fec_uncorrect_err_thrd
0x00002730,0x00000000,c2m_rx_link3_phy_fec_uncorrect_err_cnt
0x00002740,0x00000007,c2m_rx_link3_pp_i2c_ctrl0
0x00002741,0x000000ff,c2m_rx_link3_pp_i2c_ctrl1
0x00002742,0x00000003,c2m_rx_link3_pp_uart_ctrl0
0x00002743,0x000000ff,c2m_rx_link3_pp_uart_ctrl1
0x00002744,0x00000003,c2m_rx_link3_pp_spi_ctrl0
0x00002745,0x00000003,c2m_rx_link3_pp_gpio_ctrl0
0x00002746,0x00000000,c2m_rx_link3_pp_timeout_ctrl0
0x00002747,0x000000ff,c2m_rx_link3_pp_timeout_ctrl1
0x00002748,0x000000ff,c2m_rx_link3_pp_timeout_ctrl2
0x00002749,0x00000000,c2m_rx_link3_pp_video_ctrl0
0x0000274a,0x000000ff,c2m_rx_link3_pp_video_ctrl1
0x0000274b,0x000000ff,c2m_rx_link3_pp_video_ctrl2
0x0000274c,0x00000003,c2m_rx_link3_pp_video_ctrl3
0x0000274d,0x00000043,c2m_rx_link3_pp_video_ctrl4[0]
0x0000274e,0x00000034,c2m_rx_link3_pp_video_ctrl4[1]
0x0000274f,0x00000097,c2m_rx_link3_pp_video_ctrl4[2]
0x00002750,0x00000003,c2m_rx_link3_pp_video_ctrl4[3]
0x00002751,0x00000035,c2m_rx_link3_pp_video_ctrl5
0x00002752,0x00000000,c2m_rx_link3_pp_video_status0
0x00002753,0x00000000,c2m_rx_link3_pp_video_status1
0x00002754,0x00000000,c2m_rx_link3_pp_video_status2
0x00002755,0x00000000,c2m_rx_link3_pp_video_status3
0x00002756,0x00000000,c2m_rx_link3_pp_video_status4
0x00003000,0x00000032,c2m_ana_top_d2a_ch0_regnamedel0
0x00003001,0x00000000,c2m_ana_top_d2a_ch0_regnamedel1
0x00003002,0x00000090,c2m_ana_top_d2a_ch0_regnamedel2
0x00003003,0x00000064,c2m_ana_top_d2a_ch0_regnamedel3
0x00003004,0x00000014,c2m_ana_top_d2a_ch0_regnamedel4
0x00003005,0x00000032,c2m_ana_top_d2a_ch0_regnamedel5
0x00003006,0x0000007f,c2m_ana_top_d2a_ch0_regnamedel6
0x00003007,0x00000081,c2m_ana_top_d2a_ch0_regnamedel7
0x00003008,0x00000000,c2m_ana_top_d2a_ch0_regnamedel8
0x00003009,0x00000010,c2m_ana_top_d2a_ch0_regnamedel9
0x0000300a,0x00000001,c2m_ana_top_d2a_ch0_regnamedel10
0x0000300b,0x00000069,c2m_ana_top_d2a_ch0_regnamedel11
0x0000300c,0x0000001f,c2m_ana_top_d2a_ch0_regnamedel12
0x0000300d,0x00000000,c2m_ana_top_d2a_ch0_regnamedel208
0x0000300e,0x0000006f,c2m_ana_top_d2a_ch0_regnamedel13
0x0000300f,0x000000c0,c2m_ana_top_d2a_ch0_regnamedel14
0x00003010,0x00000040,c2m_ana_top_d2a_ch0_regnamedel15
0x00003011,0x0000003f,c2m_ana_top_d2a_ch0_regnamedel16
0x00003012,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel17
0x00003013,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel18
0x00003014,0x00000000,c2m_ana_top_d2a_ch0_regnamedel19
0x00003015,0x00000010,c2m_ana_top_d2a_ch0_regnamedel20
0x00003016,0x00000002,c2m_ana_top_d2a_ch0_regnamedel21
0x00003017,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel22
0x00003018,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel23
0x00003019,0x00000000,c2m_ana_top_d2a_ch0_regnamedel24
0x0000301a,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel25
0x0000301b,0x00000010,c2m_ana_top_d2a_ch0_regnamedel26
0x0000301c,0x00000002,c2m_ana_top_d2a_ch0_regnamedel27
0x0000301d,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel28
0x0000301e,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel29
0x0000301f,0x00000000,c2m_ana_top_d2a_ch0_regnamedel30
0x00003020,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel31
0x00003021,0x00000010,c2m_ana_top_d2a_ch0_regnamedel32
0x00003022,0x00000002,c2m_ana_top_d2a_ch0_regnamedel33
0x00003023,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel34
0x00003024,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel35
0x00003025,0x00000000,c2m_ana_top_d2a_ch0_regnamedel36
0x00003026,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel37
0x00003027,0x00000010,c2m_ana_top_d2a_ch0_regnamedel38
0x00003028,0x00000002,c2m_ana_top_d2a_ch0_regnamedel39
0x00003029,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel40
0x0000302a,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel41
0x0000302b,0x00000000,c2m_ana_top_d2a_ch0_regnamedel42
0x0000302c,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel43
0x0000302d,0x00000010,c2m_ana_top_d2a_ch0_regnamedel44
0x0000302e,0x00000002,c2m_ana_top_d2a_ch0_regnamedel45
0x0000302f,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel46
0x00003030,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel47
0x00003031,0x00000000,c2m_ana_top_d2a_ch0_regnamedel48
0x00003032,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel49
0x00003033,0x00000010,c2m_ana_top_d2a_ch0_regnamedel50
0x00003034,0x00000002,c2m_ana_top_d2a_ch0_regnamedel51
0x00003035,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel52
0x00003036,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel53
0x00003037,0x00000000,c2m_ana_top_d2a_ch0_regnamedel54
0x00003038,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel55
0x00003039,0x00000010,c2m_ana_top_d2a_ch0_regnamedel56
0x0000303a,0x00000002,c2m_ana_top_d2a_ch0_regnamedel57
0x0000303b,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel58
0x0000303c,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel59
0x0000303d,0x00000000,c2m_ana_top_d2a_ch0_regnamedel60
0x0000303e,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel61
0x0000303f,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel146
0x00003040,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel147
0x00003041,0x00000000,c2m_ana_top_d2a_ch0_regnamedel148
0x00003042,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel145
0x00003043,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel149
0x00003044,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel150
0x00003045,0x00000000,c2m_ana_top_d2a_ch0_regnamedel151
0x00003046,0x0000001c,c2m_ana_top_d2a_ch0_regnamedel152
0x00003047,0x0000001f,c2m_ana_top_d2a_ch0_regnamedel153
0x00003048,0x00000000,c2m_ana_top_d2a_ch0_regnamedel207
0x00003049,0x0000004b,c2m_ana_top_d2a_ch0_regnamedel154
0x0000304a,0x00000040,c2m_ana_top_d2a_ch0_regnamedel155
0x0000304b,0x00000004,c2m_ana_top_d2a_ch0_regnamedel156
0x0000304c,0x00000004,c2m_ana_top_d2a_ch0_regnamedel157
0x0000304d,0x00000010,c2m_ana_top_d2a_ch0_regnamedel62
0x0000304e,0x00000010,c2m_ana_top_d2a_ch0_regnamedel158
0x0000304f,0x00000010,c2m_ana_top_d2a_ch0_regnamedel159
0x00003050,0x00000002,c2m_ana_top_d2a_ch0_regnamedel63
0x00003051,0x00000002,c2m_ana_top_d2a_ch0_regnamedel160
0x00003052,0x00000002,c2m_ana_top_d2a_ch0_regnamedel161
0x00003053,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel64
0x00003054,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel65
0x00003055,0x00000000,c2m_ana_top_d2a_ch0_regnamedel66
0x00003056,0x00000039,c2m_ana_top_d2a_ch0_regnamedel67
0x00003057,0x00000020,c2m_ana_top_d2a_ch0_regnamedel68
0x00003058,0x00000004,c2m_ana_top_d2a_ch0_regnamedel69
0x00003059,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel70
0x0000305a,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel71
0x0000305b,0x00000000,c2m_ana_top_d2a_ch0_regnamedel72
0x0000305c,0x00000039,c2m_ana_top_d2a_ch0_regnamedel73
0x0000305d,0x00000020,c2m_ana_top_d2a_ch0_regnamedel74
0x0000305e,0x00000004,c2m_ana_top_d2a_ch0_regnamedel75
0x0000305f,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel76
0x00003060,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel77
0x00003061,0x00000000,c2m_ana_top_d2a_ch0_regnamedel78
0x00003062,0x00000039,c2m_ana_top_d2a_ch0_regnamedel79
0x00003063,0x00000020,c2m_ana_top_d2a_ch0_regnamedel80
0x00003064,0x00000004,c2m_ana_top_d2a_ch0_regnamedel81
0x00003065,0x00000006,c2m_ana_top_d2a_ch0_regnamedel82
0x00003066,0x0000003d,c2m_ana_top_d2a_ch0_regnamedel83
0x00003067,0x00000093,c2m_ana_top_d2a_ch0_regnamedel84
0x00003068,0x00000068,c2m_ana_top_d2a_ch0_regnamedel85
0x00003069,0x0000005c,c2m_ana_top_d2a_ch0_regnamedel86
0x0000306a,0x0000003c,c2m_ana_top_d2a_ch0_regnamedel87
0x0000306b,0x0000000b,c2m_ana_top_d2a_ch0_regnamedel88
0x0000306c,0x00000000,c2m_ana_top_d2a_ch0_regnamedel106
0x0000306d,0x00000000,c2m_ana_top_d2a_ch0_regnamedel107
0x0000306e,0x00000000,c2m_ana_top_d2a_ch0_regnamedel108
0x0000306f,0x00000000,c2m_ana_top_d2a_ch0_regnamedel109
0x00003070,0x00000000,c2m_ana_top_d2a_ch0_regnamedel110
0x00003071,0x00000064,c2m_ana_top_d2a_ch0_regnamedel111
0x00003072,0x00000000,c2m_ana_top_d2a_ch0_regnamedel112
0x00003073,0x0000002b,c2m_ana_top_d2a_ch0_regnamedel113
0x00003074,0x000000f4,c2m_ana_top_d2a_ch0_regnamedel114
0x00003075,0x00000040,c2m_ana_top_d2a_ch0_regnamedel115
0x00003076,0x000000d0,c2m_ana_top_d2a_ch0_regnamedel116
0x00003077,0x00000020,c2m_ana_top_d2a_ch0_regnamedel117
0x00003078,0x00000000,c2m_ana_top_d2a_ch0_regnamedel118
0x00003079,0x00000000,c2m_ana_top_d2a_ch0_regnamedel119
0x0000307a,0x00000020,c2m_ana_top_d2a_ch0_regnamedel120
0x0000307b,0x00000012,c2m_ana_top_d2a_ch0_regnamedel121
0x0000307c,0x00000060,c2m_ana_top_d2a_ch0_regnamedel122
0x0000307d,0x00000000,c2m_ana_top_d2a_ch0_regnamedel123
0x0000307e,0x00000000,c2m_ana_top_d2a_ch0_regnamedel124
0x0000307f,0x00000030,c2m_ana_top_d2a_ch0_regnamedel125
0x00003080,0x00000000,c2m_ana_top_d2a_ch0_regnamedel126
0x00003081,0x0000000c,c2m_ana_top_d2a_ch0_regnamedel127
0x00003082,0x000000dd,c2m_ana_top_d2a_ch0_regnamedel130
0x00003083,0x000000dd,c2m_ana_top_d2a_ch0_regnamedel131
0x00003084,0x0000007b,c2m_ana_top_d2a_ch0_regnamedel132
0x00003085,0x00000081,c2m_ana_top_d2a_ch0_regnamedel133
0x00003086,0x0000004a,c2m_ana_top_d2a_ch0_regnamedel134
0x00003087,0x000000c0,c2m_ana_top_d2a_ch0_regnamedel135
0x00003088,0x00000040,c2m_ana_top_d2a_ch0_regnamedel136
0x00003089,0x00000093,c2m_ana_top_d2a_ch0_regnamedel137
0x0000308a,0x000000c0,c2m_ana_top_d2a_ch0_regnamedel138
0x0000308b,0x000000c0,c2m_ana_top_d2a_ch0_regnamedel139
0x0000308c,0x00000040,c2m_ana_top_d2a_ch0_regnamedel140
0x0000308d,0x00000010,c2m_ana_top_d2a_ch0_regnamedel141
0x0000308e,0x0000009c,c2m_ana_top_d2a_ch0_regnamedel142
0x0000308f,0x00000080,c2m_ana_top_d2a_ch0_regnamedel143
0x00003090,0x0000008c,c2m_ana_top_d2a_ch0_regnamedel144
0x00003091,0x00000000,c2m_ana_top_d2a_ch0_regnamedel162
0x00003092,0x0000003f,c2m_ana_top_d2a_ch0_regnamedel163
0x00003093,0x0000001f,c2m_ana_top_d2a_ch0_regnamedel164
0x00003094,0x0000001f,c2m_ana_top_d2a_ch0_regnamedel165
0x00003095,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel166
0x00003096,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel167
0x00003097,0x0000000f,c2m_ana_top_d2a_ch0_regnamedel168
0x00003098,0x00000081,c2m_ana_top_d2a_ch0_regnamedel169
0x00003099,0x000000c1,c2m_ana_top_d2a_ch0_regnamedel170
0x0000309a,0x000000e1,c2m_ana_top_d2a_ch0_regnamedel171
0x0000309b,0x000000e1,c2m_ana_top_d2a_ch0_regnamedel172
0x0000309c,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel173
0x0000309d,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel174
0x0000309e,0x000000f1,c2m_ana_top_d2a_ch0_regnamedel175
0x0000309f,0x00000000,c2m_ana_top_d2a_ch0_regnamedel176
0x000030a0,0x00000000,c2m_ana_top_d2a_ch0_regnamedel177
0x000030a1,0x00000000,c2m_ana_top_d2a_ch0_regnamedel178
0x000030a2,0x00000000,c2m_ana_top_d2a_ch0_regnamedel179
0x000030a3,0x00000000,c2m_ana_top_d2a_ch0_regnamedel180
0x000030a4,0x00000000,c2m_ana_top_d2a_ch0_regnamedel181
0x000030a5,0x00000000,c2m_ana_top_d2a_ch0_regnamedel182
0x000030a6,0x00000059,c2m_ana_top_d2a_ch0_regnamedel183
0x000030a7,0x00000059,c2m_ana_top_d2a_ch0_regnamedel184
0x000030a8,0x00000049,c2m_ana_top_d2a_ch0_regnamedel185
0x000030a9,0x00000049,c2m_ana_top_d2a_ch0_regnamedel186
0x000030aa,0x00000039,c2m_ana_top_d2a_ch0_regnamedel187
0x000030ab,0x00000039,c2m_ana_top_d2a_ch0_regnamedel188
0x000030ac,0x00000039,c2m_ana_top_d2a_ch0_regnamedel189
0x000030ad,0x00000020,c2m_ana_top_d2a_ch0_regnamedel190
0x000030ae,0x00000020,c2m_ana_top_d2a_ch0_regnamedel191
0x000030af,0x00000020,c2m_ana_top_d2a_ch0_regnamedel192
0x000030b0,0x00000020,c2m_ana_top_d2a_ch0_regnamedel193
0x000030b1,0x00000020,c2m_ana_top_d2a_ch0_regnamedel194
0x000030b2,0x00000020,c2m_ana_top_d2a_ch0_regnamedel195
0x000030b3,0x00000020,c2m_ana_top_d2a_ch0_regnamedel196
0x000030b4,0x00000004,c2m_ana_top_d2a_ch0_regnamedel197
0x000030b5,0x00000004,c2m_ana_top_d2a_ch0_regnamedel198
0x000030b6,0x00000004,c2m_ana_top_d2a_ch0_regnamedel199
0x000030b7,0x00000004,c2m_ana_top_d2a_ch0_regnamedel200
0x000030b8,0x00000004,c2m_ana_top_d2a_ch0_regnamedel201
0x000030b9,0x00000004,c2m_ana_top_d2a_ch0_regnamedel202
0x000030ba,0x00000004,c2m_ana_top_d2a_ch0_regnamedel203
0x000030bb,0x00000020,c2m_ana_top_d2a_ch0_regnamedel204
0x000030bc,0x0000008f,c2m_ana_top_d2a_ch0_regnamedel205
0x000030bd,0x0000000e,c2m_ana_top_d2a_ch0_regnamedel206
0x000030be,0x000000fc,c2m_ana_top_d2a_ch0_regnamedel227
0x000030bf,0x00000013,c2m_ana_top_d2a_ch0_regnamedel228
0x00003100,0x00000032,c2m_ana_top_d2a_ch1_regnamedel0
0x00003101,0x00000000,c2m_ana_top_d2a_ch1_regnamedel1
0x00003102,0x00000090,c2m_ana_top_d2a_ch1_regnamedel2
0x00003103,0x00000064,c2m_ana_top_d2a_ch1_regnamedel3
0x00003104,0x00000014,c2m_ana_top_d2a_ch1_regnamedel4
0x00003105,0x00000032,c2m_ana_top_d2a_ch1_regnamedel5
0x00003106,0x0000007f,c2m_ana_top_d2a_ch1_regnamedel6
0x00003107,0x00000081,c2m_ana_top_d2a_ch1_regnamedel7
0x00003108,0x00000000,c2m_ana_top_d2a_ch1_regnamedel8
0x00003109,0x00000010,c2m_ana_top_d2a_ch1_regnamedel9
0x0000310a,0x00000001,c2m_ana_top_d2a_ch1_regnamedel10
0x0000310b,0x00000069,c2m_ana_top_d2a_ch1_regnamedel11
0x0000310c,0x0000001f,c2m_ana_top_d2a_ch1_regnamedel12
0x0000310d,0x00000000,c2m_ana_top_d2a_ch1_regnamedel208
0x0000310e,0x0000006f,c2m_ana_top_d2a_ch1_regnamedel13
0x0000310f,0x000000c0,c2m_ana_top_d2a_ch1_regnamedel14
0x00003110,0x00000040,c2m_ana_top_d2a_ch1_regnamedel15
0x00003111,0x0000003f,c2m_ana_top_d2a_ch1_regnamedel16
0x00003112,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel17
0x00003113,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel18
0x00003114,0x00000000,c2m_ana_top_d2a_ch1_regnamedel19
0x00003115,0x00000010,c2m_ana_top_d2a_ch1_regnamedel20
0x00003116,0x00000002,c2m_ana_top_d2a_ch1_regnamedel21
0x00003117,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel22
0x00003118,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel23
0x00003119,0x00000000,c2m_ana_top_d2a_ch1_regnamedel24
0x0000311a,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel25
0x0000311b,0x00000010,c2m_ana_top_d2a_ch1_regnamedel26
0x0000311c,0x00000002,c2m_ana_top_d2a_ch1_regnamedel27
0x0000311d,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel28
0x0000311e,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel29
0x0000311f,0x00000000,c2m_ana_top_d2a_ch1_regnamedel30
0x00003120,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel31
0x00003121,0x00000010,c2m_ana_top_d2a_ch1_regnamedel32
0x00003122,0x00000002,c2m_ana_top_d2a_ch1_regnamedel33
0x00003123,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel34
0x00003124,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel35
0x00003125,0x00000000,c2m_ana_top_d2a_ch1_regnamedel36
0x00003126,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel37
0x00003127,0x00000010,c2m_ana_top_d2a_ch1_regnamedel38
0x00003128,0x00000002,c2m_ana_top_d2a_ch1_regnamedel39
0x00003129,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel40
0x0000312a,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel41
0x0000312b,0x00000000,c2m_ana_top_d2a_ch1_regnamedel42
0x0000312c,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel43
0x0000312d,0x00000010,c2m_ana_top_d2a_ch1_regnamedel44
0x0000312e,0x00000002,c2m_ana_top_d2a_ch1_regnamedel45
0x0000312f,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel46
0x00003130,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel47
0x00003131,0x00000000,c2m_ana_top_d2a_ch1_regnamedel48
0x00003132,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel49
0x00003133,0x00000010,c2m_ana_top_d2a_ch1_regnamedel50
0x00003134,0x00000002,c2m_ana_top_d2a_ch1_regnamedel51
0x00003135,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel52
0x00003136,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel53
0x00003137,0x00000000,c2m_ana_top_d2a_ch1_regnamedel54
0x00003138,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel55
0x00003139,0x00000010,c2m_ana_top_d2a_ch1_regnamedel56
0x0000313a,0x00000002,c2m_ana_top_d2a_ch1_regnamedel57
0x0000313b,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel58
0x0000313c,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel59
0x0000313d,0x00000000,c2m_ana_top_d2a_ch1_regnamedel60
0x0000313e,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel61
0x0000313f,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel146
0x00003140,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel147
0x00003141,0x00000000,c2m_ana_top_d2a_ch1_regnamedel148
0x00003142,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel145
0x00003143,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel149
0x00003144,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel150
0x00003145,0x00000000,c2m_ana_top_d2a_ch1_regnamedel151
0x00003146,0x0000001c,c2m_ana_top_d2a_ch1_regnamedel152
0x00003147,0x0000001f,c2m_ana_top_d2a_ch1_regnamedel153
0x00003148,0x00000000,c2m_ana_top_d2a_ch1_regnamedel207
0x00003149,0x0000004b,c2m_ana_top_d2a_ch1_regnamedel154
0x0000314a,0x00000040,c2m_ana_top_d2a_ch1_regnamedel155
0x0000314b,0x00000004,c2m_ana_top_d2a_ch1_regnamedel156
0x0000314c,0x00000004,c2m_ana_top_d2a_ch1_regnamedel157
0x0000314d,0x00000010,c2m_ana_top_d2a_ch1_regnamedel62
0x0000314e,0x00000010,c2m_ana_top_d2a_ch1_regnamedel158
0x0000314f,0x00000010,c2m_ana_top_d2a_ch1_regnamedel159
0x00003150,0x00000002,c2m_ana_top_d2a_ch1_regnamedel63
0x00003151,0x00000002,c2m_ana_top_d2a_ch1_regnamedel160
0x00003152,0x00000002,c2m_ana_top_d2a_ch1_regnamedel161
0x00003153,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel64
0x00003154,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel65
0x00003155,0x00000000,c2m_ana_top_d2a_ch1_regnamedel66
0x00003156,0x00000039,c2m_ana_top_d2a_ch1_regnamedel67
0x00003157,0x00000020,c2m_ana_top_d2a_ch1_regnamedel68
0x00003158,0x00000004,c2m_ana_top_d2a_ch1_regnamedel69
0x00003159,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel70
0x0000315a,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel71
0x0000315b,0x00000000,c2m_ana_top_d2a_ch1_regnamedel72
0x0000315c,0x00000039,c2m_ana_top_d2a_ch1_regnamedel73
0x0000315d,0x00000020,c2m_ana_top_d2a_ch1_regnamedel74
0x0000315e,0x00000004,c2m_ana_top_d2a_ch1_regnamedel75
0x0000315f,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel76
0x00003160,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel77
0x00003161,0x00000000,c2m_ana_top_d2a_ch1_regnamedel78
0x00003162,0x00000039,c2m_ana_top_d2a_ch1_regnamedel79
0x00003163,0x00000020,c2m_ana_top_d2a_ch1_regnamedel80
0x00003164,0x00000004,c2m_ana_top_d2a_ch1_regnamedel81
0x00003165,0x00000006,c2m_ana_top_d2a_ch1_regnamedel82
0x00003166,0x0000003d,c2m_ana_top_d2a_ch1_regnamedel83
0x00003167,0x00000093,c2m_ana_top_d2a_ch1_regnamedel84
0x00003168,0x00000068,c2m_ana_top_d2a_ch1_regnamedel85
0x00003169,0x0000005c,c2m_ana_top_d2a_ch1_regnamedel86
0x0000316a,0x0000003c,c2m_ana_top_d2a_ch1_regnamedel87
0x0000316b,0x0000000b,c2m_ana_top_d2a_ch1_regnamedel88
0x0000316c,0x00000000,c2m_ana_top_d2a_ch1_regnamedel106
0x0000316d,0x00000000,c2m_ana_top_d2a_ch1_regnamedel107
0x0000316e,0x00000000,c2m_ana_top_d2a_ch1_regnamedel108
0x0000316f,0x00000000,c2m_ana_top_d2a_ch1_regnamedel109
0x00003170,0x00000000,c2m_ana_top_d2a_ch1_regnamedel110
0x00003171,0x00000064,c2m_ana_top_d2a_ch1_regnamedel111
0x00003172,0x00000000,c2m_ana_top_d2a_ch1_regnamedel112
0x00003173,0x0000002b,c2m_ana_top_d2a_ch1_regnamedel113
0x00003174,0x000000f4,c2m_ana_top_d2a_ch1_regnamedel114
0x00003175,0x00000040,c2m_ana_top_d2a_ch1_regnamedel115
0x00003176,0x000000d0,c2m_ana_top_d2a_ch1_regnamedel116
0x00003177,0x00000020,c2m_ana_top_d2a_ch1_regnamedel117
0x00003178,0x00000000,c2m_ana_top_d2a_ch1_regnamedel118
0x00003179,0x00000000,c2m_ana_top_d2a_ch1_regnamedel119
0x0000317a,0x00000020,c2m_ana_top_d2a_ch1_regnamedel120
0x0000317b,0x00000012,c2m_ana_top_d2a_ch1_regnamedel121
0x0000317c,0x00000060,c2m_ana_top_d2a_ch1_regnamedel122
0x0000317d,0x00000000,c2m_ana_top_d2a_ch1_regnamedel123
0x0000317e,0x00000000,c2m_ana_top_d2a_ch1_regnamedel124
0x0000317f,0x00000030,c2m_ana_top_d2a_ch1_regnamedel125
0x00003180,0x00000000,c2m_ana_top_d2a_ch1_regnamedel126
0x00003181,0x0000000c,c2m_ana_top_d2a_ch1_regnamedel127
0x00003182,0x000000dd,c2m_ana_top_d2a_ch1_regnamedel130
0x00003183,0x000000dd,c2m_ana_top_d2a_ch1_regnamedel131
0x00003184,0x0000007b,c2m_ana_top_d2a_ch1_regnamedel132
0x00003185,0x00000081,c2m_ana_top_d2a_ch1_regnamedel133
0x00003186,0x0000004a,c2m_ana_top_d2a_ch1_regnamedel134
0x00003187,0x000000c0,c2m_ana_top_d2a_ch1_regnamedel135
0x00003188,0x00000040,c2m_ana_top_d2a_ch1_regnamedel136
0x00003189,0x00000093,c2m_ana_top_d2a_ch1_regnamedel137
0x0000318a,0x000000c0,c2m_ana_top_d2a_ch1_regnamedel138
0x0000318b,0x000000c0,c2m_ana_top_d2a_ch1_regnamedel139
0x0000318c,0x00000040,c2m_ana_top_d2a_ch1_regnamedel140
0x0000318d,0x00000010,c2m_ana_top_d2a_ch1_regnamedel141
0x0000318e,0x0000009c,c2m_ana_top_d2a_ch1_regnamedel142
0x0000318f,0x00000080,c2m_ana_top_d2a_ch1_regnamedel143
0x00003190,0x0000008c,c2m_ana_top_d2a_ch1_regnamedel144
0x00003191,0x00000000,c2m_ana_top_d2a_ch1_regnamedel162
0x00003192,0x0000003f,c2m_ana_top_d2a_ch1_regnamedel163
0x00003193,0x0000001f,c2m_ana_top_d2a_ch1_regnamedel164
0x00003194,0x0000001f,c2m_ana_top_d2a_ch1_regnamedel165
0x00003195,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel166
0x00003196,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel167
0x00003197,0x0000000f,c2m_ana_top_d2a_ch1_regnamedel168
0x00003198,0x00000081,c2m_ana_top_d2a_ch1_regnamedel169
0x00003199,0x000000c1,c2m_ana_top_d2a_ch1_regnamedel170
0x0000319a,0x000000e1,c2m_ana_top_d2a_ch1_regnamedel171
0x0000319b,0x000000e1,c2m_ana_top_d2a_ch1_regnamedel172
0x0000319c,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel173
0x0000319d,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel174
0x0000319e,0x000000f1,c2m_ana_top_d2a_ch1_regnamedel175
0x0000319f,0x00000000,c2m_ana_top_d2a_ch1_regnamedel176
0x000031a0,0x00000000,c2m_ana_top_d2a_ch1_regnamedel177
0x000031a1,0x00000000,c2m_ana_top_d2a_ch1_regnamedel178
0x000031a2,0x00000000,c2m_ana_top_d2a_ch1_regnamedel179
0x000031a3,0x00000000,c2m_ana_top_d2a_ch1_regnamedel180
0x000031a4,0x00000000,c2m_ana_top_d2a_ch1_regnamedel181
0x000031a5,0x00000000,c2m_ana_top_d2a_ch1_regnamedel182
0x000031a6,0x00000059,c2m_ana_top_d2a_ch1_regnamedel183
0x000031a7,0x00000059,c2m_ana_top_d2a_ch1_regnamedel184
0x000031a8,0x00000049,c2m_ana_top_d2a_ch1_regnamedel185
0x000031a9,0x00000049,c2m_ana_top_d2a_ch1_regnamedel186
0x000031aa,0x00000039,c2m_ana_top_d2a_ch1_regnamedel187
0x000031ab,0x00000039,c2m_ana_top_d2a_ch1_regnamedel188
0x000031ac,0x00000039,c2m_ana_top_d2a_ch1_regnamedel189
0x000031ad,0x00000020,c2m_ana_top_d2a_ch1_regnamedel190
0x000031ae,0x00000020,c2m_ana_top_d2a_ch1_regnamedel191
0x000031af,0x00000020,c2m_ana_top_d2a_ch1_regnamedel192
0x000031b0,0x00000020,c2m_ana_top_d2a_ch1_regnamedel193
0x000031b1,0x00000020,c2m_ana_top_d2a_ch1_regnamedel194
0x000031b2,0x00000020,c2m_ana_top_d2a_ch1_regnamedel195
0x000031b3,0x00000020,c2m_ana_top_d2a_ch1_regnamedel196
0x000031b4,0x00000004,c2m_ana_top_d2a_ch1_regnamedel197
0x000031b5,0x00000004,c2m_ana_top_d2a_ch1_regnamedel198
0x000031b6,0x00000004,c2m_ana_top_d2a_ch1_regnamedel199
0x000031b7,0x00000004,c2m_ana_top_d2a_ch1_regnamedel200
0x000031b8,0x00000004,c2m_ana_top_d2a_ch1_regnamedel201
0x000031b9,0x00000004,c2m_ana_top_d2a_ch1_regnamedel202
0x000031ba,0x00000004,c2m_ana_top_d2a_ch1_regnamedel203
0x000031bb,0x00000020,c2m_ana_top_d2a_ch1_regnamedel204
0x000031bc,0x0000008f,c2m_ana_top_d2a_ch1_regnamedel205
0x000031bd,0x0000000e,c2m_ana_top_d2a_ch1_regnamedel206
0x000031be,0x000000fc,c2m_ana_top_d2a_ch1_regnamedel227
0x000031bf,0x00000013,c2m_ana_top_d2a_ch1_regnamedel228
0x00003200,0x00000032,c2m_ana_top_d2a_ch2_regnamedel0
0x00003201,0x00000000,c2m_ana_top_d2a_ch2_regnamedel1
0x00003202,0x00000090,c2m_ana_top_d2a_ch2_regnamedel2
0x00003203,0x00000064,c2m_ana_top_d2a_ch2_regnamedel3
0x00003204,0x00000014,c2m_ana_top_d2a_ch2_regnamedel4
0x00003205,0x00000032,c2m_ana_top_d2a_ch2_regnamedel5
0x00003206,0x0000007f,c2m_ana_top_d2a_ch2_regnamedel6
0x00003207,0x00000081,c2m_ana_top_d2a_ch2_regnamedel7
0x00003208,0x00000000,c2m_ana_top_d2a_ch2_regnamedel8
0x00003209,0x00000010,c2m_ana_top_d2a_ch2_regnamedel9
0x0000320a,0x00000001,c2m_ana_top_d2a_ch2_regnamedel10
0x0000320b,0x00000069,c2m_ana_top_d2a_ch2_regnamedel11
0x0000320c,0x0000001f,c2m_ana_top_d2a_ch2_regnamedel12
0x0000320d,0x00000000,c2m_ana_top_d2a_ch2_regnamedel208
0x0000320e,0x0000006f,c2m_ana_top_d2a_ch2_regnamedel13
0x0000320f,0x000000c0,c2m_ana_top_d2a_ch2_regnamedel14
0x00003210,0x00000040,c2m_ana_top_d2a_ch2_regnamedel15
0x00003211,0x0000003f,c2m_ana_top_d2a_ch2_regnamedel16
0x00003212,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel17
0x00003213,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel18
0x00003214,0x00000000,c2m_ana_top_d2a_ch2_regnamedel19
0x00003215,0x00000010,c2m_ana_top_d2a_ch2_regnamedel20
0x00003216,0x00000002,c2m_ana_top_d2a_ch2_regnamedel21
0x00003217,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel22
0x00003218,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel23
0x00003219,0x00000000,c2m_ana_top_d2a_ch2_regnamedel24
0x0000321a,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel25
0x0000321b,0x00000010,c2m_ana_top_d2a_ch2_regnamedel26
0x0000321c,0x00000002,c2m_ana_top_d2a_ch2_regnamedel27
0x0000321d,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel28
0x0000321e,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel29
0x0000321f,0x00000000,c2m_ana_top_d2a_ch2_regnamedel30
0x00003220,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel31
0x00003221,0x00000010,c2m_ana_top_d2a_ch2_regnamedel32
0x00003222,0x00000002,c2m_ana_top_d2a_ch2_regnamedel33
0x00003223,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel34
0x00003224,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel35
0x00003225,0x00000000,c2m_ana_top_d2a_ch2_regnamedel36
0x00003226,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel37
0x00003227,0x00000010,c2m_ana_top_d2a_ch2_regnamedel38
0x00003228,0x00000002,c2m_ana_top_d2a_ch2_regnamedel39
0x00003229,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel40
0x0000322a,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel41
0x0000322b,0x00000000,c2m_ana_top_d2a_ch2_regnamedel42
0x0000322c,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel43
0x0000322d,0x00000010,c2m_ana_top_d2a_ch2_regnamedel44
0x0000322e,0x00000002,c2m_ana_top_d2a_ch2_regnamedel45
0x0000322f,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel46
0x00003230,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel47
0x00003231,0x00000000,c2m_ana_top_d2a_ch2_regnamedel48
0x00003232,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel49
0x00003233,0x00000010,c2m_ana_top_d2a_ch2_regnamedel50
0x00003234,0x00000002,c2m_ana_top_d2a_ch2_regnamedel51
0x00003235,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel52
0x00003236,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel53
0x00003237,0x00000000,c2m_ana_top_d2a_ch2_regnamedel54
0x00003238,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel55
0x00003239,0x00000010,c2m_ana_top_d2a_ch2_regnamedel56
0x0000323a,0x00000002,c2m_ana_top_d2a_ch2_regnamedel57
0x0000323b,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel58
0x0000323c,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel59
0x0000323d,0x00000000,c2m_ana_top_d2a_ch2_regnamedel60
0x0000323e,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel61
0x0000323f,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel146
0x00003240,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel147
0x00003241,0x00000000,c2m_ana_top_d2a_ch2_regnamedel148
0x00003242,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel145
0x00003243,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel149
0x00003244,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel150
0x00003245,0x00000000,c2m_ana_top_d2a_ch2_regnamedel151
0x00003246,0x0000001c,c2m_ana_top_d2a_ch2_regnamedel152
0x00003247,0x0000001f,c2m_ana_top_d2a_ch2_regnamedel153
0x00003248,0x00000000,c2m_ana_top_d2a_ch2_regnamedel207
0x00003249,0x0000004b,c2m_ana_top_d2a_ch2_regnamedel154
0x0000324a,0x00000040,c2m_ana_top_d2a_ch2_regnamedel155
0x0000324b,0x00000004,c2m_ana_top_d2a_ch2_regnamedel156
0x0000324c,0x00000004,c2m_ana_top_d2a_ch2_regnamedel157
0x0000324d,0x00000010,c2m_ana_top_d2a_ch2_regnamedel62
0x0000324e,0x00000010,c2m_ana_top_d2a_ch2_regnamedel158
0x0000324f,0x00000010,c2m_ana_top_d2a_ch2_regnamedel159
0x00003250,0x00000002,c2m_ana_top_d2a_ch2_regnamedel63
0x00003251,0x00000002,c2m_ana_top_d2a_ch2_regnamedel160
0x00003252,0x00000002,c2m_ana_top_d2a_ch2_regnamedel161
0x00003253,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel64
0x00003254,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel65
0x00003255,0x00000000,c2m_ana_top_d2a_ch2_regnamedel66
0x00003256,0x00000039,c2m_ana_top_d2a_ch2_regnamedel67
0x00003257,0x00000020,c2m_ana_top_d2a_ch2_regnamedel68
0x00003258,0x00000004,c2m_ana_top_d2a_ch2_regnamedel69
0x00003259,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel70
0x0000325a,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel71
0x0000325b,0x00000000,c2m_ana_top_d2a_ch2_regnamedel72
0x0000325c,0x00000039,c2m_ana_top_d2a_ch2_regnamedel73
0x0000325d,0x00000020,c2m_ana_top_d2a_ch2_regnamedel74
0x0000325e,0x00000004,c2m_ana_top_d2a_ch2_regnamedel75
0x0000325f,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel76
0x00003260,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel77
0x00003261,0x00000000,c2m_ana_top_d2a_ch2_regnamedel78
0x00003262,0x00000039,c2m_ana_top_d2a_ch2_regnamedel79
0x00003263,0x00000020,c2m_ana_top_d2a_ch2_regnamedel80
0x00003264,0x00000004,c2m_ana_top_d2a_ch2_regnamedel81
0x00003265,0x00000006,c2m_ana_top_d2a_ch2_regnamedel82
0x00003266,0x0000003d,c2m_ana_top_d2a_ch2_regnamedel83
0x00003267,0x00000093,c2m_ana_top_d2a_ch2_regnamedel84
0x00003268,0x00000068,c2m_ana_top_d2a_ch2_regnamedel85
0x00003269,0x0000005c,c2m_ana_top_d2a_ch2_regnamedel86
0x0000326a,0x0000003c,c2m_ana_top_d2a_ch2_regnamedel87
0x0000326b,0x0000000b,c2m_ana_top_d2a_ch2_regnamedel88
0x0000326c,0x00000000,c2m_ana_top_d2a_ch2_regnamedel106
0x0000326d,0x00000000,c2m_ana_top_d2a_ch2_regnamedel107
0x0000326e,0x00000000,c2m_ana_top_d2a_ch2_regnamedel108
0x0000326f,0x00000000,c2m_ana_top_d2a_ch2_regnamedel109
0x00003270,0x00000000,c2m_ana_top_d2a_ch2_regnamedel110
0x00003271,0x00000064,c2m_ana_top_d2a_ch2_regnamedel111
0x00003272,0x00000000,c2m_ana_top_d2a_ch2_regnamedel112
0x00003273,0x0000002b,c2m_ana_top_d2a_ch2_regnamedel113
0x00003274,0x000000f4,c2m_ana_top_d2a_ch2_regnamedel114
0x00003275,0x00000040,c2m_ana_top_d2a_ch2_regnamedel115
0x00003276,0x000000d0,c2m_ana_top_d2a_ch2_regnamedel116
0x00003277,0x00000020,c2m_ana_top_d2a_ch2_regnamedel117
0x00003278,0x00000000,c2m_ana_top_d2a_ch2_regnamedel118
0x00003279,0x00000000,c2m_ana_top_d2a_ch2_regnamedel119
0x0000327a,0x00000020,c2m_ana_top_d2a_ch2_regnamedel120
0x0000327b,0x00000012,c2m_ana_top_d2a_ch2_regnamedel121
0x0000327c,0x00000060,c2m_ana_top_d2a_ch2_regnamedel122
0x0000327d,0x00000000,c2m_ana_top_d2a_ch2_regnamedel123
0x0000327e,0x00000000,c2m_ana_top_d2a_ch2_regnamedel124
0x0000327f,0x00000030,c2m_ana_top_d2a_ch2_regnamedel125
0x00003280,0x00000000,c2m_ana_top_d2a_ch2_regnamedel126
0x00003281,0x0000000c,c2m_ana_top_d2a_ch2_regnamedel127
0x00003282,0x000000dd,c2m_ana_top_d2a_ch2_regnamedel130
0x00003283,0x000000dd,c2m_ana_top_d2a_ch2_regnamedel131
0x00003284,0x0000007b,c2m_ana_top_d2a_ch2_regnamedel132
0x00003285,0x00000081,c2m_ana_top_d2a_ch2_regnamedel133
0x00003286,0x0000004a,c2m_ana_top_d2a_ch2_regnamedel134
0x00003287,0x000000c0,c2m_ana_top_d2a_ch2_regnamedel135
0x00003288,0x00000040,c2m_ana_top_d2a_ch2_regnamedel136
0x00003289,0x00000093,c2m_ana_top_d2a_ch2_regnamedel137
0x0000328a,0x000000c0,c2m_ana_top_d2a_ch2_regnamedel138
0x0000328b,0x000000c0,c2m_ana_top_d2a_ch2_regnamedel139
0x0000328c,0x00000040,c2m_ana_top_d2a_ch2_regnamedel140
0x0000328d,0x00000010,c2m_ana_top_d2a_ch2_regnamedel141
0x0000328e,0x0000009c,c2m_ana_top_d2a_ch2_regnamedel142
0x0000328f,0x00000080,c2m_ana_top_d2a_ch2_regnamedel143
0x00003290,0x0000008c,c2m_ana_top_d2a_ch2_regnamedel144
0x00003291,0x00000000,c2m_ana_top_d2a_ch2_regnamedel162
0x00003292,0x0000003f,c2m_ana_top_d2a_ch2_regnamedel163
0x00003293,0x0000001f,c2m_ana_top_d2a_ch2_regnamedel164
0x00003294,0x0000001f,c2m_ana_top_d2a_ch2_regnamedel165
0x00003295,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel166
0x00003296,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel167
0x00003297,0x0000000f,c2m_ana_top_d2a_ch2_regnamedel168
0x00003298,0x00000081,c2m_ana_top_d2a_ch2_regnamedel169
0x00003299,0x000000c1,c2m_ana_top_d2a_ch2_regnamedel170
0x0000329a,0x000000e1,c2m_ana_top_d2a_ch2_regnamedel171
0x0000329b,0x000000e1,c2m_ana_top_d2a_ch2_regnamedel172
0x0000329c,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel173
0x0000329d,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel174
0x0000329e,0x000000f1,c2m_ana_top_d2a_ch2_regnamedel175
0x0000329f,0x00000000,c2m_ana_top_d2a_ch2_regnamedel176
0x000032a0,0x00000000,c2m_ana_top_d2a_ch2_regnamedel177
0x000032a1,0x00000000,c2m_ana_top_d2a_ch2_regnamedel178
0x000032a2,0x00000000,c2m_ana_top_d2a_ch2_regnamedel179
0x000032a3,0x00000000,c2m_ana_top_d2a_ch2_regnamedel180
0x000032a4,0x00000000,c2m_ana_top_d2a_ch2_regnamedel181
0x000032a5,0x00000000,c2m_ana_top_d2a_ch2_regnamedel182
0x000032a6,0x00000059,c2m_ana_top_d2a_ch2_regnamedel183
0x000032a7,0x00000059,c2m_ana_top_d2a_ch2_regnamedel184
0x000032a8,0x00000049,c2m_ana_top_d2a_ch2_regnamedel185
0x000032a9,0x00000049,c2m_ana_top_d2a_ch2_regnamedel186
0x000032aa,0x00000039,c2m_ana_top_d2a_ch2_regnamedel187
0x000032ab,0x00000039,c2m_ana_top_d2a_ch2_regnamedel188
0x000032ac,0x00000039,c2m_ana_top_d2a_ch2_regnamedel189
0x000032ad,0x00000020,c2m_ana_top_d2a_ch2_regnamedel190
0x000032ae,0x00000020,c2m_ana_top_d2a_ch2_regnamedel191
0x000032af,0x00000020,c2m_ana_top_d2a_ch2_regnamedel192
0x000032b0,0x00000020,c2m_ana_top_d2a_ch2_regnamedel193
0x000032b1,0x00000020,c2m_ana_top_d2a_ch2_regnamedel194
0x000032b2,0x00000020,c2m_ana_top_d2a_ch2_regnamedel195
0x000032b3,0x00000020,c2m_ana_top_d2a_ch2_regnamedel196
0x000032b4,0x00000004,c2m_ana_top_d2a_ch2_regnamedel197
0x000032b5,0x00000004,c2m_ana_top_d2a_ch2_regnamedel198
0x000032b6,0x00000004,c2m_ana_top_d2a_ch2_regnamedel199
0x000032b7,0x00000004,c2m_ana_top_d2a_ch2_regnamedel200
0x000032b8,0x00000004,c2m_ana_top_d2a_ch2_regnamedel201
0x000032b9,0x00000004,c2m_ana_top_d2a_ch2_regnamedel202
0x000032ba,0x00000004,c2m_ana_top_d2a_ch2_regnamedel203
0x000032bb,0x00000020,c2m_ana_top_d2a_ch2_regnamedel204
0x000032bc,0x0000008f,c2m_ana_top_d2a_ch2_regnamedel205
0x000032bd,0x0000000e,c2m_ana_top_d2a_ch2_regnamedel206
0x000032be,0x000000fc,c2m_ana_top_d2a_ch2_regnamedel227
0x000032bf,0x00000013,c2m_ana_top_d2a_ch2_regnamedel228
0x00003300,0x00000032,c2m_ana_top_d2a_ch3_regnamedel0
0x00003301,0x00000000,c2m_ana_top_d2a_ch3_regnamedel1
0x00003302,0x00000090,c2m_ana_top_d2a_ch3_regnamedel2
0x00003303,0x00000064,c2m_ana_top_d2a_ch3_regnamedel3
0x00003304,0x00000014,c2m_ana_top_d2a_ch3_regnamedel4
0x00003305,0x00000032,c2m_ana_top_d2a_ch3_regnamedel5
0x00003306,0x0000007f,c2m_ana_top_d2a_ch3_regnamedel6
0x00003307,0x00000081,c2m_ana_top_d2a_ch3_regnamedel7
0x00003308,0x00000000,c2m_ana_top_d2a_ch3_regnamedel8
0x00003309,0x00000010,c2m_ana_top_d2a_ch3_regnamedel9
0x0000330a,0x00000001,c2m_ana_top_d2a_ch3_regnamedel10
0x0000330b,0x00000069,c2m_ana_top_d2a_ch3_regnamedel11
0x0000330c,0x0000001f,c2m_ana_top_d2a_ch3_regnamedel12
0x0000330d,0x00000000,c2m_ana_top_d2a_ch3_regnamedel208
0x0000330e,0x0000006f,c2m_ana_top_d2a_ch3_regnamedel13
0x0000330f,0x000000c0,c2m_ana_top_d2a_ch3_regnamedel14
0x00003310,0x00000040,c2m_ana_top_d2a_ch3_regnamedel15
0x00003311,0x0000003f,c2m_ana_top_d2a_ch3_regnamedel16
0x00003312,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel17
0x00003313,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel18
0x00003314,0x00000000,c2m_ana_top_d2a_ch3_regnamedel19
0x00003315,0x00000010,c2m_ana_top_d2a_ch3_regnamedel20
0x00003316,0x00000002,c2m_ana_top_d2a_ch3_regnamedel21
0x00003317,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel22
0x00003318,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel23
0x00003319,0x00000000,c2m_ana_top_d2a_ch3_regnamedel24
0x0000331a,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel25
0x0000331b,0x00000010,c2m_ana_top_d2a_ch3_regnamedel26
0x0000331c,0x00000002,c2m_ana_top_d2a_ch3_regnamedel27
0x0000331d,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel28
0x0000331e,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel29
0x0000331f,0x00000000,c2m_ana_top_d2a_ch3_regnamedel30
0x00003320,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel31
0x00003321,0x00000010,c2m_ana_top_d2a_ch3_regnamedel32
0x00003322,0x00000002,c2m_ana_top_d2a_ch3_regnamedel33
0x00003323,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel34
0x00003324,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel35
0x00003325,0x00000000,c2m_ana_top_d2a_ch3_regnamedel36
0x00003326,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel37
0x00003327,0x00000010,c2m_ana_top_d2a_ch3_regnamedel38
0x00003328,0x00000002,c2m_ana_top_d2a_ch3_regnamedel39
0x00003329,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel40
0x0000332a,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel41
0x0000332b,0x00000000,c2m_ana_top_d2a_ch3_regnamedel42
0x0000332c,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel43
0x0000332d,0x00000010,c2m_ana_top_d2a_ch3_regnamedel44
0x0000332e,0x00000002,c2m_ana_top_d2a_ch3_regnamedel45
0x0000332f,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel46
0x00003330,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel47
0x00003331,0x00000000,c2m_ana_top_d2a_ch3_regnamedel48
0x00003332,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel49
0x00003333,0x00000010,c2m_ana_top_d2a_ch3_regnamedel50
0x00003334,0x00000002,c2m_ana_top_d2a_ch3_regnamedel51
0x00003335,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel52
0x00003336,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel53
0x00003337,0x00000000,c2m_ana_top_d2a_ch3_regnamedel54
0x00003338,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel55
0x00003339,0x00000010,c2m_ana_top_d2a_ch3_regnamedel56
0x0000333a,0x00000002,c2m_ana_top_d2a_ch3_regnamedel57
0x0000333b,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel58
0x0000333c,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel59
0x0000333d,0x00000000,c2m_ana_top_d2a_ch3_regnamedel60
0x0000333e,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel61
0x0000333f,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel146
0x00003340,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel147
0x00003341,0x00000000,c2m_ana_top_d2a_ch3_regnamedel148
0x00003342,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel145
0x00003343,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel149
0x00003344,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel150
0x00003345,0x00000000,c2m_ana_top_d2a_ch3_regnamedel151
0x00003346,0x0000001c,c2m_ana_top_d2a_ch3_regnamedel152
0x00003347,0x0000001f,c2m_ana_top_d2a_ch3_regnamedel153
0x00003348,0x00000000,c2m_ana_top_d2a_ch3_regnamedel207
0x00003349,0x0000004b,c2m_ana_top_d2a_ch3_regnamedel154
0x0000334a,0x00000040,c2m_ana_top_d2a_ch3_regnamedel155
0x0000334b,0x00000004,c2m_ana_top_d2a_ch3_regnamedel156
0x0000334c,0x00000004,c2m_ana_top_d2a_ch3_regnamedel157
0x0000334d,0x00000010,c2m_ana_top_d2a_ch3_regnamedel62
0x0000334e,0x00000010,c2m_ana_top_d2a_ch3_regnamedel158
0x0000334f,0x00000010,c2m_ana_top_d2a_ch3_regnamedel159
0x00003350,0x00000002,c2m_ana_top_d2a_ch3_regnamedel63
0x00003351,0x00000002,c2m_ana_top_d2a_ch3_regnamedel160
0x00003352,0x00000002,c2m_ana_top_d2a_ch3_regnamedel161
0x00003353,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel64
0x00003354,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel65
0x00003355,0x00000000,c2m_ana_top_d2a_ch3_regnamedel66
0x00003356,0x00000039,c2m_ana_top_d2a_ch3_regnamedel67
0x00003357,0x00000020,c2m_ana_top_d2a_ch3_regnamedel68
0x00003358,0x00000004,c2m_ana_top_d2a_ch3_regnamedel69
0x00003359,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel70
0x0000335a,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel71
0x0000335b,0x00000000,c2m_ana_top_d2a_ch3_regnamedel72
0x0000335c,0x00000039,c2m_ana_top_d2a_ch3_regnamedel73
0x0000335d,0x00000020,c2m_ana_top_d2a_ch3_regnamedel74
0x0000335e,0x00000004,c2m_ana_top_d2a_ch3_regnamedel75
0x0000335f,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel76
0x00003360,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel77
0x00003361,0x00000000,c2m_ana_top_d2a_ch3_regnamedel78
0x00003362,0x00000039,c2m_ana_top_d2a_ch3_regnamedel79
0x00003363,0x00000020,c2m_ana_top_d2a_ch3_regnamedel80
0x00003364,0x00000004,c2m_ana_top_d2a_ch3_regnamedel81
0x00003365,0x00000006,c2m_ana_top_d2a_ch3_regnamedel82
0x00003366,0x0000003d,c2m_ana_top_d2a_ch3_regnamedel83
0x00003367,0x00000093,c2m_ana_top_d2a_ch3_regnamedel84
0x00003368,0x00000068,c2m_ana_top_d2a_ch3_regnamedel85
0x00003369,0x0000005c,c2m_ana_top_d2a_ch3_regnamedel86
0x0000336a,0x0000003c,c2m_ana_top_d2a_ch3_regnamedel87
0x0000336b,0x0000000b,c2m_ana_top_d2a_ch3_regnamedel88
0x0000336c,0x00000000,c2m_ana_top_d2a_ch3_regnamedel106
0x0000336d,0x00000000,c2m_ana_top_d2a_ch3_regnamedel107
0x0000336e,0x00000000,c2m_ana_top_d2a_ch3_regnamedel108
0x0000336f,0x00000000,c2m_ana_top_d2a_ch3_regnamedel109
0x00003370,0x00000000,c2m_ana_top_d2a_ch3_regnamedel110
0x00003371,0x00000064,c2m_ana_top_d2a_ch3_regnamedel111
0x00003372,0x00000000,c2m_ana_top_d2a_ch3_regnamedel112
0x00003373,0x0000002b,c2m_ana_top_d2a_ch3_regnamedel113
0x00003374,0x000000f4,c2m_ana_top_d2a_ch3_regnamedel114
0x00003375,0x00000040,c2m_ana_top_d2a_ch3_regnamedel115
0x00003376,0x000000d0,c2m_ana_top_d2a_ch3_regnamedel116
0x00003377,0x00000020,c2m_ana_top_d2a_ch3_regnamedel117
0x00003378,0x00000000,c2m_ana_top_d2a_ch3_regnamedel118
0x00003379,0x00000000,c2m_ana_top_d2a_ch3_regnamedel119
0x0000337a,0x00000020,c2m_ana_top_d2a_ch3_regnamedel120
0x0000337b,0x00000012,c2m_ana_top_d2a_ch3_regnamedel121
0x0000337c,0x00000060,c2m_ana_top_d2a_ch3_regnamedel122
0x0000337d,0x00000000,c2m_ana_top_d2a_ch3_regnamedel123
0x0000337e,0x00000000,c2m_ana_top_d2a_ch3_regnamedel124
0x0000337f,0x00000030,c2m_ana_top_d2a_ch3_regnamedel125
0x00003380,0x00000000,c2m_ana_top_d2a_ch3_regnamedel126
0x00003381,0x0000000c,c2m_ana_top_d2a_ch3_regnamedel127
0x00003382,0x000000dd,c2m_ana_top_d2a_ch3_regnamedel130
0x00003383,0x000000dd,c2m_ana_top_d2a_ch3_regnamedel131
0x00003384,0x0000007b,c2m_ana_top_d2a_ch3_regnamedel132
0x00003385,0x00000081,c2m_ana_top_d2a_ch3_regnamedel133
0x00003386,0x0000004a,c2m_ana_top_d2a_ch3_regnamedel134
0x00003387,0x000000c0,c2m_ana_top_d2a_ch3_regnamedel135
0x00003388,0x00000040,c2m_ana_top_d2a_ch3_regnamedel136
0x00003389,0x00000093,c2m_ana_top_d2a_ch3_regnamedel137
0x0000338a,0x000000c0,c2m_ana_top_d2a_ch3_regnamedel138
0x0000338b,0x000000c0,c2m_ana_top_d2a_ch3_regnamedel139
0x0000338c,0x00000040,c2m_ana_top_d2a_ch3_regnamedel140
0x0000338d,0x00000010,c2m_ana_top_d2a_ch3_regnamedel141
0x0000338e,0x0000009c,c2m_ana_top_d2a_ch3_regnamedel142
0x0000338f,0x00000080,c2m_ana_top_d2a_ch3_regnamedel143
0x00003390,0x0000008c,c2m_ana_top_d2a_ch3_regnamedel144
0x00003391,0x00000000,c2m_ana_top_d2a_ch3_regnamedel162
0x00003392,0x0000003f,c2m_ana_top_d2a_ch3_regnamedel163
0x00003393,0x0000001f,c2m_ana_top_d2a_ch3_regnamedel164
0x00003394,0x0000001f,c2m_ana_top_d2a_ch3_regnamedel165
0x00003395,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel166
0x00003396,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel167
0x00003397,0x0000000f,c2m_ana_top_d2a_ch3_regnamedel168
0x00003398,0x00000081,c2m_ana_top_d2a_ch3_regnamedel169
0x00003399,0x000000c1,c2m_ana_top_d2a_ch3_regnamedel170
0x0000339a,0x000000e1,c2m_ana_top_d2a_ch3_regnamedel171
0x0000339b,0x000000e1,c2m_ana_top_d2a_ch3_regnamedel172
0x0000339c,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel173
0x0000339d,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel174
0x0000339e,0x000000f1,c2m_ana_top_d2a_ch3_regnamedel175
0x0000339f,0x00000000,c2m_ana_top_d2a_ch3_regnamedel176
0x000033a0,0x00000000,c2m_ana_top_d2a_ch3_regnamedel177
0x000033a1,0x00000000,c2m_ana_top_d2a_ch3_regnamedel178
0x000033a2,0x00000000,c2m_ana_top_d2a_ch3_regnamedel179
0x000033a3,0x00000000,c2m_ana_top_d2a_ch3_regnamedel180
0x000033a4,0x00000000,c2m_ana_top_d2a_ch3_regnamedel181
0x000033a5,0x00000000,c2m_ana_top_d2a_ch3_regnamedel182
0x000033a6,0x00000059,c2m_ana_top_d2a_ch3_regnamedel183
0x000033a7,0x00000059,c2m_ana_top_d2a_ch3_regnamedel184
0x000033a8,0x00000049,c2m_ana_top_d2a_ch3_regnamedel185
0x000033a9,0x00000049,c2m_ana_top_d2a_ch3_regnamedel186
0x000033aa,0x00000039,c2m_ana_top_d2a_ch3_regnamedel187
0x000033ab,0x00000039,c2m_ana_top_d2a_ch3_regnamedel188
0x000033ac,0x00000039,c2m_ana_top_d2a_ch3_regnamedel189
0x000033ad,0x00000020,c2m_ana_top_d2a_ch3_regnamedel190
0x000033ae,0x00000020,c2m_ana_top_d2a_ch3_regnamedel191
0x000033af,0x00000020,c2m_ana_top_d2a_ch3_regnamedel192
0x000033b0,0x00000020,c2m_ana_top_d2a_ch3_regnamedel193
0x000033b1,0x00000020,c2m_ana_top_d2a_ch3_regnamedel194
0x000033b2,0x00000020,c2m_ana_top_d2a_ch3_regnamedel195
0x000033b3,0x00000020,c2m_ana_top_d2a_ch3_regnamedel196
0x000033b4,0x00000004,c2m_ana_top_d2a_ch3_regnamedel197
0x000033b5,0x00000004,c2m_ana_top_d2a_ch3_regnamedel198
0x000033b6,0x00000004,c2m_ana_top_d2a_ch3_regnamedel199
0x000033b7,0x00000004,c2m_ana_top_d2a_ch3_regnamedel200
0x000033b8,0x00000004,c2m_ana_top_d2a_ch3_regnamedel201
0x000033b9,0x00000004,c2m_ana_top_d2a_ch3_regnamedel202
0x000033ba,0x00000004,c2m_ana_top_d2a_ch3_regnamedel203
0x000033bb,0x00000020,c2m_ana_top_d2a_ch3_regnamedel204
0x000033bc,0x0000008f,c2m_ana_top_d2a_ch3_regnamedel205
0x000033bd,0x0000000e,c2m_ana_top_d2a_ch3_regnamedel206
0x000033be,0x000000fc,c2m_ana_top_d2a_ch3_regnamedel227
0x000033bf,0x00000013,c2m_ana_top_d2a_ch3_regnamedel228
0x00003400,0x00000000,c2m_ana_top_d2a_digldo_regnamedel
0x00003402,0x00000000,c2m_ana_top_d2a_bg_regnamedel0
0x00003403,0x00000010,c2m_ana_top_d2a_bg_regnamedel1
0x00003405,0x00000000,c2m_ana_top_d2a_ch01_regnamedel0
0x00003406,0x00000000,c2m_ana_top_d2a_ch01_regnamedel1
0x00003407,0x00000000,c2m_ana_top_d2a_ch01_regnamedel2
0x00003408,0x00000000,c2m_ana_top_d2a_ch01_regnamedel3
0x0000340a,0x00000000,c2m_ana_top_d2a_ch23_regnamedel0
0x0000340b,0x00000000,c2m_ana_top_d2a_ch23_regnamedel1
0x0000340c,0x00000000,c2m_ana_top_d2a_ch23_regnamedel2
0x0000340d,0x00000000,c2m_ana_top_d2a_ch23_regnamedel3
0x0000340f,0x000000bb,c2m_ana_top_d2a_glbck_regnamedel0
0x00003410,0x00000004,c2m_ana_top_d2a_glbck_regnamedel1
0x00003416,0x00000000,c2m_ana_top_d2a_xtal_regnamedel1
0x0000341a,0x00000015,c2m_ana_top_d2a_vgpll01_regnamedel0
0x0000341b,0x00000000,c2m_ana_top_d2a_vgpll01_regnamedel1
0x00003420,0x00000014,c2m_ana_top_d2a_vgpll0_regnamedel0
0x00003421,0x0000000f,c2m_ana_top_d2a_vgpll0_regnamedel1
0x00003422,0x00000001,c2m_ana_top_d2a_vgpll0_regnamedel2
0x00003423,0x00000000,c2m_ana_top_d2a_vgpll0_regnamedel3
0x00003424,0x00000000,c2m_ana_top_d2a_vgpll0_regnamedel4
0x00003425,0x0000000c,c2m_ana_top_d2a_vgpll0_regnamedel5
0x00003426,0x00000000,c2m_ana_top_d2a_vgpll0_regnamedel6
0x00003427,0x00000000,c2m_ana_top_d2a_vgpll0_regnamedel7
0x00003428,0x00000000,c2m_ana_top_d2a_vgpll0_regnamedel8
0x00003429,0x00000000,c2m_ana_top_d2a_vgpll0_regnamedel9
0x0000342a,0x000000d3,c2m_ana_top_d2a_vgpll0_regnamedel10
0x00003430,0x00000014,c2m_ana_top_d2a_vgpll1_regnamedel0
0x00003431,0x0000000f,c2m_ana_top_d2a_vgpll1_regnamedel1
0x00003432,0x00000001,c2m_ana_top_d2a_vgpll1_regnamedel2
0x00003433,0x00000000,c2m_ana_top_d2a_vgpll1_regnamedel3
0x00003434,0x00000080,c2m_ana_top_d2a_vgpll1_regnamedel4
0x00003435,0x0000002e,c2m_ana_top_d2a_vgpll1_regnamedel5
0x00003436,0x00000000,c2m_ana_top_d2a_vgpll1_regnamedel6
0x00003437,0x00000000,c2m_ana_top_d2a_vgpll1_regnamedel7
0x00003438,0x00000000,c2m_ana_top_d2a_vgpll1_regnamedel8
0x00003439,0x00000000,c2m_ana_top_d2a_vgpll1_regnamedel9
0x0000343a,0x000000d3,c2m_ana_top_d2a_vgpll1_regnamedel10
0x00003440,0x00000040,c2m_ana_top_d2a_linkpll_regnamedel0
0x00003441,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel1
0x00003442,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel2
0x00003443,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel3
0x00003444,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel4
0x00003445,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel5
0x00003446,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel6
0x00003447,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel7
0x00003448,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel8
0x00003449,0x00000070,c2m_ana_top_d2a_linkpll_regnamedel9
0x0000344a,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel10
0x0000344b,0x0000008b,c2m_ana_top_d2a_linkpll_regnamedel11
0x0000344c,0x00000010,c2m_ana_top_d2a_linkpll_regnamedel12
0x0000344d,0x00000000,c2m_ana_top_d2a_linkpll_regnamedel13
0x0000344e,0x00000010,c2m_ana_top_d2a_linkpll_regnamedel14
0x0000344f,0x000000a8,c2m_ana_top_d2a_linkpll_regnamedel15
0x00003450,0x000000b8,c2m_ana_top_d2a_linkpll_regnamedel16
0x00003451,0x00000034,c2m_ana_top_d2a_linkpll_regnamedel17
0x00003452,0x00000044,c2m_ana_top_d2a_linkpll_regnamedel18
0x00003453,0x00000038,c2m_ana_top_d2a_linkpll_regnamedel19
0x00003454,0x0000002c,c2m_ana_top_d2a_linkpll_regnamedel20
0x00003455,0x00000002,c2m_ana_top_d2a_linkpll_regnamedel21
0x00003456,0x000000a2,c2m_ana_top_d2a_linkpll_regnamedel22
0x00003460,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel0
0x00003461,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel1
0x00003462,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel2
0x00003463,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel3
0x00003464,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel4
0x00003465,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel5
0x00003466,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel6
0x00003467,0x00000000,c2m_ana_top_d2a_rsvd_regnamedel7
0x0000346a,0x00000000,c2m_ana_top_d2a_padtest_regnamedel0
0x0000346b,0x00000000,c2m_ana_top_d2a_padtest_regnamedel1
0x000035c8,0x00000000,c2m_ana_top_d2a_lf01_regnamedel0
0x000035c9,0x00000000,c2m_ana_top_d2a_lf01_regnamedel1
0x000035ca,0x00000000,c2m_ana_top_d2a_lf01_regnamedel2
0x000035cb,0x00000000,c2m_ana_top_d2a_lf01_regnamedel3
0x000035d0,0x00000000,c2m_ana_top_d2a_lf23_regnamedel0
0x000035d1,0x00000000,c2m_ana_top_d2a_lf23_regnamedel1
0x000035d2,0x00000000,c2m_ana_top_d2a_lf23_regnamedel2
0x000035d3,0x00000000,c2m_ana_top_d2a_lf23_regnamedel3
0x000035d8,0x00000000,c2m_ana_top_d2a_lf45_regnamedel0
0x000035d9,0x00000000,c2m_ana_top_d2a_lf45_regnamedel1
0x000035da,0x00000000,c2m_ana_top_d2a_lf45_regnamedel2
0x000035db,0x00000000,c2m_ana_top_d2a_lf45_regnamedel3
0x00003470,0x00000000,c2m_ana_top_a2d_ch0_regnamedel0
0x00003471,0x00000000,c2m_ana_top_a2d_ch0_regnamedel1
0x00003472,0x00000000,c2m_ana_top_a2d_ch0_regnamedel2
0x00003473,0x00000000,c2m_ana_top_a2d_ch0_regnamedel3
0x00003474,0x00000000,c2m_ana_top_a2d_ch0_regnamedel4
0x00003475,0x00000000,c2m_ana_top_a2d_ch0_regnamedel5
0x00003476,0x00000000,c2m_ana_top_a2d_ch0_regnamedel6
0x00003477,0x00000000,c2m_ana_top_a2d_ch0_regnamedel7
0x00003478,0x00000000,c2m_ana_top_a2d_ch0_regnamedel8
0x00003479,0x00000000,c2m_ana_top_a2d_ch0_regnamedel9
0x0000347a,0x00000000,c2m_ana_top_a2d_ch0_regnamedel10
0x0000347b,0x00000000,c2m_ana_top_a2d_ch0_regnamedel11
0x0000347c,0x00000000,c2m_ana_top_a2d_ch0_regnamedel12
0x0000347d,0x00000000,c2m_ana_top_a2d_ch0_regnamedel13
0x0000347e,0x00000000,c2m_ana_top_a2d_ch0_regnamedel14
0x0000347f,0x00000000,c2m_ana_top_a2d_ch0_regnamedel15
0x00003480,0x00000000,c2m_ana_top_a2d_ch0_regnamedel16
0x00003481,0x00000000,c2m_ana_top_a2d_ch0_regnamedel17
0x00003482,0x00000000,c2m_ana_top_a2d_ch0_regnamedel18
0x00003483,0x00000000,c2m_ana_top_a2d_ch0_regnamedel19
0x00003484,0x00000000,c2m_ana_top_a2d_ch0_regnamedel20
0x00003485,0x00000000,c2m_ana_top_a2d_ch0_regnamedel21
0x00003486,0x00000000,c2m_ana_top_a2d_ch0_regnamedel22
0x00003487,0x00000000,c2m_ana_top_a2d_ch0_regnamedel23
0x00003488,0x00000000,c2m_ana_top_a2d_ch0_regnamedel24
0x00003489,0x00000000,c2m_ana_top_a2d_ch0_regnamedel25
0x0000348a,0x00000000,c2m_ana_top_a2d_ch0_regnamedel26
0x0000348c,0x00000000,c2m_ana_top_a2d_ch0_regnamedel28
0x00003490,0x00000000,c2m_ana_top_a2d_ch0_regnamedel37
0x00003491,0x00000000,c2m_ana_top_a2d_ch0_regnamedel38
0x00003492,0x00000000,c2m_ana_top_a2d_ch0_regnamedel39
0x00003493,0x00000000,c2m_ana_top_a2d_ch0_regnamedel40
0x00003494,0x00000000,c2m_ana_top_a2d_ch0_regnamedel41
0x00003495,0x00000000,c2m_ana_top_a2d_ch0_regnamedel42
0x00003496,0x00000000,c2m_ana_top_a2d_ch0_regnamedel43
0x00003497,0x00000000,c2m_ana_top_a2d_ch0_regnamedel44
0x00003498,0x00000000,c2m_ana_top_a2d_ch0_regnamedel45
0x00003499,0x00000000,c2m_ana_top_a2d_ch0_regnamedel46
0x0000349a,0x00000000,c2m_ana_top_a2d_ch0_regnamedel47
0x0000349b,0x00000000,c2m_ana_top_a2d_ch0_regnamedel48
0x0000349c,0x00000000,c2m_ana_top_a2d_ch0_regnamedel49
0x000034c0,0x00000000,c2m_ana_top_a2d_ch1_regnamedel0
0x000034c1,0x00000000,c2m_ana_top_a2d_ch1_regnamedel1
0x000034c2,0x00000000,c2m_ana_top_a2d_ch1_regnamedel2
0x000034c3,0x00000000,c2m_ana_top_a2d_ch1_regnamedel3
0x000034c4,0x00000000,c2m_ana_top_a2d_ch1_regnamedel4
0x000034c5,0x00000000,c2m_ana_top_a2d_ch1_regnamedel5
0x000034c6,0x00000000,c2m_ana_top_a2d_ch1_regnamedel6
0x000034c7,0x00000000,c2m_ana_top_a2d_ch1_regnamedel7
0x000034c8,0x00000000,c2m_ana_top_a2d_ch1_regnamedel8
0x000034c9,0x00000000,c2m_ana_top_a2d_ch1_regnamedel9
0x000034ca,0x00000000,c2m_ana_top_a2d_ch1_regnamedel10
0x000034cb,0x00000000,c2m_ana_top_a2d_ch1_regnamedel11
0x000034cc,0x00000000,c2m_ana_top_a2d_ch1_regnamedel12
0x000034cd,0x00000000,c2m_ana_top_a2d_ch1_regnamedel13
0x000034ce,0x00000000,c2m_ana_top_a2d_ch1_regnamedel14
0x000034cf,0x00000000,c2m_ana_top_a2d_ch1_regnamedel15
0x000034d0,0x00000000,c2m_ana_top_a2d_ch1_regnamedel16
0x000034d1,0x00000000,c2m_ana_top_a2d_ch1_regnamedel17
0x000034d2,0x00000000,c2m_ana_top_a2d_ch1_regnamedel18
0x000034d3,0x00000000,c2m_ana_top_a2d_ch1_regnamedel19
0x000034d4,0x00000000,c2m_ana_top_a2d_ch1_regnamedel20
0x000034d5,0x00000000,c2m_ana_top_a2d_ch1_regnamedel21
0x000034d6,0x00000000,c2m_ana_top_a2d_ch1_regnamedel22
0x000034d7,0x00000000,c2m_ana_top_a2d_ch1_regnamedel23
0x000034d8,0x00000000,c2m_ana_top_a2d_ch1_regnamedel24
0x000034d9,0x00000000,c2m_ana_top_a2d_ch1_regnamedel25
0x000034da,0x00000000,c2m_ana_top_a2d_ch1_regnamedel26
0x000034dc,0x00000000,c2m_ana_top_a2d_ch1_regnamedel28
0x000034e0,0x00000000,c2m_ana_top_a2d_ch1_regnamedel37
0x000034e1,0x00000000,c2m_ana_top_a2d_ch1_regnamedel38
0x000034e2,0x00000000,c2m_ana_top_a2d_ch1_regnamedel39
0x000034e3,0x00000000,c2m_ana_top_a2d_ch1_regnamedel40
0x000034e4,0x00000000,c2m_ana_top_a2d_ch1_regnamedel41
0x000034e5,0x00000000,c2m_ana_top_a2d_ch1_regnamedel42
0x000034e6,0x00000000,c2m_ana_top_a2d_ch1_regnamedel43
0x000034e7,0x00000000,c2m_ana_top_a2d_ch1_regnamedel44
0x000034e8,0x00000000,c2m_ana_top_a2d_ch1_regnamedel45
0x000034e9,0x00000000,c2m_ana_top_a2d_ch1_regnamedel46
0x000034ea,0x00000000,c2m_ana_top_a2d_ch1_regnamedel47
0x000034eb,0x00000000,c2m_ana_top_a2d_ch1_regnamedel48
0x000034ec,0x00000000,c2m_ana_top_a2d_ch1_regnamedel49
0x00003510,0x00000000,c2m_ana_top_a2d_ch2_regnamedel0
0x00003511,0x00000000,c2m_ana_top_a2d_ch2_regnamedel1
0x00003512,0x00000000,c2m_ana_top_a2d_ch2_regnamedel2
0x00003513,0x00000000,c2m_ana_top_a2d_ch2_regnamedel3
0x00003514,0x00000000,c2m_ana_top_a2d_ch2_regnamedel4
0x00003515,0x00000000,c2m_ana_top_a2d_ch2_regnamedel5
0x00003516,0x00000000,c2m_ana_top_a2d_ch2_regnamedel6
0x00003517,0x00000000,c2m_ana_top_a2d_ch2_regnamedel7
0x00003518,0x00000000,c2m_ana_top_a2d_ch2_regnamedel8
0x00003519,0x00000000,c2m_ana_top_a2d_ch2_regnamedel9
0x0000351a,0x00000000,c2m_ana_top_a2d_ch2_regnamedel10
0x0000351b,0x00000000,c2m_ana_top_a2d_ch2_regnamedel11
0x0000351c,0x00000000,c2m_ana_top_a2d_ch2_regnamedel12
0x0000351d,0x00000000,c2m_ana_top_a2d_ch2_regnamedel13
0x0000351e,0x00000000,c2m_ana_top_a2d_ch2_regnamedel14
0x0000351f,0x00000000,c2m_ana_top_a2d_ch2_regnamedel15
0x00003520,0x00000000,c2m_ana_top_a2d_ch2_regnamedel16
0x00003521,0x00000000,c2m_ana_top_a2d_ch2_regnamedel17
0x00003522,0x00000000,c2m_ana_top_a2d_ch2_regnamedel18
0x00003523,0x00000000,c2m_ana_top_a2d_ch2_regnamedel19
0x00003524,0x00000000,c2m_ana_top_a2d_ch2_regnamedel20
0x00003525,0x00000000,c2m_ana_top_a2d_ch2_regnamedel21
0x00003526,0x00000000,c2m_ana_top_a2d_ch2_regnamedel22
0x00003527,0x00000000,c2m_ana_top_a2d_ch2_regnamedel23
0x00003528,0x00000000,c2m_ana_top_a2d_ch2_regnamedel24
0x00003529,0x00000000,c2m_ana_top_a2d_ch2_regnamedel25
0x0000352a,0x00000000,c2m_ana_top_a2d_ch2_regnamedel26
0x0000352c,0x00000000,c2m_ana_top_a2d_ch2_regnamedel28
0x00003530,0x00000000,c2m_ana_top_a2d_ch2_regnamedel37
0x00003531,0x00000000,c2m_ana_top_a2d_ch2_regnamedel38
0x00003532,0x00000000,c2m_ana_top_a2d_ch2_regnamedel39
0x00003533,0x00000000,c2m_ana_top_a2d_ch2_regnamedel40
0x00003534,0x00000000,c2m_ana_top_a2d_ch2_regnamedel41
0x00003535,0x00000000,c2m_ana_top_a2d_ch2_regnamedel42
0x00003536,0x00000000,c2m_ana_top_a2d_ch2_regnamedel43
0x00003537,0x00000000,c2m_ana_top_a2d_ch2_regnamedel44
0x00003538,0x00000000,c2m_ana_top_a2d_ch2_regnamedel45
0x00003539,0x00000000,c2m_ana_top_a2d_ch2_regnamedel46
0x0000353a,0x00000000,c2m_ana_top_a2d_ch2_regnamedel47
0x0000353b,0x00000000,c2m_ana_top_a2d_ch2_regnamedel48
0x0000353c,0x00000000,c2m_ana_top_a2d_ch2_regnamedel49
0x00003560,0x00000000,c2m_ana_top_a2d_ch3_regnamedel0
0x00003561,0x00000000,c2m_ana_top_a2d_ch3_regnamedel1
0x00003562,0x00000000,c2m_ana_top_a2d_ch3_regnamedel2
0x00003563,0x00000000,c2m_ana_top_a2d_ch3_regnamedel3
0x00003564,0x00000000,c2m_ana_top_a2d_ch3_regnamedel4
0x00003565,0x00000000,c2m_ana_top_a2d_ch3_regnamedel5
0x00003566,0x00000000,c2m_ana_top_a2d_ch3_regnamedel6
0x00003567,0x00000000,c2m_ana_top_a2d_ch3_regnamedel7
0x00003568,0x00000000,c2m_ana_top_a2d_ch3_regnamedel8
0x00003569,0x00000000,c2m_ana_top_a2d_ch3_regnamedel9
0x0000356a,0x00000000,c2m_ana_top_a2d_ch3_regnamedel10
0x0000356b,0x00000000,c2m_ana_top_a2d_ch3_regnamedel11
0x0000356c,0x00000000,c2m_ana_top_a2d_ch3_regnamedel12
0x0000356d,0x00000000,c2m_ana_top_a2d_ch3_regnamedel13
0x0000356e,0x00000000,c2m_ana_top_a2d_ch3_regnamedel14
0x0000356f,0x00000000,c2m_ana_top_a2d_ch3_regnamedel15
0x00003570,0x00000000,c2m_ana_top_a2d_ch3_regnamedel16
0x00003571,0x00000000,c2m_ana_top_a2d_ch3_regnamedel17
0x00003572,0x00000000,c2m_ana_top_a2d_ch3_regnamedel18
0x00003573,0x00000000,c2m_ana_top_a2d_ch3_regnamedel19
0x00003574,0x00000000,c2m_ana_top_a2d_ch3_regnamedel20
0x00003575,0x00000000,c2m_ana_top_a2d_ch3_regnamedel21
0x00003576,0x00000000,c2m_ana_top_a2d_ch3_regnamedel22
0x00003577,0x00000000,c2m_ana_top_a2d_ch3_regnamedel23
0x00003578,0x00000000,c2m_ana_top_a2d_ch3_regnamedel24
0x00003579,0x00000000,c2m_ana_top_a2d_ch3_regnamedel25
0x0000357a,0x00000000,c2m_ana_top_a2d_ch3_regnamedel26
0x0000357c,0x00000000,c2m_ana_top_a2d_ch3_regnamedel28
0x00003580,0x00000000,c2m_ana_top_a2d_ch3_regnamedel37
0x00003581,0x00000000,c2m_ana_top_a2d_ch3_regnamedel38
0x00003582,0x00000000,c2m_ana_top_a2d_ch3_regnamedel39
0x00003583,0x00000000,c2m_ana_top_a2d_ch3_regnamedel40
0x00003584,0x00000000,c2m_ana_top_a2d_ch3_regnamedel41
0x00003585,0x00000000,c2m_ana_top_a2d_ch3_regnamedel42
0x00003586,0x00000000,c2m_ana_top_a2d_ch3_regnamedel43
0x00003587,0x00000000,c2m_ana_top_a2d_ch3_regnamedel44
0x00003588,0x00000000,c2m_ana_top_a2d_ch3_regnamedel45
0x00003589,0x00000000,c2m_ana_top_a2d_ch3_regnamedel46
0x0000358a,0x00000000,c2m_ana_top_a2d_ch3_regnamedel47
0x0000358b,0x00000000,c2m_ana_top_a2d_ch3_regnamedel48
0x0000358c,0x00000000,c2m_ana_top_a2d_ch3_regnamedel49
0x000035b0,0x00000000,c2m_ana_top_a2d_linkpll_regnamedel0
0x000035b2,0x00000000,c2m_ana_top_a2d_rsvd_regnamedel0
0x000035b3,0x00000000,c2m_ana_top_a2d_rsvd_regnamedel1
0x000035b4,0x00000000,c2m_ana_top_a2d_rsvd_regnamedel2
0x000035b5,0x00000000,c2m_ana_top_a2d_rsvd_regnamedel3
0x000035bc,0x00000000,c2m_ana_top_a2d_device_regnamedel0
0x000035c0,0x00000000,c2m_ana_top_a2d_xtal_regnamedel0
0x00004000,0x00000000,c2m_vg0_config0
0x00004001,0x00000000,c2m_vg0_config1
0x00004002,0x00000000,c2m_vg0_v_id
0x00004003,0x00000000,c2m_vg0_p_id
0x00004004,0x00000000,c2m_vg0_ext_htotal_msb
0x00004005,0x00000000,c2m_vg0_ext_htotal_lsb
0x00004006,0x00000000,c2m_vg0_ext_hblank_msb
0x00004007,0x00000000,c2m_vg0_ext_hblank_lsb
0x00004008,0x00000000,c2m_vg0_ext_hfp_msb
0x00004009,0x00000000,c2m_vg0_ext_hfp_lsb
0x0000400a,0x00000000,c2m_vg0_ext_hbp_msb
0x0000400b,0x00000000,c2m_vg0_ext_hbp_lsb
0x0000400c,0x00000000,c2m_vg0_ext_vtotal_msb
0x0000400d,0x00000000,c2m_vg0_ext_vtotal_lsb
0x0000400e,0x00000000,c2m_vg0_ext_vblank_msb
0x0000400f,0x00000000,c2m_vg0_ext_vblank_lsb
0x00004010,0x00000000,c2m_vg0_ext_vfp_msb
0x00004011,0x00000000,c2m_vg0_ext_vfp_lsb
0x00004012,0x00000000,c2m_vg0_ext_vsync_msb
0x00004013,0x00000000,c2m_vg0_ext_vsync_lsb
0x00004014,0x00000000,c2m_vg0_vg_vc_id
0x00004015,0x00000000,c2m_vg0_vg_enable
0x00004016,0x00000001,c2m_vg0_yuv_config
0x00004017,0x00000000,c2m_vg0_pixel_cnt_0
0x00004018,0x00000000,c2m_vg0_pixel_cnt_1
0x00004019,0x00000000,c2m_vg0_line_cnt_0
0x0000401a,0x00000000,c2m_vg0_line_cnt_1
0x0000401b,0x00000000,c2m_vg0_fsm
0x0000401c,0x00000000,c2m_vg0_delay_cnt_0
0x0000401d,0x00000000,c2m_vg0_delay_cnt_1
0x0000401e,0x00000000,c2m_vg0_delay
0x00004100,0x00000000,c2m_vg1_config0
0x00004101,0x00000000,c2m_vg1_config1
0x00004102,0x00000000,c2m_vg1_v_id
0x00004103,0x00000000,c2m_vg1_p_id
0x00004104,0x00000000,c2m_vg1_ext_htotal_msb
0x00004105,0x00000000,c2m_vg1_ext_htotal_lsb
0x00004106,0x00000000,c2m_vg1_ext_hblank_msb
0x00004107,0x00000000,c2m_vg1_ext_hblank_lsb
0x00004108,0x00000000,c2m_vg1_ext_hfp_msb
0x00004109,0x00000000,c2m_vg1_ext_hfp_lsb
0x0000410a,0x00000000,c2m_vg1_ext_hbp_msb
0x0000410b,0x00000000,c2m_vg1_ext_hbp_lsb
0x0000410c,0x00000000,c2m_vg1_ext_vtotal_msb
0x0000410d,0x00000000,c2m_vg1_ext_vtotal_lsb
0x0000410e,0x00000000,c2m_vg1_ext_vblank_msb
0x0000410f,0x00000000,c2m_vg1_ext_vblank_lsb
0x00004110,0x00000000,c2m_vg1_ext_vfp_msb
0x00004111,0x00000000,c2m_vg1_ext_vfp_lsb
0x00004112,0x00000000,c2m_vg1_ext_vsync_msb
0x00004113,0x00000000,c2m_vg1_ext_vsync_lsb
0x00004114,0x00000000,c2m_vg1_vg_vc_id
0x00004115,0x00000000,c2m_vg1_vg_enable
0x00004116,0x00000001,c2m_vg1_yuv_config
0x00004117,0x00000000,c2m_vg1_pixel_cnt_0
0x00004118,0x00000000,c2m_vg1_pixel_cnt_1
0x00004119,0x00000000,c2m_vg1_line_cnt_0
0x0000411a,0x00000000,c2m_vg1_line_cnt_1
0x0000411b,0x00000000,c2m_vg1_fsm
0x0000411c,0x00000000,c2m_vg1_delay_cnt_0
0x0000411d,0x00000000,c2m_vg1_delay_cnt_1
0x0000411e,0x00000000,c2m_vg1_delay
0x00004200,0x00000000,c2m_vg2_config0
0x00004201,0x00000000,c2m_vg2_config1
0x00004202,0x00000000,c2m_vg2_v_id
0x00004203,0x00000000,c2m_vg2_p_id
0x00004204,0x00000000,c2m_vg2_ext_htotal_msb
0x00004205,0x00000000,c2m_vg2_ext_htotal_lsb
0x00004206,0x00000000,c2m_vg2_ext_hblank_msb
0x00004207,0x00000000,c2m_vg2_ext_hblank_lsb
0x00004208,0x00000000,c2m_vg2_ext_hfp_msb
0x00004209,0x00000000,c2m_vg2_ext_hfp_lsb
0x0000420a,0x00000000,c2m_vg2_ext_hbp_msb
0x0000420b,0x00000000,c2m_vg2_ext_hbp_lsb
0x0000420c,0x00000000,c2m_vg2_ext_vtotal_msb
0x0000420d,0x00000000,c2m_vg2_ext_vtotal_lsb
0x0000420e,0x00000000,c2m_vg2_ext_vblank_msb
0x0000420f,0x00000000,c2m_vg2_ext_vblank_lsb
0x00004210,0x00000000,c2m_vg2_ext_vfp_msb
0x00004211,0x00000000,c2m_vg2_ext_vfp_lsb
0x00004212,0x00000000,c2m_vg2_ext_vsync_msb
0x00004213,0x00000000,c2m_vg2_ext_vsync_lsb
0x00004214,0x00000000,c2m_vg2_vg_vc_id
0x00004215,0x00000000,c2m_vg2_vg_enable
0x00004216,0x00000001,c2m_vg2_yuv_config
0x00004217,0x00000000,c2m_vg2_pixel_cnt_0
0x00004218,0x00000000,c2m_vg2_pixel_cnt_1
0x00004219,0x00000000,c2m_vg2_line_cnt_0
0x0000421a,0x00000000,c2m_vg2_line_cnt_1
0x0000421b,0x00000000,c2m_vg2_fsm
0x0000421c,0x00000000,c2m_vg2_delay_cnt_0
0x0000421d,0x00000000,c2m_vg2_delay_cnt_1
0x0000421e,0x00000000,c2m_vg2_delay
0x00004300,0x00000000,c2m_vg3_config0
0x00004301,0x00000000,c2m_vg3_config1
0x00004302,0x00000000,c2m_vg3_v_id
0x00004303,0x00000000,c2m_vg3_p_id
0x00004304,0x00000000,c2m_vg3_ext_htotal_msb
0x00004305,0x00000000,c2m_vg3_ext_htotal_lsb
0x00004306,0x00000000,c2m_vg3_ext_hblank_msb
0x00004307,0x00000000,c2m_vg3_ext_hblank_lsb
0x00004308,0x00000000,c2m_vg3_ext_hfp_msb
0x00004309,0x00000000,c2m_vg3_ext_hfp_lsb
0x0000430a,0x00000000,c2m_vg3_ext_hbp_msb
0x0000430b,0x00000000,c2m_vg3_ext_hbp_lsb
0x0000430c,0x00000000,c2m_vg3_ext_vtotal_msb
0x0000430d,0x00000000,c2m_vg3_ext_vtotal_lsb
0x0000430e,0x00000000,c2m_vg3_ext_vblank_msb
0x0000430f,0x00000000,c2m_vg3_ext_vblank_lsb
0x00004310,0x00000000,c2m_vg3_ext_vfp_msb
0x00004311,0x00000000,c2m_vg3_ext_vfp_lsb
0x00004312,0x00000000,c2m_vg3_ext_vsync_msb
0x00004313,0x00000000,c2m_vg3_ext_vsync_lsb
0x00004314,0x00000000,c2m_vg3_vg_vc_id
0x00004315,0x00000000,c2m_vg3_vg_enable
0x00004316,0x00000001,c2m_vg3_yuv_config
0x00004317,0x00000000,c2m_vg3_pixel_cnt_0
0x00004318,0x00000000,c2m_vg3_pixel_cnt_1
0x00004319,0x00000000,c2m_vg3_line_cnt_0
0x0000431a,0x00000000,c2m_vg3_line_cnt_1
0x0000431b,0x00000000,c2m_vg3_fsm
0x0000431c,0x00000000,c2m_vg3_delay_cnt_0
0x0000431d,0x00000000,c2m_vg3_delay_cnt_1
0x0000431e,0x00000000,c2m_vg3_delay
0x00004400,0x00000000,c2m_pinmux_regmap_io_ow_en[0]
0x00004401,0x00000000,c2m_pinmux_regmap_io_ow_en[1]
0x00004402,0x00000000,c2m_pinmux_regmap_io_ow_en[2]
0x00004403,0x00000000,c2m_pinmux_regmap_io_ow_en[3]
0x00004404,0x00000000,c2m_pinmux_regmap_io_ow_en[4]
0x00004405,0x00000000,c2m_pinmux_regmap_io_ow_en[5]
0x00004406,0x00000000,c2m_pinmux_regmap_io_ow_en[6]
0x00004407,0x00000000,c2m_pinmux_regmap_io_ow_en[7]
0x00004408,0x00000000,c2m_pinmux_regmap_io_ow_en[8]
0x00004409,0x00000000,c2m_pinmux_regmap_io_ow_en[9]
0x0000440a,0x00000000,c2m_pinmux_regmap_io_ow_en[10]
0x0000440b,0x00000000,c2m_pinmux_regmap_io_ow_en[11]
0x0000440c,0x00000000,c2m_pinmux_regmap_io_ow_en[12]
0x0000440d,0x00000000,c2m_pinmux_regmap_io_ow_en[13]
0x0000440e,0x00000000,c2m_pinmux_regmap_io_ow_en[14]
0x0000440f,0x00000000,c2m_pinmux_regmap_io_ow_en[15]
0x00004410,0x00000000,c2m_pinmux_regmap_io_ow_en[16]
0x00004411,0x00000000,c2m_pinmux_regmap_io_ow_en[17]
0x00004412,0x00000000,c2m_pinmux_regmap_io_ow_en[18]
0x00004413,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[0]
0x00004414,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[1]
0x00004415,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[2]
0x00004416,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[3]
0x00004417,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[4]
0x00004418,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[5]
0x00004419,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[6]
0x0000441a,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[7]
0x0000441b,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[8]
0x0000441c,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[9]
0x0000441d,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[10]
0x0000441e,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[11]
0x0000441f,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[12]
0x00004420,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[13]
0x00004421,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[14]
0x00004422,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[15]
0x00004423,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[16]
0x00004424,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[17]
0x00004425,0x00000001,c2m_pinmux_regmap_io_ow_ctrl[18]
0x00004426,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[0]
0x00004427,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[1]
0x00004428,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[2]
0x00004429,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[3]
0x0000442a,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[4]
0x0000442b,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[5]
0x0000442c,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[6]
0x0000442d,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[7]
0x0000442e,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[8]
0x0000442f,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[9]
0x00004430,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[10]
0x00004431,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl0[11]
0x00004432,0x0000001c,c2m_pinmux_regmap_gpio_soft_ctrl0[12]
0x00004433,0x0000001d,c2m_pinmux_regmap_gpio_soft_ctrl0[13]
0x00004434,0x0000001e,c2m_pinmux_regmap_gpio_soft_ctrl0[14]
0x00004435,0x0000001f,c2m_pinmux_regmap_gpio_soft_ctrl0[15]
0x00004436,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[0]
0x00004437,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[1]
0x00004438,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[2]
0x00004439,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[3]
0x0000443a,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[4]
0x0000443b,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[5]
0x0000443c,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[6]
0x0000443d,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[7]
0x0000443e,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[8]
0x0000443f,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[9]
0x00004440,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[10]
0x00004441,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[11]
0x00004442,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[12]
0x00004443,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[13]
0x00004444,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[14]
0x00004445,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl1[15]
0x00004446,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl2[0]
0x00004447,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl2[1]
0x00004448,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl2[2]
0x00004449,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl2[3]
0x0000444a,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[0]
0x0000444b,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[1]
0x0000444c,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[2]
0x0000444d,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[3]
0x0000444e,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[4]
0x0000444f,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[5]
0x00004450,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[6]
0x00004451,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[7]
0x00004452,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[8]
0x00004453,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[9]
0x00004454,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[10]
0x00004455,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[11]
0x00004456,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[12]
0x00004457,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[13]
0x00004458,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[14]
0x00004459,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[15]
0x0000445a,0x00000000,c2m_pinmux_regmap_gpio_soft_ctrl3[16]
0x0000445b,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[0]
0x0000445c,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[1]
0x0000445d,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[2]
0x0000445e,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[3]
0x0000445f,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[4]
0x00004460,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[5]
0x00004461,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[6]
0x00004462,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[7]
0x00004463,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[8]
0x00004464,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[9]
0x00004465,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[10]
0x00004466,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[11]
0x00004467,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[12]
0x00004468,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[13]
0x00004469,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[14]
0x0000446a,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[15]
0x0000446b,0x0000003d,c2m_pinmux_regmap_gpio_soft_ctrl4[16]
0x0000446c,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl0
0x0000446d,0x00000001,c2m_pinmux_65q68_mfp_pin_ctrl1
0x0000446e,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl2
0x0000446f,0x00000001,c2m_pinmux_65q68_mfp_pin_ctrl3
0x00004470,0x00000001,c2m_pinmux_65q68_mfp_pin_ctrl4
0x00004471,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl5
0x00004472,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl6
0x00004473,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl7
0x00004474,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl8
0x00004475,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl9
0x00004476,0x0000000f,c2m_pinmux_65q68_mfp_pin_ctrl10
0x00004477,0x00000002,c2m_pinmux_65q68_mfp_pin_ctrl11
0x00004478,0x00000002,c2m_pinmux_65q68_mfp_pin_ctrl12
0x00004479,0x0000001f,c2m_pinmux_65q68_mfp_pin_ctrl13
0x0000447a,0x00000001,c2m_pinmux_65q68_mfp_pin_ctrl14
0x0000447b,0x00000001,c2m_pinmux_65q68_mfp_pin_ctrl15
0x0000447c,0x00000001,c2m_pinmux_65q68_mfp_pin_ctrl16
0x00004480,0x0000000f,c2m_pinmux_65q68e_mfp_pin_ctrl0
0x00004481,0x0000000f,c2m_pinmux_65q68e_mfp_pin_ctrl1
0x00004482,0x0000000f,c2m_pinmux_65q68e_mfp_pin_ctrl2
0x00004483,0x0000000f,c2m_pinmux_65q68e_mfp_pin_ctrl3
0x00004484,0x00000001,c2m_pinmux_65q68e_mfp_pin_ctrl4
0x00004485,0x00000001,c2m_pinmux_65q68e_mfp_pin_ctrl5
0x00004486,0x0000001f,c2m_pinmux_65q68e_mfp_pin_ctrl6
0x00004487,0x00000001,c2m_pinmux_65q68e_mfp_pin_ctrl7
0x00004488,0x00000001,c2m_pinmux_65q68e_mfp_pin_ctrl8
0x00004489,0x00000001,c2m_pinmux_65q68e_mfp_pin_ctrl9
0x0000448a,0x00000001,c2m_pinmux_65q68e_mfp_pin_ctrl10
0x00004490,0x0000000f,c2m_pinmux_65d68_mfp_pin_ctrl0
0x00004491,0x00000001,c2m_pinmux_65d68_mfp_pin_ctrl1
0x00004492,0x0000001f,c2m_pinmux_65d68_mfp_pin_ctrl2
0x00004493,0x0000001f,c2m_pinmux_65d68_mfp_pin_ctrl3
0x00004494,0x00000001,c2m_pinmux_65d68_mfp_pin_ctrl4
0x00004495,0x00000001,c2m_pinmux_65d68_mfp_pin_ctrl5
0x00004496,0x00000001,c2m_pinmux_65d68_mfp_pin_ctrl6
0x00004497,0x0000000f,c2m_pinmux_65d68_mfp_pin_ctrl7
0x00004498,0x0000000f,c2m_pinmux_65d68_mfp_pin_ctrl8
0x00004499,0x0000000f,c2m_pinmux_65d68_mfp_pin_ctrl9
0x0000449a,0x0000000f,c2m_pinmux_65d68_mfp_pin_ctrl10
0x0000449b,0x00000001,c2m_pinmux_65d68_mfp_pin_ctrl11
0x0000449c,0x00000001,c2m_pinmux_65d68_mfp_pin_ctrl12
0x00005000,0x00000000,c2m_test_glb_ctrl0
0x00005001,0x00000000,c2m_test_scan_ctrl
0x00005002,0x00000000,c2m_test_tx_prbs_ctrl[0]
0x00005007,0x00000000,c2m_test_tx_prbs_ctrl[1]
0x0000500c,0x00000000,c2m_test_tx_prbs_ctrl[2]
0x00005011,0x00000000,c2m_test_tx_prbs_ctrl[3]
0x00005016,0x00000000,c2m_test_rx_prbs_ctrl[0]
0x0000501b,0x00000000,c2m_test_rx_prbs_ctrl[1]
0x00005020,0x00000000,c2m_test_rx_prbs_ctrl[2]
0x00005025,0x00000000,c2m_test_rx_prbs_ctrl[3]
0x0000502a,0x00000000,c2m_test_rx_prbs_status0[0]
0x0000502f,0x00000000,c2m_test_rx_prbs_status0[1]
0x00005034,0x00000000,c2m_test_rx_prbs_status0[2]
0x00005039,0x00000000,c2m_test_rx_prbs_status0[3]
0x0000503e,0x00000000,c2m_test_rx_prbs_status1[0]
0x00005043,0x00000000,c2m_test_rx_prbs_status1[1]
0x00005048,0x00000000,c2m_test_rx_prbs_status1[2]
0x0000504d,0x00000000,c2m_test_rx_prbs_status1[3]
0x00005052,0x00000000,c2m_test_rx_prbs_status2[0]
0x00005057,0x00000000,c2m_test_rx_prbs_status2[1]
0x0000505c,0x00000000,c2m_test_rx_prbs_status2[2]
0x00005061,0x00000000,c2m_test_rx_prbs_status2[3]
0x00005066,0x0000000c,c2m_test_cfg_adc_ctrl[0]
0x00005068,0x0000000c,c2m_test_cfg_adc_ctrl[1]
0x0000506a,0x0000000c,c2m_test_cfg_adc_ctrl[2]
0x0000506c,0x00000000,c2m_test_cfg_adc_status[0]
0x0000506e,0x00000000,c2m_test_cfg_adc_status[1]
0x00005070,0x00000000,c2m_test_cfg_adc_status[2]
0x00005072,0x00000000,c2m_test_fsm_ctrl
0x00005073,0x00000000,c2m_test_fsm_status0
0x00005074,0x00000000,c2m_test_fsm_status1
0x00005075,0x00000000,c2m_test_fsm_status2
0x00005076,0x00000000,c2m_test_fsm_status3
0x00005077,0x00000000,c2m_test_fsm_status4
0x00005109,0x00000000,c2m_test_dbg_ctrl[0]
0x0000510a,0x00000000,c2m_test_dbg_ctrl[1]
0x0000510b,0x00000000,c2m_test_link_dbg_status0[0]
0x00005117,0x00000000,c2m_test_link_dbg_status0[1]
0x00005123,0x00000000,c2m_test_link_dbg_status0[2]
0x0000512f,0x00000000,c2m_test_link_dbg_status0[3]
0x0000510c,0x00000000,c2m_test_link_dbg_status1[0]
0x00005118,0x00000000,c2m_test_link_dbg_status1[1]
0x00005124,0x00000000,c2m_test_link_dbg_status1[2]
0x00005130,0x00000000,c2m_test_link_dbg_status1[3]
0x0000510d,0x00000000,c2m_test_link_dbg_status2[0]
0x00005119,0x00000000,c2m_test_link_dbg_status2[1]
0x00005125,0x00000000,c2m_test_link_dbg_status2[2]
0x00005131,0x00000000,c2m_test_link_dbg_status2[3]
0x0000510e,0x00000000,c2m_test_link_dbg_status3[0]
0x0000511a,0x00000000,c2m_test_link_dbg_status3[1]
0x00005126,0x00000000,c2m_test_link_dbg_status3[2]
0x00005132,0x00000000,c2m_test_link_dbg_status3[3]
0x0000510f,0x00000000,c2m_test_link_dbg_status4[0]
0x0000511b,0x00000000,c2m_test_link_dbg_status4[1]
0x00005127,0x00000000,c2m_test_link_dbg_status4[2]
0x00005133,0x00000000,c2m_test_link_dbg_status4[3]
0x00005110,0x00000000,c2m_test_link_dbg_status5[0]
0x0000511c,0x00000000,c2m_test_link_dbg_status5[1]
0x00005128,0x00000000,c2m_test_link_dbg_status5[2]
0x00005134,0x00000000,c2m_test_link_dbg_status5[3]
0x00005111,0x00000000,c2m_test_link_dbg_status6[0]
0x0000511d,0x00000000,c2m_test_link_dbg_status6[1]
0x00005129,0x00000000,c2m_test_link_dbg_status6[2]
0x00005135,0x00000000,c2m_test_link_dbg_status6[3]
0x00005112,0x00000000,c2m_test_link_dbg_status7[0]
0x0000511e,0x00000000,c2m_test_link_dbg_status7[1]
0x0000512a,0x00000000,c2m_test_link_dbg_status7[2]
0x00005136,0x00000000,c2m_test_link_dbg_status7[3]
0x0000514e,0x0000000f,c2m_test_tx_link_data_inv
0x0000514f,0x0000000f,c2m_test_rx_link_data_inv
0x00005150,0x00000000,c2m_test_tx_usr_ctrl
0x00005151,0x00000000,c2m_test_tx_usr_patn[0]
0x00005152,0x00000000,c2m_test_tx_usr_patn[1]
0x00005153,0x00000000,c2m_test_tx_usr_patn[2]
0x00005154,0x00000000,c2m_test_tx_usr_patn[3]
0x00005155,0x00000000,c2m_test_tx_usr_patn[4]
0x00005156,0x00000000,c2m_test_tx_usr_patn[5]
0x00005157,0x00000000,c2m_test_tx_usr_patn[6]
0x00005158,0x00000000,c2m_test_tx_usr_patn[7]
0x00005159,0x00000000,c2m_test_tx_usr_patn[8]
0x0000515a,0x00000000,c2m_test_tx_usr_patn[9]
0x0000515b,0x00000000,c2m_test_rx_raw_ctrl
0x0000515c,0x00000000,c2m_test_rx_raw_data[0]
0x0000515d,0x00000000,c2m_test_rx_raw_data[1]
0x0000515e,0x00000000,c2m_test_rx_raw_data[2]
0x0000515f,0x00000000,c2m_test_rx_raw_data[3]
0x00005160,0x00000000,c2m_test_rx_raw_data[4]
0x00005161,0x00000000,c2m_test_rx_raw_data[5]
0x00005162,0x00000000,c2m_test_rx_raw_data[6]
0x00005163,0x00000000,c2m_test_rx_raw_data[7]
0x00005164,0x00000000,c2m_test_rx_raw_data[8]
0x00005165,0x00000000,c2m_test_rx_raw_data[9]
0x00005166,0x00000000,c2m_test_fusa_ecc_en
0x00005167,0x00000000,c2m_test_fec_ecc_err_inject_en
0x00005168,0x00000000,c2m_test_fec_ecc_err_inject_1bit_tg
0x00005169,0x00000000,c2m_test_fec_ecc_err_inject_2bit_tg
0x0000516a,0x00000000,c2m_test_router_ecc_err_inject_en
0x0000516b,0x00000000,c2m_test_router_ecc_err_inject_1bit_tg
0x0000516c,0x00000000,c2m_test_router_ecc_err_inject_2bit_tg
0x0000516d,0x00000002,c2m_test_mbist_ctrl
0x0000516e,0x00000000,c2m_test_mbist_status
0x0000516f,0x00000012,c2m_test_mem_cfg
0x00005179,0x00000000,c2m_test_vbist_ctrl
0x0000517a,0x00000000,c2m_test_vbist_status
0x0000517b,0x00000000,c2m_test_rx_link_ctrl0
0x0000517c,0x00000000,c2m_test_rx_link_ctrl1
0x0000517d,0x00000000,c2m_test_tx_link0_dbg0[0]
0x0000517e,0x00000000,c2m_test_tx_link0_dbg0[1]
0x0000517f,0x00000000,c2m_test_tx_link0_dbg0[2]
0x00005180,0x00000000,c2m_test_tx_link0_dbg1[0]
0x00005181,0x00000000,c2m_test_tx_link0_dbg1[1]
0x00005182,0x00000000,c2m_test_tx_link0_dbg1[2]
0x00005183,0x00000000,c2m_test_tx_link0_dbg2[0]
0x00005184,0x00000000,c2m_test_tx_link0_dbg2[1]
0x00005185,0x00000000,c2m_test_tx_link0_dbg2[2]
0x00005186,0x00000000,c2m_test_tx_link0_dbg3[0]
0x00005187,0x00000000,c2m_test_tx_link0_dbg3[1]
0x00005188,0x00000000,c2m_test_tx_link0_dbg3[2]
0x00005189,0x00000000,c2m_test_tx_link0_dbg4
0x0000518a,0x00000000,c2m_test_tx_link0_dbg5
0x0000518b,0x00000000,c2m_test_tx_link0_dbg6[0]
0x0000518c,0x00000000,c2m_test_tx_link0_dbg6[1]
0x0000518d,0x00000000,c2m_test_tx_link0_dbg7[0]
0x0000518e,0x00000000,c2m_test_tx_link0_dbg7[1]
0x0000518f,0x00000000,c2m_test_tx_link0_dbg8
0x00005190,0x00000000,c2m_test_tx_link0_dbg9
0x000051a0,0x00000000,c2m_test_tx_link1_dbg0[0]
0x000051a1,0x00000000,c2m_test_tx_link1_dbg0[1]
0x000051a2,0x00000000,c2m_test_tx_link1_dbg0[2]
0x000051a3,0x00000000,c2m_test_tx_link1_dbg1[0]
0x000051a4,0x00000000,c2m_test_tx_link1_dbg1[1]
0x000051a5,0x00000000,c2m_test_tx_link1_dbg1[2]
0x000051a6,0x00000000,c2m_test_tx_link1_dbg2[0]
0x000051a7,0x00000000,c2m_test_tx_link1_dbg2[1]
0x000051a8,0x00000000,c2m_test_tx_link1_dbg2[2]
0x000051a9,0x00000000,c2m_test_tx_link1_dbg3[0]
0x000051aa,0x00000000,c2m_test_tx_link1_dbg3[1]
0x000051ab,0x00000000,c2m_test_tx_link1_dbg3[2]
0x000051ac,0x00000000,c2m_test_tx_link1_dbg4
0x000051ad,0x00000000,c2m_test_tx_link1_dbg5
0x000051ae,0x00000000,c2m_test_tx_link1_dbg6[0]
0x000051af,0x00000000,c2m_test_tx_link1_dbg6[1]
0x000051b0,0x00000000,c2m_test_tx_link1_dbg7[0]
0x000051b1,0x00000000,c2m_test_tx_link1_dbg7[1]
0x000051b2,0x00000000,c2m_test_tx_link1_dbg8
0x000051b3,0x00000000,c2m_test_tx_link1_dbg9
0x000051c0,0x00000000,c2m_test_tx_link2_dbg0[0]
0x000051c1,0x00000000,c2m_test_tx_link2_dbg0[1]
0x000051c2,0x00000000,c2m_test_tx_link2_dbg0[2]
0x000051c3,0x00000000,c2m_test_tx_link2_dbg1[0]
0x000051c4,0x00000000,c2m_test_tx_link2_dbg1[1]
0x000051c5,0x00000000,c2m_test_tx_link2_dbg1[2]
0x000051c6,0x00000000,c2m_test_tx_link2_dbg2[0]
0x000051c7,0x00000000,c2m_test_tx_link2_dbg2[1]
0x000051c8,0x00000000,c2m_test_tx_link2_dbg2[2]
0x000051c9,0x00000000,c2m_test_tx_link2_dbg3[0]
0x000051ca,0x00000000,c2m_test_tx_link2_dbg3[1]
0x000051cb,0x00000000,c2m_test_tx_link2_dbg3[2]
0x000051cc,0x00000000,c2m_test_tx_link2_dbg4
0x000051cd,0x00000000,c2m_test_tx_link2_dbg5
0x000051ce,0x00000000,c2m_test_tx_link2_dbg6[0]
0x000051cf,0x00000000,c2m_test_tx_link2_dbg6[1]
0x000051d0,0x00000000,c2m_test_tx_link2_dbg7[0]
0x000051d1,0x00000000,c2m_test_tx_link2_dbg7[1]
0x000051d2,0x00000000,c2m_test_tx_link2_dbg8
0x000051d3,0x00000000,c2m_test_tx_link2_dbg9
0x000051e0,0x00000000,c2m_test_tx_link3_dbg0[0]
0x000051e1,0x00000000,c2m_test_tx_link3_dbg0[1]
0x000051e2,0x00000000,c2m_test_tx_link3_dbg0[2]
0x000051e3,0x00000000,c2m_test_tx_link3_dbg1[0]
0x000051e4,0x00000000,c2m_test_tx_link3_dbg1[1]
0x000051e5,0x00000000,c2m_test_tx_link3_dbg1[2]
0x000051e6,0x00000000,c2m_test_tx_link3_dbg2[0]
0x000051e7,0x00000000,c2m_test_tx_link3_dbg2[1]
0x000051e8,0x00000000,c2m_test_tx_link3_dbg2[2]
0x000051e9,0x00000000,c2m_test_tx_link3_dbg3[0]
0x000051ea,0x00000000,c2m_test_tx_link3_dbg3[1]
0x000051eb,0x00000000,c2m_test_tx_link3_dbg3[2]
0x000051ec,0x00000000,c2m_test_tx_link3_dbg4
0x000051ed,0x00000000,c2m_test_tx_link3_dbg5
0x000051ee,0x00000000,c2m_test_tx_link3_dbg6[0]
0x000051ef,0x00000000,c2m_test_tx_link3_dbg6[1]
0x000051f0,0x00000000,c2m_test_tx_link3_dbg7[0]
0x000051f1,0x00000000,c2m_test_tx_link3_dbg7[1]
0x000051f2,0x00000000,c2m_test_tx_link3_dbg8
0x000051f3,0x00000000,c2m_test_tx_link3_dbg9
0x00005200,0x00000000,c2m_test_rx_link0_dbg0[0]
0x00005201,0x00000000,c2m_test_rx_link0_dbg0[1]
0x00005202,0x00000000,c2m_test_rx_link0_dbg0[2]
0x00005203,0x00000000,c2m_test_rx_link0_dbg1[0]
0x00005204,0x00000000,c2m_test_rx_link0_dbg1[1]
0x00005205,0x00000000,c2m_test_rx_link0_dbg1[2]
0x00005206,0x00000000,c2m_test_rx_link0_dbg2
0x00005207,0x00000000,c2m_test_rx_link0_dbg3[0]
0x00005208,0x00000000,c2m_test_rx_link0_dbg3[1]
0x00005209,0x00000000,c2m_test_rx_link0_dbg4
0x00005210,0x00000000,c2m_test_rx_link1_dbg0[0]
0x00005211,0x00000000,c2m_test_rx_link1_dbg0[1]
0x00005212,0x00000000,c2m_test_rx_link1_dbg0[2]
0x00005213,0x00000000,c2m_test_rx_link1_dbg1[0]
0x00005214,0x00000000,c2m_test_rx_link1_dbg1[1]
0x00005215,0x00000000,c2m_test_rx_link1_dbg1[2]
0x00005216,0x00000000,c2m_test_rx_link1_dbg2
0x00005217,0x00000000,c2m_test_rx_link1_dbg3[0]
0x00005218,0x00000000,c2m_test_rx_link1_dbg3[1]
0x00005219,0x00000000,c2m_test_rx_link1_dbg4
0x00005220,0x00000000,c2m_test_rx_link2_dbg0[0]
0x00005221,0x00000000,c2m_test_rx_link2_dbg0[1]
0x00005222,0x00000000,c2m_test_rx_link2_dbg0[2]
0x00005223,0x00000000,c2m_test_rx_link2_dbg1[0]
0x00005224,0x00000000,c2m_test_rx_link2_dbg1[1]
0x00005225,0x00000000,c2m_test_rx_link2_dbg1[2]
0x00005226,0x00000000,c2m_test_rx_link2_dbg2
0x00005227,0x00000000,c2m_test_rx_link2_dbg3[0]
0x00005228,0x00000000,c2m_test_rx_link2_dbg3[1]
0x00005229,0x00000000,c2m_test_rx_link2_dbg4
0x00005230,0x00000000,c2m_test_rx_link3_dbg0[0]
0x00005231,0x00000000,c2m_test_rx_link3_dbg0[1]
0x00005232,0x00000000,c2m_test_rx_link3_dbg0[2]
0x00005233,0x00000000,c2m_test_rx_link3_dbg1[0]
0x00005234,0x00000000,c2m_test_rx_link3_dbg1[1]
0x00005235,0x00000000,c2m_test_rx_link3_dbg1[2]
0x00005236,0x00000000,c2m_test_rx_link3_dbg2
0x00005237,0x00000000,c2m_test_rx_link3_dbg3[0]
0x00005238,0x00000000,c2m_test_rx_link3_dbg3[1]
0x00005239,0x00000000,c2m_test_rx_link3_dbg4
0x0000523a,0x00000000,c2m_test_dbg_gpio_fifo
0x0000523b,0x00000000,c2m_test_dbg_fs_fifo
0x0000523c,0x00000000,c2m_test_tx_link_ctrl3
0x0000523d,0x00000000,c2m_test_rx_link_ctrl3
0x0000523e,0x00000000,c2m_test_spi_tx_data_dbg0
0x0000523f,0x00000000,c2m_test_spi_tx_data_dbg1
0x00005240,0x00000000,c2m_test_spi_rx_data_dbg0
0x00005241,0x00000000,c2m_test_spi_rx_data_dbg1
0x00005242,0x00000000,c2m_test_bc_manchester
0x00005243,0x00000000,c2m_test_link_auto_test_ctrl0
0x00005244,0x00000011,c2m_test_link_auto_test_ctrl1[0]
0x00005248,0x00000011,c2m_test_link_auto_test_ctrl1[1]
0x0000524c,0x00000011,c2m_test_link_auto_test_ctrl1[2]
0x00005250,0x00000011,c2m_test_link_auto_test_ctrl1[3]
0x00005245,0x00000001,c2m_test_link_auto_test_ctrl2[0]
0x00005249,0x00000001,c2m_test_link_auto_test_ctrl2[1]
0x0000524d,0x00000001,c2m_test_link_auto_test_ctrl2[2]
0x00005251,0x00000001,c2m_test_link_auto_test_ctrl2[3]
0x00005246,0x00000036,c2m_test_link_auto_test_ctrl3[0]
0x0000524a,0x00000036,c2m_test_link_auto_test_ctrl3[1]
0x0000524e,0x00000036,c2m_test_link_auto_test_ctrl3[2]
0x00005252,0x00000036,c2m_test_link_auto_test_ctrl3[3]
0x00005254,0x00000000,c2m_test_link_auto_test_status0[0]
0x00005258,0x00000000,c2m_test_link_auto_test_status0[1]
0x0000525c,0x00000000,c2m_test_link_auto_test_status0[2]
0x00005260,0x00000000,c2m_test_link_auto_test_status0[3]
0x00005255,0x00000000,c2m_test_link_auto_test_status1[0]
0x00005259,0x00000000,c2m_test_link_auto_test_status1[1]
0x0000525d,0x00000000,c2m_test_link_auto_test_status1[2]
0x00005261,0x00000000,c2m_test_link_auto_test_status1[3]
0x00005256,0x00000000,c2m_test_link_auto_test_status2[0]
0x0000525a,0x00000000,c2m_test_link_auto_test_status2[1]
0x0000525e,0x00000000,c2m_test_link_auto_test_status2[2]
0x00005262,0x00000000,c2m_test_link_auto_test_status2[3]
0x00005264,0x00000000,c2m_test_link_auto_test_status3[0]
0x00005265,0x00000000,c2m_test_link_auto_test_status3[1]
0x00005266,0x00000000,c2m_test_link_auto_test_status3[2]
0x00005267,0x00000000,c2m_test_link_auto_test_status4[0]
0x00005268,0x00000000,c2m_test_link_auto_test_status4[1]
0x00005269,0x00000000,c2m_test_link_auto_test_status4[2]
0x0000526a,0x00000000,c2m_test_link_auto_test_status5[0]
0x0000526b,0x00000000,c2m_test_link_auto_test_status5[1]
0x0000526c,0x00000000,c2m_test_link_auto_test_status5[2]
0x0000526d,0x00000000,c2m_test_link_auto_test_status6[0]
0x0000526e,0x00000000,c2m_test_link_auto_test_status6[1]
0x0000526f,0x00000000,c2m_test_link_auto_test_status6[2]
0x00008000,0x00000013,c2m_csi2_tx0_version
0x00008001,0x00000000,c2m_csi2_tx0_csi2_resetn
0x00008002,0x00000000,c2m_csi2_tx0_data_scrambling
0x00008003,0x00000001,c2m_csi2_tx0_phy_rstz
0x00008004,0x00000003,c2m_csi2_tx0_phy_if_cfg_0
0x00008005,0x00000000,c2m_csi2_tx0_phy_if_cfg_1
0x00008006,0x00000001,c2m_csi2_tx0_combo_phy_mode
0x00008007,0x00000000,c2m_csi2_tx0_int_mdi_st_src
0x00008008,0x0000007f,c2m_csi2_tx0_int_mdi_st_en
0x00008009,0x00000000,c2m_csi2_tx0_int_mdi_st_force
0x0000800a,0x00000000,c2m_csi2_tx0_int_mdi_errl_src[0]
0x0000800b,0x00000000,c2m_csi2_tx0_int_mdi_errl_src[1]
0x0000800c,0x00000000,c2m_csi2_tx0_int_mdi_errl_src[2]
0x0000800d,0x00000000,c2m_csi2_tx0_int_mdi_errl_src[3]
0x0000800e,0x00000000,c2m_csi2_tx0_int_mdi_errl_en[0]
0x0000800f,0x00000000,c2m_csi2_tx0_int_mdi_errl_en[1]
0x00008010,0x00000000,c2m_csi2_tx0_int_mdi_errl_en[2]
0x00008011,0x00000000,c2m_csi2_tx0_int_mdi_errl_en[3]
0x00008012,0x00000000,c2m_csi2_tx0_int_mdi_errl_force[0]
0x00008013,0x00000000,c2m_csi2_tx0_int_mdi_errl_force[1]
0x00008014,0x00000000,c2m_csi2_tx0_int_mdi_errl_force[2]
0x00008015,0x00000000,c2m_csi2_tx0_int_mdi_errl_force[3]
0x00008016,0x00000000,c2m_csi2_tx0_int_mdi_errf_src[0]
0x00008017,0x00000000,c2m_csi2_tx0_int_mdi_errf_src[1]
0x00008018,0x00000000,c2m_csi2_tx0_int_mdi_errf_src[2]
0x00008019,0x00000000,c2m_csi2_tx0_int_mdi_errf_src[3]
0x0000801a,0x00000000,c2m_csi2_tx0_int_mdi_errf_en[0]
0x0000801b,0x00000000,c2m_csi2_tx0_int_mdi_errf_en[1]
0x0000801c,0x00000000,c2m_csi2_tx0_int_mdi_errf_en[2]
0x0000801d,0x00000000,c2m_csi2_tx0_int_mdi_errf_en[3]
0x0000801e,0x00000000,c2m_csi2_tx0_int_mdi_errf_force[0]
0x0000801f,0x00000000,c2m_csi2_tx0_int_mdi_errf_force[1]
0x00008020,0x00000000,c2m_csi2_tx0_int_mdi_errf_force[2]
0x00008021,0x00000000,c2m_csi2_tx0_int_mdi_errf_force[3]
0x00008023,0x00000001,c2m_csi2_tx0_lpclk_ctrl
0x00008024,0x00000003,c2m_csi2_tx0_phy_status_0
0x00008025,0x00000000,c2m_csi2_tx0_phy_status_1
0x00008026,0x00000000,c2m_csi2_tx0_phy_ulps_ctrl
0x00008100,0x00000013,c2m_csi2_tx1_version
0x00008101,0x00000000,c2m_csi2_tx1_csi2_resetn
0x00008102,0x00000000,c2m_csi2_tx1_data_scrambling
0x00008103,0x00000001,c2m_csi2_tx1_phy_rstz
0x00008104,0x00000003,c2m_csi2_tx1_phy_if_cfg_0
0x00008105,0x00000000,c2m_csi2_tx1_phy_if_cfg_1
0x00008106,0x00000001,c2m_csi2_tx1_combo_phy_mode
0x00008107,0x00000000,c2m_csi2_tx1_int_mdi_st_src
0x00008108,0x0000007f,c2m_csi2_tx1_int_mdi_st_en
0x00008109,0x00000000,c2m_csi2_tx1_int_mdi_st_force
0x0000810a,0x00000000,c2m_csi2_tx1_int_mdi_errl_src[0]
0x0000810b,0x00000000,c2m_csi2_tx1_int_mdi_errl_src[1]
0x0000810c,0x00000000,c2m_csi2_tx1_int_mdi_errl_src[2]
0x0000810d,0x00000000,c2m_csi2_tx1_int_mdi_errl_src[3]
0x0000810e,0x00000000,c2m_csi2_tx1_int_mdi_errl_en[0]
0x0000810f,0x00000000,c2m_csi2_tx1_int_mdi_errl_en[1]
0x00008110,0x00000000,c2m_csi2_tx1_int_mdi_errl_en[2]
0x00008111,0x00000000,c2m_csi2_tx1_int_mdi_errl_en[3]
0x00008112,0x00000000,c2m_csi2_tx1_int_mdi_errl_force[0]
0x00008113,0x00000000,c2m_csi2_tx1_int_mdi_errl_force[1]
0x00008114,0x00000000,c2m_csi2_tx1_int_mdi_errl_force[2]
0x00008115,0x00000000,c2m_csi2_tx1_int_mdi_errl_force[3]
0x00008116,0x00000000,c2m_csi2_tx1_int_mdi_errf_src[0]
0x00008117,0x00000000,c2m_csi2_tx1_int_mdi_errf_src[1]
0x00008118,0x00000000,c2m_csi2_tx1_int_mdi_errf_src[2]
0x00008119,0x00000000,c2m_csi2_tx1_int_mdi_errf_src[3]
0x0000811a,0x00000000,c2m_csi2_tx1_int_mdi_errf_en[0]
0x0000811b,0x00000000,c2m_csi2_tx1_int_mdi_errf_en[1]
0x0000811c,0x00000000,c2m_csi2_tx1_int_mdi_errf_en[2]
0x0000811d,0x00000000,c2m_csi2_tx1_int_mdi_errf_en[3]
0x0000811e,0x00000000,c2m_csi2_tx1_int_mdi_errf_force[0]
0x0000811f,0x00000000,c2m_csi2_tx1_int_mdi_errf_force[1]
0x00008120,0x00000000,c2m_csi2_tx1_int_mdi_errf_force[2]
0x00008121,0x00000000,c2m_csi2_tx1_int_mdi_errf_force[3]
0x00008123,0x00000001,c2m_csi2_tx1_lpclk_ctrl
0x00008124,0x00000003,c2m_csi2_tx1_phy_status_0
0x00008125,0x00000000,c2m_csi2_tx1_phy_status_1
0x00008126,0x00000000,c2m_csi2_tx1_phy_ulps_ctrl
0x00008200,0x00000013,c2m_csi2_tx2_version
0x00008201,0x00000000,c2m_csi2_tx2_csi2_resetn
0x00008202,0x00000000,c2m_csi2_tx2_data_scrambling
0x00008203,0x00000001,c2m_csi2_tx2_phy_rstz
0x00008204,0x00000003,c2m_csi2_tx2_phy_if_cfg_0
0x00008205,0x00000000,c2m_csi2_tx2_phy_if_cfg_1
0x00008206,0x00000001,c2m_csi2_tx2_combo_phy_mode
0x00008207,0x00000000,c2m_csi2_tx2_int_mdi_st_src
0x00008208,0x0000007f,c2m_csi2_tx2_int_mdi_st_en
0x00008209,0x00000000,c2m_csi2_tx2_int_mdi_st_force
0x0000820a,0x00000000,c2m_csi2_tx2_int_mdi_errl_src[0]
0x0000820b,0x00000000,c2m_csi2_tx2_int_mdi_errl_src[1]
0x0000820c,0x00000000,c2m_csi2_tx2_int_mdi_errl_src[2]
0x0000820d,0x00000000,c2m_csi2_tx2_int_mdi_errl_src[3]
0x0000820e,0x00000000,c2m_csi2_tx2_int_mdi_errl_en[0]
0x0000820f,0x00000000,c2m_csi2_tx2_int_mdi_errl_en[1]
0x00008210,0x00000000,c2m_csi2_tx2_int_mdi_errl_en[2]
0x00008211,0x00000000,c2m_csi2_tx2_int_mdi_errl_en[3]
0x00008212,0x00000000,c2m_csi2_tx2_int_mdi_errl_force[0]
0x00008213,0x00000000,c2m_csi2_tx2_int_mdi_errl_force[1]
0x00008214,0x00000000,c2m_csi2_tx2_int_mdi_errl_force[2]
0x00008215,0x00000000,c2m_csi2_tx2_int_mdi_errl_force[3]
0x00008216,0x00000000,c2m_csi2_tx2_int_mdi_errf_src[0]
0x00008217,0x00000000,c2m_csi2_tx2_int_mdi_errf_src[1]
0x00008218,0x00000000,c2m_csi2_tx2_int_mdi_errf_src[2]
0x00008219,0x00000000,c2m_csi2_tx2_int_mdi_errf_src[3]
0x0000821a,0x00000000,c2m_csi2_tx2_int_mdi_errf_en[0]
0x0000821b,0x00000000,c2m_csi2_tx2_int_mdi_errf_en[1]
0x0000821c,0x00000000,c2m_csi2_tx2_int_mdi_errf_en[2]
0x0000821d,0x00000000,c2m_csi2_tx2_int_mdi_errf_en[3]
0x0000821e,0x00000000,c2m_csi2_tx2_int_mdi_errf_force[0]
0x0000821f,0x00000000,c2m_csi2_tx2_int_mdi_errf_force[1]
0x00008220,0x00000000,c2m_csi2_tx2_int_mdi_errf_force[2]
0x00008221,0x00000000,c2m_csi2_tx2_int_mdi_errf_force[3]
0x00008223,0x00000001,c2m_csi2_tx2_lpclk_ctrl
0x00008224,0x00000003,c2m_csi2_tx2_phy_status_0
0x00008225,0x00000000,c2m_csi2_tx2_phy_status_1
0x00008226,0x00000000,c2m_csi2_tx2_phy_ulps_ctrl
0x00008300,0x00000013,c2m_csi2_tx3_version
0x00008301,0x00000000,c2m_csi2_tx3_csi2_resetn
0x00008302,0x00000000,c2m_csi2_tx3_data_scrambling
0x00008303,0x00000001,c2m_csi2_tx3_phy_rstz
0x00008304,0x00000003,c2m_csi2_tx3_phy_if_cfg_0
0x00008305,0x00000000,c2m_csi2_tx3_phy_if_cfg_1
0x00008306,0x00000001,c2m_csi2_tx3_combo_phy_mode
0x00008307,0x00000000,c2m_csi2_tx3_int_mdi_st_src
0x00008308,0x0000007f,c2m_csi2_tx3_int_mdi_st_en
0x00008309,0x00000000,c2m_csi2_tx3_int_mdi_st_force
0x0000830a,0x00000000,c2m_csi2_tx3_int_mdi_errl_src[0]
0x0000830b,0x00000000,c2m_csi2_tx3_int_mdi_errl_src[1]
0x0000830c,0x00000000,c2m_csi2_tx3_int_mdi_errl_src[2]
0x0000830d,0x00000000,c2m_csi2_tx3_int_mdi_errl_src[3]
0x0000830e,0x00000000,c2m_csi2_tx3_int_mdi_errl_en[0]
0x0000830f,0x00000000,c2m_csi2_tx3_int_mdi_errl_en[1]
0x00008310,0x00000000,c2m_csi2_tx3_int_mdi_errl_en[2]
0x00008311,0x00000000,c2m_csi2_tx3_int_mdi_errl_en[3]
0x00008312,0x00000000,c2m_csi2_tx3_int_mdi_errl_force[0]
0x00008313,0x00000000,c2m_csi2_tx3_int_mdi_errl_force[1]
0x00008314,0x00000000,c2m_csi2_tx3_int_mdi_errl_force[2]
0x00008315,0x00000000,c2m_csi2_tx3_int_mdi_errl_force[3]
0x00008316,0x00000000,c2m_csi2_tx3_int_mdi_errf_src[0]
0x00008317,0x00000000,c2m_csi2_tx3_int_mdi_errf_src[1]
0x00008318,0x00000000,c2m_csi2_tx3_int_mdi_errf_src[2]
0x00008319,0x00000000,c2m_csi2_tx3_int_mdi_errf_src[3]
0x0000831a,0x00000000,c2m_csi2_tx3_int_mdi_errf_en[0]
0x0000831b,0x00000000,c2m_csi2_tx3_int_mdi_errf_en[1]
0x0000831c,0x00000000,c2m_csi2_tx3_int_mdi_errf_en[2]
0x0000831d,0x00000000,c2m_csi2_tx3_int_mdi_errf_en[3]
0x0000831e,0x00000000,c2m_csi2_tx3_int_mdi_errf_force[0]
0x0000831f,0x00000000,c2m_csi2_tx3_int_mdi_errf_force[1]
0x00008320,0x00000000,c2m_csi2_tx3_int_mdi_errf_force[2]
0x00008321,0x00000000,c2m_csi2_tx3_int_mdi_errf_force[3]
0x00008323,0x00000001,c2m_csi2_tx3_lpclk_ctrl
0x00008324,0x00000003,c2m_csi2_tx3_phy_status_0
0x00008325,0x00000000,c2m_csi2_tx3_phy_status_1
0x00008326,0x00000000,c2m_csi2_tx3_phy_ulps_ctrl
0x00008500,0x00000000,c2m_mipi_tx_mipi_dig_tx_ctrl0[0]
0x00008501,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl0[1]
0x00008502,0x00000001,c2m_mipi_tx_mipi_dig_tx_ctrl0[2]
0x00008503,0x00000002,c2m_mipi_tx_mipi_dig_tx_ctrl0[3]
0x00008504,0x00000010,c2m_mipi_tx_mipi_dig_tx_ctrl0[4]
0x00008505,0x00000003,c2m_mipi_tx_mipi_dig_tx_ctrl0[5]
0x00008506,0x00000008,c2m_mipi_tx_mipi_dig_tx_ctrl0[6]
0x00008507,0x00000018,c2m_mipi_tx_mipi_dig_tx_ctrl0[7]
0x00008508,0x00000009,c2m_mipi_tx_mipi_dig_tx_ctrl0[8]
0x00008509,0x0000000a,c2m_mipi_tx_mipi_dig_tx_ctrl0[9]
0x0000850a,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl0[10]
0x0000850b,0x0000000b,c2m_mipi_tx_mipi_dig_tx_ctrl0[11]
0x0000850c,0x00000000,c2m_mipi_tx_mipi_dig_tx_ctrl1[0]
0x0000850d,0x00000000,c2m_mipi_tx_mipi_dig_tx_ctrl1[1]
0x0000850e,0x00000084,c2m_mipi_tx_mipi_dig_tx_res_cal0[0]
0x0000850f,0x00000084,c2m_mipi_tx_mipi_dig_tx_res_cal0[1]
0x00008510,0x00000000,c2m_mipi_tx_mipi_dig_tx_res_cal1[0]
0x00008511,0x00000000,c2m_mipi_tx_mipi_dig_tx_res_cal1[1]
0x00008512,0x00000000,c2m_mipi_tx_mipi_dig_tx_res_cal2[0]
0x00008513,0x00000000,c2m_mipi_tx_mipi_dig_tx_res_cal2[1]
0x00008514,0x00000050,c2m_mipi_tx_mipi_dig_tx_dco_cal0[0]
0x00008515,0x00000050,c2m_mipi_tx_mipi_dig_tx_dco_cal0[1]
0x00008516,0x00000055,c2m_mipi_tx_mipi_dig_tx_dco_cal1[0]
0x00008517,0x00000055,c2m_mipi_tx_mipi_dig_tx_dco_cal1[1]
0x00008518,0x00000055,c2m_mipi_tx_mipi_dig_tx_dco_cal2[0]
0x00008519,0x00000055,c2m_mipi_tx_mipi_dig_tx_dco_cal2[1]
0x0000851a,0x00000002,c2m_mipi_tx_mipi_dig_tx_dco_cal3[0]
0x0000851b,0x00000002,c2m_mipi_tx_mipi_dig_tx_dco_cal3[1]
0x0000851c,0x00000000,c2m_mipi_tx_mipi_dig_tx_dco_cal4[0]
0x0000851d,0x00000000,c2m_mipi_tx_mipi_dig_tx_dco_cal4[1]
0x0000851e,0x00000055,c2m_mipi_tx_mipi_dig_tx_boot0[0]
0x0000851f,0x00000055,c2m_mipi_tx_mipi_dig_tx_boot0[1]
0x00008520,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[0]
0x00008521,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[1]
0x00008522,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[2]
0x00008523,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[3]
0x00008524,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[4]
0x00008525,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[5]
0x00008526,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[6]
0x00008527,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[7]
0x00008528,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[8]
0x00008529,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[9]
0x0000852a,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[10]
0x0000852b,0x00000001,c2m_mipi_tx_mipi_dig_tx_boot1[11]
0x0000852c,0x00000031,c2m_mipi_tx_mipi_dig_tx_boot2[0]
0x0000852d,0x00000031,c2m_mipi_tx_mipi_dig_tx_boot2[1]
0x0000852e,0x00000013,c2m_mipi_tx_mipi_dig_tx_boot3[0]
0x0000852f,0x00000013,c2m_mipi_tx_mipi_dig_tx_boot3[1]
0x00008530,0x00000013,c2m_mipi_tx_mipi_dig_tx_boot4[0]
0x00008531,0x00000013,c2m_mipi_tx_mipi_dig_tx_boot4[1]
0x00008532,0x00000000,c2m_mipi_tx_mipi_dig_tx_boot5[0]
0x00008533,0x00000000,c2m_mipi_tx_mipi_dig_tx_boot5[1]
0x00008534,0x00000000,c2m_mipi_tx_mipi_dig_tx_mipi_pll0[0]
0x00008535,0x00000000,c2m_mipi_tx_mipi_dig_tx_mipi_pll0[1]
0x00008536,0x00000000,c2m_mipi_tx_mipi_dig_tx_boot_status0[0]
0x00008537,0x00000000,c2m_mipi_tx_mipi_dig_tx_boot_status0[1]
0x00008538,0x00000001,c2m_mipi_tx_mipi_dig_tx_test_gen0[0]
0x00008539,0x00000001,c2m_mipi_tx_mipi_dig_tx_test_gen0[1]
0x0000853a,0x00000001,c2m_mipi_tx_mipi_dig_tx_test_gen0[2]
0x0000853b,0x00000001,c2m_mipi_tx_mipi_dig_tx_test_gen0[3]
0x0000853c,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen1[0]
0x0000853d,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen1[1]
0x0000853e,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen1[2]
0x0000853f,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen1[3]
0x00008540,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen2[0]
0x00008541,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen2[1]
0x00008542,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen2[2]
0x00008543,0x00000000,c2m_mipi_tx_mipi_dig_tx_test_gen2[3]
0x00008544,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl0[0]
0x00008545,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl0[1]
0x00008546,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl0[2]
0x00008547,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl0[3]
0x00008548,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl1[0]
0x00008549,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl1[1]
0x0000854a,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl1[2]
0x0000854b,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl1[3]
0x0000854c,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl2[0]
0x0000854d,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl2[1]
0x0000854e,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl2[2]
0x0000854f,0x00000000,c2m_mipi_tx_mipi_dig_tx_skew_ctrl2[3]
0x00008550,0x00000002,c2m_mipi_tx_mipi_dig_tx_skew_ctrl3[0]
0x00008551,0x00000002,c2m_mipi_tx_mipi_dig_tx_skew_ctrl3[1]
0x00008552,0x00000002,c2m_mipi_tx_mipi_dig_tx_skew_ctrl3[2]
0x00008553,0x00000002,c2m_mipi_tx_mipi_dig_tx_skew_ctrl3[3]
0x00008554,0x00000063,c2m_mipi_tx_mipi_dig_tx_mst_init[0]
0x00008555,0x00000063,c2m_mipi_tx_mipi_dig_tx_mst_init[1]
0x00008556,0x0000009a,c2m_mipi_tx_mipi_dig_tx_prbs_seed0
0x00008557,0x00000078,c2m_mipi_tx_mipi_dig_tx_prbs_seed1
0x00008558,0x00000002,c2m_mipi_tx_mipi_dig_tx_prbs_seed2
0x00008559,0x00000000,c2m_mipi_tx_mipi_dig_tx_prbs_seed3
0x0000855a,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl2[0]
0x0000855b,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl2[1]
0x0000855c,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl2[2]
0x0000855d,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl2[3]
0x0000855e,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl2[4]
0x0000855f,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl2[5]
0x00008560,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl2[6]
0x00008561,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl2[7]
0x00008562,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl3[0]
0x00008563,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl3[1]
0x00008564,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl3[2]
0x00008565,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl3[3]
0x00008566,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl3[4]
0x00008567,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl3[5]
0x00008568,0x00000006,c2m_mipi_tx_mipi_dig_tx_ctrl3[6]
0x00008569,0x00000021,c2m_mipi_tx_mipi_dig_tx_ctrl3[7]
0x0000856a,0x00000002,c2m_mipi_tx_mipi_dig_tx_int_enable[0]
0x0000856b,0x00000002,c2m_mipi_tx_mipi_dig_tx_int_enable[1]
0x0000856c,0x00000000,c2m_mipi_tx_mipi_dig_tx_int_clear
0x0000856d,0x00000000,c2m_mipi_tx_mipi_dig_tx_manufacture
0x0000856e,0x00000000,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[0]
0x0000856f,0x00000001,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[1]
0x00008570,0x00000002,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[2]
0x00008571,0x00000003,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[3]
0x00008572,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[4]
0x00008573,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[5]
0x00008574,0x00000008,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[6]
0x00008575,0x00000009,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[7]
0x00008576,0x0000000a,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[8]
0x00008577,0x0000000b,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[9]
0x00008578,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[10]
0x00008579,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl0_cphy[11]
0x0000857a,0x00000002,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[0]
0x0000857b,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[1]
0x0000857c,0x00000003,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[2]
0x0000857d,0x00000000,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[3]
0x0000857e,0x00000010,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[4]
0x0000857f,0x00000001,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[5]
0x00008580,0x00000008,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[6]
0x00008581,0x00000018,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[7]
0x00008582,0x00000009,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[8]
0x00008583,0x0000000a,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[9]
0x00008584,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[10]
0x00008585,0x0000000b,c2m_mipi_tx_mipi_dig_tx_ctrl1_dphy[11]
0x00008586,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[0]
0x00008587,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[1]
0x00008588,0x00000000,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[2]
0x00008589,0x00000001,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[3]
0x0000858a,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[4]
0x0000858b,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[5]
0x0000858c,0x00000008,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[6]
0x0000858d,0x00000009,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[7]
0x0000858e,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[8]
0x0000858f,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[9]
0x00008590,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[10]
0x00008591,0x0000001f,c2m_mipi_tx_mipi_dig_tx_ctrl1_cphy[11]
0x00008680,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[0]
0x00008681,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[1]
0x00008682,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[2]
0x00008683,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[3]
0x00008684,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[4]
0x00008685,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[5]
0x00008686,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[6]
0x00008687,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[7]
0x00008688,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[8]
0x00008689,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[9]
0x0000868a,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[10]
0x0000868b,0x00000002,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_0[11]
0x0000868c,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[0]
0x0000868d,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[1]
0x0000868e,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[2]
0x0000868f,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[3]
0x00008690,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[4]
0x00008691,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[5]
0x00008692,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[6]
0x00008693,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[7]
0x00008694,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[8]
0x00008695,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[9]
0x00008696,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[10]
0x00008697,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_1[11]
0x00008698,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[0]
0x00008699,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[1]
0x0000869a,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[2]
0x0000869b,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[3]
0x0000869c,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[4]
0x0000869d,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[5]
0x0000869e,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[6]
0x0000869f,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[7]
0x000086a0,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[8]
0x000086a1,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[9]
0x000086a2,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[10]
0x000086a3,0x00000000,c2m_mipi_tx_lane_module_tx_r_dbg_ctrl_2[11]
0x000086a4,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[0]
0x000086a5,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[1]
0x000086a6,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[2]
0x000086a7,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[3]
0x000086a8,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[4]
0x000086a9,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[5]
0x000086aa,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[6]
0x000086ab,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[7]
0x000086ac,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[8]
0x000086ad,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[9]
0x000086ae,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[10]
0x000086af,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_0[11]
0x000086b0,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[0]
0x000086b1,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[1]
0x000086b2,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[2]
0x000086b3,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[3]
0x000086b4,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[4]
0x000086b5,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[5]
0x000086b6,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[6]
0x000086b7,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[7]
0x000086b8,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[8]
0x000086b9,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[9]
0x000086ba,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[10]
0x000086bb,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_1[11]
0x000086bc,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[0]
0x000086bd,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[1]
0x000086be,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[2]
0x000086bf,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[3]
0x000086c0,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[4]
0x000086c1,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[5]
0x000086c2,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[6]
0x000086c3,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[7]
0x000086c4,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[8]
0x000086c5,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[9]
0x000086c6,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[10]
0x000086c7,0x00000000,c2m_mipi_tx_lane_module_tx_r_sw_force_cfg_2[11]
0x000086c8,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[0]
0x000086c9,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[1]
0x000086ca,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[2]
0x000086cb,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[3]
0x000086cc,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[4]
0x000086cd,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[5]
0x000086ce,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[6]
0x000086cf,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[7]
0x000086d0,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[8]
0x000086d1,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[9]
0x000086d2,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[10]
0x000086d3,0x00000014,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_0[11]
0x000086d4,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[0]
0x000086d5,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[1]
0x000086d6,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[2]
0x000086d7,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[3]
0x000086d8,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[4]
0x000086d9,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[5]
0x000086da,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[6]
0x000086db,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[7]
0x000086dc,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[8]
0x000086dd,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[9]
0x000086de,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[10]
0x000086df,0x00000004,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_1[11]
0x000086e0,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[0]
0x000086e1,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[1]
0x000086e2,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[2]
0x000086e3,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[3]
0x000086e4,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[4]
0x000086e5,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[5]
0x000086e6,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[6]
0x000086e7,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[7]
0x000086e8,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[8]
0x000086e9,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[9]
0x000086ea,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[10]
0x000086eb,0x00000028,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_2[11]
0x000086ec,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[0]
0x000086ed,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[1]
0x000086ee,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[2]
0x000086ef,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[3]
0x000086f0,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[4]
0x000086f1,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[5]
0x000086f2,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[6]
0x000086f3,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[7]
0x000086f4,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[8]
0x000086f5,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[9]
0x000086f6,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[10]
0x000086f7,0x00000000,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_3[11]
0x000086f8,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[0]
0x000086f9,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[1]
0x000086fa,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[2]
0x000086fb,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[3]
0x000086fc,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[4]
0x000086fd,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[5]
0x000086fe,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[6]
0x000086ff,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[7]
0x00008700,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[8]
0x00008701,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[9]
0x00008702,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[10]
0x00008703,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_4[11]
0x00008704,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[0]
0x00008705,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[1]
0x00008706,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[2]
0x00008707,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[3]
0x00008708,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[4]
0x00008709,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[5]
0x0000870a,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[6]
0x0000870b,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[7]
0x0000870c,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[8]
0x0000870d,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[9]
0x0000870e,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[10]
0x0000870f,0x00000050,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_5[11]
0x00008710,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[0]
0x00008711,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[1]
0x00008712,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[2]
0x00008713,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[3]
0x00008714,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[4]
0x00008715,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[5]
0x00008716,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[6]
0x00008717,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[7]
0x00008718,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[8]
0x00008719,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[9]
0x0000871a,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[10]
0x0000871b,0x00000005,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_6[11]
0x0000871c,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[0]
0x0000871d,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[1]
0x0000871e,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[2]
0x0000871f,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[3]
0x00008720,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[4]
0x00008721,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[5]
0x00008722,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[6]
0x00008723,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[7]
0x00008724,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[8]
0x00008725,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[9]
0x00008726,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[10]
0x00008727,0x00000001,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_7[11]
0x00008728,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[0]
0x00008729,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[1]
0x0000872a,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[2]
0x0000872b,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[3]
0x0000872c,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[4]
0x0000872d,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[5]
0x0000872e,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[6]
0x0000872f,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[7]
0x00008730,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[8]
0x00008731,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[9]
0x00008732,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[10]
0x00008733,0x00000002,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8[11]
0x00008734,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[0]
0x00008735,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[1]
0x00008736,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[2]
0x00008737,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[3]
0x00008738,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[4]
0x00008739,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[5]
0x0000873a,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[6]
0x0000873b,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[7]
0x0000873c,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[8]
0x0000873d,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[9]
0x0000873e,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[10]
0x0000873f,0x00000009,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_9[11]
0x00008740,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[0]
0x00008741,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[1]
0x00008742,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[2]
0x00008743,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[3]
0x00008744,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[4]
0x00008745,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[5]
0x00008746,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[6]
0x00008747,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[7]
0x00008748,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[8]
0x00008749,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[9]
0x0000874a,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[10]
0x0000874b,0x00000040,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_0[11]
0x0000874c,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[0]
0x0000874d,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[1]
0x0000874e,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[2]
0x0000874f,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[3]
0x00008750,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[4]
0x00008751,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[5]
0x00008752,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[6]
0x00008753,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[7]
0x00008754,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[8]
0x00008755,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[9]
0x00008756,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[10]
0x00008757,0x0000000d,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_1[11]
0x00008758,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[0]
0x00008759,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[1]
0x0000875a,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[2]
0x0000875b,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[3]
0x0000875c,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[4]
0x0000875d,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[5]
0x0000875e,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[6]
0x0000875f,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[7]
0x00008760,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[8]
0x00008761,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[9]
0x00008762,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[10]
0x00008763,0x00000003,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_8_2[11]
0x00008764,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[0]
0x00008765,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[1]
0x00008766,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[2]
0x00008767,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[3]
0x00008768,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[4]
0x00008769,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[5]
0x0000876a,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[6]
0x0000876b,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[7]
0x0000876c,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[8]
0x0000876d,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[9]
0x0000876e,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[10]
0x0000876f,0x00000000,c2m_mipi_tx_lane_module_tx_r_bit_fmt_cfg_0[11]
0x00008770,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[0]
0x00008771,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[1]
0x00008772,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[2]
0x00008773,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[3]
0x00008774,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[4]
0x00008775,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[5]
0x00008776,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[6]
0x00008777,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[7]
0x00008778,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[8]
0x00008779,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[9]
0x0000877a,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[10]
0x0000877b,0x00000096,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_10[11]
0x0000877c,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[0]
0x0000877d,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[1]
0x0000877e,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[2]
0x0000877f,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[3]
0x00008780,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[4]
0x00008781,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[5]
0x00008782,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[6]
0x00008783,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[7]
0x00008784,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[8]
0x00008785,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[9]
0x00008786,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[10]
0x00008787,0x00000000,c2m_mipi_tx_lane_module_tx_ro_ls_state[11]
0x00008788,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[0]
0x00008789,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[1]
0x0000878a,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[2]
0x0000878b,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[3]
0x0000878c,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[4]
0x0000878d,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[5]
0x0000878e,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[6]
0x0000878f,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[7]
0x00008790,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[8]
0x00008791,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[9]
0x00008792,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[10]
0x00008793,0x00000000,c2m_mipi_tx_lane_module_tx_ro_hs_state[11]
0x00008794,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[0]
0x00008795,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[1]
0x00008796,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[2]
0x00008797,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[3]
0x00008798,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[4]
0x00008799,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[5]
0x0000879a,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[6]
0x0000879b,0x00000013,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_11[7]
0x0000879c,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[0]
0x0000879d,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[1]
0x0000879e,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[2]
0x0000879f,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[3]
0x000087a0,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[4]
0x000087a1,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[5]
0x000087a2,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[6]
0x000087a3,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol0[7]
0x000087a4,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[0]
0x000087a5,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[1]
0x000087a6,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[2]
0x000087a7,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[3]
0x000087a8,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[4]
0x000087a9,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[5]
0x000087aa,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[6]
0x000087ab,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol1[7]
0x000087ac,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[0]
0x000087ad,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[1]
0x000087ae,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[2]
0x000087af,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[3]
0x000087b0,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[4]
0x000087b1,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[5]
0x000087b2,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[6]
0x000087b3,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol2[7]
0x000087b4,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[0]
0x000087b5,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[1]
0x000087b6,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[2]
0x000087b7,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[3]
0x000087b8,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[4]
0x000087b9,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[5]
0x000087ba,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[6]
0x000087bb,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol3[7]
0x000087bc,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[0]
0x000087bd,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[1]
0x000087be,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[2]
0x000087bf,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[3]
0x000087c0,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[4]
0x000087c1,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[5]
0x000087c2,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[6]
0x000087c3,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol4[7]
0x000087c4,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[0]
0x000087c5,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[1]
0x000087c6,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[2]
0x000087c7,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[3]
0x000087c8,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[4]
0x000087c9,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[5]
0x000087ca,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[6]
0x000087cb,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol5[7]
0x000087cc,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[0]
0x000087cd,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[1]
0x000087ce,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[2]
0x000087cf,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[3]
0x000087d0,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[4]
0x000087d1,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[5]
0x000087d2,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[6]
0x000087d3,0x0000001b,c2m_mipi_tx_lane_module_tx_pre_symbol6[7]
0x000087d4,0x00000000,c2m_mipi_tx_lane_module_tx_cphy_debug
0x000087d5,0x00000000,c2m_mipi_tx_lane_module_tx_hstx_timeout_cnt0
0x000087d6,0x000000c0,c2m_mipi_tx_lane_module_tx_hstx_timeout_cnt1
0x000087d7,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[0]
0x000087d8,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[1]
0x000087d9,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[2]
0x000087da,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[3]
0x000087db,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[4]
0x000087dc,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[5]
0x000087dd,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[6]
0x000087de,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[7]
0x000087df,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[8]
0x000087e0,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[9]
0x000087e1,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[10]
0x000087e2,0x000000b8,c2m_mipi_tx_lane_module_tx_r_lane_paramter[11]
0x000087e3,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[0]
0x000087e4,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[1]
0x000087e5,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[2]
0x000087e6,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[3]
0x000087e7,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[4]
0x000087e8,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[5]
0x000087e9,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[6]
0x000087ea,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[7]
0x000087eb,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[8]
0x000087ec,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[9]
0x000087ed,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[10]
0x000087ee,0x0000000a,c2m_mipi_tx_lane_module_tx_r_t_parameter_cfg_12[11]
0x00008800,0x00000011,c2m_mipi_tx_cdphy_tx_ns_reg1
0x00008801,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg2
0x00008802,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg3
0x00008803,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg4
0x00008804,0x00000004,c2m_mipi_tx_cdphy_tx_ns_reg5
0x00008805,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg6
0x00008806,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg7
0x00008807,0x00000004,c2m_mipi_tx_cdphy_tx_ns_reg8
0x00008808,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg9
0x00008809,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg10
0x0000880a,0x00000004,c2m_mipi_tx_cdphy_tx_ns_reg11
0x0000880b,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg12
0x0000880c,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg13
0x0000880d,0x00000004,c2m_mipi_tx_cdphy_tx_ns_reg14
0x0000880e,0x00000040,c2m_mipi_tx_cdphy_tx_ns_reg15
0x0000880f,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg16
0x00008810,0x00000004,c2m_mipi_tx_cdphy_tx_ns_reg17
0x00008811,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg18
0x00008812,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg19
0x00008813,0x00000004,c2m_mipi_tx_cdphy_tx_ns_reg20
0x00008814,0x00000004,c2m_mipi_tx_cdphy_tx_ns_reg21
0x00008815,0x00000040,c2m_mipi_tx_cdphy_tx_ns_reg22
0x00008816,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg23
0x00008817,0x00000040,c2m_mipi_tx_cdphy_tx_ns_reg24
0x00008818,0x00000040,c2m_mipi_tx_cdphy_tx_ns_reg25
0x00008819,0x00000000,c2m_mipi_tx_cdphy_tx_ns_reg26
0x0000881a,0x00000040,c2m_mipi_tx_cdphy_tx_ns_reg27
0x00008820,0x00000011,c2m_mipi_tx_cdphy_tx_ew_reg1
0x00008821,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg2
0x00008822,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg3
0x00008823,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg4
0x00008824,0x00000006,c2m_mipi_tx_cdphy_tx_ew_reg5
0x00008825,0x00000040,c2m_mipi_tx_cdphy_tx_ew_reg6
0x00008826,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg7
0x00008827,0x00000006,c2m_mipi_tx_cdphy_tx_ew_reg8
0x00008828,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg9
0x00008829,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg10
0x0000882a,0x00000006,c2m_mipi_tx_cdphy_tx_ew_reg11
0x0000882b,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg12
0x0000882c,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg13
0x0000882d,0x00000006,c2m_mipi_tx_cdphy_tx_ew_reg14
0x0000882e,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg15
0x0000882f,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg16
0x00008830,0x00000006,c2m_mipi_tx_cdphy_tx_ew_reg17
0x00008831,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg18
0x00008832,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg19
0x00008833,0x00000006,c2m_mipi_tx_cdphy_tx_ew_reg20
0x00008834,0x00000004,c2m_mipi_tx_cdphy_tx_ew_reg21
0x00008835,0x00000040,c2m_mipi_tx_cdphy_tx_ew_reg22
0x00008836,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg23
0x00008837,0x00000040,c2m_mipi_tx_cdphy_tx_ew_reg24
0x00008838,0x00000040,c2m_mipi_tx_cdphy_tx_ew_reg25
0x00008839,0x00000000,c2m_mipi_tx_cdphy_tx_ew_reg26
0x0000883a,0x00000040,c2m_mipi_tx_cdphy_tx_ew_reg27
0x00008840,0x00000014,c2m_mipi_tx_cdphy_pll_ns_reg0_pll0_lv
0x00008841,0x00000015,c2m_mipi_tx_cdphy_pll_ns_reg1_pll0_lv
0x00008842,0x00000001,c2m_mipi_tx_cdphy_pll_ns_reg2_pll0_lv
0x00008843,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg3_pll0_lv
0x00008844,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg4_pll0_lv
0x00008845,0x0000000f,c2m_mipi_tx_cdphy_pll_ns_reg5_pll0_lv
0x00008846,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg6_pll0_lv
0x00008847,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg7_pll0_lv
0x00008848,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg8_pll0_lv
0x00008849,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg9_pll0_lv
0x0000884a,0x0000001f,c2m_mipi_tx_cdphy_pll_ns_reg0_pll1_lv
0x0000884b,0x00000065,c2m_mipi_tx_cdphy_pll_ns_reg1_pll1_lv
0x0000884c,0x00000001,c2m_mipi_tx_cdphy_pll_ns_reg2_pll1_lv
0x0000884d,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg3_pll1_lv
0x0000884e,0x00000000,c2m_mipi_tx_cdphy_pll_ns_reg4_pll1_lv
0x0000884f,0x0000000f,c2m_mipi_tx_cdphy_pll_ns_reg5_pll1_lv
0x00008850,0x00000019,c2m_mipi_tx_cdphy_pll_ns_reg6_pll1_lv
0x00008851,0x00000019,c2m_mipi_tx_cdphy_pll_ns_reg7_pll1_lv
0x00008852,0x00000019,c2m_mipi_tx_cdphy_pll_ns_reg8_pll1_lv
0x00008853,0x00000019,c2m_mipi_tx_cdphy_pll_ns_reg9_pll1_lv
0x00008854,0x00000015,c2m_mipi_tx_cdphy_pll_ns_reg_pllpwr_lv
0x00008855,0x00000011,c2m_mipi_tx_cdphy_pll_ns_reg_comb_lv
0x00008856,0x00000044,c2m_mipi_tx_cdphy_pll_ns_reg_dldo_bpssc
0x00008860,0x00000014,c2m_mipi_tx_cdphy_pll_ew_reg0_pll0_lv
0x00008861,0x00000015,c2m_mipi_tx_cdphy_pll_ew_reg1_pll0_lv
0x00008862,0x00000001,c2m_mipi_tx_cdphy_pll_ew_reg2_pll0_lv
0x00008863,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg3_pll0_lv
0x00008864,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg4_pll0_lv
0x00008865,0x0000000f,c2m_mipi_tx_cdphy_pll_ew_reg5_pll0_lv
0x00008866,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg6_pll0_lv
0x00008867,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg7_pll0_lv
0x00008868,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg8_pll0_lv
0x00008869,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg9_pll0_lv
0x0000886a,0x0000001f,c2m_mipi_tx_cdphy_pll_ew_reg0_pll1_lv
0x0000886b,0x00000065,c2m_mipi_tx_cdphy_pll_ew_reg1_pll1_lv
0x0000886c,0x00000001,c2m_mipi_tx_cdphy_pll_ew_reg2_pll1_lv
0x0000886d,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg3_pll1_lv
0x0000886e,0x00000000,c2m_mipi_tx_cdphy_pll_ew_reg4_pll1_lv
0x0000886f,0x0000000f,c2m_mipi_tx_cdphy_pll_ew_reg5_pll1_lv
0x00008870,0x00000019,c2m_mipi_tx_cdphy_pll_ew_reg6_pll1_lv
0x00008871,0x00000019,c2m_mipi_tx_cdphy_pll_ew_reg7_pll1_lv
0x00008872,0x00000019,c2m_mipi_tx_cdphy_pll_ew_reg8_pll1_lv
0x00008873,0x00000019,c2m_mipi_tx_cdphy_pll_ew_reg9_pll1_lv
0x00008874,0x00000015,c2m_mipi_tx_cdphy_pll_ew_reg_pllpwr_lv
0x00008875,0x00000011,c2m_mipi_tx_cdphy_pll_ew_reg_comb_lv
0x00008876,0x00000044,c2m_mipi_tx_cdphy_pll_ew_reg_dldo_bpssc
0x00008880,0x0000000c,c2m_mipi_tx_cdphy_top_tx_ref
0x00008881,0x00000044,c2m_mipi_tx_cdphy_top_tx_dldo
