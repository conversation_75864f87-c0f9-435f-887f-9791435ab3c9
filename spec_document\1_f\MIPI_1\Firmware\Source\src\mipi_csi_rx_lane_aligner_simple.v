`timescale 1ns/1ns

/*
MIPI CSI-2 Lane Aligner - Simplified Version
Author: Rewritten for clarity and simplicity

Purpose: 
- Align multi-lane MIPI data that may arrive at different times
- Wait for all lanes to become valid before outputting aligned data
- Use FIFO-based approach for easier understanding

Key improvements:
- Clear state machine
- Simplified delay tracking
- Better comments and naming
- Easier to debug and maintain
*/

module mipi_csi_rx_lane_aligner_simple #(
    parameter MIPI_GEAR = 16,    // Bits per lane per clock
    parameter MIPI_LANES = 4,    // Number of MIPI lanes
    parameter MAX_DELAY = 7      // Maximum delay difference between lanes
)(
    input  wire clk_i,
    input  wire reset_i,
    
    // Input from byte aligners
    input  wire [(MIPI_LANES-1):0] lane_valid_i,           // Valid signal for each lane
    input  wire [(MIPI_GEAR*MIPI_LANES-1):0] lane_data_i,  // Data from all lanes
    
    // Aligned output
    output reg  [(MIPI_GEAR*MIPI_LANES-1):0] aligned_data_o,
    output reg  aligned_valid_o
);

// ============================================================================
// Local Parameters and Types
// ============================================================================

localparam LANE_WIDTH = MIPI_GEAR;
localparam TOTAL_WIDTH = MIPI_GEAR * MIPI_LANES;
localparam DELAY_BITS = $clog2(MAX_DELAY);

// State machine states
typedef enum logic [1:0] {
    IDLE        = 2'b00,  // Waiting for first valid lane
    MEASURING   = 2'b01,  // Measuring delay between lanes  
    ALIGNED     = 2'b10   // All lanes aligned, outputting data
} state_t;

// ============================================================================
// Internal Signals
// ============================================================================

state_t current_state, next_state;

// Data storage - FIFO for each lane
reg [LANE_WIDTH-1:0] lane_fifo [0:MIPI_LANES-1][0:MAX_DELAY-1];
reg [DELAY_BITS-1:0] lane_delay [0:MIPI_LANES-1];  // Measured delay for each lane
reg [DELAY_BITS-1:0] write_ptr;                     // Write pointer for FIFO
reg [DELAY_BITS-1:0] read_ptr [0:MIPI_LANES-1];    // Read pointer for each lane

// Control signals
reg all_lanes_valid;
reg measurement_done;

integer i, j;

// ============================================================================
// Control Signal Generation
// ============================================================================

// Detect when all lanes become valid
always @(*) begin
    all_lanes_valid = &lane_valid_i;
end

// ============================================================================
// FIFO Write Logic - Store incoming data
// ============================================================================

always @(posedge clk_i) begin
    if (reset_i) begin
        write_ptr <= '0;
        // Clear all FIFOs
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            for (j = 0; j < MAX_DELAY; j = j + 1) begin
                lane_fifo[i][j] <= '0;
            end
        end
    end else begin
        // Always store incoming data in circular FIFO
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            lane_fifo[i][write_ptr] <= lane_data_i[(i+1)*LANE_WIDTH-1 : i*LANE_WIDTH];
        end
        
        // Increment write pointer (circular)
        if (write_ptr == MAX_DELAY - 1) begin
            write_ptr <= '0;
        end else begin
            write_ptr <= write_ptr + 1;
        end
    end
end

// ============================================================================
// State Machine - Control alignment process
// ============================================================================

always @(posedge clk_i) begin
    if (reset_i) begin
        current_state <= IDLE;
    end else begin
        current_state <= next_state;
    end
end

always @(*) begin
    next_state = current_state;
    
    case (current_state)
        IDLE: begin
            // Wait for at least one lane to become valid
            if (|lane_valid_i) begin
                next_state = MEASURING;
            end
        end
        
        MEASURING: begin
            // Wait for all lanes to become valid
            if (all_lanes_valid) begin
                next_state = ALIGNED;
            end
        end
        
        ALIGNED: begin
            // Stay aligned unless reset or all lanes become invalid
            if (~|lane_valid_i) begin
                next_state = IDLE;
            end
        end
        
        default: begin
            next_state = IDLE;
        end
    endcase
end

// ============================================================================
// Delay Measurement Logic
// ============================================================================

always @(posedge clk_i) begin
    if (reset_i || current_state == IDLE) begin
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            lane_delay[i] <= '0;
        end
        measurement_done <= 1'b0;
    end else if (current_state == MEASURING) begin
        // Count delay for each lane (how long it takes to become valid)
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            if (~lane_valid_i[i]) begin
                // Lane not valid yet, increment its delay counter
                if (lane_delay[i] < MAX_DELAY - 1) begin
                    lane_delay[i] <= lane_delay[i] + 1;
                end
            end
        end
        
        // Mark measurement as done when all lanes are valid
        if (all_lanes_valid) begin
            measurement_done <= 1'b1;
        end
    end
end

// ============================================================================
// Read Pointer Calculation
// ============================================================================

always @(posedge clk_i) begin
    if (reset_i) begin
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            read_ptr[i] <= '0;
        end
    end else if (current_state == ALIGNED && measurement_done) begin
        // Calculate read pointers based on measured delays
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            // Read from older data for lanes that were valid earlier
            if (write_ptr >= lane_delay[i]) begin
                read_ptr[i] <= write_ptr - lane_delay[i];
            end else begin
                read_ptr[i] <= write_ptr + MAX_DELAY - lane_delay[i];
            end
        end
    end else begin
        // Update read pointers to follow write pointer
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            if (read_ptr[i] == MAX_DELAY - 1) begin
                read_ptr[i] <= '0;
            end else begin
                read_ptr[i] <= read_ptr[i] + 1;
            end
        end
    end
end

// ============================================================================
// Output Generation
// ============================================================================

always @(posedge clk_i) begin
    if (reset_i) begin
        aligned_data_o <= '0;
        aligned_valid_o <= 1'b0;
    end else if (current_state == ALIGNED) begin
        // Output aligned data from FIFOs
        for (i = 0; i < MIPI_LANES; i = i + 1) begin
            aligned_data_o[(i+1)*LANE_WIDTH-1 : i*LANE_WIDTH] <= lane_fifo[i][read_ptr[i]];
        end
        aligned_valid_o <= 1'b1;
    end else begin
        aligned_data_o <= '0;
        aligned_valid_o <= 1'b0;
    end
end

// ============================================================================
// Debug and Monitoring (Optional)
// ============================================================================

`ifdef DEBUG_LANE_ALIGNER
    // Synthesis translate_off
    always @(posedge clk_i) begin
        if (current_state == ALIGNED && measurement_done) begin
            $display("Lane Aligner: Delays measured - L0:%0d, L1:%0d, L2:%0d, L3:%0d", 
                     lane_delay[0], lane_delay[1], lane_delay[2], lane_delay[3]);
        end
    end
    // Synthesis translate_on
`endif

endmodule
