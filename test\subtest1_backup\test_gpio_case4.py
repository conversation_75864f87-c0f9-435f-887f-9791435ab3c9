# test/subtest1/test_gpio_case1.py
import logging
import time
import pytest

@pytest.mark.fast
def test_different_s68_gpios_receive_framesync(q68, q68_remote, s68_res_dev):
    """
    验证Q68可以向所有4个S68芯片的多个特定GPIO引脚发送帧同步信号。

    测试流程:
    1. 定义每个S68上需要测试的GPIO引脚。
    2. 循环遍历每个S68芯片，设置其I2C地址。
    3. 为当前S68芯片配置所有指定的GPIO引脚为帧同步接收模式。
    4. 所有S68配置完成后，统一启动Q68的帧同步信号发生器。
    5. 等待5秒，用于示波器观察。
    6. 检查所有链路状态并断言。
    """
    print("\n[CASE-4] 开始执行: Q68 -> 所有S68的多个GPIO帧同步信号测试")

    # 定义要在每个S68芯片上测试的GPIO引脚列表
    gpios_to_test = [0, 3, 7, 8] 

    # ---------------------------------------------------------------------
    # 1) Configure GPIOs on ALL S68 chips
    # ---------------------------------------------------------------------
    print("--> 步骤1: 循环配置所有S68芯片的GPIO为帧同步输入模式...")
    for link_idx, target_s68_addr in enumerate(s68_res_dev):
        
        # 关键步骤: 选择要通信的S68芯片
        q68_remote.dongle.devAddr = target_s68_addr
        print(f"\n  - 正在配置 S68 Link-{link_idx} (地址: {hex(target_s68_addr)})")
        
        # 为当前选定的S68配置其所有需要测试的GPIO
        for gpio_pin in gpios_to_test:
            print(f"    - 配置 GPIO引脚: {gpio_pin}")
            # 命令1: 设置GPIO为普通功能模式 (mfn=0)
            q68_remote.M2CMFNSet(gpio=gpio_pin, mfn=0)
            # 命令2: 使能该GPIO接收指定的帧同步信号 (rx_id=11)
            q68_remote.M2CGPIORemoteRx(gpio=gpio_pin, rx_id=11)

    # ---------------------------------------------------------------------
    # 2) Configure and start Q68 frame-sync signal transmission
    # ---------------------------------------------------------------------
    print("\n--> 步骤2: 配置Q68芯片以30Hz频率发送帧同步信号 (ID=11)...")
    q68.FrameSyncOutConifg(
        i=0,            # 使用0号帧同步信号发生器
        per_div=0x0B, 
        duty_cycle=4, 
        period=17361,   # 这些参数组合起来产生30Hz信号
        fs_tx_id=11,    # 发送的信号ID，必须与S68接收的ID (rx_id) 一致
        auto_fs=1,      # 自动产生信号
        outen=1         # 使能信号输出
    )
    print("    - Q68信号已启动。")

    # ---------------------------------------------------------------------
    # 3) Wait for signal capture and verify link status
    # ---------------------------------------------------------------------
    print("\n--> 步骤4: 等待5秒以便用示波器捕获波形，并检查链路状态...")
    time.sleep(5)


    
    link0_status = q68.c2m.rd_test_fsm_status1_link0()
    link1_status = q68.c2m.rd_test_fsm_status1_link1()
    link2_status = q68.c2m.rd_test_fsm_status2_link2()
    link3_status = q68.c2m.rd_test_fsm_status2_link3()
    
    final_statuses = [link0_status, link1_status, link2_status, link3_status]
    print(f"    - 最终链路状态: Link0={link0_status}, Link1={link1_status}, Link2={link2_status}, Link3={link3_status}")

    # 记录每条链路状态
    for idx, status in enumerate(final_statuses):
        if status != 5:  # 5表示正常连接状态
            logging.error(f"链路{idx}错误: 状态码={status} (期望值=5)")
        else:
            logging.info(f"链路{idx}状态正常: {status}")

    

    # 断言：检查所有链路是否都处于正常的链接状态（状态码为5）
    assert all(s == 5 for s in final_statuses), \
        f"测试失败: 部分链路未处于LINKED状态(5)。实际状态: {final_statuses}"
    
    logging.info("测试完成: 所有链路正常")
    print("测试完成: 所有链路正常")

    print("\n==> [CASE-4] 测试通过: 所有S68的指定GPIO配置成功，Q68已发送信号，且所有链路状态正常。")
