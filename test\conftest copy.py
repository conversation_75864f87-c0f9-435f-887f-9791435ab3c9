import os
import sys
import importlib
import pytest

# ------------------------------------------------------------------
#  Make project root and `top/` importable
# ------------------------------------------------------------------
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 现在可以直接复用 top/top.py 里现成的全局对象和函数
for mod_name in ('top.top', 'top'):
    try:
        _top = importlib.import_module(mod_name)
        break
    except ImportError:
        _top = None

if _top is None or not hasattr(_top, 'init_global_objects'):
    raise RuntimeError("未找到包含 init_global_objects() 的 top 模块，请确认 top/top.py 存在且在 PYTHONPATH 中")

# ------------------------------------------------------------------
#  Session-scope hardware fixture (yield-style)
# ------------------------------------------------------------------
@pytest.fixture(scope="session")
def hardware_setup():
    """基础硬件环境设置 - 只包含共性配置

    包含:
    1. 电源控制
    2. 基础设备对象初始化
    3. 基础通信设置
    """
    print("\n" + "="*60)
    print("基础硬件环境设置")
    print("="*60)

    # 初始化设备对象
    _top.init_global_objects()

    # 电源控制
    _top.power_on()

    # 基础Q68-S68通信设置 (使用默认KLINK配置)
    _top.setup_q68_s68_communication()

    # 返回设备对象字典和配置函数
    devices = {
        'q68': _top.q68,
        'q68_remote': _top.q68_remote,
        'q68_i2c2': _top.q68_i2c2,
        'q68_I2C2remote': _top.q68_I2C2remote,
        's68_res_dev': _top.s68_res_dev,
        'oscilloscope': _top.oscilloscope,  # 添加示波器对象
        # 添加配置函数，让测试用例可以灵活配置
        'configure_links': _top.configure_specific_links,
        'get_link_status': _top.get_link_status,
        'setup_communication': _top.setup_q68_s68_communication,
        # 添加电源控制函数，支持完整的系统重新初始化
        'power_off': _top.power_off,
        'power_on': _top.power_on
    }

    yield devices

    print("\n基础硬件环境清理完成")


# ------------------------------------------------------------------
#  简化的设备访问fixtures
# ------------------------------------------------------------------
@pytest.fixture(scope="session")
def devices(hardware_setup):
    """返回所有设备对象的字典"""
    return hardware_setup