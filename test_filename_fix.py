#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证文件名修复效果
测试修复后的文件名格式是否能解决存储空间不足问题
"""

import os
import sys
import time

# 添加路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))

def test_filename_fix():
    """测试文件名修复效果"""
    
    print("🔧 测试文件名修复效果")
    print("="*60)
    
    # 初始化示波器
    try:
        from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
        oscilloscope = SiglentSDS5034X()
        print("✅ 示波器初始化成功")
    except Exception as e:
        print(f"❌ 示波器初始化失败: {e}")
        return
    
    # 测试参数
    gpio_num = 8
    active_links = [0, 2]
    base_path = "U-disk0/gpiotest/s68tod68_0_2/link0_gpio8"
    
    # 创建文件夹
    os.makedirs(base_path, exist_ok=True)
    
    print(f"📋 测试配置:")
    print(f"   GPIO: {gpio_num}")
    print(f"   Links: {active_links}")
    print(f"   路径: {base_path}")
    
    # 对比测试：修复前 vs 修复后的文件名格式
    timestamp = time.strftime('%m%d_%H%M%S')
    
    test_cases = [
        {
            'name': '修复前格式 (有问题)',
            'filename': f"FIXED_01_GPIO{gpio_num}_LINK{active_links}_30Hz_20ms_{timestamp}.png",
            'expected': '❌ 应该失败'
        },
        {
            'name': '修复后格式 (应该成功)',
            'filename': f"FIXED_01_GPIO{gpio_num}_LINK{'_'.join(map(str, active_links))}_30Hz_20ms_{timestamp}.png",
            'expected': '✅ 应该成功'
        },
        {
            'name': '进一步简化格式',
            'filename': f"FIXED_01_GPIO{gpio_num}_30Hz_{timestamp}.png",
            'expected': '✅ 应该成功'
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        print(f"   文件名: {test_case['filename']}")
        print(f"   预期: {test_case['expected']}")
        
        full_path = f"{base_path}/{test_case['filename']}"
        
        try:
            # 调用Save_Image
            result = oscilloscope.Save_Image(
                filepath=full_path,
                image_format="PNG",
                invert="OFF",
                menu="MOF"
            )
            
            print(f"   📋 返回值: {result}")
            time.sleep(1)
            
            # 检查结果
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                print(f"   ✅ 保存成功! 大小: {file_size} 字节")
                success_count += 1
            else:
                print(f"   ❌ 文件未找到")
                
        except Exception as e:
            print(f"   ❌ 保存失败: {e}")
            
            # 分析错误类型
            if "存储空间不足" in str(e):
                print(f"   💡 确认是文件名格式问题导致的'存储空间不足'")
            else:
                print(f"   💡 其他错误: {e}")
    
    print(f"\n📊 测试结果:")
    print(f"   成功: {success_count}/{len(test_cases)}")
    
    return success_count

def show_filename_comparison():
    """显示文件名对比"""
    
    print(f"\n📝 文件名格式对比")
    print("="*60)
    
    gpio_num = 8
    active_links = [0, 2]
    timestamp = "0708_181744"
    
    print(f"❌ 修复前 (有问题的格式):")
    old_format = f"FIXED_01_GPIO{gpio_num}_LINK{active_links}_30Hz_20ms_{timestamp}.png"
    print(f"   {old_format}")
    print(f"   问题: 包含方括号 [ ] 和逗号 ,")
    
    print(f"\n✅ 修复后 (正确的格式):")
    links_str = "_".join(map(str, active_links))
    new_format = f"FIXED_01_GPIO{gpio_num}_LINK{links_str}_30Hz_20ms_{timestamp}.png"
    print(f"   {new_format}")
    print(f"   改进: 用下划线替代方括号和逗号")
    
    print(f"\n🎯 关键变化:")
    print(f"   LINK[0, 2]  →  LINK0_2")
    print(f"   避免了示波器不支持的特殊字符")

def test_conftest_integration():
    """测试修复后的conftest.py通用方法"""
    
    print(f"\n🔧 测试修复后的conftest.py")
    print("="*60)
    
    try:
        # 导入通用方法
        sys.path.append('test')
        from conftest import get_universal_oscilloscope_screenshot
        
        # 初始化示波器
        from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
        oscilloscope = SiglentSDS5034X()
        
        # 创建截图工具
        screenshot_tool = get_universal_oscilloscope_screenshot(oscilloscope)
        
        # 测试配置
        osc_config = {
            'screenshot_folder_base': 'U-disk0/gpiotest',
            'enable_screenshot': True,
            'trigger_source': 'D1',
            'waveform_type': 'SQUARE',
            'frequency_list': [30],  # 只测试一个频率
            'timebase_list': ['20ms'],
            'amplitude': 1.8,
            'offset': 0.9,
            'persistence_mode': 'INFinite',
            'freq_observation_time': 0.5,
        }
        
        print(f"📋 测试通用方法 (修复后):")
        print(f"   GPIO: 8, Links: [0], 模式: fixed")
        
        # 调用修复后的通用方法
        result = screenshot_tool.execute_screenshot(
            gpio_num=8,
            active_links=[0],
            test_mode='fixed',
            osc_config=osc_config
        )
        
        if result:
            print(f"   ✅ 修复后的通用方法测试成功!")
            print(f"   💡 文件名修复有效，解决了存储空间不足问题")
        else:
            print(f"   ❌ 修复后的通用方法仍然失败")
            
        return result
        
    except Exception as e:
        print(f"   ❌ 通用方法测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🎯 验证文件名修复效果")
    print("📝 目标: 确认文件名格式修复解决了存储空间不足问题")
    print("")
    
    # 显示文件名对比
    show_filename_comparison()
    
    # 测试文件名修复
    success_count = test_filename_fix()
    
    if success_count > 0:
        print(f"\n✅ 文件名修复有效，继续测试通用方法...")
        # 测试修复后的通用方法
        universal_success = test_conftest_integration()
        
        if universal_success:
            print(f"\n🎉 完美! 问题已彻底解决!")
            print(f"📝 现在固定频率和扫频模式都应该能正常工作")
        else:
            print(f"\n⚠️ 通用方法仍有问题，需要进一步调试")
    else:
        print(f"\n❌ 文件名修复无效，问题可能更复杂")
    
    print(f"\n" + "="*60)
    print(f"🎉 文件名修复验证完成!")
    print(f"📝 关键发现: 示波器不支持文件名中的方括号和逗号")
    print("="*60)
