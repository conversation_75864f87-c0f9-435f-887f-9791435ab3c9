#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from USBInterface import USBImpl

if __name__ == '__main__':
    devlist = USBImpl.usbdev_scan()
    print("探测到 USBCOMM 设备列表:")
    for i in range(0, len(devlist)):
        print("    ", devlist[i])

    # 使用第一个设备
    USBImpl.usbdev_open(devlist[0].name)

    
    ret = USBImpl.usbdev_set_mode(0)
    print("operation=",ret,"   set mode=1")

    # 调用提供的功能
    ret = USBImpl.usbdev_set_gpio(1, 1)
    print("operation=",ret,"    set gpio=1")
    ret,val = USBImpl.usbdev_get_gpio(1)
    print("operation=",ret,"    get gpio1 result: ", val)
    ret = USBImpl.usbdev_set_gpio(1, 0)
    print("operation=",ret,"    set gpio=0")
    ret,val = USBImpl.usbdev_get_gpio(1)
    print("operation=",ret,"    get gpio1 result: ", val)
    ret = USBImpl.usbdev_set_gpio(1, 1)
    print("operation=",ret,"    set gpio=1")
    ret,val = USBImpl.usbdev_get_gpio(1)
    print("operation=",ret,"    get gpio1 result: ", val)
    
    ret = USBImpl.usbdev_get_sn()
    print("get serial number result: ", ret)

    val=0x23
    ret = USBImpl.usbdev_i2c_set(1, 0, 0x29, 0x2323, val)
    print("operation=",ret,"   set register[0x2323]=:", hex(val))
    ret, val=USBImpl.usbdev_i2c_get(1, 0, 0x29, 0x2323)
    print("operation=",ret,"    get register[0x2323]=:", hex(val))
    
    
    ret = USBImpl.usbdev_set_mode(1)
    print("operation=",ret,"   set mode=1")
    
    val=0x23
    ret = USBImpl.usbdev_i2c_set(1, 0, 0x29, 0x2323, val)
    print("operation=",ret,"   set register[0x2323]=:", hex(val))
    ret, val=USBImpl.usbdev_i2c_get(1, 0, 0x29, 0x2323)
    print("operation=",ret,"    get register[0x2323]=:", hex(val))
    

    # 关闭设备
    USBImpl.usbdev_close()
