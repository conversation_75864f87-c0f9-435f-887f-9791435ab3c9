
/*******************************************************************************
    Verilog netlist generated by IPGEN Radiant Software (64-bit) 2.0.1.281.2
    Soft IP Version: 1.0.1
    Sun Jul 10 20:22:25 2022
*******************************************************************************/
/*******************************************************************************
    Wrapper Module generated per user settings.
*******************************************************************************/
module csi_dphy (sync_clk_i, 
        sync_rst_i, 
        lmmi_clk_i, 
        lmmi_resetn_i, 
        lmmi_wdata_i, 
        lmmi_wr_rdn_i, 
        lmmi_offset_i, 
        lmmi_request_i, 
        lmmi_ready_o, 
        lmmi_rdata_o, 
        lmmi_rdata_valid_o, 
        hs_rx_en_i, 
        hs_rx_data_o, 
        hs_rx_data_sync_o, 
        lp_rx_en_i, 
        lp_rx_data_p_o, 
        lp_rx_data_n_o, 
        lp_rx_clk_p_o, 
        lp_rx_clk_n_o, 
        pll_lock_i, 
        clk_p_io, 
        clk_n_io, 
        data_p_io, 
        data_n_io, 
        pd_dphy_i, 
        clk_byte_o, 
        ready_o) ;
    input sync_clk_i ; 
    input sync_rst_i ; 
    input lmmi_clk_i ; 
    input lmmi_resetn_i ; 
    input [3:0] lmmi_wdata_i ; 
    input lmmi_wr_rdn_i ; 
    input [4:0] lmmi_offset_i ; 
    input lmmi_request_i ; 
    output lmmi_ready_o ; 
    output [3:0] lmmi_rdata_o ; 
    output lmmi_rdata_valid_o ; 
    input hs_rx_en_i ; 
    output [15:0] hs_rx_data_o ; 
    output [1:0] hs_rx_data_sync_o ; 
    input lp_rx_en_i ; 
    output [1:0] lp_rx_data_p_o ; 
    output [1:0] lp_rx_data_n_o ; 
    output lp_rx_clk_p_o ; 
    output lp_rx_clk_n_o ; 
    input pll_lock_i ; 
    inout clk_p_io ; 
    inout clk_n_io ; 
    inout [1:0] data_p_io ; 
    inout [1:0] data_n_io ; 
    input pd_dphy_i ; 
    output clk_byte_o ; 
    output ready_o ; 
    csi_dphy_ipgen_lscc_mipi_dphy #(.INT_TYPE("RX"),
            .FAMILY("LIFCL"),
            .INTF("CSI2_APP"),
            .DPHY_IP("HARD_IP"),
            .CIL_BYPASS("CIL_BYPASSED"),
            .PLL_MODE("EXTERNAL"),
            .CLK_MODE("DISABLED"),
            .INT_DATA_RATE(400.0),
            .GEAR(8),
            .NUM_LANE(2),
            .SYNC_CLOCK_FREQ(24),
            .HSEL("DISABLED"),
            .CN("11111"),
            .CO("010"),
            .CM("10000010"),
            .REF_CLOCK_FROM_IO_PIN(0),
            .REF_CLK_INPUT_BUF_TYPE("MIPI_DPHY"),
            .START_UP_SYNCH_LOGIC(0),
            .T_DATA_SETTLE("11"),
            .T_CLK_SETTLE("10"),
            .MODE("FREQUENCY"),
            .PLL_REFCLK_FROM_PIN(0),
            .IO_TYPE("LVDS"),
            .CLKI_DIVIDER_ACTUAL_STR("1"),
            .FBK_MODE("CLKOP"),
            .FRAC_N_EN(0),
            .SS_EN(0),
            .FBCLK_DIVIDER_ACTUAL_STR("1"),
            .SSC_N_CODE_STR("0b000000001"),
            .SSC_F_CODE_STR("0b000000000000000"),
            .CLKOP_BYPASS(0),
            .INT_FREQ(200.0),
            .DIVOP_ACTUAL_STR("3"),
            .CLKOP_FREQ_ACTUAL(200.0),
            .TRIM_EN_P(0),
            .CLKOP_TRIM_MODE("Falling"),
            .CLKOP_TRIM("0b0000"),
            .CLKOS_EN(1),
            .CLKOS_BYPASS(0),
            .DIVOS_ACTUAL_STR("3"),
            .CLKOS_FREQ_ACTUAL(200.0),
            .TRIM_EN_S(0),
            .CLKOS_TRIM_MODE("Falling"),
            .CLKOS_TRIM("0b0000"),
            .CLKOS2_EN(1),
            .CLKOS2_BYPASS(0),
            .DIVOS2_ACTUAL_STR("3"),
            .CLKOS2_FREQ_ACTUAL(200.0),
            .CLKOS3_EN(0),
            .CLKOS3_BYPASS(0),
            .DIVOS3_ACTUAL_STR("3"),
            .CLKOS3_FREQ_ACTUAL(200.0),
            .CLKOS4_EN(0),
            .CLKOS4_BYPASS(0),
            .DIVOS4_ACTUAL_STR("3"),
            .CLKOS4_FREQ_ACTUAL(200.0),
            .CLKOS5_EN(0),
            .CLKOS5_BYPASS(0),
            .DIVOS5_ACTUAL_STR("3"),
            .CLKOS5_FREQ_ACTUAL(200.0),
            .CLKOP_PHASE_ACTUAL(0.0),
            .DELA("3"),
            .PHIA("0"),
            .CLKOS_PHASE_ACTUAL(90.0),
            .DELB("4"),
            .PHIB("0"),
            .CLKOS2_PHASE_ACTUAL(0.0),
            .DELC("3"),
            .PHIC("0"),
            .CLKOS3_PHASE_ACTUAL(0.0),
            .DELD("3"),
            .PHID("0"),
            .CLKOS4_PHASE_ACTUAL(0.0),
            .DELE("3"),
            .PHIE("0"),
            .CLKOS5_PHASE_ACTUAL(0.0),
            .DELF("3"),
            .PHIF("0"),
            .DYN_PORTS_EN(0),
            .ENCLKOP_EN(0),
            .ENCLKOS_EN(0),
            .ENCLKOS2_EN(0),
            .ENCLKOS3_EN(0),
            .ENCLKOS4_EN(0),
            .ENCLKOS5_EN(0),
            .PLL_RST(1),
            .LOCK_EN(1),
            .PLL_LOCK_STICKY(0),
            .LMMI_EN(0),
            .APB_EN(0),
            .LEGACY_EN(0),
            .POWERDOWN_EN(0),
            .VOLTAGE(0),
            .CSET("8P"),
            .CRIPPLE("9P"),
            .IPP_CTRL("0b0000"),
            .IPP_SEL("0b0001"),
            .BW_CTL_BIAS("0b0111"),
            .V2I_PP_RES("10K")) lscc_mipi_dphy_inst (.sync_clk_i(sync_clk_i), 
                .sync_rst_i(sync_rst_i), 
                .lmmi_clk_i(lmmi_clk_i), 
                .lmmi_resetn_i(lmmi_resetn_i), 
                .lmmi_wdata_i(lmmi_wdata_i[3:0]), 
                .lmmi_wr_rdn_i(lmmi_wr_rdn_i), 
                .lmmi_offset_i(lmmi_offset_i[4:0]), 
                .lmmi_request_i(lmmi_request_i), 
                .lmmi_ready_o(lmmi_ready_o), 
                .lmmi_rdata_o(lmmi_rdata_o[3:0]), 
                .lmmi_rdata_valid_o(lmmi_rdata_valid_o), 
                .hs_tx_en_i(1'b0), 
                .hs_tx_clk_en_i(1'b0), 
                .hs_tx_data_i(16'b0000000000000000), 
                .hs_tx_data_en_i(1'b0), 
                .hs_rx_en_i(hs_rx_en_i), 
                .hs_rx_data_o(hs_rx_data_o[15:0]), 
                .hs_rx_data_en_o(hs_rx_data_sync_o[1:0]), 
                .lp_tx_en_i(1'b0), 
                .lp_tx_data_p_i(1'b0), 
                .lp_tx_data_n_i(1'b0), 
                .lp_tx_data_en_i(1'b0), 
                .lp_tx_clk_p_i(1'b0), 
                .lp_tx_clk_n_i(1'b0), 
                .lp_rx_en_i(lp_rx_en_i), 
                .lp_rx_data_p_o(lp_rx_data_p_o[1:0]), 
                .lp_rx_data_n_o(lp_rx_data_n_o[1:0]), 
                .lp_rx_clk_p_o(lp_rx_clk_p_o), 
                .lp_rx_clk_n_o(lp_rx_clk_n_o), 
                .pll_clkop_i(1'b0), 
                .pll_clkos_i(1'b0), 
                .pll_lock_i(pll_lock_i), 
                .hs_tx_cil_ready_o(), 
                .data_lane_ss_o(), 
                .clk_p_io(clk_p_io), 
                .clk_n_io(clk_n_io), 
                .data_p_io(data_p_io[1:0]), 
                .data_n_io(data_n_io[1:0]), 
                .usrstdby_i(1'b0), 
                .pd_dphy_i(pd_dphy_i), 
                .pll_lock_o(), 
                .clk_byte_o(clk_byte_o), 
                .ready_o(ready_o)) ; 
endmodule



// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               : DPHY_RX
// File                  : lscc_clock_divider.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           : Used to have clock from 12 MHz to 20 MHz from
//                       : input 24 - 200 MMz
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             : Lattice Semiconductor
// Mod. Date             :
// Changes Made          :
// =============================================================================
module csi_dphy_ipgen_lscc_clock_divider #(parameter integer TGT_FREQ_IN = 100) (
    // -----------------------------------------------------------------------------
    // Module Parameters
    // -----------------------------------------------------------------------------
    // target frequency in 24 - 200 MHz
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    input wire clk_i, 
    input wire reset_n_i, 
    output wire clk_o) ;
    // -----------------------------------------------------------------------------
    // Local Parameters
    // -----------------------------------------------------------------------------
    localparam DIVIDER //
        // (TGT_FREQ_IN - 1)     /|
        // ------------------ +   |
        //        20             ---
        //
        //
        //
        //
        /*(181 <= TGT_FREQ_IN & TGT_FREQ_IN <= 200)?*/ = (((24 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 40)) ? 2 : (((41 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 60)) ? 3 : (((61 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 80)) ? 4 : (((81 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 100)) ? 5 : (((101 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 120)) ? 6 : (((121 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 140)) ? 7 : (((141 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 160)) ? 8 : (((161 <= TGT_FREQ_IN) & (TGT_FREQ_IN <= 180)) ? 9 : 10)))))))) ; //
    // -----------------------------------------------------------------------------
    // Generate Variables
    // -----------------------------------------------------------------------------
    integer count ; 
    // -----------------------------------------------------------------------------
    // Sequential Registers
    // -----------------------------------------------------------------------------
    reg clk_r ; 
    // -----------------------------------------------------------------------------
    // Assign Statements
    // -----------------------------------------------------------------------------
    assign clk_o = clk_r ; 
    // -----------------------------------------------------------------------------
    // Sequential Blocks
    // -----------------------------------------------------------------------------
    always
        @(posedge clk_i)
        begin
            if ((reset_n_i == 1'd0)) 
                begin
                    count <=  32'd0 ;
                    clk_r <=  1'd0 ;
                end
            else
                begin
                    if ((count < (DIVIDER - 1))) 
                        begin
                            count <=  (count + 32'd1) ;
                            clk_r <=  1'd0 ;
                        end
                    else
                        begin
                            count <=  32'd0 ;
                            clk_r <=  1'd1 ;
                        end
                end
        end
endmodule



// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               :
// File                  : lscc_gddr_sync.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           :rst_i       => Synchronous reset
//                       :sync_clk_i  => oscillator clk or other constant
//                       :               running low speed clk.
//                       :               note that this clk should not be
//                       :               coming from clk sources
//                       :               that this module will stop_o or reset
//                       :               (e.g. ECLKSYNC, CLKDIV)
//                       :start_i     => Initialize the sync process
//                       :stop_o      => ECLKSYNC.stop_o signal
//                       :ddr_reset_o => DDR and CLKDIV reset signal
//                       :ready_o     => READY signal; clock sync is done.
//
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             :
// Mod. Date             :
// Changes Made          :
// =============================================================================
// Version               : 1.1
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 7/23/2018
// Changes Made          : Checking codding style
// =============================================================================
module csi_dphy_ipgen_lscc_gddr_sync (
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    input wire sync_clk_i, 
    input wire rst_i, 
    input wire start_i, 
    output wire stop_o, 
    output wire ddr_reset_o, 
    output wire ready_o) ;
    // -----------------------------------------------------------------------------
    // Local Parameters
    // -----------------------------------------------------------------------------
    localparam INIT = 3'b000 ; 
    localparam STOP = 3'b001 ; 
    localparam RESET = 3'b011 ; 
    localparam READY = 3'b100 ; 
    // -----------------------------------------------------------------------------
    // Sequential Registers
    // -----------------------------------------------------------------------------
    reg ddr_reset_d ; 
    reg [3:0] ctrl_cnt ; // control counter
    reg [2:0] stop_assert ; // stop_o signal counter
    reg [2:0] cs_gddr_sync ; // current state
    reg [2:0] ns_gddr_sync ; // next state
    reg reset_flag ; // flag signal that
    // indicates that RESET
    // is already done
    //-----------------------------------------------------------------------------
    //  WIRE ASSIGNMENTS
    //-----------------------------------------------------------------------------
    assign stop_o = cs_gddr_sync[0] ; 
    assign ddr_reset_o = (cs_gddr_sync[1] | ddr_reset_d) ; 
    assign ready_o = cs_gddr_sync[2] ; 
    // -----------------------------------------------------------------------------
    // Combinatorial Blocks
    // -----------------------------------------------------------------------------
    always
        @(posedge sync_clk_i)
        begin
            if ((rst_i == 1'b1)) 
                begin
                    cs_gddr_sync <=  INIT ;
                    ctrl_cnt <=  4'd0 ;
                    stop_assert <=  3'd0 ;
                    reset_flag <=  1'b0 ;
                    ddr_reset_d <=  1'b1 ;
                end
            else
                begin
                    cs_gddr_sync <=  ns_gddr_sync ;
                    ddr_reset_d <=  1'b0 ;
                    if ((((cs_gddr_sync == INIT) && (reset_flag == 1'b0)) || ((ctrl_cnt == 3) && (cs_gddr_sync != INIT)))) 
                        begin
                            ctrl_cnt <=  4'd0 ;
                        end
                    else
                        if ((ctrl_cnt < 8)) 
                            begin
                                ctrl_cnt <=  (ctrl_cnt + 1'b1) ;
                            end
                    if (((((!rst_i) && start_i) && (stop_assert < 4)) && (reset_flag == 1'b0))) 
                        begin
                            stop_assert <=  (stop_assert + 1'b1) ;
                        end
                    if (((cs_gddr_sync == RESET) && (ns_gddr_sync == STOP))) 
                        begin
                            reset_flag <=  1'b1 ;
                        end
                    if (((cs_gddr_sync == READY) && (ns_gddr_sync == INIT))) 
                        begin
                            reset_flag <=  1'b0 ;
                        end
                end
        end
    always
        @(*)
        begin
            case (cs_gddr_sync)
            INIT : 
                begin
                    if (((start_i && (stop_assert == 3)) && (reset_flag == 1'b0))) 
                        begin
                            ns_gddr_sync = STOP ;
                        end
                    else
                        if ((((reset_flag == 1'b1) && (ctrl_cnt == 7)) && start_i)) 
                            begin
                                ns_gddr_sync = READY ;
                            end
                        else
                            begin
                                ns_gddr_sync = INIT ;
                            end
                end
            STOP : 
                begin
                    if ((ctrl_cnt == 3)) 
                        begin
                            if ((reset_flag == 1'b1)) 
                                begin
                                    ns_gddr_sync = INIT ;
                                end
                            else
                                begin
                                    ns_gddr_sync = RESET ;
                                end
                        end
                    else
                        begin
                            ns_gddr_sync = STOP ;
                        end
                end
            RESET : 
                begin
                    if ((ctrl_cnt == 3)) 
                        begin
                            ns_gddr_sync = STOP ;
                        end
                    else
                        begin
                            ns_gddr_sync = RESET ;
                        end
                end
            READY : 
                begin
                    if ((!start_i)) 
                        begin
                            ns_gddr_sync = INIT ;
                        end
                    else
                        begin
                            ns_gddr_sync = READY ;
                        end
                end
            default : 
                begin
                    ns_gddr_sync = cs_gddr_sync ;
                end
            endcase 
        end
endmodule



`timescale 1ns/10ps
// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               : MIPI_DPHY
// File                  : lscc_mipi_dphy.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           : MIPI DPHY uses a point-to-point differential
//                       : interface, with modular architecture that supports
//                       : multiple data lanes and a clock lane, allowing all
//                       : possible configurations.
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 04/05/18
// Changes Made          : Initial version
// =============================================================================
// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               :
// File                  : lscc_mipi_wrapper_rx.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           :
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 06/05/18
// Changes Made          : Initial version
// =============================================================================
module csi_dphy_ipgen_lscc_mipi_wrapper_rx #(parameter NUM_LANE = 4, 
        parameter GEAR = 8, 
        parameter INTF = "CSI2_APP", 
        parameter HSEL = "DISABLED", 
        parameter CLK_MODE = "ENABLED", 
        parameter CIL_BYPASS = "CIL_BYPASSED", 
        parameter INT_DATA_RATE = 2500.0, 
        parameter REF_CLOCK_FREQ = 24, 
        parameter CM = "00000000", 
        parameter CN = "00000", 
        parameter CO = "000", 
        parameter DPHY_IP = "HARD_IP", 
        parameter DATA_WIDTH = (NUM_LANE * GEAR), 
        parameter [((8 * 6) - 1):0] T_DATA_SETTLE = "000001", 
        parameter [((8 * 6) - 1):0] T_CLK_SETTLE = "000001") (
    // -----------------------------------------------------------------------------
    // Module Parameters
    // -----------------------------------------------------------------------------
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    // Clock and reset
    input wire sync_clk_i, 
    input wire sync_rst_i, 
    /// LMMI
    input wire lmmi_clk_i, 
    input wire lmmi_resetn_i, 
    input wire [3:0] lmmi_wdata_i, 
    input wire lmmi_wr_rdn_i, 
    input wire [4:0] lmmi_offset_i, 
    input wire lmmi_request_i, 
    output wire lmmi_ready_o, 
    output wire [3:0] lmmi_rdata_o, 
    output wire lmmi_rdata_valid_o, 
    // gddr_sync input
    input wire pll_lock_i, 
    // HS_RX signals
    input wire hs_rx_en_i, 
    output wire [(DATA_WIDTH - 1):0] hs_rx_data_o, 
    output wire [(NUM_LANE - 1):0] hs_rx_data_en_o, 
    // LP_RX signals
    input wire lp_rx_en_i, 
    output wire [(NUM_LANE - 1):0] lp_rx_data_p_o, 
    output wire [(NUM_LANE - 1):0] lp_rx_data_n_o, 
    output wire lp_rx_clk_p_o, 
    output wire lp_rx_clk_n_o, 
    // LP_TX signals
    input wire lp_tx_en_i, 
    input wire lp_tx_data_p_i, 
    input wire lp_tx_data_n_i, 
    input wire lp_tx_data_en_i, 
    input wire lp_tx_clk_p_i, 
    input wire lp_tx_clk_n_i, 
    // Other signals
    output wire ready_o, 
    input wire pd_dphy_i, 
    input wire usrstdby_i, 
    output wire clk_byte_o, 
    // DPHY ports
    inout wire clk_p_io, 
    inout wire clk_n_io, 
    inout wire [(NUM_LANE - 1):0] data_p_io, 
    inout wire [(NUM_LANE - 1):0] data_n_io) ;
    // -----------------------------------------------------------------------------
    // Local Parameters
    // -----------------------------------------------------------------------------
    localparam DPHY_CLK_MODE = CLK_MODE ; ///  unused
    localparam DPHY_GEAR = ((GEAR == 8) ? "0b00" : ((GEAR == 16) ? "0b01" : ((GEAR == 32) ? "0b10" : ((GEAR == 64) ? "0b11" : "0b00")))) ; 
    localparam DPHY_DESKEW_EN = ((INT_DATA_RATE > 1500.0) ? "ENABLED" : "DISABLED") ; 
    // -----------------------------------------------------------------------------
    // Wire Declarations
    // -----------------------------------------------------------------------------
    wire [3:0] d_p_w ; 
    wire [3:0] d_n_w ; 
    wire [3:0] lp_data_p_w ; 
    wire [3:0] lp_data_n_w ; 
    wire [15:0] dphy_hs_data_line0_w ; 
    wire [15:0] dphy_hs_data_line1_w ; 
    wire [15:0] dphy_hs_data_line2_w ; 
    wire [15:0] dphy_hs_data_line3_w ; 
    wire int_clk ; 
    wire clk_lp_ctrl_i ; 
    // Now unused
    wire cd_d0_p ; 
    wire cd_d0_n ; 
    wire [3:0] d_sote_det ; 
    wire [3:0] u_rx_sync_hs_w ; 
    wire [3:0] u1_rx_sync_hs_w ; 
    wire [3:0] u2_rx_sync_hs_w ; 
    wire [3:0] u3_rx_sync_hs_w ; 
    ///
    wire [3:0] hs_rx_data_en_w ; 
    ///
    wire tx_esc_clk_w ; 
    // -----------------------------------------------------------------------------
    // Generate Assign Statements
    // -----------------------------------------------------------------------------
    generate
        if ((DPHY_IP == "HARD_IP")) 
            begin : HARD_IP_DATA_CONNECTION
                assign d_n_w[(NUM_LANE - 1):0] = data_n_io ; 
                assign d_p_w[(NUM_LANE - 1):0] = data_p_io ; 
                assign lp_rx_data_p_o[(NUM_LANE - 1):0] = lp_data_p_w[(NUM_LANE - 1):0] ; 
                assign lp_rx_data_n_o[(NUM_LANE - 1):0] = lp_data_n_w[(NUM_LANE - 1):0] ; 
                /*(NUM_LANE == 1)? */
                assign hs_rx_data_o = ((NUM_LANE == 4) ? {dphy_hs_data_line3_w[(GEAR - 1):0],
                            dphy_hs_data_line2_w[(GEAR - 1):0],
                            dphy_hs_data_line1_w[(GEAR - 1):0],
                            dphy_hs_data_line0_w[(GEAR - 1):0]} : ((NUM_LANE == 3) ? {dphy_hs_data_line3_w[(GEAR - 1):0],
                            dphy_hs_data_line2_w[(GEAR - 1):0],
                            dphy_hs_data_line1_w[(GEAR - 1):0]} : ((NUM_LANE == 2) ? {dphy_hs_data_line1_w[(GEAR - 1):0],
                            dphy_hs_data_line0_w[(GEAR - 1):0]} : {dphy_hs_data_line0_w[(GEAR - 1):0]}))) ; 
            end
    endgenerate
    assign hs_rx_data_en_w = {(|u3_rx_sync_hs_w),
                (|u2_rx_sync_hs_w),
                (|u1_rx_sync_hs_w),
                (|u_rx_sync_hs_w)} ; 
    assign hs_rx_data_en_o = hs_rx_data_en_w[(NUM_LANE - 1):0] ; 
    // -----------------------------------------------------------------------------
    // Submodule Instantiations
    // -----------------------------------------------------------------------------
    generate
        if ((DPHY_IP == "LATTICE")) 
            begin : LATTICE_SOFT_IP
                csi_dphy_ipgen_lscc_mipi_dphy_soft_rx #(.NUM_LANE(NUM_LANE),
                        .GEAR(GEAR)) u_mipi (// Clock and reset
                        .sync_clk_i(sync_clk_i), 
                            .sync_rst_i(sync_rst_i), 
                            ///                
                        .pll_lock_i(pll_lock_i), 
                            // HS_RX signals
                        .hs_rx_en_i(hs_rx_en_i), 
                            .hs_rx_data_o(hs_rx_data_o), 
                            // LP_RX signals
                        .lp_rx_en_i(lp_rx_en_i), 
                            .lp_rx_data_p_o(lp_rx_data_p_o), 
                            .lp_rx_data_n_o(lp_rx_data_n_o), 
                            .lp_rx_data_cd_o(), 
                            .lp_rx_clk_p_o(lp_rx_clk_p_o), 
                            .lp_rx_clk_n_o(lp_rx_clk_n_o), 
                            .lp_rx_clk_cd_o(), 
                            // LP_TX signals
                        .lp_tx_en_i(lp_tx_en_i), 
                            .lp_tx_data_p_i(lp_tx_data_p_i), 
                            .lp_tx_data_n_i(lp_tx_data_n_i), 
                            // Other :)
                        .byte_clk_o(clk_byte_o), 
                            .ready_o(ready_o), 
                            // DPHY ports
                        .clk_p_io(clk_p_io), 
                            .clk_n_io(clk_n_io), 
                            .data_p_io(data_p_io), 
                            .data_n_io(data_n_io)) ; 
                assign lmmi_rdata_o = 4'hF ; // Add to meet requirement from "Synplify Pro"
                assign lmmi_rdata_valid_o = 1'd1 ; // Add to meet requirement from "Synplify Pro"
                assign lmmi_ready_o = 1'd1 ; // Add to meet requirement from "Synplify Pro"
                assign d_p_w = 4'hF ; // Add to meet requirement from "Synplify Pro"
                assign d_n_w = 4'hF ; // Add to meet requirement from "Synplify Pro"
                assign lp_data_p_w = 4'hF ; // Add to meet requirement from "Synplify Pro"
                assign lp_data_n_w = 4'hF ; // Add to meet requirement from "Synplify Pro"
                assign dphy_hs_data_line0_w = 16'hFFFF ; // Add to meet requirement from "Synplify Pro"
                assign dphy_hs_data_line1_w = 16'hFFFF ; // Add to meet requirement from "Synplify Pro"
                assign dphy_hs_data_line2_w = 16'hFFFF ; // Add to meet requirement from "Synplify Pro"
                assign dphy_hs_data_line3_w = 16'hFFFF ; // Add to meet requirement from "Synplify Pro"
                assign cd_d0_p = 1'd1 ; // Add to meet requirement from "Synplify Pro"
                assign cd_d0_n = 1'd1 ; // Add to meet requirement from "Synplify Pro"
            end
        else
            if ((DPHY_IP == "HARD_IP")) 
                begin : HARD_IP
                    if ((CIL_BYPASS == "CIL_BYPASSED")) 
                        begin : NO_CIL
                            DPHY #(.GSR("ENABLED"),
                                    .AUTO_PD_EN("POWERED_UP"),
                                    .CFG_NUM_LANES(((NUM_LANE == 4) ? "FOUR_LANES" : ((NUM_LANE == 3) ? "THREE_LANES" : ((NUM_LANE == 2) ? "TWO_LANES" : "ONE_LANE")))),
                                    .CM("0b00000000"),
                                    .CN("0b00000"),
                                    .CO("0b000"),
                                    .CONT_CLK_MODE(CLK_MODE),
                                    .DESKEW_EN(DPHY_DESKEW_EN),
                                    .DSI_CSI(INTF),
                                    .EN_CIL("CIL_BYPASSED"),
                                    .HSEL(HSEL),
                                    .LANE0_SEL("LANE_0"),
                                    .LOCK_BYP("GATE_TXBYTECLKHS"),
                                    .MASTER_SLAVE("SLAVE"),
                                    .PLLCLKBYPASS("BYPASSED"),
                                    .RSEL("0b00"),
                                    .RXCDRP("0b01"),
                                    .RXDATAWIDTHHS(DPHY_GEAR),
                                    .RXLPRP("0b001"),
                                    .TEST_ENBL("0b000000"),
                                    .TEST_PATTERN("0b00000000000000000000000000000000"),
                                    .TST("0b0000"),
                                    .TXDATAWIDTHHS("0b00"),
                                    .U_PRG_HS_PREPARE("0b00"),
                                    .U_PRG_HS_TRAIL("0b000000"),
                                    .U_PRG_HS_ZERO("0b000000"),
                                    .U_PRG_RXHS_SETTLE("0b000001"),
                                    .UC_PRG_HS_PREPARE("1P0_TXCLKESC"),
                                    .UC_PRG_HS_TRAIL("0b00000"),
                                    .UC_PRG_HS_ZERO("0b0000000"),
                                    .UC_PRG_RXHS_SETTLE("0b000001")) u_dphy_nocil (// IN
                                    //LMMI
                                    .LMMICLK(lmmi_clk_i), 
                                        .LMMIRESET_N(lmmi_resetn_i), 
                                        .LMMIREQUEST(lmmi_request_i), 
                                        .LMMIWRRD_N(lmmi_wr_rdn_i), 
                                        .LMMIOFFSET(lmmi_offset_i), 
                                        .LMMIWDATA(lmmi_wdata_i), 
                                        .BITCKEXT(1'b1), 
                                        .CLKREF(sync_clk_i), 
                                        .PDDPHY((sync_rst_i | pd_dphy_i)), 
                                        .PDPLL(1'd1), 
                                        .SCCLKIN(1'd1),  // Scan clock in.
                                    // HS_RX ENABLE
                                    .UCTXUPSC(hs_rx_en_i), 
                                        .UTXSKD0N(((NUM_LANE > 0) ? hs_rx_en_i : 1'd0)), 
                                        .U1TXSK(((NUM_LANE > 1) ? hs_rx_en_i : 1'd0)), 
                                        .U2TXSKC(((NUM_LANE > 2) ? hs_rx_en_i : 1'd0)), 
                                        .U3TXSKC(((NUM_LANE > 3) ? hs_rx_en_i : 1'd0)), 
                                        // DESERIALAIZER enable
                                    .UTXENER(1'd0),  // Override the Deserializer token detector and enable Deserializer Byte Clock and DATA
                                    .UTXRD0EN((!sync_rst_i)), 
                                        .U1TXREQ((!sync_rst_i)), 
                                        .U2TXREQ((!sync_rst_i)), 
                                        .U3TXREQ((!sync_rst_i)), 
                                        // LP_RX enable
                                    .U1TDE1CK(((CLK_MODE == "ENABLED") ? 1'd0 : lp_rx_en_i)), 
                                        .UDE5D0RN(((CLK_MODE == "ENABLED") ? 1'd0 : ((NUM_LANE > 0) ? lp_rx_en_i : 1'd0))),  // Low Power Receiver Enable Signal
                                    .UDE6D1RN(((CLK_MODE == "ENABLED") ? 1'd0 : ((NUM_LANE > 1) ? lp_rx_en_i : 1'd0))), 
                                        .UDE7D2RN(((CLK_MODE == "ENABLED") ? 1'd0 : ((NUM_LANE > 2) ? lp_rx_en_i : 1'd0))), 
                                        .U1TDE0D3(((CLK_MODE == "ENABLED") ? 1'd0 : ((NUM_LANE > 3) ? lp_rx_en_i : 1'd0))), 
                                        // LP_TX ENABLE
                                    .UDE4CKTN(1'd0), 
                                        .UDE0D0TN(((CLK_MODE == "ENABLED") ? 1'd0 : ((NUM_LANE > 0) ? lp_tx_data_en_i : 1'd0))), 
                                        .UDE1D1TN(1'd0), 
                                        .UDE2D2TN(1'd0), 
                                        .UDE3D3TN(1'd0), 
                                        // LP_TX
                                    .U3TXUPSX(1'd1),  // LP_TX positive clock.
                                    .U3TXLPDT(1'd1),  // LP_TX negative clock.
                                    .UTXMDTX(lp_tx_data_p_i),  // lane 0 LP_TX positive data.
                                    .U1FTXST(lp_tx_data_n_i),  // lane 0 LP_TX negative data.
                                    .U2FTXST(1'd1),  // lane 1 LP_TX positive data.
                                    .U3FTXST(1'd1),  // lane 1 LP_TX negative data.
                                    .U3TDISD2(1'd1),  // lane 2 LP_TX positive data.
                                    .U3TREQD2(1'd1),  // lane 2 LP_TX negative data.
                                    .U3TXVD3(1'd1),  // lane 3 LP_TX positive data.
                                    .U3TXULPS(1'd1),  // lane 3 LP_TX negative data.
                                    // LP_TX power down
                                    .U2TDE4CK(1'd1), 
                                        .U2TDE5D0((~lp_tx_data_en_i)), 
                                        .U2TDE6D1(1'd1), 
                                        .U2TDE7D2(1'd1), 
                                        .U3TDE0D3(1'd1), 
                                        // HS_TX ENABLE
                                    .UCENCK(1'd0), 
                                        .UED0THEN(1'd0), 
                                        .U1ENTHEN(1'd0), 
                                        .U2END2(1'd0), 
                                        .U3END3(1'd0), 
                                        // SERIALAIZER ENABLE
                                    .UTRD0SEN(1'd0), 
                                        .U1TXREQH(1'd0), 
                                        .U2TXREQH(1'd0), 
                                        .U3TXREQH(1'd0), 
                                        // TX WORD VALID
                                    .UTXWVDHS(4'hF), 
                                        .U1TXWVHS(4'hF), 
                                        .U2TXWVHS(4'hF), 
                                        .U3TXWVHS(4'hF), 
                                        // HS_TX power down
                                    .U2TDE0D0(1'd1), 
                                        .U2TDE1D1(1'd1), 
                                        .U2TDE2D2(1'd1), 
                                        .U2TDE3D3(1'd1), 
                                        // HS_TX
                                    .U3TDE1D0(1'd1), 
                                        .U3TDE2D1(1'd1), 
                                        .U3TDE3D2(1'd1), 
                                        .U3TDE4D3(1'd1), 
                                        // HS_TX
                                    .UTXDHS(32'hFFFFFFFF), 
                                        .U1TXDHS(32'hFFFFFFFF), 
                                        .U2TXDHS(32'hFFFFFFFF), 
                                        .U3TXDHS(32'hFFFFFFFF), 
                                        //CIL
                                    .URXCKINE(1'd1), 
                                        .UTXCKE(1'd1), 
                                        .UTRNREQ(1'd1), 
                                        .UFRXMODE(1'd1), 
                                        .UTDIS(1'd1), 
                                        .UTXTGE0(1'd1), 
                                        .UTXTGE1(1'd1), 
                                        .UTXTGE2(1'd1), 
                                        .UTXTGE3(1'd1), 
                                        .UTXUPSEX(1'd1), 
                                        .UTXVDE(1'd1), 
                                        .U1FRXMD(1'd1), 
                                        .U1TDIS(1'd1), 
                                        .U1TREQ(1'd1), 
                                        .U1TXTGE0(1'd1), 
                                        .U1TXTGE1(1'd1), 
                                        .U1TXTGE2(1'd1), 
                                        .U1TXTGE3(1'd1), 
                                        .U1TXUPSE(1'd1), 
                                        .U1TXUPSX(1'd1), 
                                        .U2FRXMD(1'd1), 
                                        .U2TDIS(1'd1), 
                                        .U2TREQ(1'd1), 
                                        .U2TPDTE(1'd1), 
                                        .U2TXTGE0(1'd1), 
                                        .U2TXTGE1(1'd1), 
                                        .U2TXTGE2(1'd1), 
                                        .U2TXTGE3(1'd1), 
                                        .U2TXUPSE(1'd1), 
                                        .U2TXUPSX(1'd1), 
                                        .U2TXVDE(1'd1), 
                                        .U3FRXMD(1'd1), 
                                        .U3TXTGE0(1'd1), 
                                        .U3TXTGE1(1'd1), 
                                        .U3TXTGE2(1'd1), 
                                        .U3TXTGE3(1'd1), 
                                        .U1TXVDE(1'd1), 
                                        //
                                    .UTXULPSE(1'd1), 
                                        //Dx_CDEN
                                    .U1TDE6(1'd1), 
                                        .U1TDE2D0(1'd1), 
                                        .U1TDE3D1(1'd1), 
                                        .U1TDE4D2(1'd1), 
                                        .U1TDE5D3(1'd1), 
                                        //
                                    .U1TDE7(1'd1),  // clock_tx_hs
                                    .U1TXLPD(1'd1),  // LB_EN.(Dy_DTXHS => DPy/DNy , DPy/DNy => Dy_DRXHS)
                                    .U3TDE5CK(1'd1),  // HS_TX clock
                                    .U3TDE6(1'd0),  // MST_RV_EN.
                                    .U3TDE7(1'd0),  // SLV_RV_EN.
                                    .UCTXREQH(1'd0),  // clock hs gate.
                                    .UCTXUPSX(1'd0),  // ??? (usrstdby_i)
                                    .LTSTEN(1'd0),  // Enable signal for LPTX VOH debug test mode. Active high. default=1'b0.
                                    .LTSTLANE(2'd1),  // Lane select signal in LPTX VOH debug test mode. Choose the data lane under test. Effective only when LPTX_TST_EN=1.
                                    //OUT
                                    .LMMIRDATA(lmmi_rdata_o), 
                                        .LMMIRDATAVALID(lmmi_rdata_valid_o), 
                                        .LMMIREADY(lmmi_ready_o), 
                                        .D0ACTIVE(), 
                                        .D0BYTCNT(), 
                                        .D0ERRCNT(), 
                                        .D0PASS(), 
                                        .D0VALID(), 
                                        .D1ACTIVE(), 
                                        .D1BYTCNT(), 
                                        .D1ERRCNT(), 
                                        .D1PASS(), 
                                        .D1VALID(), 
                                        .D2ACTIVE(), 
                                        .D2BYTCNT(), 
                                        .D2ERRCNT(), 
                                        .D2PASS(), 
                                        .D2VALID(), 
                                        .D3ACTIVE(), 
                                        .D3BYTCNT(), 
                                        .D3ERRCNT(), 
                                        .D3PASS(), 
                                        .D3VALID(), 
                                        .DCTSTOUT(), 
                                        .LOCK(), 
                                        .UDIR(), 
                                        .UERCLP0(), 
                                        .UERCLP1(), 
                                        .UERCTRL(), 
                                        .UERE(), 
                                        .UERSTHS(), 
                                        .UERSSHS(d_sote_det[0]), 
                                        .U1ERSSHS(d_sote_det[1]), 
                                        .U2ERSSHS(d_sote_det[2]), 
                                        .U3ERSSHS(d_sote_det[3]), 
                                        ///
                                    .URXDHS(dphy_hs_data_line0_w), 
                                        .U1RXDHS(dphy_hs_data_line1_w), 
                                        .U2RXDHS(dphy_hs_data_line2_w), 
                                        .U3RXDHS(dphy_hs_data_line3_w), 
                                        ///
                                    .URXSHS(u_rx_sync_hs_w), 
                                        .U1RXSHS(u1_rx_sync_hs_w), 
                                        .U2RXSHS(u2_rx_sync_hs_w), 
                                        .U3RXSHS(u3_rx_sync_hs_w), 
                                        ///
                                    .URXVDHS(), 
                                        .U1RXVDHS(), 
                                        .U2RXVDHS(), 
                                        .U3RXVDHS(), 
                                        ///
                                    .URE2CKDP(lp_rx_clk_p_o), 
                                        .URE3CKDN(lp_rx_clk_n_o), 
                                        .URXDRX(lp_data_n_w[0]), 
                                        .UTXRYP(lp_data_p_w[0]), 
                                        .U1RXSK(lp_data_n_w[1]), 
                                        .U1TXRY(lp_data_p_w[1]), 
                                        .U2RXSKC(lp_data_n_w[2]), 
                                        .U2TXRYH(lp_data_p_w[2]), 
                                        .URE0D3DP(lp_data_n_w[3]), 
                                        .URE1D3DN(lp_data_p_w[3]), 
                                        ///
                                    .UERSE(), 
                                        .URXACTHS(), 
                                        .URXCKE(), 
                                        .U1RE0D(cd_d0_p), 
                                        .U1RE1CN(cd_d0_n), 
                                        .URXDE(), 
                                        .URXLPDTE(), 
                                        .URXSKCHS(), 
                                        .URXULPSE(), 
                                        .URXVDE(), 
                                        .USSTT(), 
                                        .UTXRRS(), 
                                        .UTXRYSK(), 
                                        .UUSAN(), 
                                        .U1DIR(), 
                                        .U1ERCLP0(), 
                                        .U1ERCLP1(), 
                                        .U1ERCTRL(), 
                                        .U1ERE(), 
                                        .U1ERSTHS(), 
                                        .U1ERSE(), 
                                        .U1RXATHS(), 
                                        .U1RXCKE(), 
                                        .U1RXDE(), 
                                        .U1RXDTE(), 
                                        .U1RXSKS(), 
                                        .U1RE2D(), 
                                        .U1RE3N(), 
                                        .U1RXUPSE(), 
                                        .U1RXVDE(), 
                                        .U1SSTT(), 
                                        .U1TXRYSK(), 
                                        .U1TXRYE(), 
                                        .U1USAN(), 
                                        .U2DIR(), 
                                        .U2ERCLP0(), 
                                        .U2ERCLP1(), 
                                        .U2ERCTRL(), 
                                        .U2ERE(), 
                                        .U2ERSTHS(), 
                                        .U2ERSE(), 
                                        .U2RXACHS(), 
                                        .U2RXCKE(), 
                                        .U2RXDE(), 
                                        .U2RPDTE(), 
                                        .U2RXSK(), 
                                        .U2RE0D2(), 
                                        .U2RE1D2(), 
                                        .U2RE2D3(), 
                                        .U2RE3D3(), 
                                        .U2RXUPSE(), 
                                        .U2RXVDE(), 
                                        .U2SSTT(), 
                                        .U2TXRYE(), 
                                        .U2TXRYSK(), 
                                        .U2USAN(), 
                                        .U3DIR(), 
                                        .U3ERCLP0(), 
                                        .U3ERCLP1(), 
                                        .U3ERCTRL(), 
                                        .U3ERE(), 
                                        .U3ERSTHS(), 
                                        .U3ERSE(), 
                                        .U3RXATHS(), 
                                        .U3RXCKE(), 
                                        .U3RXDE(), 
                                        .U3RPDTE(), 
                                        .U3RXSK(), 
                                        .U3RXSKC(), 
                                        .U3RE0CK(), 
                                        .U3RE1CK(), 
                                        .U3RE2(), 
                                        .U3RE3(), 
                                        .U3RXUPSE(), 
                                        .U3RXVDE(), 
                                        .U3SSTT(), 
                                        .U3TXRY(), 
                                        .U3TXRYHS(), 
                                        .U3TXRYSK(), 
                                        .U3USAN(), 
                                        .UCRXCKAT(), 
                                        .UCRXUCKN(), 
                                        .UCSSTT(), 
                                        .UCUSAN(), 
                                        .URWDCKHS(clk_byte_o), 
                                        .UTWDCKHS(), 
                                        .UCRXWCHS(int_clk),  ///clk_hs_o
                                    .CLKLBACT(), 
                                        //IO
                                    .CKN(clk_n_io), 
                                        .CKP(clk_p_io), 
                                        .DN0(data_n_io[0]), 
                                        .DN1(d_n_w[1]), 
                                        .DN2(d_n_w[2]), 
                                        .DN3(d_n_w[3]), 
                                        .DP0(data_p_io[0]), 
                                        .DP1(d_p_w[1]), 
                                        .DP2(d_p_w[2]), 
                                        .DP3(d_p_w[3])) ; 
                        end
                    else
                        begin : CIL
                            csi_dphy_ipgen_lscc_clock_divider #(.TGT_FREQ_IN(REF_CLOCK_FREQ)) u_clock_divider (.clk_i(sync_clk_i), 
                                        .reset_n_i((!sync_rst_i)), 
                                        .clk_o(tx_esc_clk_w)) ; 
                            DPHY #(.GSR("ENABLED"),
                                    .AUTO_PD_EN("POWERED_UP"),
                                    .CFG_NUM_LANES(((NUM_LANE == 4) ? "FOUR_LANES" : ((NUM_LANE == 3) ? "THREE_LANES" : ((NUM_LANE == 2) ? "TWO_LANES" : "ONE_LANE")))),
                                    .CM("0b00000000"),
                                    .CN("0b00000"),
                                    .CO("0b000"),
                                    .CONT_CLK_MODE(CLK_MODE),
                                    .DESKEW_EN(DPHY_DESKEW_EN),
                                    .DSI_CSI(INTF),
                                    .EN_CIL("CIL_ENABLED"),
                                    .HSEL(HSEL),
                                    .LANE0_SEL("LANE_0"),
                                    .LOCK_BYP("GATE_TXBYTECLKHS"),
                                    .MASTER_SLAVE("SLAVE"),
                                    .PLLCLKBYPASS("BYPASSED"),
                                    .RSEL("0b00"),
                                    .RXCDRP("0b01"),
                                    .RXDATAWIDTHHS(DPHY_GEAR),
                                    .RXLPRP("0b001"),
                                    .TEST_ENBL("0b000000"),
                                    .TEST_PATTERN("0b00000000000000000000000000000000"),
                                    .TST("0b0000"),
                                    .TXDATAWIDTHHS("0b00"),
                                    .U_PRG_HS_PREPARE("0b00"),
                                    .U_PRG_HS_TRAIL("0b000000"),
                                    .U_PRG_HS_ZERO("0b000000"),
                                    .UC_PRG_HS_PREPARE("1P0_TXCLKESC"),
                                    .UC_PRG_HS_TRAIL("0b00000"),
                                    .UC_PRG_HS_ZERO("0b0000000"),
                                    .U_PRG_RXHS_SETTLE({"0b",
                                        (T_DATA_SETTLE | "000000")}),
                                    .UC_PRG_RXHS_SETTLE({"0b",
                                        (T_CLK_SETTLE | "000000")})) u_dphy_cil (// LMMI
                                    .LMMICLK(lmmi_clk_i),  // Clock for LMMI.
                                    .LMMIRESET_N(1'd1),  // Active low reset.
                                    .LMMIREQUEST(lmmi_request_i),  // Request.
                                    .LMMIWRRD_N(lmmi_wr_rdn_i),  // Active hight write, low read.
                                    .LMMIOFFSET(lmmi_offset_i),  // Offset.
                                    .LMMIWDATA(lmmi_wdata_i),  // Data from user.
                                    // Clock and reset
                                    .BITCKEXT(1'd1),  // Maybe Bit clock external.
                                    .CLKREF(1'd1),  // Reference clock to PLL.
                                    .PDDPHY(pd_dphy_i),  // Power down DPHY.
                                    .PDPLL(1'd1),  // Power down PLL.
                                    // HS_TX request(10*)
                                    .UCTXREQH(1'd0),  // UC HS_TX request.
                                    .UTRD0SEN(1'd0),  // U0 HS_TX request.
                                    .U1TXREQH(1'd0),  // U1 HS_TX request.
                                    .U2TXREQH(1'd0),  // U2 HS_TX request.
                                    .U3TXREQH(1'd0),  // U3 HS_TX request.
                                    // HS_TX data(12*)
                                    .UTXDHS(32'hFFFFFFFF),  // U0 HS_TX data.
                                    .U1TXDHS(32'hFFFFFFFF),  // U1 HS_TX data.
                                    .U2TXDHS(32'hFFFFFFFF),  // U2 HS_TX data.
                                    .U3TXDHS(32'hFFFFFFFF),  // U3 HS_TX data.
                                    // HS_TX word valid(15*)
                                    .UTXWVDHS(4'hF),  // U0 HS_TX word valid.
                                    .U1TXWVHS(4'hF),  // U1 HS_TX word valid.
                                    .U2TXWVHS(4'hF),  // U2 HS_TX word valid.
                                    .U3TXWVHS(4'hF),  // U3 HS_TX word valid.
                                    //Enable(16*)
                                    .UCENCK((~pd_dphy_i)),  // UC enable.
                                    .UED0THEN((~pd_dphy_i)),  // U  Enable.
                                    .U1ENTHEN((~pd_dphy_i)),  // U1 Enable.
                                    .U2END2((~pd_dphy_i)),  // U2 enable.
                                    .U3END3((~pd_dphy_i)),  // U3 enable.
                                    // Turn disable(1*)
                                    .UTDIS(1'd0),  // U  Turn disable.
                                    .U1TDIS(1'd0),  // U1 Turn disable.
                                    .U2TDIS(1'd0),  // U2 turn disable.
                                    .U3TDISD2(1'd0),  // U3 turn disable.
                                    // Turn request(2*)
                                    .UTRNREQ(1'd0),  // U  turn request.
                                    .U1TREQ(1'd0),  // U1 turn request.
                                    .U2TREQ(1'd0),  // U2 turn request.
                                    .U3TREQD2(1'd0),  // U3 turn request.
                                    // Force TX(3*)
                                    .UTXMDTX(1'd0),  // U  Force TX stop mode.
                                    .U1FTXST(1'd0),  // U1 Force TX stop mode.
                                    .U2FTXST(1'd0),  // U2 force TX stop mode.
                                    .U3FTXST(1'd0),  // U3 force TX stop mode.
                                    // Force RX(4*)
                                    .UFRXMODE(1'd0),  // U  Force RX mode.
                                    .U1FRXMD(1'd0),  // U1 Force RX mode.
                                    .U2FRXMD(1'd0),  // U2 force RX mode.
                                    .U3FRXMD(1'd0),  // U3 force RX mode.
                                    //Escape ULP_TX(14*)
                                    .UCTXUPSC(1'd0),  // UC ULP_TX clock.
                                    .UTXULPSE(1'd0),  // U  ULP_TX escape.
                                    .U1TXUPSE(1'd0),  // U1 ULP_TX escape.
                                    .U2TXUPSE(1'd0),  // U2 ULP_TX escape.
                                    .U3TXULPS(1'd0),  // U3 ULP_TX escape.
                                    // Escape LP_TX(11*)
                                    .UTXENER(1'd0),  // U  LP_TX data escape
                                    .U1TXLPD(1'd0),  // U1 LP_TX data escape
                                    .U2TPDTE(1'd0),  // U2 LP_TX data escape
                                    .U3TXLPDT(1'd0),  // U3 LP_TX data escape
                                    // Escape TX data(5*)
                                    .UDE0D0TN(1'd0),  // U  TX data escape 0.
                                    .UDE1D1TN(1'd0),  // U  TX data escape 1.
                                    .UDE2D2TN(1'd0),  // U  TX data escape 2.
                                    .UDE3D3TN(1'd0),  // U  TX data escape 3.
                                    .UDE4CKTN(1'd0),  // U  TX data escape 4.
                                    .UDE5D0RN(1'd0),  // U  TX data escape 5.
                                    .UDE6D1RN(1'd0),  // U  TX data escape 6.
                                    .UDE7D2RN(1'd0),  // U  TX data escape 7.
                                    .U1TDE0D3(1'd0),  // U1 TX data escape 0.
                                    .U1TDE1CK(1'd0),  // U1 TX data escape 1.
                                    .U1TDE2D0(1'd0),  // U1 TX data escape 2.
                                    .U1TDE3D1(1'd0),  // U1 TX data escape 3.
                                    .U1TDE4D2(1'd0),  // U1 TX data escape 4.
                                    .U1TDE5D3(1'd0),  // U1 TX data escape 5.
                                    .U1TDE6(1'd0),  // U1 TX data escape 6.
                                    .U1TDE7(1'd0),  // U1 TX data escape 7.
                                    .U2TDE0D0(1'd0),  // U2 TX data escape 0.
                                    .U2TDE1D1(1'd0),  // U2 TX data escape 1.
                                    .U2TDE2D2(1'd0),  // U2 TX data escape 2.
                                    .U2TDE3D3(1'd0),  // U2 TX data escape 3.
                                    .U2TDE4CK(1'd0),  // U2 TX data escape 4.
                                    .U2TDE5D0(1'd0),  // U2 TX data escape 5.
                                    .U2TDE6D1(1'd0),  // U2 TX data escape 6.
                                    .U2TDE7D2(1'd0),  // U2 TX data escape 7.
                                    .U3TDE0D3(1'd0),  // U3 TX data escape 0.
                                    .U3TDE1D0(1'd0),  // U3 TX data escape 1.
                                    .U3TDE2D1(1'd0),  // U3 TX data escape 2.
                                    .U3TDE3D2(1'd0),  // U3 TX data escape 3.
                                    .U3TDE4D3(1'd0),  // U3 TX data escape 4.
                                    .U3TDE5CK(1'd0),  // U3 TX data escape 5.
                                    .U3TDE6(1'd0),  // U3 TX data escape 6.
                                    .U3TDE7(1'd0),  // U3 TX data escape 7.
                                    // Escape Triggers(6*)
                                    .UTXTGE0(1'd0),  // U  TX trigger escape 0.
                                    .UTXTGE1(1'd0),  // U  TX trigger escape 1.
                                    .UTXTGE2(1'd0),  // U  TX trigger escape 2.
                                    .UTXTGE3(1'd0),  // U  TX trigger escape 3.
                                    .U1TXTGE0(1'd0),  // U1 TX trigger escape 0.
                                    .U1TXTGE1(1'd0),  // U1 TX trigger escape 1.
                                    .U1TXTGE2(1'd0),  // U1 TX trigger escape 2.
                                    .U1TXTGE3(1'd0),  // U1 TX trigger escape 3.
                                    .U2TXTGE0(1'd0),  // U2 TX trigger escape 0.
                                    .U2TXTGE1(1'd0),  // U2 TX trigger escape 1.
                                    .U2TXTGE2(1'd0),  // U2 TX trigger escape 2.
                                    .U2TXTGE3(1'd0),  // U2 TX trigger escape 3.
                                    .U3TXTGE0(1'd0),  // U3 TX trigger escape 0.
                                    .U3TXTGE1(1'd0),  // U3 TX trigger escape 1.
                                    .U3TXTGE2(1'd0),  // U3 TX trigger escape 2.
                                    .U3TXTGE3(1'd0),  // U3 TX trigger escape 3.
                                    //ULPS_TX exit(7*)
                                    .UCTXUPSX(1'd0),  // UC ULP_TX exit.
                                    .UTXUPSEX(1'd0),  // U  ULP_TX exit.
                                    .U1TXUPSX(1'd0),  // U1 ULP_TX exit.
                                    .U2TXUPSX(1'd0),  // U2 ULP_TX exit.
                                    .U3TXUPSX(1'd0),  // U3 ULP_TX exit.
                                    // TX skew call HS(8*)
                                    .UTXSKD0N(1'd0),  // U  TxSkewCalHS.
                                    .U1TXSK(1'd0),  // U1 TxSkewCalHS.
                                    .U2TXSKC(1'd0),  // U2 TxSkewCalHS.
                                    .U3TXSKC(1'd0),  // U3 TxSkewCalHS.
                                    //TX escape request(9*)
                                    .UTXRD0EN(1'd0),  // U  TX request escape.
                                    .U1TXREQ(1'd0),  // U1 TX request escape.
                                    .U2TXREQ(1'd0),  // U2 TX request escape.
                                    .U3TXREQ(1'd0),  // U3 TX request escape.
                                    // Escape TX valid(13*)
                                    .UTXVDE(1'd0),  // U  TX valid escape.
                                    .U1TXVDE(1'd0),  // U1 TX valid escape.
                                    .U2TXVDE(1'd0),  // U2 TX valid escape.
                                    .U3TXVD3(1'd0),  // U3 TX valid escape.
                                    // Escape mode Clocks
                                    .URXCKINE(sync_clk_i),  // Escape mode Clock for RX. Minimum Clock frequency should be 60 MHz (16.6 ns) in order to correctly detect the LP states which are minimum 50 ns long but the minimum LP pulse duration could be as low as 20 ns as per D-PHY CTS. RxClkInEsc is synchronous with TxClkEsc
                                    .UTXCKE(tx_esc_clk_w),  // Escape mode Transmit Clock. This clock is directly used to generate escape sequences. The period of this clock determines the symbol time for low power signals. It is therefore constrained by the normative part of the D-PHY specification. So, the max frequency of TxClkEsc is 20 MHz. The Min frequency of TxClkEsc is 12 MHz. TxClkEsc is synchronous with RxClkInEsc
                                    //
                                    .LTSTEN(1'd0),  // Enable signal for LPTX VOH debug test mode. Active high. default=1'd0.
                                    .LTSTLANE(2'd0),  // Lane select signal in LPTX VOH debug test mode. Choose the data lane under test. Effective only when LPTX_TST_EN=1.
                                    //Scan ports
                                    .SCCLKIN(1'd1),  // Scan clock in.
                                    // OUT
                                    // LMMI
                                    .LMMIRDATA(lmmi_rdata_o), 
                                        .LMMIRDATAVALID(lmmi_rdata_valid_o), 
                                        .LMMIREADY(lmmi_ready_o), 
                                        //
                                    .UTXRYP(),  // U  HS_TX Ready
                                    .U1TXRY(),  // U1 HS_TX Ready
                                    .U2TXRYH(),  // U2 HS_TX Ready
                                    .U3TXRYHS(),  // U3 HS_TX Ready
                                    // Active[1:0](BIST mode)
                                    .D0ACTIVE(),  // D0_LB_ACTIVE
                                    .D1ACTIVE(),  // D1_LB_ACTIVE
                                    .D2ACTIVE(),  // D2_LB_ACTIVE
                                    .D3ACTIVE(),  // D3_LB_ACTIVE
                                    // Byte count[9:0](BIST mode)
                                    .D0BYTCNT(),  // D0_LB_BYTE_CNT
                                    .D1BYTCNT(),  // D1_LB_BYTE_CNT
                                    .D2BYTCNT(),  // D2_LB_BYTE_CNT
                                    .D3BYTCNT(),  // D3_LB_BYTE_CNT
                                    // Error count[9:0](BIST mode)
                                    .D0ERRCNT(),  // D0_LB_ERR_CNT
                                    .D1ERRCNT(),  // D1_LB_ERR_CNT
                                    .D2ERRCNT(),  // D2_LB_ERR_CNT
                                    .D3ERRCNT(),  // D3_LB_ERR_CNT
                                    // Pass[1:0](BIST mode)
                                    .D0PASS(),  // d0_lb_pass
                                    .D1PASS(),  // d1_lb_pass
                                    .D2PASS(),  // d2_lb_pass
                                    .D3PASS(),  // d3_lb_pass
                                    // Valid[1:0](BIST mode)
                                    .D0VALID(),  // d0_lb_valid
                                    .D1VALID(),  // d1_lb_valid
                                    .D2VALID(),  // d2_lb_valid
                                    .D3VALID(),  // d3_lb_valid
                                    //DS test out(BIST mode)
                                    .DCTSTOUT(),  //dc_test_out[9:0]
                                    .LOCK(),  // Lock
                                    //
                                    .UDIR(),  // u  direction
                                    .U1DIR(),  // u1 direction
                                    .U2DIR(),  // u2 direction
                                    .U3DIR(),  // u3 direction
                                    //
                                    .UERCLP0(),  // u  error contention LP0 | N/A
                                    .U1ERCLP0(),  // u1 error contention LP0 | N/A
                                    .U2ERCLP0(),  // u2 error contention LP0 | N/A
                                    .U3ERCLP0(),  // u3 error contention LP0 | N/A
                                    //
                                    .UERCLP1(),  // u  error contention LP1 | N/A
                                    .U1ERCLP1(),  // u1 error contention LP1 | N/A
                                    .U2ERCLP1(),  // u2 error contention LP1 | N/A
                                    .U3ERCLP1(),  // u3 error contention LP1 | N/A
                                    //
                                    .UERCTRL(),  // U error control | N/A
                                    .U1ERCTRL(),  // U error control | N/A
                                    .U2ERCTRL(),  // U error control | N/A
                                    .U3ERCTRL(),  // U error control | N/A
                                    //
                                    .UERE(),  // U  escape error | N/A
                                    .U1ERE(),  // U1 escape error | N/A
                                    .U2ERE(),  // U2 escape error | N/A
                                    .U3ERE(),  // U3 escape error | N/A
                                    //
                                    .UERSTHS(),  // U  HS SoT error | U  HS SoT error
                                    .U1ERSTHS(),  // U1 HS SoT error | U1 HS SoT error
                                    .U2ERSTHS(),  // U2 HS SoT error | U2 HS SoT error
                                    .U3ERSTHS(),  // U3 HS SoT error | U3 HS SoT error
                                    //
                                    .UERSSHS(d_sote_det[0]),  /*data_lane0_valid_cil_o*/// U  HS SoT sync error | U  HS SoT sync error
                                    .U2ERSSHS(d_sote_det[1]),  /*data_lane1_valid_cil_o*/// U1 HS SoT sync error | U1 HS SoT sync error
                                    .U3ERSSHS(d_sote_det[2]),  /*data_lane2_valid_cil_o*/// U2 HS SoT sync error | U2 HS SoT sync error
                                    .U1ERSSHS(d_sote_det[3]),  /*data_lane3_valid_cil_o*/// U3 HS SoT sync error | U3 HS SoT sync error
                                    //
                                    .UERSE(),  // U  Escape sync error | N/A
                                    .U1ERSE(),  // U1 Escape sync error | N/A
                                    .U2ERSE(),  // U2 Escape sync error | N/A
                                    .U3ERSE(),  // U3 Escape sync error | N/A
                                    // RX clock escape
                                    .URXCKE(),  // u  RX clock escape | N/A
                                    .U1RXCKE(),  // u1 RX clock escape | N/A
                                    .U2RXCKE(),  // u2 RX clock escape | N/A
                                    .U3RXCKE(),  // u3 RX clock escape | N/A
                                    // RX dataescape [7:0]
                                    .URXDE(),  // U  RX data escape | N/A
                                    .U1RXDE(),  // U1 RX data escape | N/A
                                    .U2RXDE(),  // U2 RX data escape | N/A
                                    .U3RXDE(),  // U3 RX data escape | N/A
                                    //HS_RX data
                                    .URXDHS(dphy_hs_data_line0_w),  /*data_lane0_cil_o*/// U  HS_RX data | U  HS_RX data
                                    .U1RXDHS(dphy_hs_data_line1_w),  /*data_lane1_cil_o*/// U1 HS_RX data | U1 HS_RX data
                                    .U2RXDHS(dphy_hs_data_line2_w),  /*data_lane2_cil_o*/// U2 HS_RX data | U2 HS_RX data
                                    .U3RXDHS(dphy_hs_data_line3_w),  /*data_lane3_cil_o*/// U3 HS_RX data | U3 HS_RX data
                                    //HS_RX active
                                    .URXACTHS(),  // U  HS_RX active | N/A
                                    .U1RXATHS(),  // U1 HS_RX active | N/A
                                    .U2RXACHS(),  // U2 HS_RX active | N/A
                                    .U3RXATHS(),  // U3 HS_RX active | N/A
                                    // HS_RX sync[3:0]
                                    .URXSHS(u_rx_sync_hs_w),  // U  HS_RX sync | U  HS_RX sync
                                    .U1RXSHS(u1_rx_sync_hs_w),  // U1 HS_RX sync | U1 HS_RX sync
                                    .U2RXSHS(u2_rx_sync_hs_w),  // U2 HS_RX sync | U2 HS_RX sync
                                    .U3RXSHS(u3_rx_sync_hs_w),  // U3 HS_RX sync | U3 HS_RX sync
                                    //
                                    .URE0D3DP(),  // U escape RX trigger 0 | U3 rx_lp positive
                                    .URE1D3DN(),  // U escape RX trigger 1 | U3 rx_lp negative
                                    .URE2CKDP(),  // U escape RX trigger 2 | UC rx_lp positive
                                    .URE3CKDN(),  // U escape RX trigger 3 | UC rx_lp negative
                                    //
                                    .U1RE0D(),  // U1 escape RX trigger 0 | D0_DCDP.
                                    .U1RE1CN(),  // U1 escape RX trigger 1 | D0_DCDN.
                                    .U1RE3N(),  // U1 escape RX trigger 2 | D1_DCDP.
                                    .U1RE2D(),  // U1 escape RX trigger 3 | D1_DCDN.
                                    //
                                    .U2RE0D2(),  // U2 escape RX trigger 0 | D2_DCDP.
                                    .U2RE1D2(),  // U2 escape RX trigger 1 | D2_DCDN.
                                    .U2RE2D3(),  // U2 escape RX trigger 2 | D3_DCDP.
                                    .U2RE3D3(),  // U2 escape RX trigger 3 | D3_DCDN.
                                    //
                                    .U3RE0CK(),  // U3 escape RX trigger 0 | CLK_DCDP in CIL BYPASSED.
                                    .U3RE1CK(),  // U3 escape RX trigger 1 | CLK_DCDN in CIL BYPASSED.
                                    .U3RE2(),  // U3 escape RX trigger 2 | N/A
                                    .U3RE3(),  // U3 escape RX trigger 3 | N/A
                                    //
                                    // ULPS_RX escape (17*)
                                    .URXULPSE(),  // U  ULP_RX escape in CIL | N/A
                                    .U1RXDTE(),  // U1 ULP_RX escape in CIL | N/A
                                    .U2RPDTE(),  // U2 ULP_RX escape in CIL | N/A
                                    .U3RPDTE(),  // U3 ULP_RX escape in CIL | N/A
                                    //
                                    .URXDRX(),  // U  HS_RX skew call done | D0 LP_RX negative
                                    .U1RXSK(),  // U1 HS_RX skew call done | D1 LP_RX negative
                                    .U2RXSKC(),  // U2 HS_RX skew call done | D2 LP_RX negative
                                    .U3RXSKC(),  // U3 HS_RX skew call done | N/A
                                    .URXSKCHS(),  // U  HS_RX skew call | N/A
                                    .U1RXSKS(),  // U1 HS_RX skew call | N/A
                                    .U2RXSK(),  // U2 HS_RX skew call | N/A
                                    .U3RXSK(),  // U3 HS_RX skew call | N/A
                                    //
                                    .URXLPDTE(),  // u  LP_RX escape | N/A
                                    .U1RXUPSE(),  // u1 LP_RX escape | N/A
                                    .U2RXUPSE(),  // u2 LP_RX escape | N/A
                                    .U3RXUPSE(),  // u3 LP_RX escape | N/A
                                    //
                                    //
                                    .UTXRRS(),  // U  TX ready escape | d0_drxhs
                                    .U1TXRYE(),  // U1 TX ready escape | d1_drxhs
                                    .U2TXRYE(),  // U2 TX ready escape | d2_drxhs
                                    .U3TXRY(),  // U3 TX ready escape | d3_drxhs
                                    //
                                    .URXVDHS(),  // U  HS_RX valid | U  HS_RX valid
                                    .U1RXVDHS(),  // U1 HS_RX valid | U1 HS_RX valid
                                    .U2RXVDHS(),  // U2 HS_RX valid | U2 HS_RX valid
                                    .U3RXVDHS(),  // U3 HS_RX valid | U3 HS_RX valid
                                    //
                                    .URXVDE(),  // U  RX valid escape | N/A
                                    .U1RXVDE(),  // U1 RX valid escape | N/A
                                    .U2RXVDE(),  // U2 RX valid escape | N/A
                                    .U3RXVDE(),  // U3 RX valid escape | N/A
                                    // Stop state
                                    .USSTT(),  // U  Stop state | N/A
                                    .U1SSTT(),  // U1 Stop state | N/A
                                    .U2SSTT(),  // U2 Stop state | N/A
                                    .U3SSTT(),  // U3 Stop state | N/A
                                    //
                                    .UTXRYSK(),  // U TX skew ready | N/A
                                    .U1TXRYSK(),  // U TX skew ready | N/A
                                    .U2TXRYSK(),  // U TX skew ready | N/A
                                    .U3TXRYSK(),  // U TX skew ready | N/A
                                    //
                                    .UCRXCKAT(),  // uc_RxClkActiveHS | CLK_DRXHS
                                    .UCRXUCKN(),  // This active low signal is asserted to indicate that the Clock Lane Module has entered the Ultra Low-Power State | N/A
                                    .UCSSTT(),  // This active high signal indicates that the Lane Module is currently in Stop state | N/A
                                    //
                                    .UCUSAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                    .UUSAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                    .U1USAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                    .U2USAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                    .U3USAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                    //
                                    .URWDCKHS(clk_byte_o),  // u_rxwordclkhs(This is used to synchronize signals in the high-speed receive clock domain.)
                                    .UTWDCKHS(),  // u_txwordclkhs(This is used to synchronize PPI signals in the high-speed transmit clock domain.)
                                    .UCRXWCHS(int_clk),  // uc_rxwordclkhs
                                    .CLKLBACT(),  // clk_lb_active
                                    //IO
                                    .CKN(clk_n_io), 
                                        .CKP(clk_p_io), 
                                        .DN0(data_n_io[0]), 
                                        .DN1(d_n_w[1]), 
                                        .DN2(d_n_w[2]), 
                                        .DN3(d_n_w[3]), 
                                        .DP0(data_p_io[0]), 
                                        .DP1(d_p_w[1]), 
                                        .DP2(d_p_w[2]), 
                                        .DP3(d_p_w[3])) ; 
                        end
                    assign ready_o = lmmi_ready_o ; 
                end
    endgenerate
endmodule



`timescale 1ns/10ps
//==============================================================================
// lscc_mipi_wrapper_rx.v
//==============================================================================
// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               : CSI2_DSI_DPHY_TX
// File                  : lscc_mipi_wrapper_tx.v
// Title                 : It instantiates hard IP or soft logic
//                         and control logic to tx global operation.
// Dependencies          : 1.
//                       : 2.
// Description           :
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             : Henry Tso
// Mod. Date             : 01/05/15
// Changes Made          : Initial release.
// =============================================================================
// Version               : 1.1
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 19/04/15
// Changes Made          : Adding Soft logic and Changing coding style.
// =============================================================================
module csi_dphy_ipgen_lscc_mipi_wrapper_tx #(parameter NUM_LANE = 1, 
        parameter GEAR = 8, 
        parameter [((8 * 8) - 1):0] INTF = "CSI2", 
        parameter [((7 * 8) - 1):0] DPHY_IP = "LATTICE", 
        parameter [((8 * 8) - 1):0] CLK_MODE = "ENABLED", 
        parameter [((12 * 8) - 1):0] DPHY_CIL_BYPASS = "CIL_BYPASSED", 
        parameter [((8 * 8) - 1):0] HSEL = "DISABLED", 
        parameter [((2 * 8) - 1):0] CIL_DATA_PREPARE = "01", 
        parameter [((6 * 8) - 1):0] CIL_DATA_TRAIL = "000001", 
        parameter [((6 * 8) - 1):0] CIL_DATA_ZERO = "000001", 
        parameter [((12 * 8) - 1):0] CIL_CLK_PREPARE = "1P0_TXCLKESC", 
        parameter [((5 * 8) - 1):0] CIL_CLK_TRAIL = "00001", 
        parameter [((7 * 8) - 1):0] CIL_CLK_ZERO = "0000001", 
        parameter INT_DATA_RATE = 500.0, 
        parameter TX_FREQ_TGT = 8'd112, 
        parameter REF_CLOCK_FREQ = 24, 
        parameter [((8 * 8) - 1):0] PLL_MODE = "INTERNAL", 
        parameter [((8 * 8) - 1):0] CM = "00000000", 
        parameter [((5 * 8) - 1):0] CN = "00000", 
        parameter [((3 * 8) - 1):0] CO = "000", 
        parameter DATA_WIDTH = (NUM_LANE * GEAR), 
        parameter LDW = ((DPHY_IP == "HARD_IP") ? 4 : 8), 
        parameter LOW = ((DPHY_IP == "HARD_IP") ? 5 : 7), 
        parameter MODE = "FREQUENCY", 
        parameter VOLTAGE = 0, 
        parameter CSET = "40P", 
        parameter CRIPPLE = "5P", 
        parameter IPP_CTRL = "0b1000", 
        parameter IPP_SEL = "0b1111", 
        parameter BW_CTL_BIAS = "0b0101", 
        parameter V2I_PP_RES = "10K", 
        parameter CLKI_FREQ = 100.0, 
        parameter CLKOP_FREQ_ACTUAL = 100.0, 
        parameter CLKOS_FREQ_ACTUAL = 100.0, 
        parameter CLKOS2_FREQ_ACTUAL = 100.0, 
        parameter CLKOS3_FREQ_ACTUAL = 100.0, 
        parameter CLKOS4_FREQ_ACTUAL = 100.0, 
        parameter CLKOS5_FREQ_ACTUAL = 100.0, 
        parameter CLKOP_PHASE_ACTUAL = 0.0, 
        parameter CLKOS_PHASE_ACTUAL = 0.0, 
        parameter CLKOS2_PHASE_ACTUAL = 0.0, 
        parameter CLKOS3_PHASE_ACTUAL = 0.0, 
        parameter CLKOS4_PHASE_ACTUAL = 0.0, 
        parameter CLKOS5_PHASE_ACTUAL = 0.0, 
        parameter PLL_REFCLK_FROM_PIN = 0, 
        parameter IO_TYPE = "LVDS", 
        parameter CLKI_DIVIDER_ACTUAL_STR = "1", 
        parameter FBCLK_DIVIDER_ACTUAL_STR = "1", 
        parameter FBK_MODE = "CLKOP", 
        parameter DIVOP_ACTUAL_STR = "1", 
        parameter DIVOS_ACTUAL_STR = "1", 
        parameter DIVOS2_ACTUAL_STR = "1", 
        parameter DIVOS3_ACTUAL_STR = "1", 
        parameter DIVOS4_ACTUAL_STR = "1", 
        parameter DIVOS5_ACTUAL_STR = "1", 
        parameter SSC_N_CODE_STR = "0b000010100", 
        parameter SSC_F_CODE_STR = "0b000000000000000", 
        parameter DELA = "0", 
        parameter DELB = "0", 
        parameter DELC = "0", 
        parameter DELD = "0", 
        parameter DELE = "0", 
        parameter DELF = "0", 
        parameter PHIA = "0", 
        parameter PHIB = "0", 
        parameter PHIC = "0", 
        parameter PHID = "0", 
        parameter PHIE = "0", 
        parameter PHIF = "0", 
        parameter FRAC_N_EN = 0, 
        parameter SS_EN = 0, 
        parameter CLKOP_BYPASS = 0, 
        parameter CLKOS_BYPASS = 0, 
        parameter CLKOS2_BYPASS = 0, 
        parameter CLKOS3_BYPASS = 0, 
        parameter CLKOS4_BYPASS = 0, 
        parameter CLKOS5_BYPASS = 0, 
        parameter CLKOS_EN = 0, 
        parameter CLKOS2_EN = 0, 
        parameter CLKOS3_EN = 0, 
        parameter CLKOS4_EN = 0, 
        parameter CLKOS5_EN = 0, 
        parameter DYN_PORTS_EN = 0, 
        parameter ENCLKOP_EN = 0, 
        parameter ENCLKOS_EN = 0, 
        parameter ENCLKOS2_EN = 0, 
        parameter ENCLKOS3_EN = 0, 
        parameter ENCLKOS4_EN = 0, 
        parameter ENCLKOS5_EN = 0, 
        parameter PLL_RST = 0, 
        parameter LOCK_EN = 0, 
        parameter PLL_LOCK_STICKY = 0, 
        parameter LEGACY_EN = 0, 
        parameter LMMI_EN = 0, 
        parameter APB_EN = 0, 
        parameter POWERDOWN_EN = 0, 
        parameter TRIM_EN_P = 0, 
        parameter TRIM_EN_S = 0, 
        parameter CLKOP_TRIM_MODE = "Falling", 
        parameter CLKOS_TRIM_MODE = "Falling", 
        parameter CLKOP_TRIM = "0b0000", 
        parameter CLKOS_TRIM = "0b0000") (
    // -----------------------------------------------------------------------------
    // Module Parameters
    // -----------------------------------------------------------------------------
    /// Configuration
    /// CIL Timing
    /// Alternative
    /// PLL
    /// INTERNAL
    // -----------------------------------------------------------------------------
    // Soft PLL
    // -----------------------------------------------------------------------------
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    // Clock and reset
    input wire sync_clk_i, 
    input wire sync_rst_i, 
    /// LMMI
    input wire lmmi_clk_i, 
    input wire lmmi_resetn_i, 
    input wire [(LDW - 1):0] lmmi_wdata_i, 
    input wire lmmi_wr_rdn_i, 
    input wire [(LOW - 1):0] lmmi_offset_i, 
    input wire lmmi_request_i, 
    output wire lmmi_ready_o, 
    output wire [(LDW - 1):0] lmmi_rdata_o, 
    output wire lmmi_rdata_valid_o, 
    // PLL outside of the Soft IP
    input wire pll_clkop_i, 
    input wire pll_clkos_i, 
    input wire pll_lock_i, 
    /// HS_TX
    input wire hs_tx_en_i, 
    input wire hs_tx_clk_en_i, 
    input wire [(DATA_WIDTH - 1):0] hs_tx_data_i, 
    input wire hs_tx_data_en_i, 
    /// LP_TX
    input wire lp_tx_en_i, 
    input wire [(NUM_LANE - 1):0] lp_tx_data_p_i, 
    input wire [(NUM_LANE - 1):0] lp_tx_data_n_i, 
    input wire lp_tx_data_en_i, 
    input wire lp_tx_clk_p_i, 
    input wire lp_tx_clk_n_i, 
    /// LP_RX
    input wire lp_rx_en_i, 
    output wire lp_rx_data_p_o, 
    output wire lp_rx_data_n_o, 
    /// Other
    input wire usrstdby_i, 
    input wire pd_dphy_i, 
    input wire txclk_hsgate_i, 
    output wire pll_lock_o, 
    output wire clk_byte_o, 
    output wire ready_o, 
    /// CIL
    output wire [(NUM_LANE - 1):0] hs_tx_cil_ready_o, 
    output wire [(NUM_LANE - 1):0] data_lane_ss_o, 
    /// DPHY
    inout wire clk_p_io, 
    inout wire clk_n_io, 
    inout wire [(NUM_LANE - 1):0] data_p_io, 
    inout wire [(NUM_LANE - 1):0] data_n_io) ;
    // -----------------------------------------------------------------------------
    // Local Parameters
    // -----------------------------------------------------------------------------
    localparam EMPTY = ((GEAR == 16) ? 16 : 24) ; 
    localparam DPHY_NUM_LANE = ((NUM_LANE == 4) ? "FOUR_LANES" : ((NUM_LANE == 3) ? "THREE_LANES" : ((NUM_LANE == 2) ? "TWO_LANES" : "ONE_LANE"))) ; 
    localparam DPHY_CLK_MODE = CLK_MODE ; /// Unused
    localparam DPHY_DSI_CSI = ((INTF == "CSI2") ? "CSI2_APP" : "DSI_APP") ; 
    localparam DPHY_DESKEW_EN = ((INT_DATA_RATE > 1500) ? "ENABLED" : "DISABLED") ; // When operating at or below 1.5 Gbps, the transmission of initial Deskew sequence is optional.
    localparam DPHY_GEAR = ((GEAR == 8) ? "0b00" : ((GEAR == 16) ? "0b01" : ((GEAR == 32) ? "0b10" : ((GEAR == 64) ? "0b11" : "0b00")))) ; 
    localparam DPHY_HSEL = HSEL ; 
    localparam DPHY_PLL = ((PLL_MODE == "INTERNAL") ? "REGISTERED" : "BYPASSED") ; 
    localparam DPHY_CM = CM ; 
    localparam DPHY_CN = CN ; 
    localparam DPHY_CO = CO ; 
    // -----------------------------------------------------------------------------
    // Wire Declarations
    // -----------------------------------------------------------------------------
    wire [3:0] d_p_w ; 
    wire [3:0] d_n_w ; 
    wire [31:0] data_lane_0_w ; 
    wire [31:0] data_lane_1_w ; 
    wire [31:0] data_lane_2_w ; 
    wire [31:0] data_lane_3_w ; 
    wire [3:0] lp_tx_data_p_w ; 
    wire [3:0] lp_tx_data_n_w ; 
    wire [3:0] tx_word_valid_w ; 
    wire [3:0] hs_tx_cil_ready_w ; 
    wire [3:0] data_lane_ss_w ; 
    wire tx_esc_clk_w ; 
    // -----------------------------------------------------------------------------
    // Assign Statement
    // -----------------------------------------------------------------------------
    assign tx_word_valid_w = ((GEAR == 16) ? 4'b0011 : 4'b0001) ; /// Now we support 8 and 16
    /// For CIL_BYPASSED
    assign hs_tx_cil_ready_o[(NUM_LANE - 1):0] = ((DPHY_CIL_BYPASS == "CIL_BYPASSED") ? {NUM_LANE{1'd1}} : hs_tx_cil_ready_w[(NUM_LANE - 1):0]) ; /// For CIL_ENABLED
    /// For CIL_BYPASSED
    assign data_lane_ss_o = ((DPHY_CIL_BYPASS == "CIL_BYPASSED") ? {NUM_LANE{1'd1}} : data_lane_ss_w[(NUM_LANE - 1):0]) ; /// For CIL_ENABLED
    // -----------------------------------------------------------------------------
    // Generate Assign Statements
    // -----------------------------------------------------------------------------
    generate
        if ((DPHY_IP == "HARD_IP")) 
            begin : genblk1
                assign data_n_io[(NUM_LANE - 1):0] = d_n_w[(NUM_LANE - 1):0] ; 
                assign data_p_io[(NUM_LANE - 1):0] = d_p_w[(NUM_LANE - 1):0] ; 
                if ((NUM_LANE > 0)) 
                    begin : genblk1
                        assign data_lane_0_w = {{EMPTY{1'd0}},
                                    hs_tx_data_i[((1 * GEAR) - 1):(0 * GEAR)]} ; 
                        assign lp_tx_data_p_w[0] = lp_tx_data_p_i[0] ; 
                        assign lp_tx_data_n_w[0] = lp_tx_data_n_i[0] ; 
                    end
                if ((NUM_LANE > 1)) 
                    begin : genblk2
                        assign data_lane_1_w = {{EMPTY{1'd0}},
                                    hs_tx_data_i[((2 * GEAR) - 1):(1 * GEAR)]} ; 
                        assign lp_tx_data_p_w[1] = lp_tx_data_p_i[1] ; 
                        assign lp_tx_data_n_w[1] = lp_tx_data_n_i[1] ; 
                    end
                else
                    begin : genblk2
                        assign data_lane_1_w = 32'hFFFFFFFF ; //Add to meet requirement from "Synplify Pro"
                    end
                if ((NUM_LANE > 2)) 
                    begin : genblk3
                        assign data_lane_2_w = {{EMPTY{1'd0}},
                                    hs_tx_data_i[((3 * GEAR) - 1):(2 * GEAR)]} ; 
                        assign lp_tx_data_p_w[2] = lp_tx_data_p_i[2] ; 
                        assign lp_tx_data_n_w[2] = lp_tx_data_n_i[2] ; 
                    end
                else
                    begin : genblk3
                        assign data_lane_2_w = 32'hFFFFFFFF ; //Add to meet requirement from "Synplify Pro"
                    end
                if ((NUM_LANE > 3)) 
                    begin : genblk4
                        assign data_lane_3_w = {{EMPTY{1'd0}},
                                    hs_tx_data_i[((4 * GEAR) - 1):(3 * GEAR)]} ; 
                        assign lp_tx_data_p_w[3] = lp_tx_data_p_i[3] ; 
                        assign lp_tx_data_n_w[3] = lp_tx_data_n_i[3] ; 
                    end
                else
                    begin : genblk4
                        assign data_lane_3_w = 32'hFFFFFFFF ; //Add to meet requirement from "Synplify Pro"
                    end
            end
    endgenerate
    generate
        if ((DPHY_CIL_BYPASS == "CIL_ENABLED")) 
            begin : GEN_CLK_DIVAIDER
                csi_dphy_ipgen_lscc_clock_divider #(.TGT_FREQ_IN(REF_CLOCK_FREQ)) u_clock_divider (.clk_i(sync_clk_i), 
                            .reset_n_i((!sync_rst_i)), 
                            .clk_o(tx_esc_clk_w)) ; 
            end
    endgenerate
    generate
        if ((DPHY_IP == "HARD_IP")) 
            begin : HARD_IP
                if ((DPHY_CIL_BYPASS == "CIL_BYPASSED")) 
                    begin : genblk1
                        DPHY #(.GSR("ENABLED"),
                                .AUTO_PD_EN("POWERED_UP"),
                                .CFG_NUM_LANES(DPHY_NUM_LANE),
                                .CM({"0b",
                                    DPHY_CM}),
                                .CN({"0b",
                                    DPHY_CN}),
                                .CO({"0b",
                                    DPHY_CO}),
                                .CONT_CLK_MODE(CLK_MODE),
                                .DESKEW_EN(DPHY_DESKEW_EN),
                                .DSI_CSI(DPHY_DSI_CSI),
                                .EN_CIL(DPHY_CIL_BYPASS),
                                .HSEL(DPHY_HSEL),
                                .LANE0_SEL("LANE_0"),
                                .LOCK_BYP("GATE_TXBYTECLKHS"),
                                .MASTER_SLAVE("MASTER"),
                                .PLLCLKBYPASS(DPHY_PLL),
                                .RSEL("0b01"),
                                .RXCDRP("0b00"),
                                .RXDATAWIDTHHS("0b01"),
                                .RXLPRP("0b000"),
                                .TEST_ENBL("0b000000"),
                                .TEST_PATTERN("0b00000000000000000000000000000000"),
                                .TST("0b1001"),
                                .TXDATAWIDTHHS(DPHY_GEAR),
                                .U_PRG_HS_PREPARE("0b01"),
                                .U_PRG_HS_TRAIL("0b000001"),
                                .U_PRG_HS_ZERO("0b000001"),
                                .U_PRG_RXHS_SETTLE("0b000001"),
                                .UC_PRG_HS_PREPARE("1P0_TXCLKESC"),
                                .UC_PRG_HS_TRAIL("0b00001"),
                                .UC_PRG_HS_ZERO("0b0000001"),
                                .UC_PRG_RXHS_SETTLE("0b000001")) u_DPHY_NO_CIL_tx (// Global set reset
                                // Powers down or driving LP-11 on inactive lanes reported by CFG_NUM_LANES parameter.
                                // Sets the number of active lanes` "FOUR_LANES", ... , "ONE_LANE".
                                // PLL dividers should be set to produce the required high speed BITCLK to be used for testing.
                                // PLL dividers should be set to produce the required high speed BITCLK to be used for testing.
                                // PLL dividers should be set to produce the required high speed BITCLK to be used for testing.
                                // Clock mode which can be continuous(Only HS mode) or noncontinuous(HS and LP mode).
                                // Enables Deskew feature that modifies ERRSYNC/NOSYNC behavior
                                // Set "CSI2_APP" for CSI2 and "DSI_APP" for DSI.
                                // Set to "CIL_ENABLED" for PHY operates with CIL enabled.
                                // Set to "DISABLED" for 1.5Gbps operation and below,Set to "ENABLED" for 2.5Gbps operation.
                                // This parameter determines which lane will act as data lane0 in HS Operation mode.
                                // "GATE_TXBYTECLKHS"` PLL LOCK signal will gate TxWordClkHS clock."NOT_GATE_TXBYTECLKHS"` CIL based counter will be used to gate the TxWordClkHS.
                                // Should be set to "MASTER" for HS-TX and LP-TX Tests and set to "SLAVE" for remaining tests.
                                // When BYPASSED maybe PLL doesn't work.
                                // On-chip termination control bits for manual calibration of HS-RX.
                                // RX_GEAR.
                                // High-Speed Receive Data Width Select.
                                // One-time programming bits that adjust the threshold voltage of LP-CD.
                                //
                                // PLL charge pump current control
                                // High-Speed Transmit Data Width Select or GEAR.
                                // Bit used to program T_CLK_PREPARE time in the beginning of high speed transmission mode.
                                // Bits used to program T_CLK_TRAIL time in the end of high speed transmission mode.
                                // Bits used to program T_CLK_ZERO time in the beginning of high speed transmission mode.
                                // Bits used to program T_HS_SETTLE. HS-RX waits for Time-out T_HS_SETTLE in order to neglect transition effects
                                // IN
                                // LMMI
                                .LMMICLK(lmmi_clk_i),  // Clock for LMMI.
                                .LMMIRESET_N(lmmi_resetn_i),  // Active low reset.
                                .LMMIREQUEST(lmmi_request_i),  // Request.
                                .LMMIWRRD_N(lmmi_wr_rdn_i),  // Active hight write, low read.
                                .LMMIOFFSET(lmmi_offset_i),  // Offset.
                                .LMMIWDATA(lmmi_wdata_i),  // Data from user.
                                // Clock and reset
                                .BITCKEXT(((DPHY_PLL == "REGISTERED") ? 1'd1 : pll_clkop_i)),  // Maybe Bit clock external.
                                .CLKREF(sync_clk_i),  // Reference clock to PLL.
                                .PDDPHY((pd_dphy_i | sync_rst_i)),  // Power down DPHY.
                                .PDPLL(((DPHY_PLL == "REGISTERED") ? (usrstdby_i | sync_rst_i) : 1'd1)),  // Power down PLL.
                                // Scan mode
                                .SCCLKIN(1'd0),  // Scan clock in.
                                // HS_TX enable ports
                                .UCENCK(((CLK_MODE == "ENABLED") ? 1'd1 : (hs_tx_en_i & hs_tx_data_en_i))),  // Clock  HS_TX enable.
                                .UED0THEN(((NUM_LANE > 0) ? hs_tx_data_en_i : 1'b0)),  // Lane 0 HS_TX enable.
                                .U1ENTHEN(((NUM_LANE > 1) ? hs_tx_data_en_i : 1'b0)),  // Lane 1 HS_TX enable.
                                .U3END3(((NUM_LANE > 3) ? hs_tx_data_en_i : 1'b0)),  // lane 3 HS_TX enable.
                                .U2END2(((NUM_LANE > 2) ? hs_tx_data_en_i : 1'b0)),  // lane 2 HS_TX enable.
                                // HS_TX ports
                                .UTXDHS(data_lane_0_w),  // Lane 0 HS_TX data.
                                .U1TXDHS(data_lane_1_w),  // Lane 1 HS_TX data.
                                .U2TXDHS(data_lane_2_w),  // Lane 2 HS_TX data.
                                .U3TXDHS(data_lane_3_w),  // Lane 3 HS_TX data.
                                // HS_TX word valid ports
                                .UTXWVDHS(tx_word_valid_w),  // Lane 0 HS_TX word valid.
                                .U1TXWVHS(tx_word_valid_w),  // Lane 1 HS_TX word valid.
                                .U2TXWVHS(tx_word_valid_w),  // Lane 2 HS_TX word valid.
                                .U3TXWVHS(tx_word_valid_w),  // Lane 3 HS_TX word valid.
                                // HS_TX power down ports
                                .U2TDE0D0(((~(hs_tx_en_i & hs_tx_data_en_i)) | lp_rx_en_i)),  // lane 0 HS_TX power down.
                                .U2TDE1D1(((~(hs_tx_en_i & hs_tx_data_en_i)) | lp_rx_en_i)),  // lane 1 HS_TX power down.
                                .U2TDE2D2(((~(hs_tx_en_i & hs_tx_data_en_i)) | lp_rx_en_i)),  // lane 2 HS_TX power down.
                                .U2TDE3D3(((~(hs_tx_en_i & hs_tx_data_en_i)) | lp_rx_en_i)),  // lane 3 HS_TX power down.
                                // LP_TX enable ports
                                .UDE4CKTN(((CLK_MODE == "ENABLED") ? 1'd0 : lp_tx_data_en_i)),  // Clock  LP_TX enable.
                                .UDE0D0TN(((NUM_LANE > 0) ? lp_tx_data_en_i : 1'b0)),  // Lane 0 LP_TX enable.
                                .UDE1D1TN(((NUM_LANE > 1) ? lp_tx_data_en_i : 1'b0)),  // Lane 1 LP_TX enable.
                                .UDE2D2TN(((NUM_LANE > 2) ? lp_tx_data_en_i : 1'b0)),  // Lane 2 LP_TX enable.
                                .UDE3D3TN(((NUM_LANE > 3) ? lp_tx_data_en_i : 1'b0)),  // Lane 3 LP_TX enable.
                                // LP_TX ports
                                .U3TXUPSX(lp_tx_clk_p_i),  // LP_TX positive clock.
                                .U3TXLPDT(lp_tx_clk_n_i),  // LP_TX negative clock.
                                .UTXMDTX(((NUM_LANE > 0) ? lp_tx_data_p_w[0] : 1'b0)),  // lane 0 LP_TX positive data.
                                .U1FTXST(((NUM_LANE > 0) ? lp_tx_data_n_w[0] : 1'b0)),  // lane 0 LP_TX negative data.
                                .U2FTXST(((NUM_LANE > 1) ? lp_tx_data_p_w[1] : 1'b0)),  // lane 1 LP_TX positive data.
                                .U3FTXST(((NUM_LANE > 1) ? lp_tx_data_n_w[1] : 1'b0)),  // lane 1 LP_TX negative data.
                                .U3TDISD2(((NUM_LANE > 2) ? lp_tx_data_p_w[2] : 1'b0)),  // lane 2 LP_TX positive data.
                                .U3TREQD2(((NUM_LANE > 2) ? lp_tx_data_n_w[2] : 1'b0)),  // lane 2 LP_TX negative data.
                                .U3TXVD3(((NUM_LANE > 3) ? lp_tx_data_p_w[3] : 1'b0)),  // lane 3 LP_TX positive data.
                                .U3TXULPS(((NUM_LANE > 3) ? lp_tx_data_n_w[3] : 1'b0)),  // lane 3 LP_TX negative data.
                                // LP_TX power down ports
                                .U2TDE4CK((hs_tx_data_en_i | lp_rx_en_i)),  // clock  LP_TX power down.
                                .U2TDE5D0((hs_tx_data_en_i | lp_rx_en_i)),  // lane 0 LP_TX power down.
                                .U2TDE6D1((hs_tx_data_en_i | lp_rx_en_i)),  // lane 1 LP_TX power down.
                                .U2TDE7D2((hs_tx_data_en_i | lp_rx_en_i)),  // lane 2 LP_TX power down.
                                .U3TDE0D3((hs_tx_data_en_i | lp_rx_en_i)),  // lane 3 LP_TX power down.
                                // HS_RX enable ports
                                .UCTXUPSC(1'd0),  // Clock  HS_RX enable.
                                .UTXSKD0N(1'd0),  // Lane 0 HS_RX enable.
                                .U1TXSK(1'd0),  // lane 1 HS_RX enable.
                                .U2TXSKC(1'd0),  // lane 2 HS_RX enable.
                                .U3TXSKC(1'd0),  // lane 3 HS_RX enable.
                                // LP_RX enable ports
                                .U1TDE1CK(lp_rx_en_i),  // Clock  LP_RX enable.
                                .UDE5D0RN(((NUM_LANE > 0) ? lp_rx_en_i : 1'd0)),  // Lane 0 LP_RX enable.
                                .UDE6D1RN(((NUM_LANE > 1) ? lp_rx_en_i : 1'd0)),  // Lane 1 LP_RX enable.
                                .UDE7D2RN(((NUM_LANE > 2) ? lp_rx_en_i : 1'd0)),  // Lane 2 LP_RX enable.
                                .U1TDE0D3(((NUM_LANE > 3) ? lp_rx_en_i : 1'd0)),  // lane 3 LP_RX enable.
                                // Serializer enable
                                .UTRD0SEN((hs_tx_en_i & hs_tx_data_en_i)),  // Lane 0 HS serialaizer enable.
                                .U1TXREQH((hs_tx_en_i & hs_tx_data_en_i)),  // Lane 1 HS serialaizer enable.
                                .U2TXREQH((hs_tx_en_i & hs_tx_data_en_i)),  // Lane 2 HS serialaizer enable.
                                .U3TXREQH((hs_tx_en_i & hs_tx_data_en_i)),  // Lane 3 HS serialaizer enable.
                                // Deserializer enable
                                .UTXENER(1'd0),  // ENP_DESER(To override the Deserializer token detector and enable Deserializer Byte Clock and DATA. Only applicable in Test mode (default) 1’b0) in CIL BYPASSED
                                .UTXRD0EN(1'd0),  // Lane 0 HS deserialaizer enable.
                                .U1TXREQ(1'd0),  // lane 1 HS deserialaizer enable.
                                .U2TXREQ(1'd0),  // Lane 2 HS deserialaizer enable.
                                .U3TXREQ(1'd0),  // Lane 3 HS deserialaizer enable.
                                //
                                .U3TDE5CK(sync_clk_i),  // HS_TX clock(CLK_DTXHS).
                                .U3TDE1D0(1'd1),  // HS_TX data(D0_DTXHS).
                                .U3TDE2D1(1'd1),  // HS_TX data(D1_DTXHS).
                                .U3TDE3D2(1'd1),  // HS_TX data(D2_DTXHS).
                                .U3TDE4D3(1'd1),  // HS_TX data(D3_DTXHS).
                                //
                                .U1TDE6(1'd0),  // CLK_CDEN.
                                .U1TDE2D0(1'd0),  // D0_CDEN.
                                .U1TDE3D1(1'd0),  // D1_CDEN.
                                .U1TDE4D2(1'd0),  // D2_CDEN.
                                .U1TDE5D3(1'd0),  // D3_CDEN.
                                //Others
                                .UTXULPSE(1'd0),  // Clock HS byte.
                                .U1TDE7(1'd0),  // CLK_TXHSPD.
                                .U1TXLPD(1'd0),  // LB_EN.(Dy_DTXHS => DPy/DNy , DPy/DNy => Dy_DRXHS)
                                .U3TDE6(1'd0),  // MST_RV_EN.
                                .U3TDE7(1'd0),  // SLV_RV_EN.
                                .UCTXREQH(txclk_hsgate_i),  // clock hs gate.
                                .UCTXUPSX(usrstdby_i),  // PDCKG.
                                // Test
                                .LTSTEN(1'd0),  // Enable signal for LPTX VOH debug test mode. Active high. default=1'b0.
                                .LTSTLANE(2'd0),  // Lane select signal in LPTX VOH debug test mode. Choose the data lane under test. Effective only when LPTX_TST_EN=1.
                                // OUT
                                // LMMI
                                .LMMIRDATA(lmmi_rdata_o),  // LMMI read data
                                .LMMIRDATAVALID(lmmi_rdata_valid_o),  // LMMI read data valid
                                .LMMIREADY(lmmi_ready_o),  // LMMI ready
                                // PLL lock
                                .LOCK(pll_lock_o),  // Lock
                                // HS SoT error
                                .UERSTHS(),  // U  HS SoT error
                                .U1ERSTHS(),  // U1 HS SoT error
                                .U2ERSTHS(),  // U2 HS SoT error
                                .U3ERSTHS(),  // U3 HS SoT error
                                // HS SoT sync error
                                .UERSSHS(),  // U  HS SoT sync error
                                .U2ERSSHS(),  // U1 HS SoT sync error
                                .U3ERSSHS(),  // U2 HS SoT sync error
                                .U1ERSSHS(),  // U3 HS SoT sync error
                                //HS_RX data
                                .URXDHS(),  // U  HS_RX data
                                .U1RXDHS(),  // U1 HS_RX data
                                .U2RXDHS(),  // U2 HS_RX data
                                .U3RXDHS(),  // U3 HS_RX data
                                // HS_RX sync[3:0]
                                .URXSHS(),  // U  HS_RX sync
                                .U1RXSHS(),  // U1 HS_RX sync
                                .U2RXSHS(),  // U2 HS_RX sync
                                .U3RXSHS(),  // U3 HS_RX sync
                                //
                                .URXVDHS(),  // U  HS_RX valid
                                .U1RXVDHS(),  // U1 HS_RX valid
                                .U2RXVDHS(),  // U2 HS_RX valid
                                .U3RXVDHS(),  // U3 HS_RX valid
                                //
                                .U1RE0D(),  // D0_DCDP.
                                .U1RE1CN(),  // D0_DCDN.
                                .U1RE3N(),  // D1_DCDP.
                                .U1RE2D(),  // D1_DCDN.
                                //
                                .U2RE0D2(),  // D2_DCDP.
                                .U2RE1D2(),  // D2_DCDN.
                                .U2RE2D3(),  // D3_DCDP.
                                .U2RE3D3(),  // D3_DCDN.
                                //
                                .U3RE0CK(),  // CLK_DCDP.
                                .U3RE1CK(),  // CLK_DCDN.
                                //
                                .URE0D3DP(),  // D3 LP_RX positive
                                .URE1D3DN(),  // D3 LP_RX negative
                                .URE2CKDP(),  // UC LP_RX positive
                                .URE3CKDN(),  // UC LP_RX negative
                                //
                                .URXDRX(lp_rx_data_n_o),  // D0 LP_RX negative
                                .U1RXSK(),  // D1 LP_RX negative
                                .U2RXSKC(),  // D2 LP_RX negative
                                //
                                .UTXRYP(lp_rx_data_p_o),  // D0 LP_RX positive
                                .U1TXRY(),  // D1 LP_RX positive
                                .U2TXRYH(),  // D2 LP_RX positive
                                //
                                .UCRXCKAT(),  // UC_drxhs
                                .UTXRRS(),  // d0_drxhs
                                .U1TXRYE(),  // d1_drxhs
                                .U2TXRYE(),  // d2_drxhs
                                .U3TXRY(),  // d3_drxhs
                                //
                                //
                                .URWDCKHS(),  // u_rxwordclkhs(This is used to synchronize signals in the high-speed receive clock domain.)
                                .UTWDCKHS(clk_byte_o),  // u_txwordclkhs(This is used to synchronize PPI signals in the high-speed transmit clock domain.)
                                .UCRXWCHS(),  // uc_rxwordclkhs
                                .CLKLBACT(),  // clk_lb_active
                                // INOUT
                                .CKN(clk_n_io),  // Negative part of differential clock.
                                .CKP(clk_p_io),  // Positive part of differential clock.
                                .DN0(data_n_io[0]),  // Negative part of differential data lane 0.
                                .DN1(d_n_w[1]),  // Negative part of differential data lane 1.
                                .DN2(d_n_w[2]),  // Negative part of differential data lane 2.
                                .DN3(d_n_w[3]),  // Negative part of differential data lane 3.
                                .DP0(data_p_io[0]),  // Positive part of differential data lane 0.
                                .DP1(d_p_w[1]),  // Positive part of differential data lane 1.
                                .DP2(d_p_w[2]),  // Positive part of differential data lane 2.
                                .DP3(d_p_w[3]),  // Positive part of differential data lane 3.
                                // Unused input ports(ports which used in CIL mode)
                                .URXCKINE(1'd1),  // N/A
                                .UTXCKE(1'd1),  // N/A
                                .UTRNREQ(1'd1),  // N/A
                                .UFRXMODE(1'd1),  // N/A
                                .UTDIS(1'd1),  // N/A
                                .UTXTGE0(1'd1),  // N/A
                                .UTXTGE1(1'd1),  // N/A
                                .UTXTGE2(1'd1),  // N/A
                                .UTXTGE3(1'd1),  // N/A
                                .UTXUPSEX(1'd1),  // N/A
                                .UTXVDE(1'd1),  // N/A
                                .U1FRXMD(1'd1),  // N/A
                                .U1TDIS(1'd1),  // N/A
                                .U1TREQ(1'd1),  // N/A
                                .U1TXTGE0(1'd1),  // N/A
                                .U1TXTGE1(1'd1),  // N/A
                                .U1TXTGE2(1'd1),  // N/A
                                .U1TXTGE3(1'd1),  // N/A
                                .U1TXUPSE(1'd1),  // N/A
                                .U1TXUPSX(1'd1),  // N/A
                                .U1TXVDE(1'd1),  // N/A
                                .U2FRXMD(1'd1),  // N/A
                                .U2TDIS(1'd1),  // N/A
                                .U2TREQ(1'd1),  // N/A
                                .U2TPDTE(1'd1),  // N/A
                                .U2TXTGE0(1'd1),  // N/A
                                .U2TXTGE1(1'd1),  // N/A
                                .U2TXTGE2(1'd1),  // N/A
                                .U2TXTGE3(1'd1),  // N/A
                                .U2TXUPSE(1'd1),  // N/A
                                .U2TXUPSX(1'd1),  // N/A
                                .U2TXVDE(1'd1),  // N/A
                                .U3FRXMD(1'd1),  // N/A
                                .U3TXTGE0(1'd1),  // N/A
                                .U3TXTGE1(1'd1),  // N/A
                                .U3TXTGE2(1'd1),  // N/A
                                .U3TXTGE3(1'd1),  // N/A
                                // Unused output ports(ports which used in CIL mode)
                                .D0ACTIVE(),  // N/A
                                .D1ACTIVE(),  // N/A
                                .D2ACTIVE(),  // N/A
                                .D3ACTIVE(),  // N/A
                                .D0BYTCNT(),  // N/A
                                .D1BYTCNT(),  // N/A
                                .D2BYTCNT(),  // N/A
                                .D3BYTCNT(),  // N/A
                                .D0ERRCNT(),  // N/A
                                .D1ERRCNT(),  // N/A
                                .D2ERRCNT(),  // N/A
                                .D3ERRCNT(),  // N/A
                                .D0PASS(),  // N/A
                                .D1PASS(),  // N/A
                                .D2PASS(),  // N/A
                                .D3PASS(),  // N/A
                                .D0VALID(),  // N/A
                                .D1VALID(),  // N/A
                                .D2VALID(),  // N/A
                                .D3VALID(),  // N/A
                                .DCTSTOUT(),  // N/A
                                .UDIR(),  // N/A
                                .U1DIR(),  // N/A
                                .U2DIR(),  // N/A
                                .U3DIR(),  // N/A
                                .UERCLP0(),  // N/A
                                .U1ERCLP0(),  // N/A
                                .U2ERCLP0(),  // N/A
                                .U3ERCLP0(),  // N/A
                                .UERCLP1(),  // N/A
                                .U1ERCLP1(),  // N/A
                                .U2ERCLP1(),  // N/A
                                .U3ERCLP1(),  // N/A
                                .UERCTRL(),  // N/A
                                .U1ERCTRL(),  // N/A
                                .U2ERCTRL(),  // N/A
                                .U3ERCTRL(),  // N/A
                                .UERE(),  // N/A
                                .U1ERE(),  // N/A
                                .U2ERE(),  // N/A
                                .U3ERE(),  // N/A
                                .UERSE(),  // N/A
                                .U1ERSE(),  // N/A
                                .U2ERSE(),  // N/A
                                .U3ERSE(),  // N/A
                                .URXCKE(),  // N/A
                                .U1RXCKE(),  // N/A
                                .U2RXCKE(),  // N/A
                                .U3RXCKE(),  // N/A
                                .URXDE(),  // N/A
                                .U1RXDE(),  // N/A
                                .U2RXDE(),  // N/A
                                .U3RXDE(),  // N/A
                                .URXACTHS(),  // N/A
                                .U1RXATHS(),  // N/A
                                .U2RXACHS(),  // N/A
                                .U3RXATHS(),  // N/A
                                .U3TXRYHS(),  // N/A
                                .URXVDE(),  // N/A
                                .U1RXVDE(),  // N/A
                                .U2RXVDE(),  // N/A
                                .U3RXVDE(),  // N/A
                                .USSTT(),  // N/A
                                .U1SSTT(),  // N/A
                                .U2SSTT(),  // N/A
                                .U3SSTT(),  // N/A
                                .UTXRYSK(),  // N/A
                                .U1TXRYSK(),  // N/A
                                .U2TXRYSK(),  // N/A
                                .U3TXRYSK(),  // N/A
                                .UCRXUCKN(),  // N/A
                                .UCSSTT(),  // N/A
                                .UCUSAN(),  // N/A
                                .UUSAN(),  // N/A
                                .U1USAN(),  // N/A
                                .U2USAN(),  // N/A
                                .U3USAN(),  // N/A
                                .U3RXSKC(),  // N/A
                                .URXSKCHS(),  // N/A
                                .U1RXSKS(),  // N/A
                                .U2RXSK(),  // N/A
                                .U3RXSK(),  // N/A
                                .URXLPDTE(),  // N/A
                                .U1RXUPSE(),  // N/A
                                .U2RXUPSE(),  // N/A
                                .U3RXUPSE(),  // N/A
                                .U3RE2(),  // N/A
                                .U3RE3(),  // N/A
                                .URXULPSE(),  // N/A
                                .U1RXDTE(),  // N/A
                                .U2RPDTE(),  // N/A
                                .U3RPDTE() // N/A
                                ) ; 
                    end
                else
                    begin : genblk1
                        DPHY #(.GSR("ENABLED"),
                                .AUTO_PD_EN("POWERED_UP"),
                                .CFG_NUM_LANES(DPHY_NUM_LANE),
                                .CM({"0b",
                                    DPHY_CM}),
                                .CN({"0b",
                                    DPHY_CN}),
                                .CO({"0b",
                                    DPHY_CO}),
                                .CONT_CLK_MODE(CLK_MODE),
                                .DESKEW_EN(DPHY_DESKEW_EN),
                                .DSI_CSI(DPHY_DSI_CSI),
                                .EN_CIL(DPHY_CIL_BYPASS),
                                .HSEL(DPHY_HSEL),
                                .LANE0_SEL("LANE_0"),
                                .LOCK_BYP("GATE_TXBYTECLKHS"),
                                .MASTER_SLAVE("MASTER"),
                                .PLLCLKBYPASS(DPHY_PLL),
                                .RSEL("0b01"),
                                .RXCDRP("0b00"),
                                .RXDATAWIDTHHS("0b01"),
                                .RXLPRP("0b000"),
                                .TEST_ENBL("0b000000"),
                                .TEST_PATTERN("0b00000000000000000000000000000000"),
                                .TST("0b1001"),
                                .TXDATAWIDTHHS(DPHY_GEAR),
                                .U_PRG_HS_PREPARE({"0b",
                                    CIL_DATA_PREPARE}),
                                .U_PRG_HS_TRAIL({"0b",
                                    CIL_DATA_TRAIL[((6 * 8) - 1):0]}),
                                .U_PRG_HS_ZERO({"0b",
                                    CIL_DATA_ZERO[((6 * 8) - 1):0]}),
                                .UC_PRG_HS_PREPARE(CIL_CLK_PREPARE),
                                .UC_PRG_HS_TRAIL({"0b",
                                    CIL_CLK_TRAIL}),
                                .UC_PRG_HS_ZERO({"0b",
                                    CIL_CLK_ZERO}),
                                .U_PRG_RXHS_SETTLE("0b000001"),
                                .UC_PRG_RXHS_SETTLE("0b000001")) u_DPHY_CIL_tx (// Global set reset
                                // Powers down or driving LP-11 on inactive lanes reported by CFG_NUM_LANES parameter.
                                // Sets the number of active lanes` "FOUR_LANES", ... , "ONE_LANE".
                                // PLL dividers should be set to produce the required high speed BITCLK to be used for testing.
                                // PLL dividers should be set to produce the required high speed BITCLK to be used for testing.
                                // PLL dividers should be set to produce the required high speed BITCLK to be used for testing.
                                // Clock mode which can be continuous(Only HS mode) or noncontinuous(HS and LP mode).
                                // Enables Deskew feature that modifies ERRSYNC/NOSYNC behavior
                                // Set "CSI2_APP" for CSI2 and "DSI_APP" for DSI.
                                // Set to "CIL_ENABLED" for PHY operates with CIL enabled.
                                // Set to "DISABLED" for 1.5Gbps operation and below,Set to "ENABLED" for 2.5Gbps operation.
                                // This parameter determines which lane will act as data lane0 in HS Operation mode.
                                // "GATE_TXBYTECLKHS"` PLL LOCK signal will gate TxWordClkHS clock."NOT_GATE_TXBYTECLKHS"` CIL based counter will be used to gate the TxWordClkHS.
                                // Should be set to "MASTER" for HS-TX and LP-TX Tests and set to "SLAVE" for remaining tests.
                                // When BYPASSED maybe PLL doesn't work.
                                // On-chip termination control bits for manual calibration of HS-RX.
                                // RX_GEAR.
                                // High-Speed Receive Data Width Select.
                                // One-time programming bits that adjust the threshold voltage of LP-CD.
                                // PLL charge pump current control
                                // On-chip termination control bits for manual calibration of HS-TX
                                // High-Speed Transmit Data Width Select or GEAR.
                                /// Data Timing Parameters
                                // Time to preparing data after start of DPHY clock
                                // Time to wait on LP-01 until lines will go to LP-11 or LP-00
                                // transmitter drives the HS-0 state prior to transmitting the B8.
                                /// Clock Timing Parameters
                                // or 1P5_TXCLKESC///Bit used to program T_CLK_PREPARE time in the beginning of high speed transmission mode
                                // Time to wait on LP-01 until lines will go to LP-11 or LP-00
                                // transmitter drives the HS-0 state
                                /// Receiver Specific Timing Parameters
                                // (RX_specific)Time interval during which the HS receiver should ignore any Clock Lane HS transitions, starting from the beginning of TCLK-PREPARE.
                                // (RX_specific) Time interval during which the HS receiver shall ignore any Data Lane HS transitions, starting from the beginning of THS-PREPARE.
                                // LMMI
                                .LMMICLK(lmmi_clk_i),  // Clock for LMMI.
                                .LMMIRESET_N(lmmi_resetn_i),  // Active low reset.
                                .LMMIREQUEST(lmmi_request_i),  // Request.
                                .LMMIWRRD_N(lmmi_wr_rdn_i),  // Active hight write, low read.
                                .LMMIOFFSET(lmmi_offset_i),  // Offset.
                                .LMMIWDATA(lmmi_wdata_i),  // Data from user.
                                // Clock and reset
                                .BITCKEXT(((DPHY_PLL == "REGISTERED") ? 1'd1 : pll_clkop_i)),  // Maybe Bit clock external.
                                .CLKREF(((DPHY_PLL == "REGISTERED") ? sync_clk_i : 1'd1)),  // Reference clock to PLL.
                                .PDDPHY((pd_dphy_i | sync_rst_i)),  // Power down DPHY.
                                .PDPLL(((DPHY_PLL == "REGISTERED") ? (usrstdby_i | sync_rst_i) : 1'd1)),  // Power down PLL.
                                // HS_TX request(10*)
                                .UCTXREQH(((CLK_MODE == "ENABLED") ? 1'd1 : hs_tx_data_en_i)),  // UC HS_TX request.
                                .UTRD0SEN(hs_tx_data_en_i),  // U0 HS_TX request.
                                .U1TXREQH(((NUM_LANE > 1) ? hs_tx_data_en_i : 1'd0)),  // U1 HS_TX request.
                                .U2TXREQH(((NUM_LANE > 2) ? hs_tx_data_en_i : 1'd0)),  // U2 HS_TX request.
                                .U3TXREQH(((NUM_LANE > 3) ? hs_tx_data_en_i : 1'd0)),  // U3 HS_TX request.
                                // HS_TX data(12*)
                                .UTXDHS(data_lane_0_w),  // U  HS_TX data.
                                .U1TXDHS(((NUM_LANE > 1) ? data_lane_1_w : 32'hFFFFFFFF)),  // U1 HS_TX data.
                                .U2TXDHS(((NUM_LANE > 2) ? data_lane_2_w : 32'hFFFFFFFF)),  // U2 HS_TX data.
                                .U3TXDHS(((NUM_LANE > 3) ? data_lane_3_w : 32'hFFFFFFFF)),  // U3 HS_TX data.
                                // HS_TX word valid(15*)
                                .UTXWVDHS(tx_word_valid_w),  // U  HS_TX word valid.
                                .U1TXWVHS(((NUM_LANE > 1) ? tx_word_valid_w : 4'd0)),  // U1 HS_TX word valid.
                                .U2TXWVHS(((NUM_LANE > 2) ? tx_word_valid_w : 4'd0)),  // U2 HS_TX word valid.
                                .U3TXWVHS(((NUM_LANE > 3) ? tx_word_valid_w : 4'd0)),  // U3 HS_TX word valid.
                                //Enable(16*)
                                .UCENCK((!sync_rst_i)),  // UC enable.
                                .UED0THEN((!sync_rst_i)),  // U  Enable.
                                .U1ENTHEN(((NUM_LANE > 1) ? (!sync_rst_i) : 1'd0)),  // U1 Enable.
                                .U2END2(((NUM_LANE > 2) ? (!sync_rst_i) : 1'd0)),  // U2 enable.
                                .U3END3(((NUM_LANE > 3) ? (!sync_rst_i) : 1'd0)),  // U3 enable.
                                // Turn disable(1*)
                                .UTDIS(1'd0),  // U  Turn disable.
                                .U1TDIS(1'd0),  // U1 Turn disable.
                                .U2TDIS(1'd0),  // U2 turn disable.
                                .U3TDISD2(1'd0),  // U3 turn disable.
                                // Turn request(2*)
                                .UTRNREQ(1'd0),  // U  turn request.
                                .U1TREQ(1'd0),  // U1 turn request.
                                .U2TREQ(1'd0),  // U2 turn request.
                                .U3TREQD2(1'd0),  // U3 turn request.
                                // Force TX(3*)
                                .UTXMDTX(1'd0),  // U  Force TX stop mode.
                                .U1FTXST(1'd0),  // U1 Force TX stop mode.
                                .U2FTXST(1'd0),  // U2 force TX stop mode.
                                .U3FTXST(1'd0),  // U3 force TX stop mode.
                                // Force RX(4*)
                                .UFRXMODE(1'd0),  // U  Force RX mode.
                                .U1FRXMD(1'd0),  // U1 Force RX mode.
                                .U2FRXMD(1'd0),  // U2 force RX mode.
                                .U3FRXMD(1'd0),  // U3 force RX mode.
                                //Escape ULP_TX(14*)
                                .UCTXUPSC(1'd0),  // UC ULP_TX clock.
                                .UTXULPSE(1'd0),  // U  ULP_TX escape.
                                .U1TXUPSE(1'd0),  // U1 ULP_TX escape.
                                .U2TXUPSE(1'd0),  // U2 ULP_TX escape.
                                .U3TXULPS(1'd0),  // U3 ULP_TX escape.
                                // Escape LP_TX(11*)
                                .UTXENER(1'd0),  // U  LP_TX data escape
                                .U1TXLPD(1'd0),  // U1 LP_TX data escape
                                .U2TPDTE(1'd0),  // U2 LP_TX data escape
                                .U3TXLPDT(1'd0),  // U3 LP_TX data escape
                                // Escape TX data(5*)
                                .UDE0D0TN(1'd0),  // U  TX data escape 0.
                                .UDE1D1TN(1'd0),  // U  TX data escape 1.
                                .UDE2D2TN(1'd0),  // U  TX data escape 2.
                                .UDE3D3TN(1'd0),  // U  TX data escape 3.
                                .UDE4CKTN(1'd0),  // U  TX data escape 4.
                                .UDE5D0RN(1'd0),  // U  TX data escape 5.
                                .UDE6D1RN(1'd0),  // U  TX data escape 6.
                                .UDE7D2RN(1'd0),  // U  TX data escape 7.
                                .U1TDE0D3(1'd0),  // U1 TX data escape 0.
                                .U1TDE1CK(1'd0),  // U1 TX data escape 1.
                                .U1TDE2D0(1'd0),  // U1 TX data escape 2.
                                .U1TDE3D1(1'd0),  // U1 TX data escape 3.
                                .U1TDE4D2(1'd0),  // U1 TX data escape 4.
                                .U1TDE5D3(1'd0),  // U1 TX data escape 5.
                                .U1TDE6(1'd0),  // U1 TX data escape 6.
                                .U1TDE7(1'd0),  // U1 TX data escape 7.
                                .U2TDE0D0(1'd0),  // U2 TX data escape 0.
                                .U2TDE1D1(1'd0),  // U2 TX data escape 1.
                                .U2TDE2D2(1'd0),  // U2 TX data escape 2.
                                .U2TDE3D3(1'd0),  // U2 TX data escape 3.
                                .U2TDE4CK(1'd0),  // U2 TX data escape 4.
                                .U2TDE5D0(1'd0),  // U2 TX data escape 5.
                                .U2TDE6D1(1'd0),  // U2 TX data escape 6.
                                .U2TDE7D2(1'd0),  // U2 TX data escape 7.
                                .U3TDE0D3(1'd0),  // U3 TX data escape 0.
                                .U3TDE1D0(1'd0),  // U3 TX data escape 1.
                                .U3TDE2D1(1'd0),  // U3 TX data escape 2.
                                .U3TDE3D2(1'd0),  // U3 TX data escape 3.
                                .U3TDE4D3(1'd0),  // U3 TX data escape 4.
                                .U3TDE5CK(1'd0),  // U3 TX data escape 5.
                                .U3TDE6(1'd0),  // U3 TX data escape 6.
                                .U3TDE7(1'd0),  // U3 TX data escape 7.
                                // Escape Triggers(6*)
                                .UTXTGE0(1'd0),  // U  TX trigger escape 0.
                                .UTXTGE1(1'd0),  // U  TX trigger escape 1.
                                .UTXTGE2(1'd0),  // U  TX trigger escape 2.
                                .UTXTGE3(1'd0),  // U  TX trigger escape 3.
                                .U1TXTGE0(1'd0),  // U1 TX trigger escape 0.
                                .U1TXTGE1(1'd0),  // U1 TX trigger escape 1.
                                .U1TXTGE2(1'd0),  // U1 TX trigger escape 2.
                                .U1TXTGE3(1'd0),  // U1 TX trigger escape 3.
                                .U2TXTGE0(1'd0),  // U2 TX trigger escape 0.
                                .U2TXTGE1(1'd0),  // U2 TX trigger escape 1.
                                .U2TXTGE2(1'd0),  // U2 TX trigger escape 2.
                                .U2TXTGE3(1'd0),  // U2 TX trigger escape 3.
                                .U3TXTGE0(1'd0),  // U3 TX trigger escape 0.
                                .U3TXTGE1(1'd0),  // U3 TX trigger escape 1.
                                .U3TXTGE2(1'd0),  // U3 TX trigger escape 2.
                                .U3TXTGE3(1'd0),  // U3 TX trigger escape 3.
                                //ULPS_TX exit(7*)
                                .UCTXUPSX(1'd0),  // UC ULP_TX exit.
                                .UTXUPSEX(1'd0),  // U  ULP_TX exit.
                                .U1TXUPSX(1'd0),  // U1 ULP_TX exit.
                                .U2TXUPSX(1'd0),  // U2 ULP_TX exit.
                                .U3TXUPSX(1'd0),  // U3 ULP_TX exit.
                                // TX skew call HS(8*)
                                .UTXSKD0N(1'd0),  // U  TxSkewCalHS.
                                .U1TXSK(1'd0),  // U1 TxSkewCalHS.
                                .U2TXSKC(1'd0),  // U2 TxSkewCalHS.
                                .U3TXSKC(1'd0),  // U3 TxSkewCalHS.
                                //TX escape request(9*)
                                .UTXRD0EN(1'd0),  // U  TX request escape.
                                .U1TXREQ(1'd0),  // U1 TX request escape.
                                .U2TXREQ(1'd0),  // U2 TX request escape.
                                .U3TXREQ(1'd0),  // U3 TX request escape.
                                // Escape TX valid(13*)
                                .UTXVDE(1'd0),  // U  TX valid escape.
                                .U1TXVDE(1'd0),  // U1 TX valid escape.
                                .U2TXVDE(1'd0),  // U2 TX valid escape.
                                .U3TXVD3(1'd0),  // U3 TX valid escape.
                                // Escape mode Clocks
                                .URXCKINE(1'd0),  // Escape mode Clock for RX. Minimum Clock frequency should be 60 MHz (16.6 ns) in order to correctly detect the LP states which are minimum 50 ns long but the minimum LP pulse duration could be as low as 20 ns as per D-PHY CTS. RxClkInEsc is synchronous with TxClkEsc
                                .UTXCKE(tx_esc_clk_w),  // Escape mode Transmit Clock. This clock is directly used to generate escape sequences. The period of this clock determines the symbol time for low power signals. It is therefore constrained by the normative part of the D-PHY specification. So, the max frequency of TxClkEsc is 20 MHz. The Min frequency of TxClkEsc is 12 MHz. TxClkEsc is synchronous with RxClkInEsc
                                //
                                .LTSTEN(1'd0),  // Enable signal for LPTX VOH debug test mode. Active high. default=1'd0.
                                .LTSTLANE(2'd0),  // Lane select signal in LPTX VOH debug test mode. Choose the data lane under test. Effective only when LPTX_TST_EN=1.
                                //Scan ports
                                .SCCLKIN(1'd1),  // Scan clock in.
                                // OUT
                                // LMMI
                                .LMMIRDATA(lmmi_rdata_o), 
                                    .LMMIRDATAVALID(lmmi_rdata_valid_o), 
                                    .LMMIREADY(lmmi_ready_o), 
                                    //
                                .UTXRYP(hs_tx_cil_ready_w[0]),  // U  HS_TX Ready
                                .U1TXRY(hs_tx_cil_ready_w[1]),  // U1 HS_TX Ready
                                .U2TXRYH(hs_tx_cil_ready_w[2]),  // U2 HS_TX Ready
                                .U3TXRYHS(hs_tx_cil_ready_w[3]),  // U3 HS_TX Ready
                                // Active[1:0](BIST mode)
                                .D0ACTIVE(),  // D0_LB_ACTIVE
                                .D1ACTIVE(),  // D1_LB_ACTIVE
                                .D2ACTIVE(),  // D2_LB_ACTIVE
                                .D3ACTIVE(),  // D3_LB_ACTIVE
                                // Byte count[9:0](BIST mode)
                                .D0BYTCNT(),  // D0_LB_BYTE_CNT
                                .D1BYTCNT(),  // D1_LB_BYTE_CNT
                                .D2BYTCNT(),  // D2_LB_BYTE_CNT
                                .D3BYTCNT(),  // D3_LB_BYTE_CNT
                                // Error count[9:0](BIST mode)
                                .D0ERRCNT(),  // D0_LB_ERR_CNT
                                .D1ERRCNT(),  // D1_LB_ERR_CNT
                                .D2ERRCNT(),  // D2_LB_ERR_CNT
                                .D3ERRCNT(),  // D3_LB_ERR_CNT
                                // Pass[1:0](BIST mode)
                                .D0PASS(),  // d0_lb_pass
                                .D1PASS(),  // d1_lb_pass
                                .D2PASS(),  // d2_lb_pass
                                .D3PASS(),  // d3_lb_pass
                                // Valid[1:0](BIST mode)
                                .D0VALID(),  // d0_lb_valid
                                .D1VALID(),  // d1_lb_valid
                                .D2VALID(),  // d2_lb_valid
                                .D3VALID(),  // d3_lb_valid
                                //DS test out(BIST mode)
                                .DCTSTOUT(),  //dc_test_out[9:0]
                                .LOCK(pll_lock_o),  // Lock
                                //
                                .UDIR(),  // u  direction
                                .U1DIR(),  // u1 direction
                                .U2DIR(),  // u2 direction
                                .U3DIR(),  // u3 direction
                                //
                                .UERCLP0(),  // u  error contention LP0 | N/A
                                .U1ERCLP0(),  // u1 error contention LP0 | N/A
                                .U2ERCLP0(),  // u2 error contention LP0 | N/A
                                .U3ERCLP0(),  // u3 error contention LP0 | N/A
                                //
                                .UERCLP1(),  // u  error contention LP1 | N/A
                                .U1ERCLP1(),  // u1 error contention LP1 | N/A
                                .U2ERCLP1(),  // u2 error contention LP1 | N/A
                                .U3ERCLP1(),  // u3 error contention LP1 | N/A
                                //
                                .UERCTRL(),  // U error control | N/A
                                .U1ERCTRL(),  // U error control | N/A
                                .U2ERCTRL(),  // U error control | N/A
                                .U3ERCTRL(),  // U error control | N/A
                                //
                                .UERE(),  // U  escape error | N/A
                                .U1ERE(),  // U1 escape error | N/A
                                .U2ERE(),  // U2 escape error | N/A
                                .U3ERE(),  // U3 escape error | N/A
                                //
                                .UERSTHS(),  // U  HS SoT error | U  HS SoT error
                                .U1ERSTHS(),  // U1 HS SoT error | U1 HS SoT error
                                .U2ERSTHS(),  // U2 HS SoT error | U2 HS SoT error
                                .U3ERSTHS(),  // U3 HS SoT error | U3 HS SoT error
                                //
                                .UERSSHS(),  // U  HS SoT sync error | U  HS SoT sync error
                                .U2ERSSHS(),  // U1 HS SoT sync error | U1 HS SoT sync error
                                .U3ERSSHS(),  // U2 HS SoT sync error | U2 HS SoT sync error
                                .U1ERSSHS(),  // U3 HS SoT sync error | U3 HS SoT sync error
                                //
                                .UERSE(),  // U  Escape sync error | N/A
                                .U1ERSE(),  // U1 Escape sync error | N/A
                                .U2ERSE(),  // U2 Escape sync error | N/A
                                .U3ERSE(),  // U3 Escape sync error | N/A
                                // RX clock escape
                                .URXCKE(),  // u  RX clock escape | N/A
                                .U1RXCKE(),  // u1 RX clock escape | N/A
                                .U2RXCKE(),  // u2 RX clock escape | N/A
                                .U3RXCKE(),  // u3 RX clock escape | N/A
                                // RX dataescape [7:0]
                                .URXDE(),  // U  RX data escape | N/A
                                .U1RXDE(),  // U1 RX data escape | N/A
                                .U2RXDE(),  // U2 RX data escape | N/A
                                .U3RXDE(),  // U3 RX data escape | N/A
                                //HS_RX data
                                .URXDHS(),  // U  HS_RX data | U  HS_RX data
                                .U1RXDHS(),  // U1 HS_RX data | U1 HS_RX data
                                .U2RXDHS(),  // U2 HS_RX data | U2 HS_RX data
                                .U3RXDHS(),  // U3 HS_RX data | U3 HS_RX data
                                //HS_RX active
                                .URXACTHS(),  // U  HS_RX active | N/A
                                .U1RXATHS(),  // U1 HS_RX active | N/A
                                .U2RXACHS(),  // U2 HS_RX active | N/A
                                .U3RXATHS(),  // U3 HS_RX active | N/A
                                // HS_RX sync[3:0]
                                .URXSHS(),  // U  HS_RX sync | U  HS_RX sync
                                .U1RXSHS(),  // U1 HS_RX sync | U1 HS_RX sync
                                .U2RXSHS(),  // U2 HS_RX sync | U2 HS_RX sync
                                .U3RXSHS(),  // U3 HS_RX sync | U3 HS_RX sync
                                //
                                .URE0D3DP(),  // U escape RX trigger 0 | U3 rx_lp positive
                                .URE1D3DN(),  // U escape RX trigger 1 | U3 rx_lp negative
                                .URE2CKDP(),  // U escape RX trigger 2 | UC rx_lp positive
                                .URE3CKDN(),  // U escape RX trigger 3 | UC rx_lp negative
                                //
                                .U1RE0D(),  // U1 escape RX trigger 0 | D0_DCDP.
                                .U1RE1CN(),  // U1 escape RX trigger 1 | D0_DCDN.
                                .U1RE3N(),  // U1 escape RX trigger 2 | D1_DCDP.
                                .U1RE2D(),  // U1 escape RX trigger 3 | D1_DCDN.
                                //
                                .U2RE0D2(),  // U2 escape RX trigger 0 | D2_DCDP.
                                .U2RE1D2(),  // U2 escape RX trigger 1 | D2_DCDN.
                                .U2RE2D3(),  // U2 escape RX trigger 2 | D3_DCDP.
                                .U2RE3D3(),  // U2 escape RX trigger 3 | D3_DCDN.
                                //
                                .U3RE0CK(),  // U3 escape RX trigger 0 | CLK_DCDP in CIL BYPASSED.
                                .U3RE1CK(),  // U3 escape RX trigger 1 | CLK_DCDN in CIL BYPASSED.
                                .U3RE2(),  // U3 escape RX trigger 2 | N/A
                                .U3RE3(),  // U3 escape RX trigger 3 | N/A
                                //
                                // ULPS_RX escape (17*)
                                .URXULPSE(),  // U  ULP_RX escape in CIL | N/A
                                .U1RXDTE(),  // U1 ULP_RX escape in CIL | N/A
                                .U2RPDTE(),  // U2 ULP_RX escape in CIL | N/A
                                .U3RPDTE(),  // U3 ULP_RX escape in CIL | N/A
                                //
                                .URXDRX(),  // U  HS_RX skew call done | D0 LP_RX negative
                                .U1RXSK(),  // U1 HS_RX skew call done | D1 LP_RX negative
                                .U2RXSKC(),  // U2 HS_RX skew call done | D2 LP_RX negative
                                .U3RXSKC(),  // U3 HS_RX skew call done | N/A
                                .URXSKCHS(),  // U  HS_RX skew call | N/A
                                .U1RXSKS(),  // U1 HS_RX skew call | N/A
                                .U2RXSK(),  // U2 HS_RX skew call | N/A
                                .U3RXSK(),  // U3 HS_RX skew call | N/A
                                //
                                .URXLPDTE(),  // u  LP_RX escape | N/A
                                .U1RXUPSE(),  // u1 LP_RX escape | N/A
                                .U2RXUPSE(),  // u2 LP_RX escape | N/A
                                .U3RXUPSE(),  // u3 LP_RX escape | N/A
                                //
                                //
                                .UTXRRS(),  // U  TX ready escape | d0_drxhs
                                .U1TXRYE(),  // U1 TX ready escape | d1_drxhs
                                .U2TXRYE(),  // U2 TX ready escape | d2_drxhs
                                .U3TXRY(),  // U3 TX ready escape | d3_drxhs
                                //
                                .URXVDHS(),  // U  HS_RX valid | U  HS_RX valid
                                .U1RXVDHS(),  // U1 HS_RX valid | U1 HS_RX valid
                                .U2RXVDHS(),  // U2 HS_RX valid | U2 HS_RX valid
                                .U3RXVDHS(),  // U3 HS_RX valid | U3 HS_RX valid
                                //
                                .URXVDE(),  // U  RX valid escape | N/A
                                .U1RXVDE(),  // U1 RX valid escape | N/A
                                .U2RXVDE(),  // U2 RX valid escape | N/A
                                .U3RXVDE(),  // U3 RX valid escape | N/A
                                // Stop state
                                .USSTT(data_lane_ss_w[0]),  // U  Stop state | N/A
                                .U1SSTT(data_lane_ss_w[1]),  // U1 Stop state | N/A
                                .U2SSTT(data_lane_ss_w[2]),  // U2 Stop state | N/A
                                .U3SSTT(data_lane_ss_w[3]),  // U3 Stop state | N/A
                                //
                                .UTXRYSK(),  // U TX skew ready | N/A
                                .U1TXRYSK(),  // U TX skew ready | N/A
                                .U2TXRYSK(),  // U TX skew ready | N/A
                                .U3TXRYSK(),  // U TX skew ready | N/A
                                //
                                .UCRXCKAT(),  // uc_RxClkActiveHS | CLK_DRXHS
                                .UCRXUCKN(),  // This active low signal is asserted to indicate that the Clock Lane Module has entered the Ultra Low-Power State | N/A
                                .UCSSTT(),  // This active high signal indicates that the Lane Module is currently in Stop state | N/A
                                //
                                .UCUSAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                .UUSAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                .U1USAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                .U2USAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                .U3USAN(),  // This active low signal is asserted to indicate that the Lane is in ULP state | N/A
                                //
                                .URWDCKHS(),  // u_rxwordclkhs(This is used to synchronize signals in the high-speed receive clock domain.)
                                .UTWDCKHS(clk_byte_o),  // u_txwordclkhs(This is used to synchronize PPI signals in the high-speed transmit clock domain.)
                                .UCRXWCHS(),  // uc_rxwordclkhs
                                .CLKLBACT(),  // clk_lb_active
                                //IO
                                .CKN(clk_n_io),  // Negative part of differential clock.
                                .CKP(clk_p_io),  // Positive part of differential clock.
                                .DN0(d_n_w[0]),  // Negative part of differential data lane 0.
                                .DN1(d_n_w[1]),  // Negative part of differential data lane 1.
                                .DN2(d_n_w[2]),  // Negative part of differential data lane 2.
                                .DN3(d_n_w[3]),  // Negative part of differential data lane 3.
                                .DP0(d_p_w[0]),  // Positive part of differential data lane 0.
                                .DP1(d_p_w[1]),  // Positive part of differential data lane 1.
                                .DP2(d_p_w[2]),  // Positive part of differential data lane 2.
                                .DP3(d_p_w[3]) // Positive part of differential data lane 3.
                                ) ; 
                    end
                assign ready_o = ((PLL_MODE == "INTERNAL") ? pll_lock_o : pll_lock_i) ; 
            end
        else
            if ((DPHY_IP == "LATTICE")) 
                begin : LATTICE_SOFT_IP
                    csi_dphy_ipgen_lscc_mipi_dphy_soft_tx #(.GEAR(GEAR),
                            .NUM_LANE(NUM_LANE),
                            .PLL_MODE(PLL_MODE),
                            .MODE(MODE),
                            .VOLTAGE(VOLTAGE),
                            .CSET(CSET),
                            .CRIPPLE(CRIPPLE),
                            .IPP_CTRL(IPP_CTRL),
                            .IPP_SEL(IPP_SEL),
                            .BW_CTL_BIAS(BW_CTL_BIAS),
                            .V2I_PP_RES(V2I_PP_RES),
                            .CLKI_FREQ(CLKI_FREQ),
                            .CLKOP_FREQ_ACTUAL(CLKOP_FREQ_ACTUAL),
                            .CLKOS_FREQ_ACTUAL(CLKOS_FREQ_ACTUAL),
                            .CLKOS2_FREQ_ACTUAL(CLKOS2_FREQ_ACTUAL),
                            .CLKOS3_FREQ_ACTUAL(CLKOS3_FREQ_ACTUAL),
                            .CLKOS4_FREQ_ACTUAL(CLKOS4_FREQ_ACTUAL),
                            .CLKOS5_FREQ_ACTUAL(CLKOS5_FREQ_ACTUAL),
                            .CLKOP_PHASE_ACTUAL(CLKOP_PHASE_ACTUAL),
                            .CLKOS_PHASE_ACTUAL(CLKOS_PHASE_ACTUAL),
                            .CLKOS2_PHASE_ACTUAL(CLKOS2_PHASE_ACTUAL),
                            .CLKOS3_PHASE_ACTUAL(CLKOS3_PHASE_ACTUAL),
                            .CLKOS4_PHASE_ACTUAL(CLKOS4_PHASE_ACTUAL),
                            .CLKOS5_PHASE_ACTUAL(CLKOS5_PHASE_ACTUAL),
                            .PLL_REFCLK_FROM_PIN(PLL_REFCLK_FROM_PIN),
                            .IO_TYPE(IO_TYPE),
                            .CLKI_DIVIDER_ACTUAL_STR(CLKI_DIVIDER_ACTUAL_STR),
                            .FBCLK_DIVIDER_ACTUAL_STR(FBCLK_DIVIDER_ACTUAL_STR),
                            .FBK_MODE(FBK_MODE),
                            .DIVOP_ACTUAL_STR(DIVOP_ACTUAL_STR),
                            .DIVOS_ACTUAL_STR(DIVOS_ACTUAL_STR),
                            .DIVOS2_ACTUAL_STR(DIVOS2_ACTUAL_STR),
                            .DIVOS3_ACTUAL_STR(DIVOS3_ACTUAL_STR),
                            .DIVOS4_ACTUAL_STR(DIVOS4_ACTUAL_STR),
                            .DIVOS5_ACTUAL_STR(DIVOS5_ACTUAL_STR),
                            .SSC_N_CODE_STR(SSC_N_CODE_STR),
                            .SSC_F_CODE_STR(SSC_F_CODE_STR),
                            .DELA(DELA),
                            .DELB(DELB),
                            .DELC(DELC),
                            .DELD(DELD),
                            .DELE(DELE),
                            .DELF(DELF),
                            .PHIA(PHIA),
                            .PHIB(PHIB),
                            .PHIC(PHIC),
                            .PHID(PHID),
                            .PHIE(PHIE),
                            .PHIF(PHIF),
                            .FRAC_N_EN(FRAC_N_EN),
                            .SS_EN(SS_EN),
                            .CLKOP_BYPASS(CLKOP_BYPASS),
                            .CLKOS_BYPASS(CLKOS_BYPASS),
                            .CLKOS2_BYPASS(CLKOS2_BYPASS),
                            .CLKOS3_BYPASS(CLKOS3_BYPASS),
                            .CLKOS4_BYPASS(CLKOS4_BYPASS),
                            .CLKOS5_BYPASS(CLKOS5_BYPASS),
                            .CLKOS_EN(CLKOS_EN),
                            .CLKOS2_EN(CLKOS2_EN),
                            .CLKOS3_EN(CLKOS3_EN),
                            .CLKOS4_EN(CLKOS4_EN),
                            .CLKOS5_EN(CLKOS5_EN),
                            .DYN_PORTS_EN(DYN_PORTS_EN),
                            .ENCLKOP_EN(ENCLKOP_EN),
                            .ENCLKOS_EN(ENCLKOS_EN),
                            .ENCLKOS2_EN(ENCLKOS2_EN),
                            .ENCLKOS3_EN(ENCLKOS3_EN),
                            .ENCLKOS4_EN(ENCLKOS4_EN),
                            .ENCLKOS5_EN(ENCLKOS5_EN),
                            .PLL_RST(PLL_RST),
                            .LOCK_EN(LOCK_EN),
                            .PLL_LOCK_STICKY(PLL_LOCK_STICKY),
                            .LEGACY_EN(LEGACY_EN),
                            .LMMI_EN(LMMI_EN),
                            .APB_EN(APB_EN),
                            .POWERDOWN_EN(POWERDOWN_EN),
                            .TRIM_EN_P(TRIM_EN_P),
                            .TRIM_EN_S(TRIM_EN_S),
                            .CLKOP_TRIM_MODE(CLKOP_TRIM_MODE),
                            .CLKOS_TRIM_MODE(CLKOS_TRIM_MODE),
                            .CLKOP_TRIM(CLKOP_TRIM),
                            .CLKOS_TRIM(CLKOS_TRIM)) u_lscc_mipi_dphy_soft_tx (// .CLK_MODE                 (CLK_MODE),
                            // PLL Parameters
                            .sync_clk_i(sync_clk_i), 
                                .sync_rst_i(sync_rst_i), 
                                ///
                            .pll_clkop_i(pll_clkop_i), 
                                .pll_clkos_i(pll_clkos_i), 
                                .pll_lock_i(pll_lock_i), 
                                ///
                            .hs_tx_en_i(hs_tx_en_i), 
                                .hs_tx_clk_en_i(hs_tx_clk_en_i), 
                                .hs_tx_data_i(hs_tx_data_i), 
                                .hs_tx_data_en_i(hs_tx_data_en_i), 
                                ///
                            .lp_tx_en_i(lp_tx_en_i), 
                                .lp_tx_data_p_i(lp_tx_data_p_i), 
                                .lp_tx_data_n_i(lp_tx_data_n_i), 
                                .lp_tx_data_en_i(lp_tx_data_en_i), 
                                .lp_tx_clk_p_i(lp_tx_clk_p_i), 
                                .lp_tx_clk_n_i(lp_tx_clk_n_i), 
                                ///
                            .lp_rx_en_i(lp_rx_en_i), 
                                .lp_rx_data_p_o(lp_rx_data_p_o), 
                                .lp_rx_data_n_o(lp_rx_data_n_o), 
                                ///
                            .pd_dphy_i(pd_dphy_i), 
                                .pll_lock_o(pll_lock_o), 
                                .clk_byte_o(clk_byte_o), 
                                .ready_o(ready_o), 
                                ///
                            .clk_p_io(clk_p_io), 
                                .clk_n_io(clk_n_io), 
                                .data_p_io(data_p_io), 
                                .data_n_io(data_n_io)) ; 
                    assign d_p_w = 4'hF ; //Add to meet requirement from "Synplify Pro"
                    assign d_n_w = 4'hF ; //Add to meet requirement from "Synplify Pro"
                    assign lp_tx_data_p_w = 4'hF ; //Add to meet requirement from "Synplify Pro"
                    assign lp_tx_data_n_w = 4'hF ; //Add to meet requirement from "Synplify Pro"
                    assign data_lane_0_w = 32'hFFFFFFFF ; //Add to meet requirement from "Synplify Pro"
                    assign data_lane_1_w = 32'hFFFFFFFF ; //Add to meet requirement from "Synplify Pro"
                    assign data_lane_2_w = 32'hFFFFFFFF ; //Add to meet requirement from "Synplify Pro"
                    assign data_lane_3_w = 32'hFFFFFFFF ; //Add to meet requirement from "Synplify Pro"
                end
    endgenerate
endmodule



`timescale 1ns/10ps
// =============================================================================
// lscc_mipi_wrapper_tx.v
// =============================================================================
// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               : CSI2_DSI_DPHY_TX
// File                  : lscc_mipi_dphy_soft_tx.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           : Includes PLL, ECLKDIV, ECLKSYNC, lscc_gddr_sync,
//                       : one MIPI for clock generation, one - four MIPI's for
//                       : data transmission.Supports GEAR x2, x4, x8 for
//                       : 1, 2, 3, 4 lanes.Has a continuous clock mode and
//                       : non continuous clock mode.
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 10/07/18
// Changes Made          : Initial version.
// -----------------------------------------------------------------------------
// Version               : 1.1
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 9/06/19
// Changes Made          : Ported to Radiant 2.265.
// =============================================================================
module csi_dphy_ipgen_lscc_mipi_dphy_soft_tx #(parameter GEAR = 2, 
        parameter NUM_LANE = 1, 
        parameter PLL_MODE = "INTERNAL", 
        parameter MODE = "FREQUENCY", 
        parameter VOLTAGE = 0, 
        parameter CSET = "40P", 
        parameter CRIPPLE = "5P", 
        parameter IPP_CTRL = "0b1000", 
        parameter IPP_SEL = "0b1111", 
        parameter BW_CTL_BIAS = "0b0101", 
        parameter V2I_PP_RES = "10K", 
        parameter CLKI_FREQ = 100.0, 
        parameter CLKOP_FREQ_ACTUAL = 100.0, 
        parameter CLKOS_FREQ_ACTUAL = 100.0, 
        parameter CLKOS2_FREQ_ACTUAL = 100.0, 
        parameter CLKOS3_FREQ_ACTUAL = 100.0, 
        parameter CLKOS4_FREQ_ACTUAL = 100.0, 
        parameter CLKOS5_FREQ_ACTUAL = 100.0, 
        parameter CLKOP_PHASE_ACTUAL = 0.0, 
        parameter CLKOS_PHASE_ACTUAL = 0.0, 
        parameter CLKOS2_PHASE_ACTUAL = 0.0, 
        parameter CLKOS3_PHASE_ACTUAL = 0.0, 
        parameter CLKOS4_PHASE_ACTUAL = 0.0, 
        parameter CLKOS5_PHASE_ACTUAL = 0.0, 
        parameter PLL_REFCLK_FROM_PIN = 0, 
        parameter IO_TYPE = "LVDS", 
        parameter CLKI_DIVIDER_ACTUAL_STR = "1", 
        parameter FBCLK_DIVIDER_ACTUAL_STR = "1", 
        parameter FBK_MODE = "CLKOP", 
        parameter DIVOP_ACTUAL_STR = "1", 
        parameter DIVOS_ACTUAL_STR = "1", 
        parameter DIVOS2_ACTUAL_STR = "1", 
        parameter DIVOS3_ACTUAL_STR = "1", 
        parameter DIVOS4_ACTUAL_STR = "1", 
        parameter DIVOS5_ACTUAL_STR = "1", 
        parameter SSC_N_CODE_STR = "0b000010100", 
        parameter SSC_F_CODE_STR = "0b000000000000000", 
        parameter DELA = "0", 
        parameter DELB = "0", 
        parameter DELC = "0", 
        parameter DELD = "0", 
        parameter DELE = "0", 
        parameter DELF = "0", 
        parameter PHIA = "0", 
        parameter PHIB = "0", 
        parameter PHIC = "0", 
        parameter PHID = "0", 
        parameter PHIE = "0", 
        parameter PHIF = "0", 
        parameter FRAC_N_EN = 0, 
        parameter SS_EN = 0, 
        parameter CLKOP_BYPASS = 0, 
        parameter CLKOS_BYPASS = 0, 
        parameter CLKOS2_BYPASS = 0, 
        parameter CLKOS3_BYPASS = 0, 
        parameter CLKOS4_BYPASS = 0, 
        parameter CLKOS5_BYPASS = 0, 
        parameter CLKOS_EN = 0, 
        parameter CLKOS2_EN = 0, 
        parameter CLKOS3_EN = 0, 
        parameter CLKOS4_EN = 0, 
        parameter CLKOS5_EN = 0, 
        parameter DYN_PORTS_EN = 0, 
        parameter ENCLKOP_EN = 0, 
        parameter ENCLKOS_EN = 0, 
        parameter ENCLKOS2_EN = 0, 
        parameter ENCLKOS3_EN = 0, 
        parameter ENCLKOS4_EN = 0, 
        parameter ENCLKOS5_EN = 0, 
        parameter PLL_RST = 0, 
        parameter LOCK_EN = 0, 
        parameter PLL_LOCK_STICKY = 0, 
        parameter LEGACY_EN = 0, 
        parameter LMMI_EN = 0, 
        parameter APB_EN = 0, 
        parameter POWERDOWN_EN = 0, 
        parameter TRIM_EN_P = 0, 
        parameter TRIM_EN_S = 0, 
        parameter CLKOP_TRIM_MODE = "Falling", 
        parameter CLKOS_TRIM_MODE = "Falling", 
        parameter CLKOP_TRIM = "0b0000", 
        parameter CLKOS_TRIM = "0b0000") (
    // -----------------------------------------------------------------------------
    // Module Parameters
    // -----------------------------------------------------------------------------
    // -----------------------------------------------------------------------------
    // Soft PLL
    // -----------------------------------------------------------------------------
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    // Clock and reset
    input wire sync_clk_i, 
    input wire sync_rst_i, 
    // PLL outside of the Soft IP
    input wire pll_clkop_i, 
    input wire pll_clkos_i, 
    input wire pll_lock_i, 
    // HS_TX signals
    input wire hs_tx_en_i, 
    input wire hs_tx_clk_en_i, 
    input wire [((GEAR * NUM_LANE) - 1):0] hs_tx_data_i, 
    input wire hs_tx_data_en_i, 
    // LP_TX signals
    input wire lp_tx_en_i, 
    input wire [(NUM_LANE - 1):0] lp_tx_data_p_i, 
    input wire [(NUM_LANE - 1):0] lp_tx_data_n_i, 
    input wire lp_tx_data_en_i, 
    input wire lp_tx_clk_p_i, 
    input wire lp_tx_clk_n_i, 
    // LP_RX signals
    input wire lp_rx_en_i, 
    output wire lp_rx_data_p_o, 
    output wire lp_rx_data_n_o, 
    // Other signals
    input wire pd_dphy_i, 
    output wire pll_lock_o, 
    output wire clk_byte_o, 
    output wire ready_o, 
    // DPHY signals
    inout wire clk_p_io, 
    inout wire clk_n_io, 
    inout wire [(NUM_LANE - 1):0] data_p_io, 
    inout wire [(NUM_LANE - 1):0] data_n_io) ;
    // -----------------------------------------------------------------------------
    // Wire Declarations
    // -----------------------------------------------------------------------------
    wire pll_clkop_w ; 
    wire pll_clkos_w ; 
    wire pll_lock_w ; 
    wire gddr_stop_w ; 
    wire eclksync_clk_w ; 
    wire gddr_reset_w ; 
    wire eclkdiv_clk_w ; 
    wire clk_dif_w ; 
    wire [(NUM_LANE - 1):0] data_dif_w ; 
    wire [(NUM_LANE - 1):0] lp_rx_data_p_w ; 
    wire [(NUM_LANE - 1):0] lp_rx_data_n_w ; 
    // -----------------------------------------------------------------------------
    // Assign Statements
    // -----------------------------------------------------------------------------
    assign lp_rx_data_p_o = lp_rx_data_p_w[0] ; 
    assign lp_rx_data_n_o = lp_rx_data_n_w[0] ; 
    assign clk_byte_o = eclkdiv_clk_w ; 
    ///////////////////////////////////////////////////////////////////////////////
    /// PLL
    ///////////////////////////////////////////////////////////////////////////////
    generate
        if ((PLL_MODE == "INTERNAL")) 
            begin : INTERNAL_PLL
                csi_dphy_ipgen_lscc_pll #(.MODE(MODE),
                        .VOLTAGE(VOLTAGE),
                        .CSET(CSET),
                        .CRIPPLE(CRIPPLE),
                        .IPP_CTRL(IPP_CTRL),
                        .IPP_SEL(IPP_SEL),
                        .BW_CTL_BIAS(BW_CTL_BIAS),
                        .V2I_PP_RES(V2I_PP_RES),
                        .CLKI_FREQ(CLKI_FREQ),
                        .CLKOP_FREQ_ACTUAL(CLKOP_FREQ_ACTUAL),
                        .CLKOS_FREQ_ACTUAL(CLKOS_FREQ_ACTUAL),
                        .CLKOS2_FREQ_ACTUAL(CLKOS2_FREQ_ACTUAL),
                        .CLKOS3_FREQ_ACTUAL(CLKOS3_FREQ_ACTUAL),
                        .CLKOS4_FREQ_ACTUAL(CLKOS4_FREQ_ACTUAL),
                        .CLKOS5_FREQ_ACTUAL(CLKOS5_FREQ_ACTUAL),
                        .CLKOP_PHASE_ACTUAL(CLKOP_PHASE_ACTUAL),
                        .CLKOS_PHASE_ACTUAL(CLKOS_PHASE_ACTUAL),
                        .CLKOS2_PHASE_ACTUAL(CLKOS2_PHASE_ACTUAL),
                        .CLKOS3_PHASE_ACTUAL(CLKOS3_PHASE_ACTUAL),
                        .CLKOS4_PHASE_ACTUAL(CLKOS4_PHASE_ACTUAL),
                        .CLKOS5_PHASE_ACTUAL(CLKOS5_PHASE_ACTUAL),
                        .PLL_REFCLK_FROM_PIN(PLL_REFCLK_FROM_PIN),
                        .IO_TYPE(IO_TYPE),
                        .CLKI_DIVIDER_ACTUAL_STR(CLKI_DIVIDER_ACTUAL_STR),
                        .FBCLK_DIVIDER_ACTUAL_STR(FBCLK_DIVIDER_ACTUAL_STR),
                        .FBK_MODE(FBK_MODE),
                        .DIVOP_ACTUAL_STR(DIVOP_ACTUAL_STR),
                        .DIVOS_ACTUAL_STR(DIVOS_ACTUAL_STR),
                        .DIVOS2_ACTUAL_STR(DIVOS2_ACTUAL_STR),
                        .DIVOS3_ACTUAL_STR(DIVOS3_ACTUAL_STR),
                        .DIVOS4_ACTUAL_STR(DIVOS4_ACTUAL_STR),
                        .DIVOS5_ACTUAL_STR(DIVOS5_ACTUAL_STR),
                        .SSC_N_CODE_STR(SSC_N_CODE_STR),
                        .SSC_F_CODE_STR(SSC_F_CODE_STR),
                        .DELA(DELA),
                        .DELB(DELB),
                        .DELC(DELC),
                        .DELD(DELD),
                        .DELE(DELE),
                        .DELF(DELF),
                        .PHIA(PHIA),
                        .PHIB(PHIB),
                        .PHIC(PHIC),
                        .PHID(PHID),
                        .PHIE(PHIE),
                        .PHIF(PHIF),
                        .FRAC_N_EN(FRAC_N_EN),
                        .SS_EN(SS_EN),
                        .CLKOP_BYPASS(CLKOP_BYPASS),
                        .CLKOS_BYPASS(CLKOS_BYPASS),
                        .CLKOS2_BYPASS(CLKOS2_BYPASS),
                        .CLKOS3_BYPASS(CLKOS3_BYPASS),
                        .CLKOS4_BYPASS(CLKOS4_BYPASS),
                        .CLKOS5_BYPASS(CLKOS5_BYPASS),
                        .CLKOS_EN(CLKOS_EN),
                        .CLKOS2_EN(CLKOS2_EN),
                        .CLKOS3_EN(CLKOS3_EN),
                        .CLKOS4_EN(CLKOS4_EN),
                        .CLKOS5_EN(CLKOS5_EN),
                        .DYN_PORTS_EN(DYN_PORTS_EN),
                        .ENCLKOP_EN(ENCLKOP_EN),
                        .ENCLKOS_EN(ENCLKOS_EN),
                        .ENCLKOS2_EN(ENCLKOS2_EN),
                        .ENCLKOS3_EN(ENCLKOS3_EN),
                        .ENCLKOS4_EN(ENCLKOS4_EN),
                        .ENCLKOS5_EN(ENCLKOS5_EN),
                        .PLL_RST(PLL_RST),
                        .LOCK_EN(LOCK_EN),
                        .PLL_LOCK_STICKY(PLL_LOCK_STICKY),
                        .LEGACY_EN(LEGACY_EN),
                        .LMMI_EN(LMMI_EN),
                        .APB_EN(APB_EN),
                        .POWERDOWN_EN(POWERDOWN_EN),
                        .TRIM_EN_P(TRIM_EN_P),
                        .TRIM_EN_S(TRIM_EN_S),
                        .CLKOP_TRIM_MODE(CLKOP_TRIM_MODE),
                        .CLKOS_TRIM_MODE(CLKOS_TRIM_MODE),
                        .CLKOP_TRIM(CLKOP_TRIM),
                        .CLKOS_TRIM(CLKOS_TRIM)) u_pll (.clki_i(sync_clk_i), 
                            .rstn_i((!sync_rst_i)), 
                            .legacy_i(1'b1), 
                            .pllpd_en_n_i(1'b0), 
                            .phasedir_i(1'b1), 
                            .phasestep_i(1'b1), 
                            .phaseloadreg_i(1'b1), 
                            .phasesel_i(3'b111), 
                            .enclkop_i(1'b1), 
                            .enclkos_i(1'b1), 
                            .enclkos2_i(1'b0), 
                            .enclkos3_i(1'b0), 
                            .enclkos4_i(1'b0), 
                            .enclkos5_i(1'b0), 
                            .clkop_o(pll_clkop_w), 
                            .clkos_o(pll_clkos_w), 
                            .clkos2_o(), 
                            .clkos3_o(), 
                            .clkos4_o(), 
                            .clkos5_o(), 
                            .lock_o(pll_lock_w), 
                            .lmmi_clk_i(1'b1), 
                            .lmmi_resetn_i(1'b1), 
                            .lmmi_request_i(1'b1), 
                            .lmmi_wr_rdn_i(1'b1), 
                            .lmmi_offset_i(7'b1111111), 
                            .lmmi_wdata_i(8'b11111111), 
                            .lmmi_rdata_o(), 
                            .lmmi_rdata_valid_o(), 
                            .lmmi_ready_o(), 
                            .apb_penable_i(1'b0), 
                            .apb_psel_i(1'b0), 
                            .apb_pwrite_i(1'b0), 
                            .apb_paddr_i(7'b0000000), 
                            .apb_pwdata_i(8'b00000000), 
                            .apb_pready_o(), 
                            .apb_pslverr_o(), 
                            .apb_prdata_o()) ; 
                assign pll_lock_o = pll_lock_w ; 
            end
        else
            begin : EXTERNAL_PLL
                assign pll_clkop_w = pll_clkop_i ; 
                assign pll_clkos_w = pll_clkos_i ; 
                assign pll_lock_w = pll_lock_i ; 
                assign lmmi_rdata_o = 8'hFF ; 
                assign lmmi_rdata_valid_o = 1'd1 ; 
                assign lmmi_ready_o = 1'd1 ; 
            end
    endgenerate
    ///////////////////////////////////////////////////////////////////////////////
    /// Clock Divider and gddr_sync
    ///////////////////////////////////////////////////////////////////////////////
    ECLKSYNC #(.STOP_EN("ENABLE")) u_eclksync_core (.ECLKIN(pll_clkop_w), 
                .STOP(gddr_stop_w), 
                .ECLKOUT(eclksync_clk_w)) ; 
    ECLKDIV #(.ECLK_DIV("4"),
            .GSR("DISABLED")) u_eclkdiv (.DIVRST(gddr_reset_w), 
                .ECLKIN(eclksync_clk_w), 
                .SLIP(pd_dphy_i), 
                .DIVOUT(eclkdiv_clk_w)) ; 
    csi_dphy_ipgen_lscc_gddr_sync u_gddr_sync (.sync_clk_i(sync_clk_i), 
                .rst_i(sync_rst_i), 
                .start_i(pll_lock_w), 
                .stop_o(gddr_stop_w), 
                .ddr_reset_o(gddr_reset_w), 
                .ready_o(ready_o)) ; 
    ///////////////////////////////////////////////////////////////////////////////
    /// Clock
    ///////////////////////////////////////////////////////////////////////////////
    ODDRX4 #(.GSR("DISABLED")) u_serializer (.Q(clk_dif_w), 
                .SCLK(eclkdiv_clk_w), 
                .RST(gddr_reset_w), 
                .ECLK(pll_clkos_w), 
                .D0(1'd0), 
                .D1(1'd1), 
                .D2(1'd0), 
                .D3(1'd1), 
                .D4(1'd0), 
                .D5(1'd1), 
                .D6(1'd0), 
                .D7(1'd1)) ; 
    MIPI #(.MIPI_ID("0")) u_mipi_clk (.AP(lp_tx_clk_p_i), 
                .AN(lp_tx_clk_n_i), 
                .TP(hs_tx_clk_en_i), 
                .TN(hs_tx_clk_en_i), 
                .IHS(clk_dif_w), 
                .HSRXEN(1'd0), 
                .HSTXEN(hs_tx_clk_en_i), 
                ///
            .OHS(), 
                .OLSP(), 
                .OLSN(), 
                ///
            .BP(clk_p_io), 
                .BN(clk_n_io)) ; 
    ///////////////////////////////////////////////////////////////////////////////
    /// Data
    ///////////////////////////////////////////////////////////////////////////////
    genvar i ; 
    generate
        for (i = 0;(i < NUM_LANE);i = (i + 1))
        begin : DATA_LINES
            ///
            ///
            ODDRX4 #(.GSR("DISABLED")) u_serializer (.Q(data_dif_w[i]), 
                        .SCLK(eclkdiv_clk_w), 
                        .RST(gddr_reset_w), 
                        .ECLK(eclksync_clk_w), 
                        .D0(hs_tx_data_i[((i * GEAR) + 0)]), 
                        .D1(hs_tx_data_i[((i * GEAR) + 1)]), 
                        .D2(hs_tx_data_i[((i * GEAR) + 2)]), 
                        .D3(hs_tx_data_i[((i * GEAR) + 3)]), 
                        .D4(hs_tx_data_i[((i * GEAR) + 4)]), 
                        .D5(hs_tx_data_i[((i * GEAR) + 5)]), 
                        .D6(hs_tx_data_i[((i * GEAR) + 6)]), 
                        .D7(hs_tx_data_i[((i * GEAR) + 7)])) ; 
            MIPI #(.MIPI_ID("0")) u_mipi_clk (.AP(((i == 0) ? (lp_tx_data_p_i[i] & (!lp_rx_en_i)) : lp_tx_data_p_i[i])), 
                        .AN(((i == 0) ? (lp_tx_data_n_i[i] & (!lp_rx_en_i)) : lp_tx_data_n_i[i])), 
                        .TP((hs_tx_en_i | lp_rx_en_i)), 
                        .TN((hs_tx_en_i | lp_rx_en_i)), 
                        .IHS(data_dif_w[i]), 
                        .HSRXEN(1'd0), 
                        .HSTXEN((hs_tx_en_i & (~lp_tx_en_i))), 
                        ///
                    .OHS(), 
                        .OLSP(lp_rx_data_p_w[i]), 
                        .OLSN(lp_rx_data_n_w[i]), 
                        ///
                    .BP(data_p_io[i]), 
                        .BN(data_n_io[i])) ; 
        end
    endgenerate
endmodule



`timescale 1ns/10ps
//==============================================================================
// lscc_mipi_dphy_soft_tx.v
//==============================================================================
// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               : MIPI_DPHY
// File                  : lscc_mipi_dphy_soft_rx.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           :
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 7/20/18
// Changes Made          : Works only GEAR x8 and x4 receiver part.
// -----------------------------------------------------------------------------
// Version               : 1.1
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 9/06/19
// Changes Made          : Ported to Radiant 2.265.
// -----------------------------------------------------------------------------
// Version               : 1.2
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 19/09/19
// Changes Made          : Ported to Radiant 2.38
// =============================================================================
module csi_dphy_ipgen_lscc_mipi_dphy_soft_rx #(parameter integer NUM_LANE = 1, 
        parameter integer GEAR = 8) (
    // -----------------------------------------------------------------------------
    // Module Parameters
    // -----------------------------------------------------------------------------
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    // Eeset
    input wire sync_clk_i, 
    input wire sync_rst_i, 
    input wire pll_lock_i, 
    // HS_RX signals
    input wire hs_rx_en_i, 
    output wire [((NUM_LANE * GEAR) - 1):0] hs_rx_data_o, 
    // LP_RX signals
    input wire lp_rx_en_i, 
    output wire [(NUM_LANE - 1):0] lp_rx_data_p_o, 
    output wire [(NUM_LANE - 1):0] lp_rx_data_n_o, 
    output wire [(NUM_LANE - 1):0] lp_rx_data_cd_o, 
    output wire lp_rx_clk_p_o, 
    output wire lp_rx_clk_n_o, 
    output wire lp_rx_clk_cd_o, 
    // LP_TX signals
    input wire lp_tx_en_i, 
    input wire lp_tx_data_p_i, 
    input wire lp_tx_data_n_i, 
    // Other signals
    output wire byte_clk_o, 
    output wire ready_o, 
    // DPHY ports
    inout wire clk_p_io, 
    inout wire clk_n_io, 
    inout wire [(NUM_LANE - 1):0] data_p_io, 
    inout wire [(NUM_LANE - 1):0] data_n_io) ;
    // -----------------------------------------------------------------------------
    // Wire Declarations
    // -----------------------------------------------------------------------------
    wire clk_hs_w ; 
    wire clk_hs_sync_w ; 
    wire [(NUM_LANE - 1):0] data_hs_w ; 
    wire [(NUM_LANE - 1):0] data_hs_delay_w ; 
    wire byte_clk_w ; 
    wire stop_w ; 
    // -----------------------------------------------------------------------------
    // Assign Statements
    // -----------------------------------------------------------------------------
    assign lp_rx_clk_cd_o = (lp_rx_clk_p_o | lp_rx_clk_n_o) ; 
    assign lp_rx_data_cd_o = (lp_rx_data_p_o | lp_rx_data_n_o) ; 
    assign byte_clk_o = byte_clk_w ; 
    ///////////////////////////////////////////////////////////////////////////////
    /// Clock
    ///////////////////////////////////////////////////////////////////////////////
    MIPI #(.MIPI_ID("0")) u_mipi_clk (.AP(1'd0), 
                .AN(1'd0), 
                .TP(1'd1), 
                .TN(1'd1), 
                .IHS(1'd1), 
                .HSRXEN(1'd1), 
                .HSTXEN(1'd0), 
                ///
            .OHS(clk_hs_w), 
                .OLSP(lp_rx_clk_p_o), 
                .OLSN(lp_rx_clk_n_o), 
                ///
            .BP(clk_p_io), 
                .BN(clk_n_io)) ; 
    ECLKSYNC #(.STOP_EN("DISABLE")) u_clksync (.ECLKIN(clk_hs_w), 
                .STOP(stop_w), 
                .ECLKOUT(clk_hs_sync_w)) ; 
    ECLKDIV #(.ECLK_DIV(((GEAR == 16) ? "8" : "4")),
            .GSR("DISABLED")) u_eclkdiv (.DIVRST(sync_rst_i), 
                .ECLKIN(clk_hs_sync_w), 
                .SLIP(1'd0), 
                .DIVOUT(byte_clk_w)) ; 
    csi_dphy_ipgen_lscc_gddr_sync u_lscc_gddr_sync (.sync_clk_i(sync_clk_i), 
                .rst_i(sync_rst_i), 
                .start_i(pll_lock_i), 
                .ddr_reset_o(), 
                .stop_o(stop_w), 
                .ready_o(ready_o)) ; 
    ///////////////////////////////////////////////////////////////////////////////
    /// Data
    ///////////////////////////////////////////////////////////////////////////////
    genvar i ; 
    generate
        for (i = 0;(i < NUM_LANE);i = (i + 1))
        begin : DATA_LINES
            MIPI #(.MIPI_ID("0")) u_mipi_data (.AP(((i == 0) ? lp_tx_data_p_i : 1'd0)),  /// For bidir lane 0
                    .AN(((i == 0) ? lp_tx_data_n_i : 1'd0)),  /// For bidir lane 0
                    .TP(((i == 0) ? (!lp_tx_en_i) : 1'd1)),  /// For bidir lane 0
                    .TN(((i == 0) ? (!lp_tx_en_i) : 1'd1)),  /// For bidir lane 0
                    .IHS(1'd1), 
                        .HSRXEN((hs_rx_en_i & (!lp_rx_en_i))), 
                        .HSTXEN(1'd0), 
                        ///
                    .OHS(data_hs_w[i]), 
                        .OLSP(lp_rx_data_p_o[i]), 
                        .OLSN(lp_rx_data_n_o[i]), 
                        /// 
                    .BP(data_p_io[i]), 
                        .BN(data_n_io[i])) ; 
            DELAYB #(.DEL_VALUE("0"),
                    .COARSE_DELAY("0NS"),
                    .DEL_MODE("ECLK_CENTERED")) u_delay_data_line (.A(data_hs_w[i]), 
                        .Z(data_hs_delay_w[i])) ; 
            IDDRX4 #(.GSR("ENABLED")) u_IDDRX4_data_line (.D(data_hs_delay_w[i]), 
                        .SCLK(byte_clk_w), 
                        .RST(sync_rst_i), 
                        .ECLK(clk_hs_sync_w), 
                        .ALIGNWD(1'd0), 
                        .Q0(hs_rx_data_o[((i * GEAR) + 0)]), 
                        .Q1(hs_rx_data_o[((i * GEAR) + 1)]), 
                        .Q2(hs_rx_data_o[((i * GEAR) + 2)]), 
                        .Q3(hs_rx_data_o[((i * GEAR) + 3)]), 
                        .Q4(hs_rx_data_o[((i * GEAR) + 4)]), 
                        .Q5(hs_rx_data_o[((i * GEAR) + 5)]), 
                        .Q6(hs_rx_data_o[((i * GEAR) + 6)]), 
                        .Q7(hs_rx_data_o[((i * GEAR) + 7)])) ; 
        end
    endgenerate
endmodule



`timescale 1ns/1ns
//==============================================================================
// lscc_mipi_dphy_soft_rx.v
//==============================================================================
// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               :
// File                  : lscc_gddr_sync.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           :rst_i       => Synchronous reset
//                       :sync_clk_i  => oscillator clk or other constant
//                       :               running low speed clk.
//                       :               note that this clk should not be
//                       :               coming from clk sources
//                       :               that this module will stop_o or reset
//                       :               (e.g. ECLKSYNC, CLKDIV)
//                       :start_i     => Initialize the sync process
//                       :stop_o      => ECLKSYNC.stop_o signal
//                       :ddr_reset_o => DDR and CLKDIV reset signal
//                       :ready_o     => READY signal; clock sync is done.
//
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             :
// Mod. Date             :
// Changes Made          :
// =============================================================================
// Version               : 1.1
// Author(s)             : Lattice Semiconductor
// Mod. Date             : 7/23/2018
// Changes Made          : Checking codding style
// =============================================================================
// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2017 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement.
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS
// Project               : DPHY_RX
// File                  : lscc_clock_divider.v
// Title                 :
// Dependencies          : 1.
//                       : 2.
// Description           : Used to have clock from 12 MHz to 20 MHz from
//                       : input 24 - 200 MMz
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0
// Author(s)             : Lattice Semiconductor
// Mod. Date             :
// Changes Made          :
// =============================================================================
module csi_dphy_ipgen_lscc_pll #(parameter MODE = "FREQUENCY", 
        parameter VOLTAGE = 0, 
        parameter CSET = "40P", 
        parameter CRIPPLE = "5P", 
        parameter IPP_CTRL = "0b1000", 
        parameter IPP_SEL = "0b1111", 
        parameter BW_CTL_BIAS = "0b0101", 
        parameter V2I_PP_RES = "10K", 
        parameter CLKI_FREQ = 100.0, 
        parameter CLKOP_FREQ_ACTUAL = 100.0, 
        parameter CLKOS_FREQ_ACTUAL = 100.0, 
        parameter CLKOS2_FREQ_ACTUAL = 100.0, 
        parameter CLKOS3_FREQ_ACTUAL = 100.0, 
        parameter CLKOS4_FREQ_ACTUAL = 100.0, 
        parameter CLKOS5_FREQ_ACTUAL = 100.0, 
        parameter CLKOP_PHASE_ACTUAL = 0.0, 
        parameter CLKOS_PHASE_ACTUAL = 0.0, 
        parameter CLKOS2_PHASE_ACTUAL = 0.0, 
        parameter CLKOS3_PHASE_ACTUAL = 0.0, 
        parameter CLKOS4_PHASE_ACTUAL = 0.0, 
        parameter CLKOS5_PHASE_ACTUAL = 0.0, 
        parameter PLL_REFCLK_FROM_PIN = 0, 
        parameter IO_TYPE = "LVDS", 
        parameter CLKI_DIVIDER_ACTUAL_STR = "1", 
        parameter FBCLK_DIVIDER_ACTUAL_STR = "1", 
        parameter FBK_MODE = "CLKOP", 
        parameter DIVOP_ACTUAL_STR = "1", 
        parameter DIVOS_ACTUAL_STR = "1", 
        parameter DIVOS2_ACTUAL_STR = "1", 
        parameter DIVOS3_ACTUAL_STR = "1", 
        parameter DIVOS4_ACTUAL_STR = "1", 
        parameter DIVOS5_ACTUAL_STR = "1", 
        parameter SSC_N_CODE_STR = "0b000010100", 
        parameter SSC_F_CODE_STR = "0b000000000000000", 
        parameter DELA = "0", 
        parameter DELB = "0", 
        parameter DELC = "0", 
        parameter DELD = "0", 
        parameter DELE = "0", 
        parameter DELF = "0", 
        parameter PHIA = "0", 
        parameter PHIB = "0", 
        parameter PHIC = "0", 
        parameter PHID = "0", 
        parameter PHIE = "0", 
        parameter PHIF = "0", 
        parameter FRAC_N_EN = 0, 
        parameter SS_EN = 0, 
        parameter CLKOP_BYPASS = 0, 
        parameter CLKOS_BYPASS = 0, 
        parameter CLKOS2_BYPASS = 0, 
        parameter CLKOS3_BYPASS = 0, 
        parameter CLKOS4_BYPASS = 0, 
        parameter CLKOS5_BYPASS = 0, 
        parameter CLKOS_EN = 0, 
        parameter CLKOS2_EN = 0, 
        parameter CLKOS3_EN = 0, 
        parameter CLKOS4_EN = 0, 
        parameter CLKOS5_EN = 0, 
        parameter DYN_PORTS_EN = 0, 
        parameter ENCLKOP_EN = 0, 
        parameter ENCLKOS_EN = 0, 
        parameter ENCLKOS2_EN = 0, 
        parameter ENCLKOS3_EN = 0, 
        parameter ENCLKOS4_EN = 0, 
        parameter ENCLKOS5_EN = 0, 
        parameter PLL_RST = 0, 
        parameter LOCK_EN = 0, 
        parameter PLL_LOCK_STICKY = 0, 
        parameter LEGACY_EN = 0, 
        parameter LMMI_EN = 0, 
        parameter APB_EN = 0, 
        parameter POWERDOWN_EN = 0, 
        parameter TRIM_EN_P = 0, 
        parameter TRIM_EN_S = 0, 
        parameter CLKOP_TRIM_MODE = "Falling", 
        parameter CLKOS_TRIM_MODE = "Falling", 
        parameter CLKOP_TRIM = "0b0000", 
        parameter CLKOS_TRIM = "0b0000") (
    // LMMI bus
    input wire lmmi_clk_i, 
    input wire lmmi_resetn_i, 
    input wire lmmi_request_i, 
    input wire lmmi_wr_rdn_i, 
    input wire [6:0] lmmi_offset_i, 
    input wire [7:0] lmmi_wdata_i, 
    output wire lmmi_ready_o, 
    output wire lmmi_rdata_valid_o, 
    output wire [7:0] lmmi_rdata_o, 
    // APB bus   
    input wire apb_penable_i, 
    input wire apb_psel_i, 
    input wire apb_pwrite_i, 
    input wire [6:0] apb_paddr_i, 
    input wire [7:0] apb_pwdata_i, 
    output wire apb_pready_o, 
    output wire apb_pslverr_o, 
    output wire [7:0] apb_prdata_o, 
    input rstn_i, 
    input clki_i, 
    input phasedir_i, 
    input phasestep_i, 
    input phaseloadreg_i, 
    input [2:0] phasesel_i, 
    input enclkop_i, 
    input enclkos_i, 
    input enclkos2_i, 
    input enclkos3_i, 
    input enclkos4_i, 
    input enclkos5_i, 
    input pllpd_en_n_i, 
    input legacy_i, 
    output clkop_o, 
    output clkos_o, 
    output clkos2_o, 
    output clkos3_o, 
    output clkos4_o, 
    output clkos5_o, 
    output lock_o) ;
    wire intclkop_w ; 
    wire intclkos_w ; 
    wire intclkos2_w ; 
    wire intclkos3_w ; 
    wire intclkos4_w ; 
    wire intclkos5_w ; 
    localparam SEL_FBK_STR = ((FBK_MODE == "CLKOP") ? "FBKCLK4" : ((FBK_MODE == "CLKOS") ? "FBKCLK4" : ((FBK_MODE == "CLKOS2") ? "FBKCLK4" : ((FBK_MODE == "CLKOS3") ? "FBKCLK4" : ((FBK_MODE == "CLKOS4") ? "FBKCLK4" : ((FBK_MODE == "CLKOS5") ? "FBKCLK4" : ((FBK_MODE == "INTCLKOP") ? "DIVA" : ((FBK_MODE == "INTCLKOS") ? "DIVB" : ((FBK_MODE == "INTCLKOS2") ? "DIVC" : ((FBK_MODE == "INTCLKOS3") ? "DIVD" : ((FBK_MODE == "INTCLKOS4") ? "DIVE" : "DIVF"))))))))))) ; 
    localparam CLKMUX_FB_STR = ((FBK_MODE == "CLKOP") ? "CMUX_CLKOP" : ((FBK_MODE == "CLKOS") ? "CMUX_CLKOS" : ((FBK_MODE == "CLKOS2") ? "CMUX_CLKOS2" : ((FBK_MODE == "CLKOS3") ? "CMUX_CLKOS3" : ((FBK_MODE == "CLKOS4") ? "CMUX_CLKOS4" : ((FBK_MODE == "CLKOS5") ? "CMUX_CLKOS5" : ((FBK_MODE == "INTCLKOP") ? "CMUX_CLKOP" : ((FBK_MODE == "INTCLKOS") ? "CMUX_CLKOS" : ((FBK_MODE == "INTCLKOS2") ? "CMUX_CLKOS2" : ((FBK_MODE == "INTCLKOS3") ? "CMUX_CLKOS3" : ((FBK_MODE == "INTCLKOS4") ? "CMUX_CLKOS4" : "CMUX_CLKOS5"))))))))))) ; 
    wire fbclk_w = ((FBK_MODE == "CLKOP") ? clkop_o : ((FBK_MODE == "CLKOS") ? clkos_o : ((FBK_MODE == "CLKOS2") ? clkos2_o : ((FBK_MODE == "CLKOS3") ? clkos3_o : ((FBK_MODE == "CLKOS4") ? clkos4_o : ((FBK_MODE == "CLKOS5") ? clkos5_o : ((FBK_MODE == "INTCLKOP") ? intclkop_w : ((FBK_MODE == "INTCLKOS") ? intclkos_w : ((FBK_MODE == "INTCLKOS2") ? intclkos2_w : ((FBK_MODE == "INTCLKOS3") ? intclkos3_w : ((FBK_MODE == "INTCLKOS4") ? intclkos4_w : intclkos5_w))))))))))) ; 
    localparam SELOUT_A = ((CLKOP_BYPASS == 1) ? "ENABLED" : "DISABLED") ; 
    localparam SELOUT_B = ((CLKOS_BYPASS == 1) ? "ENABLED" : "DISABLED") ; 
    localparam SELOUT_C = ((CLKOS2_BYPASS == 1) ? "ENABLED" : "DISABLED") ; 
    localparam SELOUT_D = ((CLKOS3_BYPASS == 1) ? "ENABLED" : "DISABLED") ; 
    localparam SELOUT_E = ((CLKOS4_BYPASS == 1) ? "ENABLED" : "DISABLED") ; 
    localparam SELOUT_F = ((CLKOS5_BYPASS == 1) ? "ENABLED" : "DISABLED") ; 
    localparam INTEGER_MODE_TRUE = (((FRAC_N_EN == 0) && (SS_EN == 0)) ? "ENABLED" : "DISABLED") ; 
    localparam FRAC_N_EN_STR = ((FRAC_N_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam SS_EN_STR = ((SS_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam ENCLKOS_EN_LOCAL = ((CLKOS_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam ENCLKOS2_EN_LOCAL = (CLKOS2_EN ? "ENABLED" : "DISABLED") ; 
    localparam ENCLKOS3_EN_LOCAL = ((CLKOS3_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam ENCLKOS4_EN_LOCAL = (CLKOS4_EN ? "ENABLED" : "DISABLED") ; 
    localparam ENCLKOS5_EN_LOCAL = ((CLKOS5_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam DYN_EN = ((DYN_PORTS_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam DYN_SOURCE = ((DYN_PORTS_EN == 1) ? "DYNAMIC" : "STATIC") ; 
    localparam RESET_PLL = ((PLL_RST == 1) ? "ENABLED" : "DISABLED") ; 
    localparam PLL_POWERDOWN_EN = ((POWERDOWN_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam PLL_PD_N = ((POWERDOWN_EN == 1) ? "USED" : "UNUSED") ; 
    localparam LEGACY_ATT = ((LEGACY_EN == 1) ? "ENABLED" : "DISABLED") ; 
    localparam LDT_INT_LOCK_STICKY = ((PLL_LOCK_STICKY == 1) ? "ENABLED" : "DISABLED") ; 
    localparam LDT_LOCK = ((PLL_LOCK_STICKY == 1) ? "6144CYC" : "1536CYC") ; 
    localparam TRIMOP_BYPASS_N = ((TRIM_EN_P == 1) ? "USED" : "BYPASSED") ; 
    localparam TRIMOS_BYPASS_N = ((TRIM_EN_S == 1) ? "USED" : "BYPASSED") ; 
    localparam KP_VCO = ((VOLTAGE == 1) ? "0b11001" : "0b00011") ; 
    localparam V2I_KVCO_SEL = ((VOLTAGE == 1) ? "85" : "60") ; 
    localparam V2I_1V_EN = ((VOLTAGE == 1) ? "DISABLED" : "ENABLED") ; 
    localparam FBK_MMD_PULS_CTL = (((FBCLK_DIVIDER_ACTUAL_STR == "1") || (FBCLK_DIVIDER_ACTUAL_STR == "2")) ? "0b0000" : "0b0001") ; 
    localparam REF_MMD_PULS_CTL = (((CLKI_DIVIDER_ACTUAL_STR == "1") || (CLKI_DIVIDER_ACTUAL_STR == "2")) ? "0b0000" : "0b0001") ; 
    wire [7:0] lmmi_wdata_w ; 
    wire lmmi_wr_rdn_w ; 
    wire [6:0] lmmi_offset_w ; 
    wire lmmi_request_w ; 
    wire apb_lmmi_request_w ; 
    wire [6:0] apb_lmmi_offset_w ; 
    wire [7:0] apb_lmmi_wdata_w ; 
    wire [7:0] apb_lmmi_rdata_w ; 
    wire apb_lmmi_wr_rdn_w ; 
    wire clki_w ; 
    generate
        if ((PLL_REFCLK_FROM_PIN == 0)) 
            begin : genblk1
                assign clki_w = clki_i ; 
            end
        else
            begin : genblk1
                BB u0_BB (.B(clki_i), 
                            .I(1'b0), 
                            .T(1'b1), 
                            .O(clki_w)) ; 
            end
    endgenerate
    // -----------------------------------------------------------------------------
    // Generate Assign Statements
    // -----------------------------------------------------------------------------
    generate
        if ((APB_EN == 0)) 
            begin : LMMI
                assign lmmi_wdata_w = lmmi_wdata_i ; 
                assign lmmi_wr_rdn_w = lmmi_wr_rdn_i ; 
                assign lmmi_offset_w = lmmi_offset_i ; 
                assign lmmi_request_w = lmmi_request_i ; 
            end
    endgenerate
    PLL #(.CLKOP_TRIM(CLKOP_TRIM),
            .CLKOS_TRIM(CLKOS_TRIM),
            .TRIMOP_BYPASS_N(TRIMOP_BYPASS_N),
            .TRIMOS_BYPASS_N(TRIMOS_BYPASS_N),
            .DELA(DELA),
            .DELB(DELB),
            .DELC(DELC),
            .DELD(DELD),
            .DELE(DELE),
            .DELF(DELF),
            .DIRECTION(DYN_EN),
            .DIVA(DIVOP_ACTUAL_STR),
            .DIVB(DIVOS_ACTUAL_STR),
            .DIVC(DIVOS2_ACTUAL_STR),
            .DIVD(DIVOS3_ACTUAL_STR),
            .DIVE(DIVOS4_ACTUAL_STR),
            .DIVF(DIVOS5_ACTUAL_STR),
            .DYN_SEL("0b000"),
            .DYN_SOURCE(DYN_SOURCE),
            .ENCLK_CLKOP("ENABLED"),
            .ENCLK_CLKOS(ENCLKOS_EN_LOCAL),
            .ENCLK_CLKOS2(ENCLKOS2_EN_LOCAL),
            .ENCLK_CLKOS3(ENCLKOS3_EN_LOCAL),
            .ENCLK_CLKOS4(ENCLKOS4_EN_LOCAL),
            .ENCLK_CLKOS5(ENCLKOS5_EN_LOCAL),
            .ENABLE_SYNC("DISABLED"),
            .FBK_INTEGER_MODE(INTEGER_MODE_TRUE),
            .FBK_MASK("0b00000000"),
            .FBK_MMD_DIG(FBCLK_DIVIDER_ACTUAL_STR),
            .LDT_INT_LOCK_STICKY(LDT_INT_LOCK_STICKY),
            .LDT_LOCK(LDT_LOCK),
            .LEGACY_ATT(LEGACY_ATT),
            .LOAD_REG(DYN_EN),
            .OPENLOOP_EN("DISABLED"),
            .PHIA(PHIA),
            .PHIB(PHIB),
            .PHIC(PHIC),
            .PHID(PHID),
            .PHIE(PHIE),
            .PHIF(PHIF),
            .PLLPDN_EN(PLL_POWERDOWN_EN),
            .PLLPD_N("USED"),
            .PLLRESET_ENA(RESET_PLL),
            .REF_INTEGER_MODE("ENABLED"),
            .REF_MASK("0b00000000"),
            .REF_MMD_DIG(CLKI_DIVIDER_ACTUAL_STR),
            .ROTATE(DYN_EN),
            .SEL_OUTA(SELOUT_A),
            .SEL_OUTB(SELOUT_B),
            .SEL_OUTC(SELOUT_C),
            .SEL_OUTD(SELOUT_D),
            .SEL_OUTE(SELOUT_E),
            .SEL_OUTF(SELOUT_F),
            .SSC_EN_SDM(FRAC_N_EN_STR),
            .SSC_EN_SSC(SS_EN_STR),
            .REF_MMD_PULS_CTL(REF_MMD_PULS_CTL),
            .FBK_MMD_PULS_CTL(FBK_MMD_PULS_CTL),
            .V2I_PP_ICTRL("0b00110"),
            .SEL_FBK(SEL_FBK_STR),
            .CLKMUX_FB(CLKMUX_FB_STR),
            .KP_VCO(KP_VCO),
            .CSET(CSET),
            .CRIPPLE(CRIPPLE),
            .IPP_CTRL(IPP_CTRL),
            .IPP_SEL(IPP_SEL),
            .BW_CTL_BIAS(BW_CTL_BIAS),
            .V2I_PP_RES(V2I_PP_RES),
            .V2I_KVCO_SEL(V2I_KVCO_SEL),
            .V2I_1V_EN(V2I_1V_EN),
            .SSC_N_CODE(SSC_N_CODE_STR),
            .SSC_F_CODE(SSC_F_CODE_STR),
            .EXTERNAL_DIVIDE_FACTOR("1")) u0_PLL (.INTFBKOP(intclkop_w), 
                .INTFBKOS(intclkos_w), 
                .INTFBKOS2(intclkos2_w), 
                .INTFBKOS3(intclkos3_w), 
                .INTFBKOS4(intclkos4_w), 
                .INTFBKOS5(intclkos5_w), 
                .DIR(phasedir_i), 
                .DIRSEL(phasesel_i), 
                .LOADREG(phaseloadreg_i), 
                .DYNROTATE(phasestep_i), 
                .LMMICLK(lmmi_clk_i), 
                .LMMIRESET_N(lmmi_resetn_i), 
                .LMMIREQUEST(lmmi_request_w), 
                .LMMIWRRD_N(lmmi_wr_rdn_w), 
                .LMMIOFFSET(lmmi_offset_w), 
                .LMMIWDATA(lmmi_wdata_w), 
                .LMMIRDATA(lmmi_rdata_o), 
                .LMMIRDATAVALID(lmmi_rdata_valid_o), 
                .LMMIREADY(lmmi_ready_o), 
                .PLLPOWERDOWN_N(pllpd_en_n_i), 
                .REFCK(clki_w), 
                .CLKOP(clkop_o), 
                .CLKOS(clkos_o), 
                .CLKOS2(clkos2_o), 
                .CLKOS3(clkos3_o), 
                .CLKOS4(clkos4_o), 
                .CLKOS5(clkos5_o), 
                .ENCLKOP(enclkop_i), 
                .ENCLKOS(enclkos_i), 
                .ENCLKOS2(enclkos2_i), 
                .ENCLKOS3(enclkos3_i), 
                .ENCLKOS4(enclkos4_i), 
                .ENCLKOS5(enclkos5_i), 
                .FBKCK(fbclk_w), 
                .INTLOCK(), 
                .LEGACY(legacy_i), 
                .LEGRDYN(), 
                .LOCK(lock_o), 
                .PFDDN(), 
                .PFDUP(), 
                .PLLRESET((~rstn_i)), 
                .STDBY(1'b0), 
                .REFMUXCK(), 
                .REGQA(), 
                .REGQB(), 
                .REGQB1(), 
                .CLKOUTDL(), 
                .ROTDEL(1'b0), 
                .DIRDEL(1'b0), 
                .ROTDELP1(1'b0), 
                .GRAYTEST(5'b0), 
                .BINTEST(2'b0), 
                .DIRDELP1(1'b0), 
                .GRAYACT(5'b0), 
                .BINACT(2'b0)) ; 
endmodule



`timescale 1ns/1ns
module csi_dphy_ipgen_lscc_mipi_dphy #(parameter FAMILY = "common", 
        parameter INT_TYPE = "TX", 
        parameter INTF = "CSI2_APP", 
        parameter DPHY_IP = "HARD_IP", 
        parameter INT_FREQ = 1250.0, 
        parameter INT_DATA_RATE = 2500.0, 
        parameter GEAR = 8, 
        parameter NUM_LANE = 1, 
        parameter SYNC_CLOCK_FREQ = 200, 
        parameter HSEL = "DISABLED", 
        parameter [((8 * 8) - 1):0] PLL_MODE = "INTERNAL", 
        parameter CN = "00000", 
        parameter CO = "000", 
        parameter CM = "00000000", 
        parameter CLK_MODE = "ENABLED", 
        parameter CIL_BYPASS = "CIL_BYPASSED", 
        parameter REF_CLOCK_FROM_IO_PIN = 0, 
        parameter REF_CLK_INPUT_BUF_TYPE = "MIPI_DPHY", 
        parameter START_UP_SYNCH_LOGIC = 0, 
        parameter LOW = (((INT_TYPE == "TX") && (DPHY_IP == "LATTICE")) ? 7 : 5), 
        parameter LDW = (((INT_TYPE == "TX") && (DPHY_IP == "LATTICE")) ? 8 : 4), 
        parameter DATA_WIDTH = (GEAR * NUM_LANE), 
        parameter LP_RX_DW = ((INT_TYPE == "RX") ? NUM_LANE : 1), 
        parameter LP_TX_DW = ((INT_TYPE == "TX") ? NUM_LANE : 1), 
        parameter [((8 * 6) - 1):0] T_DATA_SETTLE = "000001", 
        parameter [((8 * 6) - 1):0] T_CLK_SETTLE = "000001", 
        parameter MODE = "FREQUENCY", 
        parameter VOLTAGE = 0, 
        parameter CSET = "40P", 
        parameter CRIPPLE = "5P", 
        parameter IPP_CTRL = "0b1000", 
        parameter IPP_SEL = "0b1111", 
        parameter BW_CTL_BIAS = "0b0101", 
        parameter V2I_PP_RES = "10K", 
        parameter CLKI_FREQ = 100.0, 
        parameter CLKOP_FREQ_ACTUAL = 100.0, 
        parameter CLKOS_FREQ_ACTUAL = 100.0, 
        parameter CLKOS2_FREQ_ACTUAL = 100.0, 
        parameter CLKOS3_FREQ_ACTUAL = 100.0, 
        parameter CLKOS4_FREQ_ACTUAL = 100.0, 
        parameter CLKOS5_FREQ_ACTUAL = 100.0, 
        parameter CLKOP_PHASE_ACTUAL = 0.0, 
        parameter CLKOS_PHASE_ACTUAL = 0.0, 
        parameter CLKOS2_PHASE_ACTUAL = 0.0, 
        parameter CLKOS3_PHASE_ACTUAL = 0.0, 
        parameter CLKOS4_PHASE_ACTUAL = 0.0, 
        parameter CLKOS5_PHASE_ACTUAL = 0.0, 
        parameter PLL_REFCLK_FROM_PIN = 0, 
        parameter IO_TYPE = "LVDS", 
        parameter CLKI_DIVIDER_ACTUAL_STR = "1", 
        parameter FBCLK_DIVIDER_ACTUAL_STR = "1", 
        parameter FBK_MODE = "CLKOP", 
        parameter DIVOP_ACTUAL_STR = "1", 
        parameter DIVOS_ACTUAL_STR = "1", 
        parameter DIVOS2_ACTUAL_STR = "1", 
        parameter DIVOS3_ACTUAL_STR = "1", 
        parameter DIVOS4_ACTUAL_STR = "1", 
        parameter DIVOS5_ACTUAL_STR = "1", 
        parameter SSC_N_CODE_STR = "0b000010100", 
        parameter SSC_F_CODE_STR = "0b000000000000000", 
        parameter DELA = "0", 
        parameter DELB = "0", 
        parameter DELC = "0", 
        parameter DELD = "0", 
        parameter DELE = "0", 
        parameter DELF = "0", 
        parameter PHIA = "0", 
        parameter PHIB = "0", 
        parameter PHIC = "0", 
        parameter PHID = "0", 
        parameter PHIE = "0", 
        parameter PHIF = "0", 
        parameter FRAC_N_EN = 0, 
        parameter SS_EN = 0, 
        parameter CLKOP_BYPASS = 0, 
        parameter CLKOS_BYPASS = 0, 
        parameter CLKOS2_BYPASS = 0, 
        parameter CLKOS3_BYPASS = 0, 
        parameter CLKOS4_BYPASS = 0, 
        parameter CLKOS5_BYPASS = 0, 
        parameter CLKOS_EN = 0, 
        parameter CLKOS2_EN = 0, 
        parameter CLKOS3_EN = 0, 
        parameter CLKOS4_EN = 0, 
        parameter CLKOS5_EN = 0, 
        parameter DYN_PORTS_EN = 0, 
        parameter ENCLKOP_EN = 0, 
        parameter ENCLKOS_EN = 0, 
        parameter ENCLKOS2_EN = 0, 
        parameter ENCLKOS3_EN = 0, 
        parameter ENCLKOS4_EN = 0, 
        parameter ENCLKOS5_EN = 0, 
        parameter PLL_RST = 0, 
        parameter LOCK_EN = 0, 
        parameter PLL_LOCK_STICKY = 0, 
        parameter LEGACY_EN = 0, 
        parameter LMMI_EN = 0, 
        parameter APB_EN = 0, 
        parameter POWERDOWN_EN = 0, 
        parameter TRIM_EN_P = 0, 
        parameter TRIM_EN_S = 0, 
        parameter CLKOP_TRIM_MODE = "Falling", 
        parameter CLKOS_TRIM_MODE = "Falling", 
        parameter CLKOP_TRIM = "0b0000", 
        parameter CLKOS_TRIM = "0b0000") (
    // -----------------------------------------------------------------------------
    // Module Parameters
    // -----------------------------------------------------------------------------
    // Select the Transmit or Receive interface type.
    // Select a DSI or CSI-2 application.
    // In Hard MIPI DPHY mode, Scuba will generate the MIPI Hard DPHY Rx or Tx interface modules form HARD_IP. In Soft MIPI DPHY mode, Scuba will generate the Soft MIPI DPHY Rx or tx interface module.
    // This sets the Frequency MIPI Input Clock, which determines the gearing ratio used for DPHY Soft and Hard modules.
    // A read-only field displaying a calculated value based on the clock frequency.
    // This value is set automatically based on the clock frequency. It can also be manually adjusted.
    // The desired bus width to generate.
    // Input Clock Frequency of the DPHY PLL. Based on this value and the divider settings, Scuba automatically calculates the output clock frequency. The CLKOUT frequency will be the interface Clock Frequency entered above.
    // Automatically enabled when INT_DATA_RATE equal to 2500.000000.
    // PLL programing parameters.
    // PLL programing parameters.
    // PLL programing parameters.
    // Clock mode which can be continuous(Only HS mode) or noncontinuous(HS and LP mode).
    // Enable CIL mode in HARD DPHY.
    // Used to set the IO_TYPE for the reference clock input to the PLL so that the Planner can place the module.
    // Used to set the IO_TYPE for the reference clock input to the PLL so that the Planner can place the module.
    // Adds start-up synchronization logic.
    /// Internal parameters
    //        ||
    //        \/
    // LMMI offset width.
    //        ||
    //        \/
    // LMMI data width.
    // Input or output data width in hight speed mode.
    //        \/
    // LP_RX data width for RX 4 lanes data for TX only lane 0 by spec.
    //        \/
    // LP_TX data width for TX 4 lanes data for RX only lane 0 by spec.
    // -----------------------------------------------------------------------------
    // Soft PLL
    // -----------------------------------------------------------------------------
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    // Clock and reset
    input wire sync_clk_i,  // Clock for mipi_dphy.
    input wire sync_rst_i,  // Reset for mipi_dphy.
    // LMMI
    input wire lmmi_clk_i,  // Clock for LMMI.
    input wire lmmi_resetn_i,  // Active low reset.
    input wire [(LDW - 1):0] lmmi_wdata_i,  // Data from user.
    input wire lmmi_wr_rdn_i,  // Active hight write, low read.
    input wire [(LOW - 1):0] lmmi_offset_i,  // Offset.
    input wire lmmi_request_i,  // Request.
    output wire lmmi_ready_o,  // If hight LMMI is ready for communicate.
    output wire [(LDW - 1):0] lmmi_rdata_o,  // Data from LMMI.
    output wire lmmi_rdata_valid_o,  // Valid for data from LMMI.
    // HS_TX signals
    input wire hs_tx_en_i,  // High speed transmit enable.
    input wire hs_tx_clk_en_i,  // High speed clock enable.
    input wire [(DATA_WIDTH - 1):0] hs_tx_data_i,  // High speed transmit data.
    input wire hs_tx_data_en_i,  // High speed transmit data enable.
    // HS_RX signals
    input wire hs_rx_en_i,  // High speed receive enable.
    output wire [(DATA_WIDTH - 1):0] hs_rx_data_o,  // High speed receive data.
    output wire [(NUM_LANE - 1):0] hs_rx_data_en_o,  // High speed receive data is synced.
    // LP_TX signals
    input wire lp_tx_en_i,  // Low power transmit enable.
    input wire [(LP_TX_DW - 1):0] lp_tx_data_p_i,  // Low power transmit data.
    input wire [(LP_TX_DW - 1):0] lp_tx_data_n_i,  // Low power transmit data.
    input wire lp_tx_data_en_i,  // Low power transmit data enable.
    input wire lp_tx_clk_p_i,  // Low power transmit positive data.
    input wire lp_tx_clk_n_i,  // Low power transmit negative data.
    // LP_RX signals
    input wire lp_rx_en_i,  // Low power receive enable.
    output wire [(LP_RX_DW - 1):0] lp_rx_data_p_o,  // Low power receive data.
    output wire [(LP_RX_DW - 1):0] lp_rx_data_n_o,  // Low power receive data.
    output wire lp_rx_clk_p_o,  // Low power positive clock.
    output wire lp_rx_clk_n_o,  // Low power negative clock.
    // ExternalPLL for SOFT DPHY_Tx
    input wire pll_clkop_i,  // Output clock from PLL
    input wire pll_clkos_i,  // Output clock from PLL 90 degree *** then pll_clkop_i
    input wire pll_lock_i,  // PLL lock
    // DPHY ports
    inout wire clk_p_io,  // Positive part of differential clock.
    inout wire clk_n_io,  // Negative part of differential clock.
    inout wire [(NUM_LANE - 1):0] data_p_io,  // Positive part of differential data.
    inout wire [(NUM_LANE - 1):0] data_n_io,  // Negative part of differential data.
    // CIL
    output wire [(NUM_LANE - 1):0] hs_tx_cil_ready_o,  // CIL HARD DPHY ready to transmit HS data
    output wire [(NUM_LANE - 1):0] data_lane_ss_o,  // CIL HARD DPHY ready to transmit HS data
    // Other
    input wire usrstdby_i,  // User standby for PLL and DPHY.
    input wire pd_dphy_i,  // Power down for DPHY or MIPI.
    output wire pll_lock_o,  // PLL ready.
    output wire clk_byte_o,  // Byte clock.
    output wire ready_o // Ready from DPHY or from PLL.
        ) ;
    // -----------------------------------------------------------------------------
    // Submodule Instantiations
    // -----------------------------------------------------------------------------
    generate
        if ((INT_TYPE == "TX")) 
            begin : TRANSMITTER
                csi_dphy_ipgen_lscc_mipi_wrapper_tx #(.NUM_LANE(NUM_LANE),
                        .GEAR(GEAR),
                        .INTF(INTF),
                        .DPHY_IP(DPHY_IP),
                        .CLK_MODE(CLK_MODE),
                        .DPHY_CIL_BYPASS(CIL_BYPASS),
                        .HSEL(HSEL),
                        .CIL_DATA_PREPARE("01"),
                        .CIL_DATA_TRAIL("000001"),
                        .CIL_DATA_ZERO("000001"),
                        .CIL_CLK_PREPARE("1P0_TXCLKESC"),
                        .CIL_CLK_TRAIL("00001"),
                        .CIL_CLK_ZERO("0000001"),
                        .INT_DATA_RATE(INT_DATA_RATE),
                        .TX_FREQ_TGT(0),
                        .REF_CLOCK_FREQ(SYNC_CLOCK_FREQ),
                        .PLL_MODE(PLL_MODE),
                        .CM(CM),
                        .CN(CN),
                        .CO(CO),
                        .MODE(MODE),
                        .VOLTAGE(VOLTAGE),
                        .CSET(CSET),
                        .CRIPPLE(CRIPPLE),
                        .IPP_CTRL(IPP_CTRL),
                        .IPP_SEL(IPP_SEL),
                        .BW_CTL_BIAS(BW_CTL_BIAS),
                        .V2I_PP_RES(V2I_PP_RES),
                        .CLKI_FREQ(CLKI_FREQ),
                        .CLKOP_FREQ_ACTUAL(CLKOP_FREQ_ACTUAL),
                        .CLKOS_FREQ_ACTUAL(CLKOS_FREQ_ACTUAL),
                        .CLKOS2_FREQ_ACTUAL(CLKOS2_FREQ_ACTUAL),
                        .CLKOS3_FREQ_ACTUAL(CLKOS3_FREQ_ACTUAL),
                        .CLKOS4_FREQ_ACTUAL(CLKOS4_FREQ_ACTUAL),
                        .CLKOS5_FREQ_ACTUAL(CLKOS5_FREQ_ACTUAL),
                        .CLKOP_PHASE_ACTUAL(CLKOP_PHASE_ACTUAL),
                        .CLKOS_PHASE_ACTUAL(CLKOS_PHASE_ACTUAL),
                        .CLKOS2_PHASE_ACTUAL(CLKOS2_PHASE_ACTUAL),
                        .CLKOS3_PHASE_ACTUAL(CLKOS3_PHASE_ACTUAL),
                        .CLKOS4_PHASE_ACTUAL(CLKOS4_PHASE_ACTUAL),
                        .CLKOS5_PHASE_ACTUAL(CLKOS5_PHASE_ACTUAL),
                        .PLL_REFCLK_FROM_PIN(PLL_REFCLK_FROM_PIN),
                        .IO_TYPE(IO_TYPE),
                        .CLKI_DIVIDER_ACTUAL_STR(CLKI_DIVIDER_ACTUAL_STR),
                        .FBCLK_DIVIDER_ACTUAL_STR(FBCLK_DIVIDER_ACTUAL_STR),
                        .FBK_MODE(FBK_MODE),
                        .DIVOP_ACTUAL_STR(DIVOP_ACTUAL_STR),
                        .DIVOS_ACTUAL_STR(DIVOS_ACTUAL_STR),
                        .DIVOS2_ACTUAL_STR(DIVOS2_ACTUAL_STR),
                        .DIVOS3_ACTUAL_STR(DIVOS3_ACTUAL_STR),
                        .DIVOS4_ACTUAL_STR(DIVOS4_ACTUAL_STR),
                        .DIVOS5_ACTUAL_STR(DIVOS5_ACTUAL_STR),
                        .SSC_N_CODE_STR(SSC_N_CODE_STR),
                        .SSC_F_CODE_STR(SSC_F_CODE_STR),
                        .DELA(DELA),
                        .DELB(DELB),
                        .DELC(DELC),
                        .DELD(DELD),
                        .DELE(DELE),
                        .DELF(DELF),
                        .PHIA(PHIA),
                        .PHIB(PHIB),
                        .PHIC(PHIC),
                        .PHID(PHID),
                        .PHIE(PHIE),
                        .PHIF(PHIF),
                        .FRAC_N_EN(FRAC_N_EN),
                        .SS_EN(SS_EN),
                        .CLKOP_BYPASS(CLKOP_BYPASS),
                        .CLKOS_BYPASS(CLKOS_BYPASS),
                        .CLKOS2_BYPASS(CLKOS2_BYPASS),
                        .CLKOS3_BYPASS(CLKOS3_BYPASS),
                        .CLKOS4_BYPASS(CLKOS4_BYPASS),
                        .CLKOS5_BYPASS(CLKOS5_BYPASS),
                        .CLKOS_EN(CLKOS_EN),
                        .CLKOS2_EN(CLKOS2_EN),
                        .CLKOS3_EN(CLKOS3_EN),
                        .CLKOS4_EN(CLKOS4_EN),
                        .CLKOS5_EN(CLKOS5_EN),
                        .DYN_PORTS_EN(DYN_PORTS_EN),
                        .ENCLKOP_EN(ENCLKOP_EN),
                        .ENCLKOS_EN(ENCLKOS_EN),
                        .ENCLKOS2_EN(ENCLKOS2_EN),
                        .ENCLKOS3_EN(ENCLKOS3_EN),
                        .ENCLKOS4_EN(ENCLKOS4_EN),
                        .ENCLKOS5_EN(ENCLKOS5_EN),
                        .PLL_RST(PLL_RST),
                        .LOCK_EN(LOCK_EN),
                        .PLL_LOCK_STICKY(PLL_LOCK_STICKY),
                        .LEGACY_EN(LEGACY_EN),
                        .LMMI_EN(LMMI_EN),
                        .APB_EN(APB_EN),
                        .POWERDOWN_EN(POWERDOWN_EN),
                        .TRIM_EN_P(TRIM_EN_P),
                        .TRIM_EN_S(TRIM_EN_S),
                        .CLKOP_TRIM_MODE(CLKOP_TRIM_MODE),
                        .CLKOS_TRIM_MODE(CLKOS_TRIM_MODE),
                        .CLKOP_TRIM(CLKOP_TRIM),
                        .CLKOS_TRIM(CLKOS_TRIM)) lscc_mipi_wrapper_tx (/// Configuration
                        /// CIL Timing
                        /// Alternative
                        /// Not used
                        /// PLL
                        // Soft TX PLL Parameters
                        /// Clock and Reset
                        .sync_clk_i(sync_clk_i), 
                            .sync_rst_i(sync_rst_i), 
                            /// LMMI
                        .lmmi_clk_i(lmmi_clk_i), 
                            .lmmi_resetn_i(lmmi_resetn_i), 
                            .lmmi_wdata_i(lmmi_wdata_i), 
                            .lmmi_wr_rdn_i(lmmi_wr_rdn_i), 
                            .lmmi_offset_i(lmmi_offset_i), 
                            .lmmi_request_i(lmmi_request_i), 
                            .lmmi_ready_o(lmmi_ready_o), 
                            .lmmi_rdata_o(lmmi_rdata_o), 
                            .lmmi_rdata_valid_o(lmmi_rdata_valid_o), 
                            // PLL outside of the Soft IP
                        .pll_clkop_i(pll_clkop_i), 
                            .pll_clkos_i(pll_clkos_i), 
                            .pll_lock_i(pll_lock_i), 
                            /// HS_TX
                        .hs_tx_en_i(hs_tx_en_i), 
                            .hs_tx_clk_en_i(hs_tx_clk_en_i), 
                            .hs_tx_data_i(hs_tx_data_i), 
                            .hs_tx_data_en_i(hs_tx_data_en_i), 
                            /// LP_TX
                        .lp_tx_en_i(lp_tx_en_i), 
                            .lp_tx_data_p_i(lp_tx_data_p_i), 
                            .lp_tx_data_n_i(lp_tx_data_n_i), 
                            .lp_tx_data_en_i(lp_tx_data_en_i), 
                            .lp_tx_clk_p_i(lp_tx_clk_p_i), 
                            .lp_tx_clk_n_i(lp_tx_clk_n_i), 
                            /// LP_RX
                        .lp_rx_en_i(lp_rx_en_i), 
                            .lp_rx_data_p_o(lp_rx_data_p_o), 
                            .lp_rx_data_n_o(lp_rx_data_n_o), 
                            /// Other
                        .usrstdby_i(usrstdby_i), 
                            .pd_dphy_i(pd_dphy_i), 
                            .txclk_hsgate_i(1'd0), 
                            .pll_lock_o(pll_lock_o), 
                            .clk_byte_o(clk_byte_o), 
                            .ready_o(ready_o), 
                            /// CIL
                        .hs_tx_cil_ready_o(hs_tx_cil_ready_o), 
                            .data_lane_ss_o(data_lane_ss_o), 
                            /// DPHY
                        .clk_p_io(clk_p_io), 
                            .clk_n_io(clk_n_io), 
                            .data_p_io(data_p_io), 
                            .data_n_io(data_n_io)) ; 
                if (((DPHY_IP == "LATTICE") && (PLL_MODE == "INTERNAL"))) 
                    begin : PLL_DEFPARAM
                    end
                assign hs_rx_data_o = {DATA_WIDTH{1'd1}} ; //Added to meet requirement from "Synplify Pro"
            end
        else
            if ((INT_TYPE == "RX")) 
                begin : RECEIVER
                    csi_dphy_ipgen_lscc_mipi_wrapper_rx #(.NUM_LANE(NUM_LANE),
                            .GEAR(GEAR),
                            .INTF(INTF),
                            .HSEL(HSEL),
                            .CLK_MODE(CLK_MODE),
                            .CIL_BYPASS(CIL_BYPASS),
                            .INT_DATA_RATE(INT_DATA_RATE),
                            .REF_CLOCK_FREQ(SYNC_CLOCK_FREQ),
                            .CM(CM),
                            .CN(CN),
                            .CO(CO),
                            .DPHY_IP(DPHY_IP),
                            .T_DATA_SETTLE(T_DATA_SETTLE),
                            .T_CLK_SETTLE(T_CLK_SETTLE)) lscc_mipi_wrapper_rx (.sync_clk_i(sync_clk_i), 
                                .sync_rst_i(sync_rst_i), 
                                .lmmi_clk_i(lmmi_clk_i), 
                                .lmmi_resetn_i(lmmi_resetn_i), 
                                .lmmi_wdata_i(lmmi_wdata_i), 
                                .lmmi_wr_rdn_i(lmmi_wr_rdn_i), 
                                .lmmi_offset_i(lmmi_offset_i), 
                                .lmmi_request_i(lmmi_request_i), 
                                .lmmi_ready_o(lmmi_ready_o), 
                                .lmmi_rdata_o(lmmi_rdata_o), 
                                .lmmi_rdata_valid_o(lmmi_rdata_valid_o), 
                                .pll_lock_i(pll_lock_i), 
                                .hs_rx_en_i(hs_rx_en_i), 
                                .hs_rx_data_o(hs_rx_data_o), 
                                .hs_rx_data_en_o(hs_rx_data_en_o), 
                                .lp_rx_en_i(lp_rx_en_i), 
                                .lp_rx_data_p_o(lp_rx_data_p_o), 
                                .lp_rx_data_n_o(lp_rx_data_n_o), 
                                .lp_rx_clk_p_o(lp_rx_clk_p_o), 
                                .lp_rx_clk_n_o(lp_rx_clk_n_o), 
                                .lp_tx_en_i(lp_tx_en_i), 
                                .lp_tx_data_p_i(lp_tx_data_p_i), 
                                .lp_tx_data_n_i(lp_tx_data_n_i), 
                                .lp_tx_data_en_i(lp_tx_data_en_i), 
                                .lp_tx_clk_p_i(lp_tx_clk_p_i), 
                                .lp_tx_clk_n_i(lp_tx_clk_n_i), 
                                .ready_o(ready_o), 
                                .pd_dphy_i(pd_dphy_i), 
                                .usrstdby_i(usrstdby_i), 
                                .clk_byte_o(clk_byte_o), 
                                .clk_p_io(clk_p_io), 
                                .clk_n_io(clk_n_io), 
                                .data_p_io(data_p_io), 
                                .data_n_io(data_n_io)) ; 
                    assign pll_lock_o = 1'd1 ; //Added to meet requirement from "Synplify Pro"
                    assign hs_tx_cil_ready_o = {NUM_LANE{1'd1}} ; 
                    assign data_lane_ss_o = {NUM_LANE{1'd1}} ; 
                end
    endgenerate
endmodule


