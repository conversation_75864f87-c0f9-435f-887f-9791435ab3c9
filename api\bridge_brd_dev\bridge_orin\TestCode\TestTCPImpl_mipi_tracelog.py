#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import HWImpl
import time
import datetime

if __name__ == '__main__':
    # 使用第一个设备
    HWImpl.hw_open(ip_addr="*************")

    ret = HWImpl.hw_waithw_ok(100)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")

    # 创建cvs文件(local)
    result_csv_file_name = "mipi_trace_" + datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S') + ".csv"
    result_csv = open(result_csv_file_name, 'w', encoding='utf-8')
    name = ['time', "idx", "result"]
    result_csv.write(','.join(name))
    result_csv.write('\n')

    for idx in range(200):
        ret = HWImpl.hw_show_form_index(1)  #GUI table#, 5=CV
        if ret == 0:
            print("hw_show_form_index SysLog ok")
        else:
            print("hw_show_form_index timeout")
        time.sleep(1)

        ret = HWImpl.hw_clear_tracelog_status()  #clear log
        print("operation=",ret," hw_clear_tracelog_status")
             
        ret = HWImpl.hw_start_tracelog()    #start log
        print("operation=",ret," hw_start_mipilog")


        
        ret = HWImpl.hw_set_video_cam(1920, 1551, 3, 0)
        print("operation=",ret," hw_set_video_cam")
        ret = HWImpl.hw_open_video(0)
        print("operation=",ret," hw_open_video")
    
        time.sleep(4)
        
        ret = HWImpl.hw_save_one_videoframe(0)      #save image
        print("operation=",ret," hw_save_one_videoframe")
        
        time.sleep(2)   #
        
        ret = HWImpl.hw_close_video(0)
        print("operation=",ret," hw_close_video")
    
        ret = HWImpl.hw_stop_tracelog()     #stop log
        print("operation=",ret," hw_stop_mipilog")
        ret,tracelog_status = HWImpl.hw_get_tracelog_status()   #1=error during start to stop hit(all vedio)
        print("operation=",ret," hw_get_tracelog_status")
        
        ret = HWImpl.hw_show_form_index(6)  
        if ret == 0:
            print("hw_show_form_index OrinTraceLog ok")
        else:
            print("hw_show_form_index timeout")
        time.sleep(1)

        # 将结果写入cvs文件
        row = [datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'), idx, tracelog_status]
        result_csv.write(','.join(str(r) for r in row))
        result_csv.write('\n')
        result_csv.flush()

    # 关闭设备
    HWImpl.hw_close()
