import time
from ftd2xx import QRegisterAccess
from future.backports.test.pystone import FALSE

if QRegisterAccess.findMeritechI2cChan():

    # memory map:
    #     0x10:0x00~0xff, ana_tx
    #     0x11:0x00~0xff, ana_pll
    #     0x12:0x00~0xff, dig_clk_rst
    #     0x13:0x00~0xff, dig_core
    #     0x14:0x00~0xff, dig_irq
    #     0x15:0x00~0xff, dig_pinmux
    ANA_TX     = 0x10
    ANA_PLL    = 0x11
    DIG_CLK    = 0x12
    DIG_CORE   = 0x13
    DIG_IRQ    = 0x14
    DIG_PINMUX = 0x15
    TESTBENCH  = 0x60
    PLL_config = 1
    if PLL_config:
    # VCO output
        print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x04))
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x04, 0x53)  # 
        print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x04))
        
        print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x05, 0x40)
        print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
        
        print '0x07 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x07))
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x07, 0x14)
        print '0x07 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x07))
        
        print '0x06 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x06))
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x06, 0xd6) # default 0x16, [7:6] post div set, 00 6G, 01 3G, 10 1.5G, 11 0.75G
        print '0x06 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x06))
        
        # print '0x16 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x16))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x16, 0x01)
        # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x16, 0x03)
        # print '0x16 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x16))
        
        print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x00))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x00, 0x01)
        #sign_pre
        #sign_main
        #sign_post
        #driver_pmos_off_txn
        #driver_pmos_off_txp
        #en_tx- 0: tx_off, 1: tx_on
        #[0] pck_pol_sel
        print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x00))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x38, 0x0F)
        
        
    prbs_check_debug = 1
    if prbs_check_debug:
        # prbs checker
        # print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xbc,0x03) #reg1_ana_open, prbs_chk_node_sel, 0:before fif 1:after fifo 2/3:back data
        # print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        #
        # print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb8,0x00) #reg1_ana_open,  0:7, 1:11,  2:15,  3:23, 4:31
        # print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        #
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01) #reg1_ana_open
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        #
        #
        # # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        # #
        # # print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        # # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        # # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        # #
        # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        #
        # print '0xc0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
        # time.sleep(1)
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x00)  # prbs_chk_en
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01)  # reg1_ana_open, prbs_err_clr
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x00)  # reg1_ana_open, prbs_err_clr
        #
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        #
        # print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        # time.sleep(2)
        #
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        #
        # print 'wait 100s!'
        # print 'prbs_err_cnt_15_8(0xc4) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # print 'prbs_err_cnt_7_0(0xc8) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        #
        # print 'prbs_err_cnt_vld(0xc0) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
        # print 'done'
    
    enable_reverse_ch = 1
    if enable_reverse_ch:
    #Rx prbs check
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x68,0x80) #reg9_ana_open
        print '0x68 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x68))
        #[7] ck5i_digital_reverse
        #[6:0] reserved
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x64,0xe3) #reg8_ana_open
        print '0x64 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x64))
        #[7] ck10ui cdr clk phase reverse
        #[6] ck10ui digital clk phase reverse
        #[5] en bcrx, enable back channel
        #[4] en hratio, not come true
        #[3:2] brcx_lpfc_cvar_ctr
        #[1:0] kp, cdr kp tune
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x60,0xF4) #reg7_ana_open
        print '0x60 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x60))
        #[7:6] pi output slew rate ctrl
        #[5:4] pi input slew rate ctrl
        #[3:0] pi code step
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x5C,0x60) #reg6_ana_open
        print '0x5C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x5C))
        #[7] reset cdr
        #[6] en kp
        #[5] en kf
        #[4:0] kcycle reset cdr
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x58,11) #reg5_ana_open
        print '0x58 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x0))
        #[7:0] rx_lpfc_dac_ctrl, afe idac tune control
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x54,0) #reg4_ana_open  
        print '0x54 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x54))
        #hratio, not come true
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x50,0xf6) #reg3_ana_open
        print '0x50 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x50))
        #[7:5] kf, cdr kf settings
        #[4:0] kmr, cdr kmr settings
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x4C,0) #reg2_ana_open, test chip not come true
        print '0x4C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x4C))
        #[7] enable cdr pi code offset
        #[6:0] cdr pi code offset
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x48,0) #reg1_ana_open
        print '0x48 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x48))
        #[7] enable cdr pi code b override
        #[6:0] cdr pi code b settings
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x44,0x80) #reg1_ana_open
        print '0x44 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x44))
        #[7] enable cdr
        #[6:0] cdr pi code a settings
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x16, 0x33)  # pll_ctr_reserved(back channel input clk, 1:600MHz, 300Mbps, 0: 300MHz, 150Mbps.  Internal div by 2, full rate mode)
            
        #
        # print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xbc,0x03) #reg1_ana_open, prbs_chk_node_sel, 0:before fif 1:after fifo 2/3:back data
        # print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        #
        # print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb8,0x00) #reg1_ana_open, 0:7, 1:11,  2:15,  3:23, 4:31
        # print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        #
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01) #reg1_ana_open
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x00)  # prbs_chk_en
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01)  # reg1_ana_open, prbs_err_clr
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x00)  # reg1_ana_open, prbs_err_clr
        
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        #
        # print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        # time.sleep(2)
        #
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        #
        # print 'wait 100s!'
        # print 'prbs_err_cnt_15_8(0xc4) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # print 'prbs_err_cnt_7_0(0xc8) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        #
        # print 'prbs_err_cnt_vld(0xc0) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
        # print 'done'
    
    
    tx_output = 0
    if tx_output:
    
    #Tx output
    # print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x04))
    # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x04, 0x53)  # 
    # print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x04))
    #
    # print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
    # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x05, 0x40)
    # print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
        
        print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x00))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x00, 0x01)
        print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x00))
        
        print '0x38 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x38))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x38, 0x0f)
        print '0x38 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x38))
        
        print '0x34 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x34))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x34, 0x15)
        print '0x34 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x34))
        
        print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x00))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x00, 0x03)
        print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x00))
        
        print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x04))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x04, 0x00)
        print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x04))
        
        print '0x08 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x08))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x08, 0x00)
        print '0x08 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x08))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x0C, 63)
        tx_swing=QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x0C)
        print '0x0C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x0C))
        
        
        
        # print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x12, 0x00))
        # # QRegisterAccess.i2cWriteC3TxTestReg(0x12, 0x00, 0x10)
        # QRegisterAccess.i2cWriteC3TxTestReg(0x12, 0x00, 0x00)
        # print '0x00 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x12, 0x00))
        
        print '0x68 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x68))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x68, 0x00)
        print '0x68 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x68))
        
        print '0x6C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x6C))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x6C, 0x00)
        print '0x6C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x6C))
        
        print '0x70 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x70))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x70, 0x01)
        print '0x70 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x70))
    
    
    prbs_check = 0
    if prbs_check:
        # prbs checker
        print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xbc,0x03) #reg1_ana_open, prbs_chk_node_sel, 0:before fif 1:after fifo 2/3:back data
        print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        
        print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb8,0x00) #reg1_ana_open,  0:7, 1:11,  2:15,  3:23, 4:31
        print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01) #reg1_ana_open
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        
        
        print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        
        print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print '0xc0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
    
    user_pattern = 0
    if user_pattern:
        #customer generator
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x70, 0x00)
        print '0x70 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x70))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x78, 0xFF)
        print '0x78 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x78))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x7C, 0x00)
        print '0x7C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x7C))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x80, 0xFF)
        print '0x80 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x80))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x84, 0x00)
        print '0x84 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x84))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x88, 0xFF)
        print '0x88 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x88))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x8C, 0x00)
        print '0x8C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x8C))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x90, 0xFF)
        print '0x90 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x90))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x94, 0x00)
        print '0x94 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x94))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x98, 0xFF)
        print '0x98 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x98))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x9C, 0x00)
        print '0x9C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x9C))
        
        QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x74, 0xFF)
        print '0x70 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x70))       
    
    user_pattern1 = 1
    if user_pattern1:
        #customer generator
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x70, 0x00)
        # print '0x70 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x70))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x78, 0x55)
        # print '0x78 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x78))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x7C, 0x55)
        # print '0x7C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x7C))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x80, 0x55)
        # print '0x80 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x80))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x84, 0x55)
        # print '0x84 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x84))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x88, 0x55)
        # print '0x88 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x88))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x8C, 0x55)
        # print '0x8C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x8C))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x90, 0x55)
        # print '0x90 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x90))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x94, 0x55)
        # print '0x94 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x94))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x98, 0x55)
        # print '0x98 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x98))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x9C, 0x55)
        # print '0x9C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x9C))
        #
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13, 0x74, 0x01)
        # print '0x70 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0x70))
        
        
        # PLL test mux
        # print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x04))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x04, 0x53)
        # print '0x04 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x04))
        #
        # print '0x15 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x15, 0x00))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x15, 0x00, 0x01)
        # print '0x15 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x15, 0x00))
        #
        # print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x05, 0x20)
        # print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
        #
        # print '0x07 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x07))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x07, 0x24)
        # print '0x07 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x07))
        #
        # print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x05, 0x60)
        # print '0x05 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x05))
        #

        print '0x16 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x16))
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x16, 0x31)
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x16, 0x33)
        print '0x16 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x16))
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x5C,0x80) #reg6_ana_open, #[7] reset cdr
        QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x5C,0) #reg6_ana_open
        
        # QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x5C,1) #reg6_ana_open
        # print '0x5C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x5C))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x5C,0) #reg6_ana_open
        # print '0x5C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x5C))
        #[7] reset cdr
        #[6] en kp
        #[5] en kf
        #[4:0] kcycle reset cdr
        #[7] reset cdr
        #[6] en kp
        #[5] en kf
        #[4:0] kcycle reset cdr
        # print '0x38 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x38))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x38, 0x0F)
        # print '0x38 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x38))
        #
        # print '0x3C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x3C))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x3C, 0x01)
        # print '0x3C is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x3C))
        #
        # print '0x40 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x40))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x10, 0x40, 0x00)
        # print '0x40 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x10, 0x40))
    prbs_check_debug = 1
    if prbs_check_debug:
        # prbs checker
        print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xbc,0x03) #reg1_ana_open, prbs_chk_node_sel, 0:before fif 1:after fifo 2/3:back data
        print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        
        print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb8,0x00) #reg1_ana_open,  0:7, 1:11,  2:15,  3:23, 4:31
        print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01) #reg1_ana_open
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        
        
        # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        #
        # print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        #
        print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print '0xc0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
        time.sleep(1)
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x00)  # prbs_chk_en
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01)  # reg1_ana_open, prbs_err_clr
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x00)  # reg1_ana_open, prbs_err_clr
        
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        for ti in range(0, 1001, 1500):
            time.sleep(0)
            print 'PRBS error after checking '+str(ti)+'s:'
            print 'prbs_err_cnt_15_8(0xc4) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
            print 'prbs_err_cnt_7_0(0xc8) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
            
            print 'prbs_err_cnt_vld(0xc0) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
            print ''       
        
        #QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x00)  # prbs_chk_en
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01)  # reg1_ana_open, prbs_err_clr
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x00)  # reg1_ana_open, prbs_err_clr
        
        print 'prbs_err_cnt_15_8(0xc4) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print 'prbs_err_cnt_7_0(0xc8) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print 'prbs_err_cnt_vld(0xc0) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
        print ''       
        print 'done'
        
    #Horizontal 
    hrorizontal=0
    if hrorizontal:   
        for phase in [127,1,15,16,31,32,47,48,63,64,79,80,96,97,111,112,126,127]:
            # QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x48,0xff)   
            # QRegisterAccess.i2cWriteC3TxTestReg(0x10,0x44,0xff)                                               
            print 'phase is:',  phase                                                   
            # QRegisterAccess.M66S68_Write_Bitfield(devi_addr=0x10,regi_addr=0x48, bitnum=1, startbit=7, value=1) #Enable CDR PI code override
            # print QRegisterAccess.M66S68_Read_Bitfield(devi_addr=0x10,regi_addr=0x48, bitnum=1, startbit=7)
            
            QRegisterAccess.M66S68_Write_Bitfield(devi_addr=0x10,regi_addr=0x44, bitnum=7, startbit=0, value=phase) #CDR pi code a for override mode
            phasea = QRegisterAccess.M66S68_Read_Bitfield(devi_addr=0x10,regi_addr=0x44, bitnum=7, startbit=0)
            
            QRegisterAccess.M66S68_Write_Bitfield(devi_addr=0x10,regi_addr=0x48, bitnum=7, startbit=0, value=phase) #CDR pi code b for override mode
            phaseb = QRegisterAccess.M66S68_Read_Bitfield(devi_addr=0x10,regi_addr=0x48, bitnum=7, startbit=0)
            
            QRegisterAccess.M66S68_Write_Bitfield(devi_addr=0x10,regi_addr=0x48, bitnum=1, startbit=7, value=1) #Enable CDR PI code override
            print QRegisterAccess.M66S68_Read_Bitfield(devi_addr=0x10,regi_addr=0x48, bitnum=1, startbit=7)
            
            prbs_check_debug = 1
            if prbs_check_debug:
                # prbs checker
                # print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xbc,0x03) #reg1_ana_open, prbs_chk_node_sel, 0:before fif 1:after fifo 2/3:back data
                # # print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
                #
                # # print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb8,0x00) #reg1_ana_open,  0:7, 1:11,  2:15,  3:23, 4:31
                # # print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
                #
                # # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01) #reg1_ana_open
                # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
                
                
                # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
                # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
                #
                # print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
                # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
                #
                # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
                # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
                #
                # print '0xc0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
                # time.sleep(0.1)
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x00)  # prbs_chk_en
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01)  # reg1_ana_open, prbs_err_clr
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x00)  # reg1_ana_open, prbs_err_clr
                #
                # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
                # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
                # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
                #
                # print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
                # time.sleep(0.1)
                
                print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
                
                print 'wait 100s!'
                print 'prbs_err_cnt_15_8(0xc4) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
                print 'prbs_err_cnt_7_0(0xc8) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
                
                print 'prbs_err_cnt_vld(0xc0) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
            
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x00)  # prbs_chk_en
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01)  # reg1_ana_open, prbs_err_clr
                # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x00)  # reg1_ana_open, prbs_err_clr
        
        
        
        
    prbs_check_debug = 0
    if prbs_check_debug:
        # prbs checker
        print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xbc,0x03) #reg1_ana_open, prbs_chk_node_sel, 0:before fif 1:after fifo 2/3:back data
        print '0x13 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xbc))
        
        print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb8,0x00) #reg1_ana_open,  0:7, 1:11,  2:15,  3:23, 4:31
        print '0xb8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb8))
        
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01) #reg1_ana_open
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        
        
        # print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        # print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        #
        # print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        # QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        # print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        #
        print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print '0xc0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
        time.sleep(1)
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x00)  # prbs_chk_en
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x01)  # reg1_ana_open, prbs_err_clr
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb0,0x00)  # reg1_ana_open, prbs_err_clr
        
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb0))
        print '0xc4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print '0xc8 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print '0xb4 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        QRegisterAccess.i2cWriteC3TxTestReg(0x13,0xb4,0x01) #reg1_ana_open
        time.sleep(2)
        
        print '0xb0 is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xb4))
        
        print 'wait 100s!'
        print 'prbs_err_cnt_15_8(0xc4) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc4))
        print 'prbs_err_cnt_7_0(0xc8) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc8))
        
        print 'prbs_err_cnt_vld(0xc0) is: ', hex(QRegisterAccess.i2cReadC3TxTestReg(0x13, 0xc0))
        print 'done'
        
    iic_verification = False
    if iic_verification:
        # IIC verification
        print 'IIC reading check: '
        reg_ana_0x02 =  QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x02) # expect 0xC2
        print 'read back value: reg_0x02: ', hex(reg_ana_0x02), ', expect value: 0xC2 '
        if reg_ana_0x02 != 0xC2:
            print 'IIC output incorrect!!'
        else:
            print 'PASS'
        
        
        for i in range(1):
            print ''
        print 'IIC read writing check: '
        QRegisterAccess.i2cWriteC3TxTestReg(0x11, 0x02, 0x2C)
        reg_ana_0x02 =  QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x02) # expect 0xC2
        print 'read back value: reg_0x02: ', hex(reg_ana_0x02), ', expect value: 0x2C '
        if reg_ana_0x02 != 0x2C:
            print 'FAIL: IIC output incorrect!!'
        else:
            print 'PASS'
            
            
    fpga_verification = False
    if fpga_verification:
        # reset tb and dut
        # QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x0) # tb_resetn
        # QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x1) # tb_resetn
        print '0x02 is:', QRegisterAccess.i2cReadC3TxTestReg(0x11, 0x02)
        # test register r/w
        print('')
        print('testing reg r/w ...')
        print('    ana_tx, read  cfg_1(0x04): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(ANA_TX, 4)))
        print('    ana_tx, write cfg_1(0x04): 0x%02x'%(0x55))
        QRegisterAccess.i2cWriteC3TxTestReg(ANA_TX, 4, 0x55)
        print('    ana_tx, read  cfg_1(0x04): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(ANA_TX, 4)))
        print('    ana_tx, write cfg_1(0x04): 0x%02x'%(0xaa))
        QRegisterAccess.i2cWriteC3TxTestReg(ANA_TX, 4, 0xaa)
        print('    ana_tx, read  cfg_1(0x04): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(ANA_TX, 4)))
        print('')
        print('    testbench, read  id(0x00): 0x%02x'%(QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x00)))
        print('')
    
        prbs_modes = [(0, 'prbs7'), (1, 'prbs11'), (2, 'prbs15'), (3, 'prbs23'), (4, 'prbs31')]
    
        for loop in range(1):
            
            for prbs_mode in prbs_modes:
    
                mode = prbs_mode[0]
                print('testing %s'%(prbs_mode[1]))
    
                # diable tb to insert error
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x07, 0x0) # ins_err_en
    
                # reset tb and dut
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x0) # tb_resetn
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x01, 0x1) # tb_resetn
    
                # set tb test mode
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x03, mode) # prbs mode
    
                # config dut prbs generator
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0x70, 0x0)  # prbs_gen_en
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0x6c, mode) # prbs_gen_mode, 0-7,1-11,2-15,3-23,4-31
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0x70, 0x1)  # prbs_gen_en
    
                # config dut prbs checker
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb4, 0x0)  # prbs_chk_en
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb0, 0x1)  # prbs_err_clr
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb8, mode) # prbs_chk_mode, 0-7,1-11,2-15,3-23,4-31
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xbc, 0x2)  # prbs_chk_node_sel, 0-before fifo, 1-after fifo, 2-phy input
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xf0, 0x1)  # pack
                QRegisterAccess.i2cWriteC3TxTestReg(DIG_CORE, 0xb4, 0x1)  # prbs_chk_en
    
                # delay
                time.sleep(0.1)
    
                # read dut prbs checker result w/o inserting error
                dut_prbs_err_cnt_vld = QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc0)
                dut_prbs_err_cnt     = (QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc4) << 8) + QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc8)
                if dut_prbs_err_cnt > 0:
                    print('    dig_core, prbs checker valid: %x, error cnt = 0x%x (should be 0)'%(dut_prbs_err_cnt_vld, dut_prbs_err_cnt))
    
                # enable tb to insert error
                err_gap = 10
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x0a, err_gap & 0xff) # ins_err_gap
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x0b, (err_gap >> 8) & 0xff) # ins_err_gap
                err_num = 0x1234
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x08, err_num & 0xff) # ins_err_num
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x09, (err_num >> 8) & 0xff) # ins_err_num
                QRegisterAccess.i2cWriteC3TxTestReg(TESTBENCH, 0x07, 0x1) # ins_err_en
    
                # delay
                time.sleep(0.1)
    
                # read dut prbs checker result
                dut_prbs_err_cnt_vld = QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc0)
                dut_prbs_err_cnt     = (QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc4) << 8) + QRegisterAccess.i2cReadC3TxTestReg(DIG_CORE, 0xc8)
                if (dut_prbs_err_cnt != err_num):
                    print('    dig_core, prbs checker valid: %x, error cnt = 0x%x (should be 0x%x)'%(dut_prbs_err_cnt_vld, dut_prbs_err_cnt, err_num))
                
                # read testbench prbs checker result
                tb_prbs_match    = QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x02)
                tb_prbs_err_stat = (QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x06) << 16) + (QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x05) << 8) + QRegisterAccess.i2cReadC3TxTestReg(TESTBENCH, 0x04)
                if tb_prbs_err_stat > 0:
                    print('    testbench, prbs checker match: %x, error status = 0x%x (should be 0)'%(tb_prbs_match, tb_prbs_err_stat))
