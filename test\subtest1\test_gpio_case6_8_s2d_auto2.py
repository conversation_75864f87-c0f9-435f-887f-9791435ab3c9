# -*- coding: utf-8 -*-
"""
GPIO Case-6: Signal Transmission Path from S68 to D68 - D68 Chip Version

基于test_gpio_case6_8_s2q_auto2.py模板，适配D68芯片测试。
信号方向：S68 GPIO → D68 Q68 GPIO
自动遍历测试：遍历所有S68 GPIO（0-8），每个S68 GPIO作为信号源同时测试所有Q68 GPIO（0-14）作为接收端

主要修改：
1. 导入模块改为M65D68_Common_Fuction_A0
2. MFN设置函数改为MFNSet_D68
3. 添加D68特定初始化代码
4. 仅使用Link0和Link2进行测试
5. 使用独立设备初始化，不依赖conftest.py
"""
import logging
import time
import pytest
import os
import sys
import tkinter as tk
from tkinter import messagebox
from conftest import get_universal_oscilloscope_screenshot

# 禁用pyvisa的DEBUG日志，减少冗余输出
logging.getLogger('pyvisa').setLevel(logging.WARNING)

# 添加D68模块路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65d68_a0'))  # D68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'))  # S68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # 示波器模块路径

# D68芯片专用导入
try:
    from Common_d.M65D68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    print("✅ D68模块导入成功")
except ImportError as e:
    print(f"⚠️ D68模块导入失败: {e}")
    print("🔄 尝试使用Q68模块作为兼容...")
    try:
        from Common.M65Q68_Common_Fuction_A0 import *
        from Common_var.M66S68_Common_Fuction_A0 import *
        print("✅ 使用Q68模块作为D68兼容")
    except ImportError as e2:
        print(f"❌ 所有模块导入失败: {e2}")
        # 定义空类以避免错误
        class M65Q68_A0: pass
        class M65Q68_A0_Remote_M66S68: pass

# D68设备初始化配置
D68_DEVICE_CONFIG = {
    'q68_iic_addr': 0x73,
    's68_iic_dev': [0x40, 0x40, 0x40, 0x40],
    's68_res_dev': [0x20, 0x21, 0x22, 0x23],
    's68_res_sensor_dev': [0x24, 0x25, 0x26, 0x27],
    'active_links': [0, 2],  # D68仅支持Link0和Link2
    'RATE':         [2, 2, 2, 2],
    'RATE_final':   [2, 2, 2, 2],
    'BCRATE':       [0, 0, 0, 0]  
}

# 测试配置 - D68版本
TEST_CONFIG = {
    'active_links': [0, 2],                 # D68版本仅使用Link0和Link2
    'signal_id': 11,                        # GPIO信号ID
    'observation_time': 1,                  # 观察时间(秒)
    's68_source_gpio': 8,                   # S68源GPIO (用于截图文件命名，每次测试都设置同一个)

    # 示波器自动截图配置
    'oscilloscope_config': {
        'enable_screenshot': True,          # 是否启用自动截图
        'test_mode': 'combined',            # 测试模式: 'fixed'=仅固定频率, 'sweep'=仅扫频, 'combined'=组合测试
        'trigger_source': 'D1',            # 触发源: C1-C4(模拟), D0-D15(数字), EX, EX5, LINE
        'waveform_type': 'SQUARE',          # 方波
        # 固定频率模式配置
        'frequency_list': [30, 1000, 10000, 50000, 100000],  # 固定频率列表 (Hz)
        'timebase_list': ['20ms', '1ms', '100us', '10us', '5us'],   # 对应的时基 - 优化100kHz时基
        # 扫频模式配置
        'frequency_range': {
            'start': 1210000,                    # 1210kHz
            'end': 1240000,                      # 1240kHz
            'step': 10000                       # 10kHz步进
        },
        'sweep_timebase': '500ns',              # 扫频模式的时基
        'amplitude': 1.8,                      # 1.8Vpp
        'offset': 0.9,                          # 900mVdc偏移
        'screenshot_folder_base': 'U-disk0/gpiotest',  # D68版本截图保存基础文件夹
        'persistence_mode': 'INFinite',         # 余晖模式 (该机型唯一选项)
        'probe_wait_time': 1,                  # 第一次截图前等待插探头时间(秒)
        'freq_observation_time': 1.2,             # 每个频率的观察时间(秒)
    },

    # 动态生成configurations，使用统一的S68 GPIO变量
    'configurations': None  # 将在运行时动态生成
}


class D68_DeviceManager:
    """D68设备管理器 - 独立的D68设备初始化和管理"""
    
    def __init__(self):
        self.q68 = None
        self.q68_remote = None
        self.oscilloscope = None
        self.devices = {}
        
    def initialize_d68_devices(self):
        """初始化D68设备"""
        try:
            print("🔧 初始化D68设备...")
            
            # 初始化D68 Q68设备
            print("  📡 初始化D68 Q68设备...")
            self.q68 = M65Q68_A0(dongle='stm32', id=0, bus='i2c')
            print("    ✅ D68 Q68设备初始化完成")
            
            # 初始化D68 Q68远程设备
            print("  📡 初始化D68 Q68远程设备...")
            self.q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')
            print("    ✅ D68 Q68远程设备初始化完成")
            
            # D68特定初始化
            print("  🔧 执行D68特定初始化...")
            self.q68.c2m.wr_test_glb_ctrl0_fields(key=0x5c)  # Test register write access key
            self.q68.c2m.wr_test_tx_link_data_inv_fields(tx_polar_sel=0x6)    # R5/R7 polarity
            self.q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel=0x6)
            print("    ✅ D68特定初始化完成")
            
            # 初始化示波器（可选）
            try:
                print("  📊 尝试初始化示波器...")
                from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
                self.oscilloscope = SiglentSDS5034X()
                print("    ✅ 示波器初始化完成")
            except Exception as e:
                print(f"    ⚠️ 示波器初始化失败: {e}")
                self.oscilloscope = None
            
            # 设置链路参数
            print("  🔗 配置D68链路参数...")
            config = D68_DEVICE_CONFIG
            
            # 设置链路速率
            self.q68.c2m.wr_sys_cfg_link_ctrl1_fields(
                rate0=config['RATE'][0], 
                rate1=config['RATE'][1], 
                rate2=config['RATE'][2], 
                rate3=config['RATE'][3]
            )
            self.q68.c2m.wr_sys_cfg_link_ctrl3_fields(
                bc_rate0=config['BCRATE'][0], 
                bc_rate1=config['BCRATE'][1], 
                bc_rate2=config['BCRATE'][2], 
                bc_rate3=config['BCRATE'][3]
            )
            
            # 初始化链路
            self.q68.Q68_C3_6G_Init(
                rate0=config['RATE'][0], 
                rate1=config['RATE'][1], 
                rate2=config['RATE'][2], 
                rate3=config['RATE'][3]
            )
            
            # 设置最终链路速率
            self.q68.c2m.wr_sys_cfg_link_ctrl1_fields(
                rate0=config['RATE_final'][0], 
                rate1=config['RATE_final'][1], 
                rate2=config['RATE_final'][2], 
                rate3=config['RATE_final'][3]
            )
            self.q68.Q68_C3_6G_Init(
                rate0=config['RATE_final'][0], 
                rate1=config['RATE_final'][1], 
                rate2=config['RATE_final'][2], 
                rate3=config['RATE_final'][3]
            )
            print("    ✅ D68链路参数配置完成")
            
            # 创建设备字典
            self.devices = {
                'q68': self.q68,
                'q68_remote': self.q68_remote,
                's68_res_dev': config['s68_res_dev'],
                'configure_links': self.configure_links_d68,
                'oscilloscope': self.oscilloscope
            }
            
            print("✅ D68设备初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ D68设备初始化失败: {e}")
            return False
    
    def configure_links_d68(self, active_links):
        """D68链路配置函数"""
        try:
            print(f"🔗 配置D68活跃链路: {active_links}")
            # 这里可以添加具体的链路配置逻辑
            return True
        except Exception as e:
            print(f"❌ D68链路配置失败: {e}")
            return False
    
    def get_devices(self):
        """获取设备字典"""
        return self.devices
    
    def cleanup(self):
        """清理设备资源"""
        try:
            print("🧹 清理D68设备资源...")
            # 这里可以添加设备清理代码
            print("✅ D68设备资源清理完成")
        except Exception as e:
            print(f"⚠️ D68设备资源清理失败: {e}")


class GPIO_S68_D68_AutoTester:
    """S68到D68的GPIO自动测试器"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def show_confirmation_dialog(self, s68_gpio, current_test, total_tests, current_link=None):
        """显示确认对话框，显示当前Link信息"""
        try:
            import platform

            # 创建根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口

            # 设置窗口标题和图标
            root.title("D68 GPIO测试确认")

            # 强制窗口显示在最前面的多种方法
            root.attributes('-topmost', True)  # 置顶显示

            # # Windows系统特有的设置
            # if platform.system() == "Windows":
            #     try:
            #         # 尝试使用Windows API强制置顶
            #         import ctypes

            #         # 获取窗口句柄
            #         root.update_idletasks()
            #         hwnd = int(root.wm_frame(), 16) if hasattr(root, 'wm_frame') else None

            #         if hwnd:
            #             # 使用Windows API设置窗口为前台
            #             ctypes.windll.user32.SetForegroundWindow(hwnd)
            #             ctypes.windll.user32.BringWindowToTop(hwnd)

            #     except Exception as win_e:
            #         print(f"⚠️ Windows API设置失败: {win_e}")

            # 通用设置方法
            root.attributes('-alpha', 0.0)     # 先设为透明
            root.deiconify()                   # 显示窗口
            root.lift()                        # 提升窗口层级
            root.focus_force()                 # 强制获取焦点
            root.attributes('-alpha', 1.0)     # 恢复不透明

            # 确保窗口更新和居中显示
            root.update_idletasks()
            root.update()

            # 获取屏幕尺寸并居中显示
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            x = (screen_width // 2) - 200
            y = (screen_height // 2) - 100
            root.geometry(f"+{x}+{y}")

            # 再次确保置顶
            root.attributes('-topmost', True)
            root.lift()
            root.focus_force()

            print(f"🔔 显示确认对话框: S68 GPIO{s68_gpio} (进度: {current_test}/{total_tests})")

            # 显示对话框 - 添加Link信息
            link_info = f"🔗 当前Link: Link{current_link}\n" if current_link is not None else ""

            dialog_result = messagebox.askyesnocancel(
                "D68 S68 GPIO测试确认",
                f"是否测试 S68 GPIO{s68_gpio} → D68 所有Q68 GPIO?\n\n"
                f"{link_info}"
                f"当前进度: {current_test}/{total_tests}\n"
                f"活跃Links: {TEST_CONFIG['active_links']}\n\n"
                f"点击:\n"
                f"• 是 - 执行测试\n"
                f"• 否 - 跳过此GPIO\n"
                f"• 取消 - 终止所有测试",
                parent=root  # 指定父窗口
            )

            # 清理窗口
            root.quit()
            root.destroy()

            return dialog_result

        except Exception as e:
            print(f"⚠️ 弹窗显示失败: {e}")
            # 如果弹窗失败，提供命令行确认
            print(f"🔔 命令行确认: 是否测试 S68 GPIO{s68_gpio}? (y/n/c): ", end="")
            try:
                user_input = input().strip().lower()
                if user_input in ['c', 'cancel']:
                    return None  # 取消
                elif user_input in ['n', 'no']:
                    return False  # 跳过
                else:
                    return True  # 执行测试
            except:
                return True  # 默认执行测试

    def setup_address_translation_for_links(self, active_links=None):
        """为指定Links设置地址转换"""
        if active_links is None:
            active_links = TEST_CONFIG['active_links']
            
        try:
            print(f"  📡 为Links {active_links} 设置地址转换...")
            
            for link in active_links:
                if link < len(self.s68_res_dev):
                    self.q68_remote.S68_AddrTrans(
                        link=link,
                        q68_iic_addr=0x73,                    # Q68地址
                        s68_iic_addr=0x40,                    # S68实际地址
                        s68_retrans_addr=self.s68_res_dev[link],   # S68转译地址 (0x20, 0x21, 0x22, 0x23)
                        sensor_addr=0x24,                     # sensor地址 (如果需要)
                        sensor_retrans_addr=0x24 + link,      # sensor转译地址
 
                    )
                    print(f"      ✅ Link{link} 地址转换: 0x{self.s68_res_dev[link]:02X} -> 0x40")
                else:
                    print(f"      ⚠️ Link{link} 超出范围，跳过")
            
            print(f"    ✅ Links地址转换设置完成")
            return True
            
        except Exception as e:
            print(f"    ❌ 地址转换设置失败: {e}")
            return False

    def test_single_s68_gpio_single_link_d68(self, s68_gpio, target_link):
        """测试单个S68 GPIO到所有Q68 GPIO - D68版本（单个Link）"""
        try:
            signal_id = TEST_CONFIG['signal_id']

            # 配置S68和Q68 GPIO
            if target_link < len(self.s68_res_dev):
                # S68 Side Configuration (Transmitter)
                target_s68_addr = self.s68_res_dev[target_link]
                self.q68_remote.dongle.devAddr = target_s68_addr

                self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                self.q68_remote.M2CGPIORemoteTx(gpio=s68_gpio, tx_id=signal_id)

                # Q68 Side Configuration (Receiver) - 配置所有Q68 GPIO 0-10
                for q68_gpio in range(11):  # GPIO 0-10
                    self.q68.MFNSet_D68(gpio=q68_gpio, mfn=0)
                    self.q68.GPIORemoteRx(gpio=q68_gpio, rx_id=signal_id)

            # 示波器测试
            test_mode = TEST_CONFIG['oscilloscope_config']['test_mode']
            self.oscilloscope_screenshot_universal(s68_gpio, [target_link], test_mode)

            return True

        except Exception as e:
            print(f"❌ Link{target_link}测试失败: {e}")
            return False

    # 删除冗余的复杂方法，使用简化版本


    def full_system_reinit_d68(self, s68_gpio):
        """完整系统重新初始化 - D68版本"""
        try:
            print(f"🔄 执行D68系统重新初始化 (S68 GPIO{s68_gpio})...")

            # 重新初始化设备管理器
            device_manager = D68_DeviceManager()
            if not device_manager.initialize_d68_devices():
                print(f"❌ D68设备重新初始化失败")
                return False

            # 更新设备引用
            new_devices = device_manager.get_devices()
            self.q68 = new_devices['q68']
            self.q68_remote = new_devices['q68_remote']
            self.s68_res_dev = new_devices['s68_res_dev']
            self.configure_links = new_devices['configure_links']
            self.oscilloscope = new_devices.get('oscilloscope')

            print(f"✅ D68系统重新初始化完成")
            return True

        except Exception as e:
            print(f"❌ D68系统重新初始化失败: {e}")
            return False


    # 原有的200+行复杂逻辑替换为10行
    def oscilloscope_screenshot_universal(self, s68_gpio, active_links=None, test_mode='combined'):
        screenshot_tool = get_universal_oscilloscope_screenshot(self.oscilloscope)
        return screenshot_tool.execute_screenshot(
            gpio_num=s68_gpio,
            active_links=active_links or TEST_CONFIG['active_links'],
            test_mode=test_mode,
            osc_config=TEST_CONFIG['oscilloscope_config']
        )
    
    def test_all_s68_gpios_by_link_d68(self, s68_gpio_range, enable_dialog=True):
        """按Link遍历测试所有S68 GPIO - D68版本 (先完成一个Link的所有GPIO，再测试下一个Link)"""
        print(f"\n{'='*80}")
        print(f"🚀 开始S68→D68 GPIO按Link遍历测试")
        print(f"📱 测试范围: S68 GPIO {s68_gpio_range}")
        print(f"🔗 活跃Links: {TEST_CONFIG['active_links']}")
        print(f"💬 确认对话框: {'启用' if enable_dialog else '禁用'}")
        print(f"🎯 测试策略: 先完成Link0所有GPIO，再测试Link2所有GPIO")
        print(f"{'='*80}")

        all_results = {}
        failed_tests = []
        skipped_tests = []
        active_links = TEST_CONFIG['active_links']
        total_tests = len(active_links) * len(s68_gpio_range)
        current_test_count = 0

        # 外层循环：按Link遍历
        for link_index, current_link in enumerate(active_links, 1):
            print(f"\n{'='*80}")
            print(f"🔗 开始测试Link{current_link} ({link_index}/{len(active_links)})")
            print(f"📊 将测试S68 GPIO: {s68_gpio_range}")
            print(f"{'='*80}")

            link_results = {}

            # 内层循环：当前Link的所有GPIO
            for gpio_index, s68_gpio in enumerate(s68_gpio_range, 1):
                current_test_count += 1
                test_key = f"Link{current_link}_S68_GPIO{s68_gpio}"

                print(f"\n📍 测试进度: {current_test_count}/{total_tests}")
                print(f"   当前测试: {test_key}")
                print(f"   Link{current_link} GPIO进度: {gpio_index}/{len(s68_gpio_range)}")

                # 弹窗确认 (如果启用) - 传入当前Link信息
                if enable_dialog:
                    dialog_result = self.show_confirmation_dialog(s68_gpio, current_test_count, total_tests, current_link)

                    if dialog_result is None:  # 用户选择取消
                        print(f"❌ 用户取消测试，停止所有后续测试")
                        all_results[test_key] = "cancelled"
                        return {
                            'results': all_results,
                            'summary': {
                                'total': current_test_count - 1,
                                'completed': len([r for r in all_results.values() if r in [True, False]]),
                                'failed': len(failed_tests),
                                'skipped': len(skipped_tests),
                                'cancelled': True
                            }
                        }

                    elif dialog_result is False:  # 用户选择跳过
                        print(f"⏭️  跳过测试: {test_key}")
                        skipped_tests.append(test_key)
                        all_results[test_key] = "skipped"
                        continue

                # 执行完整的系统重新初始化
                print(f"🔄 执行完整系统重新初始化 (模拟断电重新开机)...")
                reinit_success = self.full_system_reinit_d68(s68_gpio)

                if not reinit_success:
                    print(f"❌ 系统重新初始化失败，跳过此S68 GPIO测试")
                    all_results[test_key] = False
                    failed_tests.append(test_key)
                    continue

                # 执行测试 - 修改为只测试当前Link
                try:
                    print(f"🚀 开始执行S68 GPIO{s68_gpio}测试 (仅Link{current_link})...")
                    result = self.test_single_s68_gpio_single_link_d68(s68_gpio, current_link)

                    all_results[test_key] = result
                    link_results[test_key] = result

                    if result:
                        print(f"✅ 测试通过: {test_key}")
                    else:
                        print(f"❌ 测试失败: {test_key}")
                        failed_tests.append(test_key)

                except Exception as e:
                    print(f"💥 测试异常: {test_key} - {e}")
                    all_results[test_key] = False
                    link_results[test_key] = False
                    failed_tests.append(test_key)

                # 测试间隔
                if gpio_index < len(s68_gpio_range):
                    print(f"⏳ 等待1秒后进行下一个GPIO测试...")
                    time.sleep(1)

            # Link测试完成总结
            print(f"\n{'='*60}")
            print(f"📊 Link{current_link} 测试总结:")
            link_passed = [r for r in link_results.values() if r == True]
            link_failed = [r for r in link_results.values() if r == False]
            print(f"   ✅ 成功: {len(link_passed)}/{len(s68_gpio_range)}")
            print(f"   ❌ 失败: {len(link_failed)}/{len(s68_gpio_range)}")
            print(f"   📈 成功率: {len(link_passed)/len(s68_gpio_range)*100:.1f}%")
            print(f"{'='*60}")

            # Link间隔
            if link_index < len(active_links):
                print(f"⏳ 等待2秒后测试下一个Link...")
                time.sleep(2)

        # 最终总结
        print(f"\n{'='*80}")
        print(f"🎯 S68→D68 GPIO按Link遍历测试完成!")

        completed_tests = [r for r in all_results.values() if r in [True, False]]
        passed_tests = [r for r in all_results.values() if r == True]

        summary = {
            'total': total_tests,
            'completed': len(completed_tests),
            'passed': len(passed_tests),
            'failed': len(failed_tests),
            'skipped': len(skipped_tests),
            'cancelled': False
        }

        print(f"📈 测试统计:")
        print(f"  - 总计划测试: {summary['total']}")
        print(f"  - 实际完成: {summary['completed']}")
        print(f"  - 测试通过: {summary['passed']}")
        print(f"  - 测试失败: {summary['failed']}")
        print(f"  - 跳过测试: {summary['skipped']}")

        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test}")

        if skipped_tests:
            print(f"\n⏭️  跳过的测试:")
            for test in skipped_tests:
                print(f"  - {test}")

        print(f"{'='*80}")

        return {
            'results': all_results,
            'summary': summary,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests
        }


@pytest.mark.fast
def test_gpio_s68_d68_auto_traverse_by_link():
    """按Link遍历测试所有S68 GPIO - D68版本 (先完成Link0所有GPIO，再测试Link2所有GPIO)"""
    print(f"\n🚀 开始S68→D68 GPIO按Link遍历测试")
    print(f"📱 每个S68 GPIO测试前会弹窗确认")
    print(f"🔄 每个S68 GPIO会同时测试所有Q68 GPIO 0-14")
    print(f"🔗 仅使用Link0和Link2 (D68限制)")
    print(f"🎯 测试策略: 先完成Link0所有GPIO，再测试Link2所有GPIO")

    # 初始化D68设备管理器
    device_manager = D68_DeviceManager()

    # 初始化D68设备
    if not device_manager.initialize_d68_devices():
        pytest.fail("D68设备初始化失败")

    # 获取设备并创建测试器
    devices = device_manager.get_devices()
    tester = GPIO_S68_D68_AutoTester(devices)

    # 使用新的按Link遍历方法测试所有S68 GPIO
    result = tester.test_all_s68_gpios_by_link_d68(
        s68_gpio_range=[0,1,2,3,4,5,6, 7, 8],
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ S68→D68 GPIO自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")

    # 清理设备资源
    device_manager.cleanup()




if __name__ == "__main__":
    """
    使用说明:

    🆕 D68芯片GPIO测试功能:

    1. 自动遍历测试所有S68 GPIO (带确认对话框):
       pytest test_gpio_case6_8_s2d_auto2.py::test_gpio_s68_d68_auto_traverse_single_link -v -s

    3. 单个S68 GPIO测试:
       pytest test_gpio_case6_8_s2d_auto2.py::test_gpio_s68_d68_single_gpio -v -s

    🎯 多频率测试模式:
    4. 组合频率测试 (固定频率 + 扫频):
       pytest test_gpio_case6_8_s2d_auto2.py::test_gpio_s68_d68_combined_frequency_modes -v -s


    🔥 D68版本特点:
    - 基于test_gpio_case6_8_s2q_auto2.py适配D68芯片
    - 信号方向: S68 GPIO → D68 Q68 GPIO
    - 仅使用Link0和Link2 (D68硬件限制)
    - 使用MFNSet_D68方法替代MFNSet
    - 独立设备初始化，不依赖conftest.py
    - 自动遍历S68 GPIO0-8 (每个S68 GPIO同时测试所有Q68 GPIO 0-14)
    - 弹窗确认每个S68 GPIO测试，可选择跳过或取消
    - 每个S68 GPIO测试前完整系统重新初始化
    - 固定频率测试: 30Hz, 1kHz, 10kHz, 50kHz, 100kHz
    - 对应时基: 20ms, 1ms, 100us, 10us, 10us
    - 扫频测试: 1210kHz-1240kHz, 10kHz步进
    - 扫频时基: 500ns

    📊 频率测试模式:
    - fixed: 仅固定频率测试
    - sweep: 仅扫频测试
    - combined: 组合测试 (先固定频率，后扫频)

    📁 截图文件夹结构:
    U-disk0/gpiotest/s68tod68_0_2/
    ├── link0_gpio4/
    │   ├── S68GPIO4_D68_Link0_30Hz_*.png (固定频率)
    │   └── sweep/
    │       └── S68GPIO4_D68_Link0_SWEEP_1210000Hz_*.png (扫频)
    └── link2_gpio4/
        ├── S68GPIO4_D68_Link2_30Hz_*.png (固定频率)
        └── sweep/
            └── S68GPIO4_D68_Link2_SWEEP_1210000Hz_*.png (扫频)

    - 完全兼容原有代码流程，针对D68芯片优化
    """
    print("S68→D68 GPIO自动遍历测试用例 (D68芯片版本)")
    print("请使用pytest运行测试")
