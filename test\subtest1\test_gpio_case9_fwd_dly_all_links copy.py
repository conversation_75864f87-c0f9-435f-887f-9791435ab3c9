# -*- coding: utf-8 -*-
"""
GPIO延迟补偿测试 - 4个Link一起测试

基于test_gpio_case9_fwd_dly.py，专门用于4个Link的延迟补偿测试
关键修复：添加S68_AddrTrans地址转换设置，解决I2C通信问题
"""
import logging
import time
import pytest
import os

# 延迟补偿测试配置
TEST_CONFIG = {
    'all_links': [0, 1, 2, 3],              # 所有4个Link
    'q68_source_gpio': 0,                   # Q68源GPIO
    's68_target_gpios': [0, 1, 2, 3, 4, 5, 6, 7, 8],  # S68目标GPIO列表
    'signal_id': 11,                        # GPIO信号ID
    
    # 延迟补偿配置
    'delay_compensation_config': {
        'enable_dly_comp': True,            # 强制启用延迟补偿
        'fwd_dly_test_values': [0],         # fwd_dly测试值序列
        'rvs_dly_test_values': [50],         # rvs_dly测试值序列 q -> s
        'test_both_delays': False,          # 是否同时测试fwd_dly和rvs_dly
        'delay_explanation': {
            'fwd_dly': '前向延迟：控制Q68发送到S68的信号延迟',
            'rvs_dly': '反向延迟：控制S68返回到Q68的信号延迟',
            'unit': '3.5us per step',
            'range': '0-63 (6-bit parameter)'
        }
    },
    
    # 示波器配置
    'oscilloscope_config': {
        'enable_screenshot': True,          # 启用示波器截图
        'frequency': 30,                    # 固定30Hz频率
        'timebase': '20ms',                 # 对应时基
        'waveform_type': 'SQUARE',          # 方波
        'amplitude': 1.8,                   # 1.8Vpp
        'offset': 0.9,                      # 900mVdc偏移
        'screenshot_folder': 'U-disk0/gpiotest/fwd_dly_all_links',  # 截图文件夹
        'observation_time': 1,              # 每个延迟值的观察时间
    }
}


class GPIO_DelayCompensation_AllLinks_Tester:
    """GPIO延迟补偿测试器 - 专门用于4个Link一起测试"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def configure_delay_compensation_parameters(self, fwd_dly_value, rvs_dly_value):
        """
        配置延迟补偿参数 - 按照参考代码模式

        按照参考代码的配置顺序：
        1. 先配置所有Links的S68设备
        2. 最后配置Q68并打开TX

        Args:
            fwd_dly_value: 前向延迟值 (0-63)，单位3.5us
            rvs_dly_value: 反向延迟值 (0-63)，单位3.5us，默认5
        """
        try:
            print(f"  🔧 配置延迟补偿参数 (fwd_dly={fwd_dly_value}, rvs_dly={rvs_dly_value})...")

            # 按照参考代码模式：先配置所有Links的S68
            for link in TEST_CONFIG['all_links']:
                if link < len(self.s68_res_dev):
                    print(f"    � 配置Link{link} S68设备...")
                    s68_iic_dev = self.s68_res_dev[link]
                    self.q68_remote.dongle.devAddr = s68_iic_dev
                    self.q68_remote.M2CMFNSet(gpio=0, mfn=0)
                    self.q68_remote.M2CGPIORemoteRx(gpio=0)       #enable frame sync output to camera
                    self.q68_remote.m2c.wr_gpios_ctrl0_fields(fwd_dly=fwd_dly_value)
                    self.q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)
                    self.q68_remote.m2c.rd_gpios_ctrl0_fwd_dly()
                    print(f"      ✅ Link{link} S68配置完成")

            # 最后配置Q68并打开TX (在循环外面，按照参考代码的缩进)
            print(f"    � 配置Q68并启动TX...")
            self.q68.c2m.wr_gpios_ctrl0_fields(fwd_dly=fwd_dly_value)
            self.q68.c2m.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)
            self.q68.MFNSet(gpio=0, mfn=0)
            self.q68.GPIORemoteTx(gpio=0, tx_id=11, link_id=1, dly_comp_en=1)
            print(f"    ✅ Q68配置完成，TX已启动")

            return True
        except Exception as e:
            print(f"    ❌ 延迟补偿参数配置失败: {e}")
            return False

    def setup_address_translation_for_all_links(self, q68_gpio):
        """为所有Links设置地址转换 - 关键修复步骤"""
        try:
            print(f"  📡 为所有Links设置地址转换...")
            
            # 根据GPIO类型确定I2C总线配置
            i2c_bus_config = 1 if q68_gpio in [15, 16] else 0
            gpio_type = "特殊GPIO (I2C1)" if i2c_bus_config == 1 else "标准GPIO"
            print(f"    📋 GPIO{q68_gpio} ({gpio_type})，使用i2c_bus={i2c_bus_config}")
            
            # 为所有Links设置地址转换
            for link_id in TEST_CONFIG['all_links']:
                if link_id < len(self.s68_res_dev):
                    self.q68_remote.S68_AddrTrans(
                        link=link_id,
                        q68_iic_addr=0x73,
                        s68_iic_addr=0x40,
                        s68_retrans_addr=self.s68_res_dev[link_id],
                        sensor_addr=0x24,
                        sensor_retrans_addr=0x24 + link_id,
                        i2c_bus=i2c_bus_config
                    )
                    print(f"      ✅ Link{link_id} 地址转换: 0x{self.s68_res_dev[link_id]:02X} -> 0x40")
                else:
                    print(f"      ⚠️ Link{link_id} 超出范围，跳过")
            
            print(f"    ✅ 所有Links地址转换设置完成")
            return True
            
        except Exception as e:
            print(f"    ❌ 地址转换设置失败: {e}")
            return False

    def configure_q68_gpio_with_delay_compensation(self, q68_gpio, signal_id, fwd_dly_value, rvs_dly_value):
        """
        配置Q68 GPIO并启用延迟补偿 - 简化版本

        注意：延迟补偿参数的配置已经在configure_delay_compensation_parameters中完成
        这里只需要配置特定GPIO的MFN和远程发送
        """
        try:
            print(f"  📤 配置Q68 GPIO{q68_gpio} (延迟补偿模式)...")

            # 步骤1: 配置延迟补偿参数 (必须在GPIORemoteTx之前)
            if not self.configure_delay_compensation_parameters(fwd_dly_value, rvs_dly_value):
                return False

            # 步骤2: 如果需要配置特定GPIO (不是GPIO0)
            if q68_gpio != 0:
                self.q68.MFNSet(gpio=q68_gpio, mfn=0)
                print(f"    ✅ GPIO{q68_gpio} MFN设置完成")

                # 步骤3: 配置远程发送，使用Link0作为主Link，启用延迟补偿
                primary_link = TEST_CONFIG['all_links'][0]
                dly_comp_en = 1 if TEST_CONFIG['delay_compensation_config']['enable_dly_comp'] else 0

                print(f"    🔗 调用GPIORemoteTx (dly_comp_en={dly_comp_en})...")
                self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id, link_id=primary_link, dly_comp_en=dly_comp_en)

                comp_status = "启用" if dly_comp_en else "禁用"
                print(f"    ✅ GPIO{q68_gpio} 远程发送配置完成")
                print(f"    📊 延迟补偿: {comp_status}, 主Link: {primary_link}")
            else:
                print(f"    ✅ GPIO0已在延迟补偿参数配置中完成")

            print(f"    📊 延迟参数: fwd_dly={fwd_dly_value} ({fwd_dly_value * 3.5:.1f}us), rvs_dly={rvs_dly_value} ({rvs_dly_value * 3.5:.1f}us)")

            return True

        except Exception as e:
            print(f"    ❌ Q68 GPIO{q68_gpio} 延迟补偿配置失败: {e}")
            return False

    def configure_s68_gpios_for_all_links(self, s68_gpio_range, signal_id):
        """配置所有Links上的S68 GPIOs - 增强错误检测版本"""
        try:
            print(f"  📥 配置所有Links上的S68 GPIO{s68_gpio_range}...")

            success_count = 0
            total_count = 0
            i2c_error_count = 0

            for link_id in TEST_CONFIG['all_links']:
                if link_id < len(self.s68_res_dev):
                    print(f"    🔗 配置Link{link_id} S68 GPIOs...")

                    # 设置设备地址
                    target_addr = self.s68_res_dev[link_id]
                    self.q68_remote.dongle.devAddr = target_addr
                    time.sleep(0.1)  # 等待地址设置生效

                    # 配置所有S68 GPIO
                    link_success = 0
                    link_i2c_errors = 0

                    for s68_gpio in s68_gpio_range:
                        total_count += 1
                        try:
                            # 尝试配置MFN
                            self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                            # 尝试配置远程接收
                            self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
                            link_success += 1
                            print(f"        ✅ Link{link_id} GPIO{s68_gpio} 配置成功")
                            # time.sleep(1)  # 等待

                        except Exception as e:
                            error_msg = str(e)
                            if self.check_i2c_communication_error(error_msg):
                                link_i2c_errors += 1
                                i2c_error_count += 1
                                print(f"        🔴 Link{link_id} GPIO{s68_gpio} I2C通信错误: {e}")
                                print(f"        💡 提示: 这可能是由于地址转换未正确设置导致的")
                            else:
                                print(f"        ❌ Link{link_id} GPIO{s68_gpio} 其他错误: {e}")

                    if link_success > 0:
                        success_count += link_success
                        print(f"      ✅ Link{link_id} S68 GPIO配置: {link_success}/{len(s68_gpio_range)} 成功")
                    else:
                        print(f"      ❌ Link{link_id} S68 GPIO配置全部失败")

                    if link_i2c_errors > 0:
                        print(f"      🔴 Link{link_id} I2C通信错误: {link_i2c_errors}/{len(s68_gpio_range)}")

                else:
                    print(f"    ⚠️ Link{link_id} 超出范围，跳过")

            print(f"    📊 总S68 GPIO配置: {success_count}/{total_count} 成功")
            if i2c_error_count > 0:
                print(f"    🔴 总I2C通信错误: {i2c_error_count}/{total_count}")
                print(f"    💡 建议: 检查S68_AddrTrans地址转换设置是否正确")

            # 如果有成功的配置，就认为测试可以继续
            return success_count > 0

        except Exception as e:
            print(f"    ❌ S68 GPIO配置失败: {e}")
            if self.check_i2c_communication_error(str(e)):
                print(f"    🔴 检测到I2C通信错误，可能需要检查地址转换设置")
            return False

    def oscilloscope_screenshot(self, q68_gpio, fwd_dly_value):
        """
        示波器截图 - 修正版本

        修正频率切换逻辑：先设置不同频率，再切换到目标频率，确保波形正常显示
        """
        osc_config = TEST_CONFIG['oscilloscope_config']

        if not osc_config['enable_screenshot'] or self.oscilloscope is None:
            print("  📷 示波器截图已禁用或不可用")
            return

        try:
            print(f"  📷 示波器延迟补偿测试...")

            # 设置时基
            self.oscilloscope.Set_Timebase_Scale(timebase_scale=osc_config['timebase'])
            print(f"    ⏱️ 时基设置: {osc_config['timebase']}")

            # 修正：频率切换逻辑，确保波形正常显示
            target_frequency = osc_config['frequency']
            temp_frequency = 1000 if target_frequency != 1000 else 467  # 选择不同的临时频率

            print(f"    🔄 频率切换修正逻辑...")

            # 步骤1: 先设置临时频率
            print(f"      📡 设置临时频率: {temp_frequency}Hz")
            self.oscilloscope.Set_Wavegen_Basic(
                waveform=osc_config['waveform_type'],
                frequency=temp_frequency,
                amplitude=osc_config['amplitude'],
                offset=osc_config['offset'],
                output_state='ON',
                load=50
            )
            time.sleep(0.5)  # 短暂等待

            # # 步骤2: 切换到目标频率
            # print(f"      🎯 切换到目标频率: {target_frequency}Hz")
            # self.oscilloscope.Set_Wavegen_Basic(
            #     waveform=osc_config['waveform_type'],
            #     frequency=target_frequency,
            #     amplitude=osc_config['amplitude'],
            #     offset=osc_config['offset'],
            #     output_state='ON',
            #     load=50
            # )



            # 等待信号稳定
            print(f"    ⏳ 等待信号稳定 ({osc_config['observation_time']}秒)...")
            time.sleep(osc_config['observation_time'])

            # 截图
            timestamp = time.strftime('%m%d_%H%M%S')
            screenshot_filename = f"AllLinks_GPIO{q68_gpio}_fwd_dly{fwd_dly_value:02d}_{timestamp}.png"
            screenshot_path = f"{osc_config['screenshot_folder']}/{screenshot_filename}"

            os.makedirs(osc_config['screenshot_folder'], exist_ok=True)

            self.oscilloscope.Save_Image(
                filepath=screenshot_path,
                image_format="PNG",
                invert="OFF",
                menu="MOF"
            )
            print(f"    📸 截图保存: {screenshot_filename}")

        except Exception as e:
            print(f"    ❌ 示波器测试失败: {e}")

    def test_all_links_fwd_dly_single_value(self, q68_gpio, s68_gpio_range, signal_id, fwd_dly_value, rvs_dly_value):
        """4个Link一起的延迟补偿测试 - 修正版本"""
        try:
            print(f"\n🔧 开始全Link延迟补偿测试: GPIO{q68_gpio}")
            print(f"   延迟参数: fwd_dly={fwd_dly_value} ({fwd_dly_value * 3.5:.1f}us), rvs_dly={rvs_dly_value} ({rvs_dly_value * 3.5:.1f}us)")

            # 步骤1: 配置所有Links
            print(f"  🔗 步骤1: 配置所有Links{TEST_CONFIG['all_links']}...")
            if not self.configure_links(TEST_CONFIG['all_links']):
                print(f"    ❌ Links配置失败")
                return False
            print(f"    ✅ 所有Links配置完成")

            # 步骤2: 设置地址转换
            print(f"  📡 步骤2: 设置地址转换...")
            if not self.setup_address_translation_for_all_links(q68_gpio):
                return False

            # 步骤3: 配置Q68 GPIO（带延迟补偿）
            print(f"  📤 步骤3: 配置Q68 GPIO{q68_gpio} (延迟补偿)...")
            if not self.configure_q68_gpio_with_delay_compensation(q68_gpio, signal_id, fwd_dly_value, rvs_dly_value):
                return False
            
            # 步骤4: 配置所有Links上的S68 GPIOs
            print(f"  📥 步骤4: 配置所有Links上的S68 GPIO{s68_gpio_range}...")
            if not self.configure_s68_gpios_for_all_links(s68_gpio_range, signal_id):
                return False
            
            # 步骤5: 示波器测试
            print(f"  📷 步骤5: 示波器延迟测试...")
            self.oscilloscope_screenshot(q68_gpio, fwd_dly_value)
            
            print(f"  ✅ 全Link延迟补偿测试完成: fwd_dly={fwd_dly_value}")
            return True
            
        except Exception as e:
            print(f"    ❌ 全Link延迟补偿测试失败: {e}")
            return False


@pytest.mark.fast
def test_gpio_delay_compensation_all_links(devices):
    """
    4个Link一起的GPIO延迟补偿测试
    """
    print("\n" + "="*80)
    print("🚀 GPIO延迟补偿测试 - 4个Link一起")
    print("="*80)
    
    tester = GPIO_DelayCompensation_AllLinks_Tester(devices)
    
    # 测试参数
    q68_gpio = TEST_CONFIG['q68_source_gpio']
    s68_gpio_range = TEST_CONFIG['s68_target_gpios']
    signal_id = TEST_CONFIG['signal_id']
    fwd_dly_values = TEST_CONFIG['delay_compensation_config']['fwd_dly_test_values']
    rvs_dly_value = TEST_CONFIG['delay_compensation_config']['rvs_dly_test_values'][0]  # 取第一个值
    
    print(f"📊 测试配置:")
    print(f"  - Links: {TEST_CONFIG['all_links']}")
    print(f"  - Q68 GPIO: {q68_gpio}")
    print(f"  - S68 GPIOs: {s68_gpio_range}")
    print(f"  - fwd_dly值: {fwd_dly_values}")
    print(f"  - rvs_dly值: {rvs_dly_value}")
    print(f"  - 总测试数: {len(fwd_dly_values)}")
    
    results = {}
    for i, fwd_dly_value in enumerate(fwd_dly_values, 1):
        print(f"\n📍 测试进度: {i}/{len(fwd_dly_values)} - fwd_dly = {fwd_dly_value}")
        
        result = tester.test_all_links_fwd_dly_single_value(
            q68_gpio, s68_gpio_range, signal_id, fwd_dly_value, rvs_dly_value
        )
        results[fwd_dly_value] = result
        
        if result:
            print(f"  ✅ fwd_dly = {fwd_dly_value} (延迟: {fwd_dly_value * 3.5:.1f}us) 测试通过")
        else:
            print(f"  ❌ fwd_dly = {fwd_dly_value} (延迟: {fwd_dly_value * 3.5:.1f}us) 测试失败")
        
        # 测试间隔
        if i < len(fwd_dly_values):
            time.sleep(2)
    
    # 测试总结
    successful_tests = sum(1 for result in results.values() if result)
    print(f"\n📊 测试总结:")
    print(f"  - 总测试: {len(fwd_dly_values)}")
    print(f"  - 成功: {successful_tests}")
    print(f"  - 失败: {len(fwd_dly_values) - successful_tests}")
    print(f"  - 成功率: {successful_tests/len(fwd_dly_values)*100:.1f}%")
    
    # 验证测试结果
    assert len(results) > 0, "延迟补偿测试未执行"
    assert successful_tests > 0, "所有延迟补偿测试都失败了"
    
    print(f"\n✅ 4个Link延迟补偿测试完成")


if __name__ == "__main__":
    """
    GPIO延迟补偿测试 - 4个Link一起测试
    
    🎯 测试目标:
    - 验证4个Link同时的延迟补偿效果
    - 测试多Link环境下的信号同步性
    - 通过示波器观察延迟补偿的实际效果
    
    🔧 核心功能:
    - fwd_dly参数扫描 (0-63, 单位3.5us)
    - 强制启用dly_comp_en=1
    - 4个Link并行GPIO配置
    - 示波器30Hz频率测试和截图
    
    🚀 使用方法:
    pytest test_gpio_case9_fwd_dly_all_links.py::test_gpio_delay_compensation_all_links
    
    📊 测试配置:
    - Links: [0, 1, 2, 3] (所有4个Link)
    - fwd_dly值: [0, 10, 20, 30, 40, 50, 63]
    - 延迟范围: 0 ~ 220.5us
    - 截图保存: U-disk0/gpiotest/fwd_dly_all_links/
    """
    print("GPIO延迟补偿测试 - 4个Link一起测试")
    print("请使用pytest运行测试")
