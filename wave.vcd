$date
	Mon Jul 28 12:11:06 2025
$end
$version
	Icarus Verilog
$end
$timescale
	1ns
$end
$scope module tb_csi_rx_lane_aligner_8b2lane $end
$var wire 1 ! synced $end
$var wire 16 " bytes_o [15:0] $end
$var reg 16 # bytes_i [15:0] $end
$var reg 3 $ bytes_valid [2:0] $end
$var reg 1 % clk $end
$var reg 1 & reset $end
$scope module ins1 $end
$var wire 16 ' byte_i [15:0] $end
$var wire 2 ( bytes_valid_i [1:0] $end
$var wire 1 % clk_i $end
$var wire 1 & reset_i $end
$var parameter 4 ) ALIGN_DEPTH $end
$var parameter 32 * MIPI_GEAR $end
$var parameter 32 + MIPI_LANES $end
$var reg 4 , i [3:0] $end
$var reg 16 - lane_byte_o [15:0] $end
$var reg 1 ! lane_valid_o $end
$var reg 1 . lane_valid_reg $end
$var reg 3 / offset [2:0] $end
$var reg 2 0 valid [1:0] $end
$upscope $end
$scope task sendbytes $end
$var reg 16 1 bytes [15:0] $end
$upscope $end
$upscope $end
$enddefinitions $end
$comment Show the parameter values. $end
$dumpall
b10 +
b1000 *
b111 )
$end
#0
$dumpvars
bx 1
bx 0
bx /
x.
bx -
bx ,
b0 (
bx '
0&
0%
b0 $
bx #
bx "
x!
$end
#50
b0 #
b0 '
b0 1
#54
b0 0
b10 ,
1%
#58
0%
1&
#62
0.
0!
b101 /
b10 ,
1%
#66
0%
0&
#70
b0 "
b0 -
b10 ,
1%
#74
0%
#78
b10 ,
1%
#82
b1 (
0%
b10111000 #
b10111000 '
b10111000 1
b1 $
#86
b1 0
b10 ,
1%
#90
b11 (
0%
b1011100000010001 #
b1011100000010001 '
b1011100000010001 1
b11 $
#94
b100 /
b11 0
b10 ,
1%
#98
0%
b1000100100010 #
b1000100100010 '
b1000100100010 1
#102
b11 /
1.
b10 ,
1%
#106
0%
b10001000110011 #
b10001000110011 '
b10001000110011 1
#110
1!
b10 /
b1011100010111000 "
b1011100010111000 -
b10 ,
1%
#114
0%
b11001101000100 #
b11001101000100 '
b11001101000100 1
#118
b1 /
b1000100010001 "
b1000100010001 -
b10 ,
1%
#122
0%
b100010001010101 #
b100010001010101 '
b100010001010101 1
#126
b0 /
b10001000100010 "
b10001000100010 -
b10 ,
1%
#130
0%
b101010101100110 #
b101010101100110 '
b101010101100110 1
#134
b111 /
b11001100110011 "
b11001100110011 -
b10 ,
1%
#138
b10 (
0%
b110011001110111 #
b110011001110111 '
b110011001110111 1
b10 $
#142
b110 /
b10 0
b100010001000100 "
b100010001000100 -
b10 ,
1%
#146
b0 (
0%
b0 #
b0 '
b0 1
b0 $
#150
b0 0
b101 /
b101010101010101 "
b101010101010101 -
b10 ,
1%
#154
0%
#158
0.
b100 /
b110011001100110 "
b110011001100110 -
b10 ,
1%
#162
0%
#166
b11 /
0!
b0 "
b0 -
b10 ,
1%
#170
0%
1&
#174
b101 /
b10 ,
1%
#178
0%
0&
#182
b10 ,
1%
#186
0%
#190
b10 ,
1%
#194
b11 (
0%
b1011100010111000 #
b1011100010111000 '
b1011100010111000 1
b11 $
#198
b11 0
b10 ,
1%
#202
0%
b1000100010001 #
b1000100010001 '
b1000100010001 1
#206
1.
b100 /
b10 ,
1%
#210
0%
b10001000100010 #
b10001000100010 '
b10001000100010 1
#214
b11 /
1!
b1011100010111000 "
b1011100010111000 -
b10 ,
1%
#218
0%
b11001100110011 #
b11001100110011 '
b11001100110011 1
#222
b10 /
b1000100010001 "
b1000100010001 -
b10 ,
1%
#226
0%
b100010001000100 #
b100010001000100 '
b100010001000100 1
#230
b1 /
b10001000100010 "
b10001000100010 -
b10 ,
1%
#234
0%
b101010101010101 #
b101010101010101 '
b101010101010101 1
#238
b0 /
b11001100110011 "
b11001100110011 -
b10 ,
1%
#242
0%
b110011001100110 #
b110011001100110 '
b110011001100110 1
#246
b111 /
b100010001000100 "
b100010001000100 -
b10 ,
1%
#250
b0 (
0%
b111011101110111 #
b111011101110111 '
b111011101110111 1
b0 $
#254
b110 /
b0 0
b101010101010101 "
b101010101010101 -
b10 ,
1%
#258
0%
b0 #
b0 '
b0 1
#262
b101 /
0.
b110011001100110 "
b110011001100110 -
b10 ,
1%
#266
0%
#270
0!
b100 /
b0 "
b0 -
b10 ,
1%
#274
0%
#278
b101 /
b10 ,
1%
#282
0%
1&
#286
b10 ,
1%
#290
0%
0&
#294
b10 ,
1%
#298
0%
#302
b10 ,
1%
#306
b10 (
0%
b1011100000000000 #
b1011100000000000 '
b1011100000000000 1
b10 $
#310
b10 0
b10 ,
1%
#314
0%
b1000100000000 #
b1000100000000 '
b1000100000000 1
#318
b100 /
b10 ,
1%
#322
b11 (
0%
b10001010111000 #
b10001010111000 '
b10001010111000 1
b11 $
#326
b11 0
b11 /
b10 ,
1%
#330
0%
b11001100010001 #
b11001100010001 '
b11001100010001 1
#334
1.
b10 /
b10 ,
1%
#338
0%
b100010000100010 #
b100010000100010 '
b100010000100010 1
#342
b1 /
1!
b1011100010111000 "
b1011100010111000 -
b10 ,
1%
#346
0%
b101010100110011 #
b101010100110011 '
b101010100110011 1
#350
b0 /
b1000100010001 "
b1000100010001 -
b10 ,
1%
#354
0%
b110011001000100 #
b110011001000100 '
b110011001000100 1
#358
b111 /
b10001000100010 "
b10001000100010 -
b10 ,
1%
#362
b1 (
0%
b111011101010101 #
b111011101010101 '
b111011101010101 1
b1 $
#366
b110 /
b1 0
b11001100110011 "
b11001100110011 -
b10 ,
1%
#370
0%
b1000100001100110 #
b1000100001100110 '
b1000100001100110 1
#374
b101 /
b100010001000100 "
b100010001000100 -
b10 ,
1%
#378
b0 (
0%
b0 #
b0 '
b0 1
b0 $
#382
b100 /
b0 0
b101010101010101 "
b101010101010101 -
b10 ,
1%
#386
0%
#390
b11 /
0.
b110011001100110 "
b110011001100110 -
b10 ,
1%
#394
0%
#398
0!
b10 /
b0 "
b0 -
b10 ,
1%
#402
