from QHWInterface import QHWinterface

# 硬件设备1
qHWinterface1 = QHWinterface()
qHWinterface1.initHWDevice(device_index=0, save_data=False)

reg_ana_0x1001 = qHWinterface1.readI2CR16V8(0, 0x69, 0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')
qHWinterface1.writeI2CR16V8(0, 0x69, 0x1001, 0x00)


reg_ana_0x1001 = qHWinterface1.readI2CR16V8(1, 0x54, 0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')
qHWinterface1.writeI2CR16V8(1, 0x54, 0x1001, 0x00)


# 硬件设备2
qHWinterface2 = QHWinterface()
qHWinterface2.initHWDevice(device_index=1, save_data=False)

reg_ana_0x1001 = qHWinterface2.readI2CR16V8(0, 0x69, 0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')
qHWinterface2.writeI2CR16V8(0, 0x69, 0x1001, 0x00)


qHWinterface2 = qHWinterface1.readI2CR16V8(1, 0x54, 0x1001)
print ('read back value on reg_0x1001: ', hex(reg_ana_0x1001), ', expect value: 0x05 ')
qHWinterface2.writeI2CR16V8(1, 0x54, 0x1001, 0x00)
