#!python3

import time
import csv

# package for supporting stm32
from stm32 import USBImpl
import ft4222  
# package for supporting ft4222
# import ft4222

# global ft4222 usb hardware instances list, as some silicon may share same instance
ft4222_inst_list = None

# global stm32 usb hardware instances list, as some silicon may share same instance
stm32_inst_list = None

q68_dev = None
q68_search = True
devlist = 0
dutsearch = None

save_data = 0
if save_data:
    data_addr = "C:\\project\\m65q68_r7\\Raw_Data\\01_test\\reg_record\\Test_Q68_R68_PRBS_sim.csv"
    with open(data_addr, "a", encoding="utf-8", newline="") as f:
        csv_writer = csv.writer(f)
        name = ['DeviceAddr','Reg_Addr', 'R_or_W', 'Write_or_Read_Value']
        csv_writer.writerow(name)

register_sim = 0    # access register without chip
if register_sim:
    csv_file_reg_default_c2m = "D:\\project\\m65q68_a0\\Script\\Test\\Common\\c2m_top_reg_default_value_20250218.csv"       #register default value
    csv_file_reg_default_m2c = "D:\\project\\m66s68_a0\\Script\Test\\Common_var\\m2c_top_reg_default_value_20250218.csv" 

class QRegisterAccess:

    # i2c device address
    devAddr = 0x00
    bitrate = 115200
    uarttype = 0 #0为muanual mode, 1为auto mode    
    
    # dongle name, 'stm32'/'ft4222'/'ft4232'
    dongle = None

    # ft4222 i2c, spi & gpio
    ft4222_dev_location = None
    ft4222_devA = None
    ft4222_devB = None
    ft4222_bus  = None

    # stm32 support
    stm32_dev = None
    stm32_bus = None
    stm32_dev_index = 0x00
    stm32_dev_sn    = 0x00
    stm32_bus_chan  = 0x00 # 1: i2c1, 2:i2c2, 3:i2c3
    
    # todo: ft4222 support
    
    # todo: ft4232 support
    def __init__(self, dongle='stm32', dev=None, i2c_addr=None, dongle_id=None, bus='i2c', bus_chan=1, product='c2m', id=0, optype='auto'):
        
        '''
        optype: 
            auto:    scan q68/s68
            manual:  give a dongle_id of MCUs
        '''
        global stm32_inst_list
        global q68_search 
        global q68_dev
        global devlist
        global ft4222_devA
        global dutsearch
        
        self.bus=bus
        if optype=='auto':
            self.dongle = dongle
            
            if dongle == 'sim':
                # c2m_regs[addr] = value
                if product == 'c2m':
                    csv_file = csv_file_reg_default_c2m
                    print('[Simulation] load Q68 register default setting!')
                if product == 'm2c':
                    csv_file = csv_file_reg_default_m2c
                    print('[Simulation]load S68 register default setting!')
                    
                c2m_regs = {} 
                with open(csv_file, mode='r', newline='', encoding='utf-8') as file:
                    reader = csv.reader(file)
                    next(reader)  # 跳过标题行（如果存在）
                    for row in reader:
                        if len(row) >= 2:  # 确保每行至少有两列数据
                            key = int(row[0], 16)  # 将第一列的十六进制字符串转换为整数
                            value = int(row[1], 16)  # 将第二列的十六进制字符串转换为整数
                            c2m_regs[key] = value
                            
                self.c2m_regs = c2m_regs
                # print(c2m_re g)         
                
            if dongle == 'stm32':
            
                #find all device
                if stm32_inst_list is None:
                    stm32_inst_list = []
                    self.stm32_dev = USBImpl.USBImpl()
                    devlist = self.stm32_dev.usbdev_scan()
                    self.stm32_dev = None
                    print("Find %d STM32 Devices:"%(len(devlist)))
                    for i in range(len(devlist)):
                        dev = USBImpl.USBImpl()
                        dev.usbdev_open(devlist[i].name)
                        dev.usbdev_i2c_setbusspeed(1, 400)
                        dev.usbdev_i2c_setbusspeed(2, 400)
                        dev.usbdev_i2c_setbusspeed(3, 400)
                        (ret, sn) = dev.usbdev_get_sn()
                        stm32_inst_list.append((dev, sn))
                        print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                        
                i = 0
                for inst in stm32_inst_list:
                        if len(devlist)==1:  # means 1 Q68, 1 s8 or 1 Q68+1s68
                    # if inst[1] == dongle_id:
                            self.stm32_dev = inst[0]
                            self.stm32_dev_sn = dongle_id
                            self.stm32_bus = bus
                            self.stm32_bus_chan = bus_chan
                            if self.stm32_dev == None:
                                raise BaseException('unable to find specified stm32 dongle')
                            if id<=1:
                                if bus == 'i2c':
                                    self.stm32_bus = bus
                                    if product=='c2m':
                                        if id==0: 
                                            if i2c_addr is None:
                                                print('sn: ', inst[1])
                                                for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                                    # self.dongle.stm32_dev = inst[0]
                                                    # self.dongle.stm32_dev_sn = inst[1]
                                                    self.devAddr = devAddr
                                                    
                                                    self.stm32_bus_chan = 1
                                                    data = self.readReg(moduleName=product, addr=0x1001)
                                                    if data == 0x5:
                                                        i2c_addr = devAddr
                                                        # self.dongle.devAddr = i2c_addr
                                                        print('find q68 device 0x%x'%(devAddr))
                                                        q68_dev = inst
                                                        break
                                        #             if i2c_addr is None:
                                        #                 raise('q68 not found')
                                        # else:
                                        #     raise('please connect Q68 to MCU channel 1 !!!')
                                    if product=='m2c' and id==1:
                                        if i2c_addr is None:
                                            for devAddr in [0x40, 0x44]: 
                                                # self.dongle.stm32_dev = inst[0]
                                                # self.dongle.stm32_dev_sn = inst[1]
                                                self.devAddr = devAddr
                                                self.stm32_bus_chan = 2
                                                data = self.readReg(moduleName=product, addr=0x0101)
                                                if data == 0x55:
                                                    i2c_addr = devAddr
                                                    # self.dongle.devAddr = i2c_addr
                                                    print('find s68 device 0x%x'%(devAddr))
                                                    break
                                            # if i2c_addr is None:
                                            #     raise('s68 not found')
                        if len(devlist)>1:  # means q68 + s681, s682 or more
                            
                            self.stm32_dev = inst[0]
                            self.stm32_dev_sn = dongle_id
                            self.stm32_bus = bus
                            self.stm32_bus_chan = bus_chan
                            if self.stm32_dev == None:
                                raise BaseException('unable to find specified stm32 dongle')
                            if id<=1:
                                if bus == 'i2c':
                                    self.stm32_bus = bus
                                    if product=='c2m':
                                        if id==0: 
                                            if i2c_addr is None:
                                                print('sn: ', inst[1])
                                                if q68_search:
                                                    for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                                        # self.stm32_dev = inst[0]
                                                        # self.stm32_dev_sn = inst[1]
                                                        self.devAddr = devAddr
                                                        
                                                        self.stm32_bus_chan = 1
                                                        data = self.readReg(moduleName=product, addr=0x1001)
                                                        if data == 0x5:
                                                            i2c_addr = devAddr
                                                            # self.dongle.devAddr = i2c_addr
                                                            print('find q68 device 0x%x'%(devAddr))
                                                            q68_dev = inst
                                                            q68_search = False
                                                            
                                                            break
                                        #                 if i2c_addr is None:
                                        #                     raise('q68 not found')
                                        # else:
                                        #     raise('please connect Q68 to MCU channel 1 !!!')
                                    
                                    if product=='m2c' and id==1:
                                        if i2c_addr is None:
                                            for devAddr in [0x40, 0x44]: 
                                                self.stm32_dev = q68_dev[0]
                                                # self.stm32_dev_sn = inst[1]
                                                self.devAddr = devAddr
                                                self.stm32_bus_chan = 2
                                                data = self.readReg(moduleName=product, addr=0x0101)
                                                if data == 0x55:
                                                    i2c_addr = devAddr
                                                    # self.devAddr = i2c_addr
                                            #         print('find s68 device 0x%x'%(devAddr))
                                            #         break
                                            # if i2c_addr is None:
                                            #     raise('s68 not found')
                        if q68_search ==False:
                            break
                if product=='m2c' and id>1:
                    for inst in stm32_inst_list:
                        if inst[1] != q68_dev[1]:
                            if i2c_addr is None:
                                for devAddr in [0x40, 0x44]: 
                                    self.stm32_dev = inst[0]
                                    # self.stm32_dev_sn = inst[1]
                                    self.devAddr = devAddr
                                    self.stm32_bus_chan = id-1
                                    data = self.readReg(moduleName=product, addr=0x0101)
                                    if data == 0x55:
                                        i2c_addr = devAddr
                                        # self.dongle.devAddr = i2c_addr
                                        print('find s68 device 0x%x'%(devAddr))
                                        break
                        
                                    # if i2c_addr is None:
                                    #     raise('s68 not found')
                                    
                                    
                        
            elif dongle == 'ft4222':
                  
            
                location0=801  #801#12817
                location1=802  #802#12818
            
                devNum = ft4222.createDeviceInfoList()
                add = [0 for i in range(devNum)]
                for i in range(devNum):
                    add[i]=ft4222.getDeviceInfoDetail(i, False)
                    print(ft4222.getDeviceInfoDetail(i, False))
                
                for useadd in range(int(devNum/2)):
                    location0 = add[2*useadd]['location']
                    location1 = add[2*useadd+1]['location']
                    if location0 != dutsearch:
                        ft4222_devA=ft4222.openByLocation(location0)
                        ft4222_devB=ft4222.openByLocation(location1)
                        ft4222_devA.setClock(ft4222.SysClock.CLK_80)
                    # if MCU=='ft4222':
                    #     from QRegisterAccess import *
                    # elif MCU=='ftd2xx':
                    #     from ftd2xx.QRegisterAccess import *
                    
                        if self.findMeritechI2cChan():
                            print ('Bridge board i2c is found!')
                        else:
                            print ('Can not find bridge i2c!') 
                            raise 
                        if product=='m2c':
                            for devAddr in [0x40,0x44]:
                                self.devAddr = devAddr
                                data = self.readReg(moduleName=product, addr=0x0101)
                                if data == 0x55:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find s68 device 0x%x'%(devAddr))
                                    break
                            if data == 0x55:
                                dutsearch = location0
                                break
                        if product=='c2m':
                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                # self.stm32_dev = inst[0]
                                # self.stm32_dev_sn = inst[1]
                                self.devAddr = devAddr
                                data = self.readReg(moduleName=product, addr=0x1001)
                                if data == 0x5:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find q68 device 0x%x'%(devAddr))
                                    break
                            if data == 0x5:
                                dutsearch = location0
                                break
                #raise BaseException('not support')
            elif dongle == 'ft4232':
                raise BaseException('not support')
            elif dongle == 'sim':
                print('[Simulation] Access registers without MUC')
            else:
                raise BaseException('unknown dongle')
        elif optype=='manual':
            self.dongle = dongle
            if dongle == 'stm32':
                
                if bus=='i2c':
                    # find all device
                    if stm32_inst_list is None:
                        stm32_inst_list = []
                        self.stm32_dev = USBImpl.USBImpl()
                        devlist = self.stm32_dev.usbdev_scan()
                        self.stm32_dev = None
                        print("Find %d STM32 Devices:"%(len(devlist)))
                        for i in range(len(devlist)):
                            dev = USBImpl.USBImpl()
                            try:                                            #assign dongle_id
                                dev.usbdev_open(devlist[i].name)
                                dev.usbdev_i2c_setbusspeed(1, 400)
                                dev.usbdev_i2c_setbusspeed(2, 400)
                                dev.usbdev_i2c_setbusspeed(3, 400)
                                (ret, sn) = dev.usbdev_get_sn()
                                stm32_inst_list.append((dev, sn))
                                print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                            except Exception as e:
                                print('device error', e)

                    for inst in stm32_inst_list:
                        if inst[1] == dongle_id:
                            self.stm32_dev = inst[0]
                            self.stm32_dev_sn = dongle_id
                            self.stm32_bus = bus
                            self.stm32_bus_chan = bus_chan
                    
                    if product=='c2m':
                       
                        if i2c_addr is None:
                            print('sn: ', inst[1])
                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                # self.dongle.stm32_dev = inst[0]
                                # self.dongle.stm32_dev_sn = inst[1]
                                self.devAddr = devAddr
                                
                                # self.stm32_bus_chan = 1
                                data = self.readReg(moduleName=product, addr=0x1001)
                                if data == 0x5:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find q68 device 0x%x'%(devAddr))
                                    q68_dev = inst
                                    break
                        #             if i2c_addr is None:
                        #                 raise('q68 not found')
                        # else:
                        #     raise('please connect Q68 to MCU channel 1 !!!')
                    if product=='m2c':
                        if i2c_addr is None:
                            for devAddr in [0x40, 0x44]: 
                                # self.dongle.stm32_dev = inst[0]
                                # self.dongle.stm32_dev_sn = inst[1]
                                self.devAddr = devAddr
                                # self.stm32_bus_chan = 2
                                data = self.readReg(moduleName=product, addr=0x0101)
                                if data == 0x55:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find s68 device 0x%x'%(devAddr))
                                    break
                  
                            
                    if self.stm32_dev == None:
                        raise BaseException('unable to find specified stm32 dongle')
                elif bus=='uart':
                    if stm32_inst_list is None:
                        stm32_inst_list = []
                        self.stm32_dev = USBImpl.USBImpl()
                        devlist = self.stm32_dev.usbdev_scan()
                        self.stm32_dev = None
                        print("Find %d STM32 Devices:"%(len(devlist)))
                        for i in range(len(devlist)):
                            dev = USBImpl.USBImpl()
                            dev.usbdev_open(devlist[i].name)
                            (ret, sn) = dev.usbdev_get_sn()
                            stm32_inst_list.append((dev, sn))
                            print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                    for inst in stm32_inst_list:
                        if inst[1] == dongle_id:
                            self.stm32_dev = inst[0]
                            self.stm32_dev_sn = dongle_id
                            self.stm32_bus = bus
                            self.stm32_bus_chan = bus_chan
                            
                    if product=='c2m':
                       
                        if i2c_addr is None:
                            print('sn: ', inst[1])
                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                            # for devAddr in [0xE7, 0x63, 0x65, 0x6B, 0xA3, 0xA7, 0xAB, 0xE3,]: 
                                # self.dongle.stm32_dev = inst[0]
                                # self.dongle.stm32_dev_sn = inst[1]
                                self.devAddr = devAddr
                                
                                # self.stm32_bus_chan = 1
                                data = self.readReg(moduleName=product, addr=0x1001)
                                if data == 0x5:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find q68 device 0x%x'%(devAddr))
                                    q68_dev = inst
                                    break
                        #             if i2c_addr is None:
                        #                 raise('q68 not found')
                        # else:
                        #     raise('please connect Q68 to MCU channel 1 !!!')
                    if product=='m2c':
                        if i2c_addr is None:
                            for devAddr in [0x40, 0x44]: 
                                # self.dongle.stm32_dev = inst[0]
                                # self.dongle.stm32_dev_sn = inst[1]
                                self.devAddr = devAddr
                                # self.stm32_bus_chan = 2
                                data = self.readReg(moduleName=product, addr=0x0101)
                                if data == 0x55:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find s68 device 0x%x'%(devAddr))
                                    break
            else:
                if bus=='i2c':

                    devNum = ft4222.createDeviceInfoList()
                    add = [0 for i in range(devNum)]
                    for i in range(devNum):
                        add[i]=ft4222.getDeviceInfoDetail(i, False)
                        print(ft4222.getDeviceInfoDetail(i, False))
                    
                    
                    ft4222_devA=ft4222.openByLocation(location0)
                    ft4222_devB=ft4222.openByLocation(location1)
                    ft4222_devA.setClock(ft4222.SysClock.CLK_80)
                # if MCU=='ft4222':
                #     from QRegisterAccess import *
                # elif MCU=='ftd2xx':
                #     from ftd2xx.QRegisterAccess import *
                
                    if self.findMeritechI2cChan():
                        print ('Bridge board i2c is found!')
                    else:
                        print ('Can not find bridge i2c!') 
                        raise 
                    if product=='m2c':
                        for devAddr in [0x40,0x44]:
                            self.devAddr = devAddr
                            data = self.readReg(moduleName=product, addr=0x0101)
                            if data == 0x55:
                                i2c_addr = devAddr
                                # self.dongle.devAddr = i2c_addr
                                print('find s68 device 0x%x'%(devAddr))
                                break
                        if data == 0x55:
                            dutsearch = location0
                            # break
                    if product=='c2m':
                        for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                            # self.stm32_dev = inst[0]
                            # self.stm32_dev_sn = inst[1]
                            self.devAddr = devAddr
                            data = self.readReg(moduleName=product, addr=0x1001)
                            if data == 0x5:
                                i2c_addr = devAddr
                                # self.dongle.devAddr = i2c_addr
                                print('find q68 device 0x%x'%(devAddr))
                                break
                else:
                    ser = serial.Serial(port, 115200, 8, 'E', 1)
                                         
                if product==None:
                    pass
                
                        
                if self.stm32_dev == None:
                    raise BaseException('unable to find specified stm32 dongle')

        
    def __del__(self):
        global stm32_inst_list
        if stm32_inst_list != None:
            if self.dongle == 'stm32':
                for i in range(len(stm32_inst_list)):
                    print("closing stm32 device %d (sn = %s)"%(i, stm32_inst_list[i][1]))
                    stm32_inst_list[i][0].usbdev_close()
                stm32_inst_list = None
    
    def readReg(self, moduleName, addr, crc = False):
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'sim':
                value = self.c2m_regs[addr]
            if ((self.dongle == 'stm32')&(self.bus=='i2c')):
                ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, self.devAddr, addr)
                # print(f"{hex(self.devAddr)},{hex(addr)},R,{hex(value)}")
            if self.dongle == 'ft4222':
                wrBuf = [(addr >> 8) & 0xFF, (addr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
            if ((self.dongle == 'ft4222')&(self.bus=='uart')):
                rdBuf = [(self.devAddr+1), (addr >> 8) & 0xFF, (addr & 0xFF), 0x01]
                ser.write(bytearray(rdBuf))
                count = self.ser.inWaiting() 
                if count > 0:  
                    data = self.ser.read(count)  
                    if data != b'':  
                        
                        value = str(binascii.b2a_hex(data))[4:-1]  
                        # print(hex_data)  
                         
                    else:
                        print("notgood")
            
                else:
                    value = str("none")  
                    print(value)
            if ((self.dongle == 'stm32')&(self.bus=='uart')&(self.uarttype==0)): 
                
                self.mcuUartSetMode(index=self.stm32_bus_chan, rate=self.bitrate, stop=1.0, parity=1, hwflow=0)
                # time.sleep(0.02)
                ret, rx_buffer = self.uartTransfer(moduleName, self.stm32_bus_chan, [((self.devAddr<<1)+1), (addr>>8)&0xff, addr&0xff, 0x01], rx_len=2, rx_timeout=0xFF)
                try: 
                    value = rx_buffer[1]
                except:
                    value = -1
                
            if ((self.dongle == 'stm32')&(self.bus=='uart')&(self.uarttype==1)): 
                
                self.mcuUartSetMode(index=self.stm32_bus_chan, rate=self.bitrate, stop=1.0, parity=1, hwflow=0)
                # time.sleep(0.02)
                ret, rx_buffer = self.uartTransfer(moduleName, self.stm32_bus_chan, [0x79, ((self.devAddr<<1)+1), (addr>>8)&0xff, addr&0xff, 0x01], rx_len=2, rx_timeout=0xFF)
                try: 
                    value = rx_buffer[1]
                except:
                    value = -1    
            if save_data:
                with open(data_addr, "a", encoding="utf-8", newline="") as f:
                    csv_writer = csv.writer(f)
                    row = [hex(self.devAddr), hex(addr), 'R', hex(value)]
                    csv_writer.writerow(row) 
        else:
            raise BaseException('unknown module')
        return value

    '''............new add I2C_brust and without CRC mode............................'''
    def readMuiltReg(self, dev_addr, addr, count, moduleName,crc = False):
        
        self.devAddr=dev_addr
        
        if crc==False:
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values = self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
            return values
        
    def readMuiltReg_CRC(self,dev_addr, addr, count, moduleName,crc = True):
    
        self.devAddr=dev_addr
        #print('s68_real_addr = ',s68_real_addr)
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x73:
                addr_crc=0xE6
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            elif self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x24:
                addr_crc=0x68
            elif self.devAddr==0x25:
                addr_crc=0x6a
            elif self.devAddr==0x26:
                addr_crc=0x6c
            elif self.devAddr==0x27:
                addr_crc=0x6e
            elif self.devAddr==0x35:
                addr_crc=0x6A
            elif self.devAddr==0x51:
                addr_crc=0xA2
            elif self.devAddr==0x53:
                addr_crc=0xA6
            elif self.devAddr==0x55:
                addr_crc=0xAA
            elif self.devAddr==0x71:
                addr_crc=0xE2
              
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
            
        return values
    
    def writeMuiltS68Reg_CRC_crc0is8bitdevaddr(self, s68_real_addr,dev_addr, addr, values:list, moduleName, crc = True):
        
        self.devAddr=dev_addr
        
        if s68_real_addr == 0x44:
            s68_real_addr_crc0 = 0x88
        elif s68_real_addr == 0x40:
            s68_real_addr_crc0 = 0x80
        
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            
            if len(values)  <2:
                
                crc0=self.crc8([s68_real_addr_crc0,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
                return True
                  
            if len(values)  ==3:
                
                crc0=self.crc8([s68_real_addr_crc0,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                # crc3=self.crc8([values[3]])
                # crc4=self.crc8([values[4]])
                # crc5=self.crc8([values[5]])
                # crc6=self.crc8([values[6]])
                # crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2
            
            if len(values)  ==8:
                
                crc0=self.crc8([s68_real_addr_crc0,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                crc3=self.crc8([values[3]])
                crc4=self.crc8([values[4]])
                crc5=self.crc8([values[5]])
                crc6=self.crc8([values[6]])
                crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
            
        else:
                raise BaseException('unknown module')
    
    def writeMuiltS68Reg_CRC(self, dev_addr, addr, values:list, moduleName, crc = True):
        
        self.devAddr=dev_addr
        
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            
            if len(values)  <2:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
                return True
                  
            if len(values)  ==8:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                crc3=self.crc8([values[3]])
                crc4=self.crc8([values[4]])
                crc5=self.crc8([values[5]])
                crc6=self.crc8([values[6]])
                crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
            
        else:
                raise BaseException('unknown module')
    
    def readMuiltS68Reg_CRC(self, dev_addr, addr, count:list, moduleName, crc = True):
    
        self.devAddr=dev_addr
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x68
              
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
            
        return values


    def writeMuiltReg(self, dev_addr, addr, values:list, moduleName,crc = False):
        
        self.devAddr = dev_addr
        if crc==False:
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, values)
            else:
                raise BaseException('unknown module')
        return True
        
    def writeMuiltReg_CRC(self, dev_addr, addr, values:list, moduleName,crc = True):
        
        self.devAddr = dev_addr
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x73:
                addr_crc=0xE6
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x35:
                addr_crc=0x6A
            elif self.devAddr==0x51:
                addr_crc=0xA2
            elif self.devAddr==0x53:
                addr_crc=0xA6
            elif self.devAddr==0x55:
                addr_crc=0xAA
            elif self.devAddr==0x71:
                addr_crc=0xE2
            elif self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            
            if len(values)  <2:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
                return True
                  
            if len(values)  ==8:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                crc3=self.crc8([values[3]])
                crc4=self.crc8([values[4]])
                crc5=self.crc8([values[5]])
                crc6=self.crc8([values[6]])
                crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
            
            if len(values)  ==3:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                #crc3=self.crc8([values[3]])
                # crc4=self.crc8([values[4]])
                # crc5=self.crc8([values[5]])
                # crc6=self.crc8([values[6]])
                # crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2
    
        else:
                raise BaseException('unknown module')
    
    ''' .....................end..................................'''

    def I2CreadReg(self, mode=1, bus=2, devAddr=0x44, reg_addr=0x00, crc = False):
        '''
        mode:           0:8bits,   1:16bis register address
        bus: 1/2/3:     MUC I2C1/I2C2/I2C3
        devAddr:        slave address
        reg_addr:       register address
        '''
        if self.dongle == 'stm32':
            ret, value = self.stm32_dev.usbdev_i2c_get(mode=mode, bus=bus, dev_addr=devAddr, reg_addr=reg_addr)
            print('Read register',hex(reg_addr),'value=',hex(value))

        if save_data:
            with open(data_addr, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(self.devAddr), hex(reg_addr), 'R', hex(value)]
                csv_writer.writerow(row) 
                
        return value

    def I2CwriteReg(self, mode=1, bus=2, devAddr=0x44, reg_addr=0x00, value=0x00, crc = False):
        '''
        mode:           0:8bits,   1:16bis register address
        bus: 1/2/3:     MUC I2C1/I2C2/I2C3
        devAddr:        slave address
        reg_addr:       register address
        value:          register value
        '''  
        if self.dongle == 'stm32':
            self.stm32_dev.usbdev_i2c_set(mode=mode, bus=bus, dev_addr=devAddr, reg_addr=reg_addr,reg_val=value)

        if save_data:
            with open(data_addr, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(self.devAddr), hex(reg_addr), 'W', hex(value)]
                csv_writer.writerow(row)
                
        return value

    def writeReg(self, moduleName, addr, value, crc=False):
        if moduleName=='c2m' or moduleName == 'm2c':
            if ((self.dongle == 'sim')&(self.bus=='i2c')):
                self.c2m_regs[addr] = value
            if ((self.dongle == 'stm32')&(self.bus=='i2c')):
                self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, self.devAddr, addr, value)
                # print(f"{hex(self.devAddr)},{hex(addr)},W,{hex(value)}")
            if self.dongle == 'ft4222':
                wrBuf = [(addr >> 8) & 0xFF, (addr & 0xFF), value & 0xFF]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
            
            if ((self.dongle == 'ft4222')&(self.bus=='uart')):
                wrBuf = [(self.devAddr), (addr >> 8) & 0xFF, (addr & 0xFF), 0x01, value]
                ser.write(bytearray(wrBuf))
                
            if ((self.dongle == 'stm32')&(self.bus=='uart')&(self.uarttype==0)): 
                self.mcuUartSetMode(index=self.stm32_bus_chan, rate=self.bitrate, stop=1.0, parity=1, hwflow=0)
                # time.sleep(0.02)(devAddr<<1)+1
                ret, rx_buffer = self.uartTransfer(moduleName, self.stm32_bus_chan, [(self.devAddr<<1), (addr>>8)&0xff, addr&0xff, 0x01, value], rx_len=1, rx_timeout=0xFF)
            
            if ((self.dongle == 'stm32')&(self.bus=='uart')&(self.uarttype==1)): 
                self.mcuUartSetMode(index=self.stm32_bus_chan, rate=self.bitrate, stop=1.0, parity=1, hwflow=0)
                # time.sleep(0.02)(devAddr<<1)+1
                ret, rx_buffer = self.uartTransfer(moduleName, self.stm32_bus_chan, [0x79, (self.devAddr<<1), (addr>>8)&0xff, addr&0xff, 0x01, value], rx_len=1, rx_timeout=0xFF)
                
            if save_data:
                with open(data_addr, "a", encoding="utf-8", newline="") as f:
                    csv_writer = csv.writer(f)
                    row = [hex(self.devAddr), hex(addr), 'W', hex(value)]
                    csv_writer.writerow(row)
        else:
            raise BaseException('unknown module')
        return True

    ''' s68 remote'''
    def M66S68I2CWrite_normal_without_crc(self, regAddr, values:list, moduleName,crc = False):
    
        if moduleName =='m2c' or moduleName =='c2m' :
    
            if self.dongle == 'stm32':
                self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, regAddr, values)
    
            if self.dongle == 'ft4222':
                if ft4222_devA != None:
                    wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                    ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        return True
    
    def M66S68I2CRead_normal_without_crc(self, regAddr, count:int, moduleName,crc = False):
    
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, values = self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, regAddr, count)
            if self.dongle == 'ft4222':
                value = 0
                if ft4222_devA != None:
                    wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                    ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                    rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                    value = rdBuf[0]
        return value
    
    def M66S68I2CRead_normal_with_crc(self, addr, count:int ,moduleName, crc = True):
    
        if crc==True:
            packetcounter=0
    
            if self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x30:
                addr_crc=0xc0
            elif self.devAddr==0x31:
                addr_crc=0xc2
            elif self.devAddr==0x32:
                addr_crc=0xc4
            elif self.devAddr==0x33:
                addr_crc=0xc8
    
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
    
        return values
    
    
    # def writeMuiltReg(self, moduleName, addr, values:list, crc = False):
    #
    #     if crc==False:
    #         if moduleName=='c2m' or moduleName == 'm2c':
    #             if self.dongle == 'stm32':
    #                 self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, values)
    #         else:
    #             raise BaseException('unknown module')
    #     return True
    #
    #
    # def writeMuiltReg_CRC(self, moduleName, addr, values:list, crc = True):
    #
    #     if crc==True:
    #         packetcounter=0
    #
    #         if self.devAddr==0x73:
    #             addr_crc=0xE6
    #         elif self.devAddr==0x31:
    #             addr_crc=0x62
    #         elif self.devAddr==0x32:
    #             addr_crc=0x64
    #         elif self.devAddr==0x35:
    #             addr_crc=0x6A
    #         elif self.devAddr==0x51:
    #             addr_crc=0xA2
    #         elif self.devAddr==0x53:
    #             addr_crc=0xA6
    #         elif self.devAddr==0x55:
    #             addr_crc=0xAA
    #         elif self.devAddr==0x71:
    #             addr_crc=0xE2
    #
    #         if len(values)  <2:
    #
    #             crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
    #             wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
    #             if moduleName=='c2m' or moduleName == 'm2c':
    #                 if self.dongle == 'stm32':
    #                     self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
    #                 else:
    #                     raise BaseException('unknown module')
    #             return True
    #
    #         if len(values)  ==8:
    #
    #             crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
    #             crc1=self.crc8([values[1]])
    #             crc2=self.crc8([values[2]])
    #             crc3=self.crc8([values[3]])
    #             crc4=self.crc8([values[4]])
    #             crc5=self.crc8([values[5]])
    #             crc6=self.crc8([values[6]])
    #             crc7=self.crc8([values[7]])
    #
    #             wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
    #
    #             if moduleName=='c2m' or moduleName == 'm2c':
    #                 if self.dongle == 'stm32':
    #                     self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
    #             else:
    #                 raise BaseException('unknown module')
    #
    #             return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
    #
    #         if len(values)  ==11:
    #
    #             crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
    #             crc1=self.crc8([values[1]])
    #             crc2=self.crc8([values[2]])
    #             crc3=self.crc8([values[3]])
    #             crc4=self.crc8([values[4]])
    #             crc5=self.crc8([values[5]])
    #             crc6=self.crc8([values[6]])
    #             crc7=self.crc8([values[7]])
    #             crc8=self.crc8([values[7]])
    #             crc9=self.crc8([values[8]])
    #             crc10=self.crc8([values[10]])
    #
    #             wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7,values[8],crc8,values[9],crc9,values[10],crc10]
    #
    #             if moduleName=='c2m' or moduleName == 'm2c':
    #                 if self.dongle == 'stm32':
    #                     self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
    #             else:
    #                 raise BaseException('unknown module')
    #
    #             return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7,crc8,crc9,crc10
    #
    #     else:
    #             raise BaseException('unknown module')
    #


    
        
    '''......end......'''
    
    def M66S68I2CWrite_normal(self, regAddr, value):
        
        if self.dongle == 'stm32':
            self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, self.devAddr, regAddr, value)
            
        if self.dongle == 'ft4222':
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        if save_data:
            with open(data_addr, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(self.devAddr), hex(regAddr), 'W', hex(value)]
                csv_writer.writerow(row)
        return True
    
    def M66S68I2CRead_normal(self, regAddr):
        
        #if moduleName=='c2m' or moduleName == 'm2c':
        if self.dongle == 'stm32':
            ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, self.devAddr, regAddr)
        if self.dongle == 'ft4222':
            value = 0
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
        if save_data:
            with open(data_addr, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(self.devAddr), hex(regAddr), 'R', hex(value)]
                csv_writer.writerow(row)
        return value
    
    def M66S68I2CWrite_CRC(self, regAddr, value):
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF), value)
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        return True
    
    def M66S68I2CRead_CRC(self, regAddr):
        value = 0
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF))
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
            rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 2)
            value = rdBuf[0]
        return value
    
    def M65Q68I2CWrite_normal(self, regAddr, value):
    
        if self.dongle == 'stm32':
            self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, self.devAddr, regAddr, value)
            
        if self.dongle == 'ft4222':
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        
        if save_data:
            with open(data_addr, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(self.devAddr), hex(regAddr), 'W', hex(value)]
                csv_writer.writerow(row)
        return True
    
    def M65Q68I2CRead_normal(self, regAddr):
       
        if self.dongle == 'stm32':
            ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, self.devAddr, regAddr)
            
        if self.dongle == 'ft4222':
            
            value = 0
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
                
        if save_data:
            with open(data_addr, "a", encoding="utf-8", newline="") as f:
                csv_writer = csv.writer(f)
                row = [hex(self.devAddr), hex(regAddr), 'R', hex(value)]
                csv_writer.writerow(row)
        return value
    
    def M65Q68I2CWrite_CRC(self, regAddr, packetcounter,value):
        
        '''
            Q68 dev_addr(7bit) = 0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71
            addr_crc(8bit) syntax: remove bit7, and add 0 into bit0
        '''
        if self.devAddr==0x73:
            addr_crc=0xE6
        elif self.devAddr==0x31:
            addr_crc=0x62
        elif self.devAddr==0x32:
            addr_crc=0x64
        elif self.devAddr==0x35:
            addr_crc=0x6A
        elif self.devAddr==0x51:
            addr_crc=0xA2
        elif self.devAddr==0x53:
            addr_crc=0xA6
        elif self.devAddr==0x55:
            addr_crc=0xAA
        elif self.devAddr==0x71:
            addr_crc=0xE2
        crc=self.crc8([addr_crc,(regAddr >> 8) & 0xFF, (regAddr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), value])
        
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF),value,crc)
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        return True
    
    def M65Q68I2CRead_CRC(self, regAddr):
            
        value = 0
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF))
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
            rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 4)
            value = rdBuf[2]
        return (rdBuf[0],rdBuf[1],rdBuf[2],rdBuf[3])
    
    def cal_crc(self, data):
        '''
        计算1字节数据的CRC8
        '''
        crc = data
        poly = 0x07    # 多项式x^8 + x^2 + x^1 + 1，即0x107，（根据原理）省略了最高位1而得0x07
        for i in range(8,0,-1):
            if ((crc & 0x80) >> 7) == 1:    # 判断最高位是否为1，如果是需要异或，否则仅左移
                crc = (crc << 1) ^ poly;
            else:
                crc = (crc << 1)
        return crc & 0xFF    # 计算后需要进行截取
     
    def crc8(self, datas):
        '''
        计算数据的CRC8校验码
        '''
        length = len(datas)
        crc = 0xFF;
        for i in range(length):
            # if i == 0:
            #     crc = crc ^ datas[0]     # 先计算第1个数据的CRC8
            #     crc = cal_crc(crc)
            # else:
            crc = (crc ^ datas[i]) & 0xFF    # 其余的均将上次的CRC8结果与本次数据异或
            crc = self.cal_crc(crc)               # 再计算CRC8
        return crc & 0xFF
    
    
    def initGpio(self, index, mode, pull):
        if self.dongle == 'stm32':
            # index: [0~23]
            # mode: [0:input 1:output_pp 2:output_od 3:ad_pp 4:ad_od]
            # pull: [0:nopull 1:pullup 2:pulldown]
            self.stm32_dev.usbdev_init_gpio(index, mode, pull)
    
    def setGpio(self, index, value):
        if self.dongle == 'stm32':
            # index: [0~23]
            self.stm32_dev.usbdev_set_gpio(index, value)
    
    def getGpio(self, index):
        value = 0
        if self.dongle == 'stm32':
            # index: [0~23]
            value = self.stm32_dev.usbdev_get_gpio(index)
        return value
    
# 清除gpio对应的中断次数(GPIO作为中断脚时才有用)
    def clearGpioTiCount(self, index):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_clear_gpio_ticount(index)
        return ret
    
    # 获取gpio对应的中断次数(GPIO作为中断脚时才有用)
    def getGpioTiCount(self, index):
        value = 0
        if self.dongle == 'stm32':
            ret, value = self.stm32_dev.usbdev_get_gpio_ticount(index)
        return value
    def resetDongle(self):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_mcu_reset()
            if ret < 0:
                print("failed to reset stm32.")
            else:
                print("reset stm32 successfully.")
                self.stm32_dev.uninitUsbDev()
        return ret

    def setBusSpeed(self, bus, speed):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_i2c_setbusspeed(bus, speed)
            if ret < 0:
                print("failed to set stm32 bus speed.")
            else:
                print("")
        return ret
    
    # 设置I2C 读过程中是否有stop, 0:无stop， 1:有stop
    def setReadStopFlag(self, stop_flag:int = 0):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_i2c_set_readstop_flag(stop_flag)
            if ret < 0:
                print("failed to set stm32 bus speed.")
            else:
                print("")
        return ret

    def findMeritechI2cChan(self):
        # init i2c master
        ft4222_devA.i2cMaster_Init(500)  # 10kHz
        return True
    def M66S68PCII2CWrite_normal(self, pcidevAddr,regAddr, value):
        # print('pcidevAddr is: ', pcidevAddr) 
       
        moduleName = 'm2c'
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, pcidevAddr, regAddr, value)
            if self.dongle == 'ft4222':
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                self.ft4222_devA.i2cMaster_WriteEx(pcidevAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
            if ((self.dongle == 'ft4222')&(self.bus=='uart')):
                rdBuf = [(self.pcidevAddr+1), (regAddr >> 8) & 0xFF, (regAddr & 0xFF), 0x01]
                ser.write(bytearray(rdBuf))
                count = self.ser.inWaiting() 
                if count > 0:  
                    data = self.ser.read(count)  
                    if data != b'':  
                        
                        value = str(binascii.b2a_hex(data))[4:-1]  
                        # print(hex_data)  
                         
                    else:
                        print("notgood")
            
                else:
                    value = str("none")  
                    print(value)
            if save_data:
                with open(data_addr, "a", encoding="utf-8", newline="") as f:
                    csv_writer = csv.writer(f)
                    row = [hex(pcidevAddr), hex(regAddr), 'W', hex(value)]
                    csv_writer.writerow(row) 
        else:
            raise BaseException('unknown module')
        return True
    
    def M66S68PCII2CRead_normal(self, pcidevAddr, regAddr):
        
        moduleName = 'm2c'
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, pcidevAddr, regAddr)
            if self.dongle == 'ft4222':
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(pcidevAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(pcidevAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
            if save_data:
                with open(data_addr, "a", encoding="utf-8", newline="") as f:
                    csv_writer = csv.writer(f)
                    row = [hex(pcidevAddr), hex(regAddr), 'R', hex(value)]
                    csv_writer.writerow(row) 
        else:
            raise BaseException('unknown module')
        return value

    def M66S68PCII2CWrite_crc(self, pcidevAddr,regAddr, value,crcmoduleName, addr, values:list, crc = True):
        # print('pcidevAddr is: ', pcidevAddr) 
       
        moduleName = 'm2c'
        self.devAddr=pcidevAddr
        if crc==True:
            packetcounter=0
            
            if self.pcidevAddr==0x30:
                addr_crc=0x60
            elif self.pcidevAddr==0x31:
                addr_crc=0x62
            elif self.pcidevAddr==0x32:
                addr_crc=0x64
            elif self.pcidevAddr==0x33:
                addr_crc=0x66
            elif self.pcidevAddr==0x24:
                addr_crc=0x48
            elif self.devAddr==0x25:
                addr_crc=0x4a
            elif self.devAddr==0x26:
                addr_crc=0x4c
            elif self.devAddr==0x27:
                addr_crc=0x4e
            
            if len(values)  <2:
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, reg_addr=addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
        else:
            raise BaseException('unknown module')
        
        return True
    
    def M66S68PCII2CRead_crc(self, moduleName,pcidevAddr, addr,count,crc):
        
        self.devAddr=pcidevAddr
        reg_addr=addr
        if crc==True:
            packetcounter=0
            
            if pcidevAddr==0x30:
                addr_crc=0x60
            elif pcidevAddr==0x31:
                addr_crc=0x62
            elif pcidevAddr==0x32:
                addr_crc=0x64
            elif pcidevAddr==0x33:
                addr_crc=0x66
            elif pcidevAddr==0x24:
                addr_crc=0x48
            elif pcidevAddr==0x25:
                addr_crc=0x4a
            elif pcidevAddr==0x26:
                addr_crc=0x4c
            elif pcidevAddr==0x27:
                addr_crc=0x4e
                
            elif pcidevAddr==0x36:
                addr_crc=0x6c
            elif pcidevAddr==0x1A:
                addr_crc=0x34
            elif pcidevAddr==0x10:
                addr_crc=0x20
              
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, reg_addr, count)
            else:
                raise BaseException('unknown module')
            
        return values

    def Q68SPIInitGpio(self):
        self.stm32_dev.usbdev_init_gpio(pin_num=0, pin_mode=1, pin_pull=0) #set gpio0 as RO input
        self.stm32_dev.usbdev_init_gpio(pin_num=1, pin_mode=0, pin_pull=0) #set gpio1 as BNE output
    
    def Q68SPIMasterWrite(self,addr=0,value=[]):

        self.stm32_dev.usbdev_set_gpio(pin_num=0, pin_val=0) #set RO to 0 to clear spi read buffer
        time.sleep(0.02)
        self.stm32_dev.usbdev_set_gpio(pin_num=0, pin_val=1) #set RO to 1 to set local spi cmd
        time.sleep(0.02)
        self.SPIC3InbandControl(addr=0xA0,value=[]) # set spi id
        self.SPIC3InbandControl(addr=0xA5,value=[]) # set spi ss2 to lowyou
        time.sleep(0.02)
        self.stm32_dev.usbdev_set_gpio(pin_num=0, pin_val=0) # set RO to 0 to remote spi master send cmd
        
        time.sleep(0.02)   
        ret, rdBuf = self.SPIC3InbandControl(addr=addr,value=value)   #s68 spi master send cmd
        print('ret & rdBuf is: ', ret, rdBuf)
        
        self.stm32_dev.usbdev_set_gpio(pin_num=0, pin_val=1) # set RO to 1 to get data from local spi
        time.sleep(0.02)
        self.SPIC3InbandControl(addr=0xA6) # deassert spi ss
        result = []
        delaycount=0
        while True:
            time.sleep(0.8)
            delaycount+=1
            if delaycount>20:
                break  
            (gpio,gpiostatus)= self.stm32_dev.usbdev_get_gpio(pin_num=1) 
            # print(gpio,gpiostatus)   
            if (gpiostatus):
                datar = self.SPIC3InbandControl(addr=0xff,value=[])
                data1=datar[1]
                result.append(data1[0])
            else:
                break
        print('spi readback is: ', result)
        return result

    def Q68SPISlavePreWrite(self,addr=0,value=[]):
        num=len(value)
        print(num)
        wrBuf = [0]*(num+1)
        for i in range(num+1):
            if i==0:
                wrBuf[0]=(addr&0xff)
            else:
                wrBuf[i] = value[i-1]
        print(wrBuf)
                    
        ret = self.stm32_dev.usbdev_spi_slave_t(index=4, tx_buffer= bytearray(wrBuf), len=len(wrBuf))   #s68 spi master send cmd
        return (ret)
         
    def Q68SPISlaveR(self,num,):
        
        ret, rdBuf = self.stm32_dev.usbdev_spi_slave_r(index=4, len=num)
        data=rdBuf.hex()
        dataf = [0]*(ret)
        for i in range(int(len(data)/2)):
            dataf[i] = int((data[2*i]+data[2*i+1]),16)
            
        
        return (ret, dataf)
    
    def SPIC3InbandControl(self, addr=0,value=[]):
        num=len(value)
        # print (num)
        wrBuf = [0]*(num+1)
        for i in range(num+1):
            if i==0:
                wrBuf[0]=(addr&0xff)
            else:
                wrBuf[i] = value[i-1]
        # print(wrBuf)
        ret, rdBuf = self.stm32_dev.usbdev_spi_master_tr(5, bytearray(wrBuf), len(wrBuf))
        data=rdBuf.hex()
        dataf = [0]*(ret)
        for i in range(ret):
            dataf[i] = int((data[2*i]+data[2*i+1]),16)
            
        
        return (ret, dataf)
    
    # Uart接口交互，传入需要发送的数据，将返回的数据接收回来
    # index：那一路Uart tx_buffer，需要发送的数据 
    # return: ret,rx_buffer,  第一个值是ret值，第二个值为获取的rx_buffer
    def uartTransfer(self, moduleName, index: int, tx_buffer: list, rx_len:int=2, rx_timeout:int=0x10):
        rx_buffer = []
        ret = 0
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                # mode:0:r8v8 1:r16v8
                ret, rx_buffer = self.stm32_dev.usbdev_uart_transfer(index, tx_buffer, rx_len, rx_timeout)
                if ret < 0:
                    print("uartTransfer Error:", )
        else:
            ret = -1
            raise BaseException('unknown module')
        return ret, rx_buffer

    # 控制debug状态及接口
    # debug_en:是否使能调试信息， uart_port:调试的串口选择
    def mcuSetDebugStatus(self, debug_en: int, uart_port: int, debug_level:int = 1):
        ret = 0
        if self.dongle == 'stm32':
            # mode:0:r8v8 1:r16v8
            ret = self.stm32_dev.usbdev_set_debug_status(debug_en, uart_port, debug_level)
            if ret < 0:
                print("mcuSetDebugStatus Error:", )
        return ret

    # 设置串口模式
    # default:115200, stop:1 parity:None hw:None
    # rate:(baudrate) stop(STOPBITS 0.5 1.0 1.5 2.0)  parity(0:None 1:EVEN 2:ODD) hwflow(0:None 1:RTS 2:CTS 3:RTS_CTS)
    def mcuUartSetMode(self, index:int, rate:int, stop:float, parity:int, hwflow:int):
        ret = 0
        if self.dongle == 'stm32':
            # print('bitrate is: ', rate)
            # mode:0:r8v8 1:r16v8
            ret = self.stm32_dev.usbdev_set_uart_mode(index, rate, stop, parity, hwflow)
            if ret < 0:
                print("mcuUartSetMode Error:", )
        return ret


    def readUartR16V8(self, moduleName, bus, devAddr, regAddr):
        tx_buffer = [(devAddr<<1)+1, regAddr>>8&0xFF, regAddr&0xFF, 0x01]
        rx_buffer = [-1, -1]
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, rx_buffer = self.uartTransfer(self.stm32_bus_chan, tx_buffer, 2, rx_timeout=0x10)
                if ret != 0 or rx_buffer[0] != 0xC3:
                    print("Uart Reg Get Error:", " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr, " rx_buffer[0]:", rx_buffer[0])
        else:
            raise BaseException('unknown module')
            
        return rx_buffer[1]
    
    def writeUartR16V8(self, moduleName, bus, devAddr, regAddr, regValue):
        tx_buffer = [(devAddr<<1), regAddr>>8&0xFF, regAddr&0xFF, 0x01, regValue&0xFF]
        rx_buffer = [-1]
        ret = 0
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, rx_buffer = self.uartTransfer(bus, tx_buffer, 1, rx_timeout=0x10)
                if ret != 0 or rx_buffer[0] != 0xC3:
                    print("Uart Reg Set Error:", " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr, " rx_buffer[0]:", rx_buffer[0])
        else:
            ret = -1
            raise BaseException('unknown module')
    
        return ret != 0 and rx_buffer[0] != 0xC3
    
    
    def readUartMuiltR16V8(self, moduleName, bus, devAddr, regAddr, rx_count: int):
        tx_buffer = [(devAddr<<1)+1, regAddr>>8&0xFF, regAddr&0xFF, rx_count]
        rx_buffer = [-1]
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                # 1Byte(0xC3)+rx_count*Byte
                ret, rx_buffer = self.uartTransfer(bus, tx_buffer, rx_count+1, rx_timeout=0x80)
                if ret != 0 or rx_buffer[0] != 0xC3:
                    print("Uart Reg Muilt Get Error:", " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr, " rx_buffer[0]:", rx_buffer[0])
        else:
            ret = -1
            raise BaseException('unknown module')

        return rx_buffer[1:]
    
    
    def writeUartMuiltR16V8(self, moduleName, bus, devAddr, regAddr, regValues: list):
        tx_buffer = [(devAddr<<1), regAddr>>8&0xFF, regAddr&0xFF, len(regValues)]
        for value in regValues:  
            tx_buffer.append(value) 
        rx_buffer = [-1]
        ret = 0
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, rx_buffer = self.uartTransfer(bus, tx_buffer, 1, rx_timeout=0x80)
                if ret != 0 or rx_buffer[0] != 0xC3:
                    print("Uart Reg Muilt Set Error:", " bus=",bus, " devAddr=", devAddr, " regAddr=", regAddr, " rx_buffer[0]:", rx_buffer[0])
        else:
            ret = -1
            raise BaseException('unknown module')
    
        return ret != 0 and rx_buffer[0] != 0xC3   