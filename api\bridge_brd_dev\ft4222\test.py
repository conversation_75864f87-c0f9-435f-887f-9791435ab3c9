
addr=6

addrbin=bin(addr)
list=0
addrbin=addrbin[2:]
print (addrbin)
lenabs=len(addrbin)%8

print (lenabs)
if lenabs == 0:
    pass
else:
    for i in range(16-lenabs):
        addrbin='0'+addrbin
print (addrbin)
for i in range(0,len(addrbin),1):
    list+=int(addrbin[len(addrbin)-1-i])*(2**(len(addrbin)-1-i))
print (list)
print(bin(list))

addr=6
addrbin=bin(addr)
addrlsbf=0
addrbin=addrbin[2:]
print (addrbin)
lenabs=len(addrbin)%8
print (lenabs)
if lenabs == 0:
    pass
else:
    for i in range(8-lenabs):
        addrbin='0'+addrbin
print (addrbin)
for i in range(0,len(addrbin),1):
    addrlsbf+=int(addrbin[len(addrbin)-1-i])*(2**(len(addrbin)-1-i))  
    
print (addrlsbf)  
addrac=(2<<14) + addrlsbf
print (addrac)