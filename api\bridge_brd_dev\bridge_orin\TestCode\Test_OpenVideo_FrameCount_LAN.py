#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import HWImpl

# SSHFileCtrl需要安装下面模块
# pip3.9.exe install paramiko
from HWInterface import SSHFileCtrl 

import time

if __name__ == '__main__':
    # 使用第一个设备
    # HWImpl.hw_open(ip_addr="*************") # wifi
    HWImpl.hw_open(ip_addr="*************") # wired LAN, using a IP Router to connect both Orin and PC togger

    ret = HWImpl.hw_waithw_ok(100)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")
 
    
    ''' open Video(MIPI), when prepared for Q68 data capture'''
    ret = HWImpl.hw_open_video(0)   # open video window 0~13, every CSIx has 0~3, max 4CSI
    ret = HWImpl.hw_open_video(4)
    ret = HWImpl.hw_open_video(8)
    ret = HWImpl.hw_open_video(12)

    '''  Q68 MIPI output'''
    
    
    
    
    
    

    ''' get video frame count '''
    for i in range(5):
        video0 = HWImpl.hw_get_video_info(channel = 0)
        video1 = HWImpl.hw_get_video_info(channel = 4)
        video2 = HWImpl.hw_get_video_info(channel = 8)
        video3 = HWImpl.hw_get_video_info(channel = 12)
        time.sleep(1)

    ret = HWImpl.hw_close_video(0)
    ret = HWImpl.hw_close_video(4)
    ret = HWImpl.hw_close_video(8)
    ret = HWImpl.hw_close_video(12)


    time.sleep(1)
 
                                                                   
    ''' reboot Orin, when test finished, ~80seconds'''                                               
    HWImpl.hw_reboot()   # reboot Orin, when re-send csi data        
                                                                                                                                   

    
    # 关闭设备
    # HWImpl.hw_close()
