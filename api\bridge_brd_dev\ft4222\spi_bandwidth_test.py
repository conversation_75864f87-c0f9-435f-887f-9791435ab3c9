# packages
import QRegisterAccess
import time

# ft4222 (loop = 1024, size = 1024 * 5)
#     ~0.57Mbytes/s for 40MHz SPI clock
#     ~0.34Mbytes/s for 10MHz SPI clock
#     ~0.26Mbytes/s for  5MHz SPI clock

if QRegisterAccess.findMeritechSpiChan():
    t0 = time.time()
    loop = 1024
    size = 1024 * 5
    for i in range(loop):
        QRegisterAccess.spiReadC3(0x0, size)
    t1 = time.time()
    d  = t1 - t0
    print("spi read speed: %.3f Mbytes/s"%(1.0*loop*size/1024/1024/d)) 
