#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import HWImpl
import time

if __name__ == '__main__':
    # 使用第一个设备
    HWImpl.hw_open(ip_addr="*************")

    ret = HWImpl.hw_waithw_ok(100)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")


    ret = HWImpl.hw_set_csi_param(csiindex = 0, csiport = 6, phy_mode = 0, bus_width = 1, discontinuous_clk = 1, pix_clk_hz = 74100000, serdes_pix_clk_hz = 116250000)
    if ret == 0:
        print("send_set_csi_param ok")
    else:
        print("send_set_csi_param error")

    ret = HWImpl.hw_set_csi_param(csiindex = 1, csiport = 4, phy_mode = 1, bus_width = 2, discontinuous_clk = 1, pix_clk_hz = 74200000, serdes_pix_clk_hz = 126250000)
    if ret == 0:
        print("send_set_csi_param ok")
    else:
        print("send_set_csi_param error")

    ret = HWImpl.hw_set_csi_param(csiindex = 2, csiport = 2, phy_mode = 0, bus_width = 3, discontinuous_clk = 1, pix_clk_hz = 74300000, serdes_pix_clk_hz = 136250000)
    if ret == 0:
        print("send_set_csi_param ok")
    else:
        print("send_set_csi_param error")

    ret = HWImpl.hw_set_csi_param(csiindex = 3, csiport = 0, phy_mode = 1, bus_width = 4, discontinuous_clk = 1, pix_clk_hz = 74400000, serdes_pix_clk_hz = 146250000)
    if ret == 0:
        print("send_set_csi_param ok")
    else:
        print("send_set_csi_param error")


    ret = HWImpl.hw_set_csi_channel_param(csiindex = 0, channel = 0, vcid = 0, active_w = 1001, active_h = 1002, line_length = 1003, data_fmt = 0)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 0, channel = 1, vcid = 1, active_w = 1004, active_h = 1005, line_length = 1006, data_fmt = 1)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 0, channel = 2, vcid = 2, active_w = 1007, active_h = 1008, line_length = 1009, data_fmt = 11)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 0, channel = 3, vcid = 3, active_w = 1010, active_h = 1011, line_length = 1012, data_fmt = 12)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 1, channel = 0, vcid = 0, active_w = 1013, active_h = 1014, line_length = 1015, data_fmt = 20)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 1, channel = 1, vcid = 1, active_w = 1016, active_h = 1017, line_length = 1018, data_fmt = 0)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 1, channel = 2, vcid = 2, active_w = 1019, active_h = 1020, line_length = 1021, data_fmt = 1)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 1, channel = 3, vcid = 3, active_w = 1022, active_h = 1023, line_length = 1024, data_fmt = 11)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 2, channel = 0, vcid = 0, active_w = 1025, active_h = 1026, line_length = 1027, data_fmt = 12)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 2, channel = 1, vcid = 1, active_w = 1028, active_h = 1029, line_length = 1030, data_fmt = 20)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 2, channel = 2, vcid = 2, active_w = 1031, active_h = 1032, line_length = 1033, data_fmt = 0)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 2, channel = 3, vcid = 3, active_w = 1034, active_h = 1035, line_length = 1036, data_fmt = 1)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 3, channel = 0, vcid = 0, active_w = 1037, active_h = 1038, line_length = 1039, data_fmt = 11)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 3, channel = 1, vcid = 1, active_w = 1040, active_h = 1041, line_length = 1042, data_fmt = 12)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 3, channel = 2, vcid = 2, active_w = 1043, active_h = 1044, line_length = 1045, data_fmt = 20)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")
    ret = HWImpl.hw_set_csi_channel_param(csiindex = 3, channel = 3, vcid = 3, active_w = 1046, active_h = 1047, line_length = 1048, data_fmt = 0)
    if ret == 0:
        print("hw_set_csi_channel_param ok")
    else:
        print("hw_set_csi_channel_param error")

    ret = HWImpl.hw_save_mipi_param()
    if ret == 0:
        print("hw_save_mipi_param ok")
    else:
        print("hw_save_mipi_param error")




    # 重启，并等待设备启动完成
    ret = HWImpl.hw_reboot()
    if ret == 0:
        print("hw_save_mipi_param ok")
    else:
        print("hw_save_mipi_param error")
    
    
    ret = HWImpl.hw_waithw_ok(100)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")

    # 关闭设备
    HWImpl.hw_close()
