#!/usr/bin/env python3  
# -*- coding: utf-8 -*-  
# 用于交互中的指令响应  

import subprocess  

class Device:  
    def __init__(self, name, serial_number):  
        self.name = name  
        self.serial_number = serial_number  

class AdbFunction:  
    def __init__(self, adb_path='adb'):  
        self.adb_path = adb_path  
        self.adb_port = None  

    def __del__(self):  
        pass  

    def adb_open(self, port_name):  
        self.adb_port = port_name  
        print(f"adb {self.adb_port} open")  
        return 0  

    def adb_close(self):  
        self.adb_port = None  
        print(f"adb {self.adb_path} close")  
    
    def get_connected_devices(self):  
        try:  
            # 执行 adb devices 命令  
            result = subprocess.run(  
                [self.adb_path, 'devices'],  
                capture_output=True,  
                text=True,  
                check=True  
            )  
            
            # 获取命令输出并分割为行  
            output_lines = result.stdout.strip().split('\n')  
            
            # 解析设备列表，跳过第一行标题  
            adbdev_list:list = []
            for line in output_lines[1:]:  
                if line.strip():  # 确保行不为空  
                    parts = line.split()  
                    if len(parts) == 2 and parts[1] == 'device':  
                        adbdev_list.append(Device(parts[0], parts[0]))
            
            return adbdev_list  
    
        except subprocess.CalledProcessError as e:  
            print(f"ADB 命令失败: {e}")  
            return []  

    def get_sn(self):  
        return self.adb_port is not None, self.adb_port  

    def _run_adb_command(self, command):  
        try:  
            result = subprocess.run(  
                [self.adb_path, '-s', self.adb_port, 'shell', command],  
                capture_output=True,  
                text=True,  
                check=True  
            )  
            return result.stdout.strip()  
        except subprocess.CalledProcessError as e:  
            print(f"ADB 命令失败: {e}")  
            return None  

    def i2c_set(self, mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):  
        high_byte = (reg_addr >> 8) & 0xFF  
        low_byte = reg_addr & 0xFF  
        command = f'i2ctransfer -y -f {str(bus)} w3@0x{dev_addr:02X} 0x{high_byte:02X} 0x{low_byte:02X} 0x{reg_val:02X}'   
        output = self._run_adb_command(command)  
        if output is not None and output == "":  
            print(f"w_r16v8,0x{dev_addr:02X},0x{reg_addr:04X},0x{reg_val:02X}")  
            return 0  
        else:  
            print(f"adb shell i2c set error: w2@0x{dev_addr:02X} 0x{reg_addr:04X} 0x{reg_val:02X}")  
            return -1  

    def i2c_get(self, mode: int, bus: int, dev_addr: int, reg_addr: int):  
        high_byte = (reg_addr >> 8) & 0xFF  
        low_byte = reg_addr & 0xFF   
        command = f'i2ctransfer -y -f {str(bus)} w2@0x{dev_addr:02X} 0x{high_byte:02X} 0x{low_byte:02X} r1'  
        output = self._run_adb_command(command)  
        if self.is_hexadecimal(output):  
            print(f"r_r16v8,0x{dev_addr:02X},0x{reg_addr:04X},{output}")  
            return 0, int(output, 16)  
        else:  
            print(f"adb shell i2c get error: w2@0x{dev_addr:02X} 0x{reg_addr:04X}, output={output}")  
            return -1, None  

    def is_hexadecimal(self, output):  
        if not isinstance(output, str):  
            return False  
        output = output.strip()  
        if not output:  
            return False  
        try:  
            int(output, 16)  
            return True  
        except ValueError:  
            return False
