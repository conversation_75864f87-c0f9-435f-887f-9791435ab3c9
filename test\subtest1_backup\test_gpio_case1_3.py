# -*- coding: utf-8 -*-
"""
GPIO Case-1_3: Multiple Frame-Sync Signal Transmission

This test verifies that the Q68 can generate and transmit three different
frame-sync signals simultaneously, and that all S68 chips can be configured
to receive these signals on specific GPIO pins for observation.
"""
import logging
import time
import pytest

# 测试配置 - 可以手动修改
TEST_CONFIG = {
    'active_links': [0, 1, 2, 3],  # 默认使用所有Link (多帧同步广播需要所有Link)
    'signal_configs': {
        11: {'freq': '30Hz', 'gpio': 0},  # Signal ID 11 -> GPIO 0
        13: {'freq': '50Hz', 'gpio': 7},  # Signal ID 13 -> GPIO 7
        15: {'freq': '12Hz', 'gpio': 8},  # Signal ID 15 -> GPIO 8
    },
    'observation_time': 5  # 观察时间(秒)
}

def test_gpio_multi_frame_sync_broadcast(devices):
    """
    Configures a broadcast of three distinct frame-sync signals from Q68 to all S68s.

    Test Steps:
    0.  Configure specific Links for this test
    1.  Define the three frame-sync signals (frequencies and signal IDs).
    2.  Configure the Q68 to generate all three signals.
    3.  Iterate through each S68 chip.
    4.  For each S68, configure its GPIOs 0, 7, and 8 to receive the three signals respectively.
    5.  Wait for 5 seconds for oscilloscope observation.
    6.  Check and assert that all communication links remain stable.

    Expected Observation:
    - On each S68 chip:
        - GPIO 0 should show a 30 Hz signal.
        - GPIO 7 should show a 50 Hz signal.
        - GPIO 8 should show a 12 Hz signal.
    """
    print(f"\n[CASE-1] 开始执行: Q68 -> 所有S68, 多帧同步信号广播配置")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")

    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']
    get_link_status = devices['get_link_status']

    # ---------------------------------------------------------------------
    # 0. Configure specific Links for this test
    # ---------------------------------------------------------------------
    print(f"\n步骤0: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")

    # 设置地址转换 (关键步骤！)
    print(f"\n步骤0.5: 设置激活Links的地址转换...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            # 设置地址转换：转译地址 -> 实际设备地址
            q68_remote.S68_AddrTrans(
                link=link,
                q68_iic_addr=0x73,                    # Q68地址
                s68_iic_addr=0x40,                    # S68实际地址
                s68_retrans_addr=s68_res_dev[link],   # S68转译地址 (0x20, 0x21, 0x22, 0x23)
                sensor_addr=0x24,                     # sensor地址 (如果需要)
                sensor_retrans_addr=0x24 + link      # sensor转译地址
            )
            print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40")
    print("  - 地址转换设置完成")

    # ---------------------------------------------------------------------
    # 1. Q68 Signal Generation Configuration
    # ---------------------------------------------------------------------
    print("--> 步骤1: 配置Q68以产生三路不同频率的帧同步信号...")

    # Signal 1: 30 Hz on ID 11
    q68.FrameSyncOutConifg(i=0, per_div=0x0B, duty_cycle=4,
                           period=17361, fs_tx_id=11, auto_fs=1, outen=1)
    print("    - 已配置信号发生器-0: 30 Hz, ID=11")

    # Signal 2: 50 Hz on ID 13
    q68.FrameSyncOutConifg(i=1, per_div=0x0A, duty_cycle=4,
                           period=15625, fs_tx_id=13, auto_fs=1, outen=1)
    print("    - 已配置信号发生器-1: 50 Hz, ID=13")

    # Signal 3: 12 Hz on ID 15
    q68.FrameSyncOutConifg(i=2, per_div=0x0B, duty_cycle=4,
                           period=43403, fs_tx_id=15, auto_fs=1, outen=1)
    print("    - 已配置信号发生器-2: 12 Hz, ID=15")
    
    # ---------------------------------------------------------------------
    # 2. S68 Signal Reception Configuration
    # ---------------------------------------------------------------------
    print("\n--> 步骤2: 配置所有S68芯片的GPIO以接收对应的帧同步信号...")
    
    s68_gpio_map = {
        0: 11,  # GPIO 0 receives Signal ID 11 (30 Hz)
        7: 13,  # GPIO 7 receives Signal ID 13 (50 Hz)
        8: 15,  # GPIO 8 receives Signal ID 15 (12 Hz)
    }

    # 只配置激活的Links
    for link_idx in TEST_CONFIG['active_links']:
        if link_idx < len(s68_res_dev):
            target_s68_addr = s68_res_dev[link_idx]
            print(f"  - 正在配置 S68 Link-{link_idx} (地址: {hex(target_s68_addr)})...")
            q68_remote.dongle.devAddr = target_s68_addr

            for gpio_pin, signal_id in s68_gpio_map.items():
                q68_remote.M2CMFNSet(gpio=gpio_pin, mfn=0)
                q68_remote.M2CGPIORemoteRx(gpio=gpio_pin, rx_id=signal_id)
                print(f"    - GPIO-{gpio_pin} 设置为接收 Signal ID {signal_id}")
        else:
            print(f"  - 警告: Link{link_idx} 超出s68_res_dev范围，跳过")

    # ---------------------------------------------------------------------
    # 3. Observation and Verification
    # ---------------------------------------------------------------------
    print(f"\n--> 步骤3: 等待{TEST_CONFIG['observation_time']}秒以便用示波器捕获波形...")
    time.sleep(TEST_CONFIG['observation_time'])

    # 只检查激活Links的状态
    print(f"\n--> 步骤4: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    active_statuses = {}
    for link_id in TEST_CONFIG['active_links']:
        if 0 <= link_id <= 3:
            status = link_status_funcs[link_id]()
            active_statuses[f'link{link_id}'] = status
            print(f"    - Link{link_id} 状态: {status}")

    # 验证激活Links的状态
    failed_links = [link for link, status in active_statuses.items() if status != 5]
    assert len(failed_links) == 0, \
        f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"

    print(f"\n==> [CASE-1] 测试通过: 多路信号已配置广播到Links {TEST_CONFIG['active_links']}，链路状态正常。")