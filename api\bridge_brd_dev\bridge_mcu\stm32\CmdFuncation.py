#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 用于交互中的指令响应

import binascii
from USBInterface import <PERSON>Impl
from USBInterface import USBDataDeal
from threading import Event
import struct

class CmdFuncation:
    def __init__(self):        
        self.res_nodata_event = Event()
        self.res_no_data = None
        self.res_datas_event = Event()
        self.res_have_data = None
        self.usbDataDeal = None
            
        # 处理回应过来的子指令
        self.function_cmd_respond_map = {
            0x0000: self.respond_cmd_datas_fun,          # get sn
            0x0001: self.respond_cmd_nodata_fun,         # set gpio
            0x0002: self.respond_cmd_datas_fun,          # get gpio
            0x0003: self.respond_cmd_nodata_fun,         # set i2c
            0x0004: self.respond_cmd_datas_fun,          # get i2c
            0x0005: self.respond_cmd_nodata_fun,         # set data mode
            0x0006: self.respond_cmd_nodata_fun,         # set bus speed
            0x0007: self.respond_cmd_datas_fun,          # spi master transfer / receive
            0x0008: self.respond_cmd_nodata_fun,         # spi slave transfer
            0x0009: self.respond_cmd_datas_fun,          # spi slave receive
            0x000A: self.respond_cmd_nodata_fun,         # show spi info[print to mcu serial]
            0x000B: self.respond_cmd_nodata_fun,         # init gpio
            0x000C: self.respond_cmd_nodata_fun,         # spi master setmode
            0x000D: self.respond_cmd_datas_fun,          # adc get vol
            0x000E: self.respond_cmd_nodata_fun,         # dac set vol
            0x000F: self.respond_cmd_nodata_fun,         # can put one frame
            0x0010: self.respond_cmd_datas_fun,          # can get one frame
            0x0011: self.respond_cmd_nodata_fun,         # show can info[print to mcu serial]
            0x0012: self.respond_cmd_nodata_fun,         # spi slave setmode
            0x0013: self.respond_cmd_nodata_fun,         # mcu reset
            0x0014: self.respond_cmd_nodata_fun,         # show i2c info[print to mcu serial]
            0x0015: self.respond_cmd_datas_fun,          # get dev version
            0x0016: self.respond_cmd_nodata_fun,         # show sdcard info
            0x0017: self.respond_cmd_nodata_fun,         # clear gpio ti count
            0x0018: self.respond_cmd_datas_fun,          # get gpio ti count
            0x0019: self.respond_cmd_nodata_fun,         # set i2c muilt regs
            0x001A: self.respond_cmd_datas_fun,          # get i2c muilt regs
            0x001B: self.respond_cmd_datas_fun,          # uart data transfer
            0x001C: self.respond_cmd_nodata_fun,         # set debug control
            0x001D: self.respond_cmd_nodata_fun,         # set uart mode
            0x001E: self.respond_cmd_nodata_fun,         # qspi master cmd
            0x001F: self.respond_cmd_nodata_fun,         # qspi master transfer
            0x0020: self.respond_cmd_datas_fun,          # qspi master receive
            0x0021: self.respond_cmd_nodata_fun,         # qspi master set mode
            0x0022: self.respond_cmd_nodata_fun,         # qspi set test times
            0x0023: self.respond_cmd_datas_fun,         # qspi get test status
            0x0024: self.respond_cmd_datas_fun,         # qspi get test buffer
            0x0025: self.respond_cmd_nodata_fun,         # qspi get test buffer
            # 添加更多的函数映射
        }
        
        # 处理发送过来的指令
        self.function_cmd_map = {
            0x1001: self.receive_cmd_1001_fun,  # MCU发送过来的指令
            0xFFFF: self.receive_cmd_ffff_fun,  # MCU对指令的响应
            # 添加更多的函数映射
        }
        

    def __del__(self):
        pass
    
    def setCMDFuncation(self, in_usbDataDeal):
        self.usbDataDeal = in_usbDataDeal
    
    ###########################################################################
    # 处理需要发送的信息
    
    # 响应指令【无携带数据】
    def send_cmd_respond_nodata(self, cmd_id, result):
        data = bytearray(12)
        # 帧头
        data[0] = 0xAA
        data[1] = 0xAA
    
        # 回复指令ID: 0xFFFE
        data[2] = 0xFF
        data[3] = 0xFE
    
        # 回复对应的指令
        data[4] = cmd_id >> 8 & 0xFF
        data[5] = cmd_id & 0xFF
        data[6] = result
    
        # 数据长度
        data[7] = 0x00
        data[8] = 0x00
    
        # CRC
        data[9] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8]
    
        # 帧尾
        data[10] = 0xA5
        data[11] = 0xA5
    
        self.usbDataDeal.usb_send_buff(data)
    
    
    # ID:0x0000: 获取设备SN号
    def send_cmd_get_sn(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x00  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x00  # 数据长度
        data[6] = data[2]^data[3]^data[4]^data[5]
    
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_get_sn usb_send_buff error")
            return -1, -1
    
        # 等待响应
        if not self.res_datas_event.wait(0.1):
            print("send_cmd_get_sn respond error")
            return -1, -1
    
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x00:
            print("send_cmd_get_sn respond id Error")
            return -1, -1
        
        if self.res_have_data[2] != 0:
            print("send_cmd_get_sn cmd ret error:", self.res_have_data[2])
            return -1, -1
        else:
            return -self.res_have_data[2], self.res_have_data[5:17]
    
    
    # ID:0x0001: 设置指定输出引脚的值
    def send_cmd_set_gpio(self, pin_num: int, pin_val: int):
        data = bytearray(11)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x01  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x02  # 数据长度
        data[6] = pin_num
        data[7] = pin_val
        data[8] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7]
    
        data[9] = 0xA5  # 帧尾
        data[10] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_set_gpio usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_set_gpio respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x01:
            print("send_cmd_set_gpio respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_set_gpio cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x0002: 获取指定输入引脚的值
    def send_cmd_get_gpio(self, pin_num: int):
        data = bytearray(10)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x02  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x01  # 数据长度
        data[6] = pin_num
        data[7] = data[2]^data[3]^data[4]^data[5]^data[6]
    
        data[8] = 0xA5  # 帧尾
        data[9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_get_gpio usb_send_buff error")
            return -1, -1
    
        # 等待响应
        if not self.res_datas_event.wait(0.1):
            print("send_cmd_get_gpio respond error")
            return -1, -1
        
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x02:
            print("send_cmd_get_gpio respond id Error")
            return -1, -1  
    
        if self.res_have_data[2] != 0:
            print("send_cmd_get_gpio cmd ret error:", self.res_have_data[2])
            return -1, -1
        else:
            return -self.res_have_data[2], self.res_have_data[5]
    
    
    # ID:0x0003:设置I2C寄存器的值
    # mode:0:r8v8 1:r16v8
    def send_cmd_i2c_set(self, mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):
        data = bytearray(16)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x03  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x07  # 数据长度
        data[6] = mode
        data[7] = bus
        data[8] = dev_addr
        data[9] = reg_addr >> 8 & 0xFF
        data[10] = reg_addr & 0xFF
        data[11] = reg_val >> 8 & 0xFF
        data[12] = reg_val & 0xFF
    
        data[13] = (data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9]
                    ^ data[10] ^ data[11] ^ data[12])
    
        data[14] = 0xA5  # 帧尾
        data[15] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_i2c_set usb_send_buff error")
            return -1

        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_i2c_set respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x03:
            print("send_cmd_i2c_set respond id Error")
            return -1
         
        if self.res_no_data[2] != 0:
            print("send_cmd_i2c_set cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x0004:获取I2C寄存器的值
    # return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
    def send_cmd_i2c_get(self, mode: int, bus: int, dev_addr: int, reg_addr: int):
        data = bytearray(14)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x04  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x05  # 数据长度
        data[6] = mode
        data[7] = bus
        data[8] = dev_addr
        data[9] = reg_addr >> 8 & 0xFF
        data[10] = reg_addr & 0xFF
    
        data[11] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10]
    
        data[12] = 0xA5  # 帧尾
        data[13] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_i2c_get usb_send_buff error")
            return -1, -1
    
        # 等待响应
        if not self.res_datas_event.wait(0.3):
            print("send_cmd_i2c_get respond error")
            return -1, -1
        
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x04:
            print("send_cmd_i2c_get respond id Error")
            return -1, -1
         
            
        if self.res_have_data[2] != 0:
            print("send_cmd_i2c_get cmd ret error:", self.res_have_data[2])
            return -1, -1
        else:
            return -self.res_have_data[2], (self.res_have_data[5] << 8) +  self.res_have_data[6]
    
    # ID:0x0005: 设置数据类型，0:真实数据，1:模拟数据
    def send_cmd_set_mode(self, mode: int):
        data = bytearray(10)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x05  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x01  # 数据长度
        data[6] = mode
        data[7] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6]
    
        data[8] = 0xA5  # 帧尾
        data[9] = 0xA5  # 帧尾
    
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_set_gpio usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_set_gpio respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x05:
            print("send_cmd_set_gpio respond id Error")
            return -1

        if self.res_no_data[2] != 0:
            print("send_cmd_set_gpio cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x0006: 设置I2C速率，  bus：需要设置的总线，speed:需要设置的频率（kbps）
    def send_cmd_i2c_setbusspeed(self, bus: int, speed: int):
        data = bytearray(12)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x06  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x01  # 数据长度
        data[6] = bus
        data[7] = speed >> 8 & 0xFF
        data[8] = speed & 0xFF
        data[9] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8]
    
        data[10] = 0xA5  # 帧尾
        data[11] = 0xA5  # 帧尾
    
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_i2c_setbusspeed usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_i2c_setbusspeed respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x06:
            print("send_cmd_i2c_setbusspeed respond id Error")
            return -1    

        if self.res_no_data[2] != 0:
            print("send_cmd_i2c_setbusspeed cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x0007: SPI Master 收发数据
    def send_cmd_spi_master_tr(self, index: int, tx_buffer: bytearray, txrx_len: int):
        data = bytearray(txrx_len+9+1)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x07  # 设置引脚指令
    
        data[4] = (txrx_len+1)>>8 & 0xFF  # 数据长度
        data[5] = (txrx_len+1) & 0xFF     # 数据长度
        data[6] = index
        for i in range(txrx_len):
            data[i+7] = tx_buffer[i];
        
        crcval = 0
        for i in range(2,txrx_len+6):
            crcval = crcval ^ data[i]
        
        data[txrx_len+7] = crcval
        data[txrx_len+8] = 0xA5  # 帧尾
        data[txrx_len+9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_spi_master_tr usb_send_buff error")
            return -1,bytearray()
    
        # 等待响应
        if not self.res_datas_event.wait(0.2):
            print("send_cmd_spi_master_tr respond error")
            return -1,bytearray()

        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x07:
            print("send_cmd_spi_master_tr respond id Error")
            return -1,bytearray()   

        rx_buffer = self.res_have_data[5:(len(self.res_have_data)-3)]
        ret = self.get_signalval(self.res_have_data[2])
    
        if ret < 0:
            print("send_cmd_spi_master_tr cmd ret error:", ret)

        return ret, rx_buffer
    
    
    # ID:0x0008: SPI Slave 发数据
    # return: >0 是发送成功的数量, <=0:是错误编号
    def send_cmd_spi_slave_t(self, index: int, tx_buffer: bytearray, tx_len: int):
        data = bytearray(tx_len+9+1)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x08  # 设置引脚指令
    
        data[4] = (tx_len+1)>>8 & 0xFF  # 数据长度
        data[5] = (tx_len+1) & 0xFF  # 数据长度
        data[6] = index
        for i in range(tx_len):
            data[i+7] = tx_buffer[i];
        
        crcval = 0
        for i in range(2,tx_len+6):
            crcval = crcval ^ data[i]
        
        data[tx_len+7] = crcval
        data[tx_len+8] = 0xA5  # 帧尾
        data[tx_len+9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_spi_slave_t usb_send_buff error")
            return -1
        
        # 等待响应
        if not self.res_nodata_event.wait(0.2):
            print("send_cmd_spi_slave_t respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x08:
            print("send_cmd_spi_slave_t respond id Error")
            return -1 
    
        if self.res_no_data[2] < 0:
            print("send_cmd_spi_slave_t cmd ret error:", self.res_no_data[2])

        return self.res_no_data[2]
    
    # ID:0x0009: SPI Slave 收数据
    def send_cmd_spi_slave_r(self, index: int, rx_len: int):
        rx_buffer: bytearray = None
        data = bytearray(11)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x09  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x02  # 数据长度
        data[6] = index
        data[7] = rx_len & 0xFF
    
        crcval = 0
        for i in range(2,7):
            crcval = crcval ^ data[i]
    
        data[8] = crcval
        data[9] = 0xA5  # 帧尾
        data[10] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_spi_slave_r usb_send_buff error")
            return -1,bytearray()
    
        # 等待响应
        if not self.res_datas_event.wait(0.2):
            print("send_cmd_spi_slave_r respond error")
            return -1,bytearray()

        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x09:
            print("send_cmd_spi_slave_r respond id Error")
            return -1,bytearray()
        
        rx_buffer = self.res_have_data[5:(len(self.res_have_data)-3)]
        ret = self.get_signalval(self.res_have_data[2])

        if ret < 0:
            print("send_cmd_spi_slave_r cmd ret error:", ret)

        return ret, rx_buffer
    
    # ID:0x000A: 让MCU打印内部SPI信息到MCU串口上
    def send_cmd_spi_showinfo(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x0A  # 设置引脚指令
    
        data[4] = 0x00      # 数据长度
        data[5] = 0x00      # 数据长度
        
        crcval = 0
        for i in range(2,5):
            crcval = crcval ^ data[i]
    
        data[6] = crcval
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_spi_showinfo usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(3):    # 打印数据比较耗时,打印完成后MCU才会返回结果,所以这里等待1S,防止超时错误.
            print("send_cmd_spi_showinfo respond error")
            return -1
       
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x0A:
            print("send_cmd_spi_showinfo respond id Error")
            return -1
    
        if self.res_no_data[2] != 0:
            print("send_cmd_spi_showinfo cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x000B: 设置对应编号的GPIO的输入输出方向
    # pin[0~23]
    # mode[0:input 1:output_pp 2:output_od 3:ad_pp 4:ad_od]
    # pull[0:nopull 1:pullup 2:pulldown]
    def send_cmd_init_gpio(self, pin_num: int = 0, pin_mode: int = 0, pin_pull: int = 1):
        data = bytearray(12)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x0B  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x03  # 数据长度
        data[6] = pin_num
        data[7] = pin_mode
        data[8] = pin_pull
        data[9] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8]
    
        data[10] = 0xA5  # 帧尾
        data[11] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_init_gpio usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_init_gpio respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x0B:
            print("send_cmd_init_gpio respond id Error")
            return -1

        if self.res_no_data[2] != 0:
            print("send_cmd_init_gpio cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x000C: 设置SPI Master的主频率
    # cpol:    0:Low 1:Hight
    # clph:    0:1Edga 1:2Edga
    # nss:     0:soft 1:hw input 2:hw output
    # rate:    kbps
    def send_cmd_spi_master_setmode(self, index:int = 0, cpol: int = 0, clph: int = 0, nss: int = 1, rate:int = 32000):
        data = bytearray(17)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x0C  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x08  # 数据长度
        data[6] = index&0xFF
        data[7] = cpol&0xFF
        data[8] = clph&0xFF
        data[9] = nss&0xFF
        
        data[10] = (rate>>24)&0xFF
        data[11] = (rate>>16)&0xFF
        data[12] = (rate>>8)&0xFF
        data[13] = (rate>>0)&0xFF
        
        crcval = 0
        for i in range(2,13):
            crcval = crcval ^ data[i]
        
        data[14] = crcval
        data[15] = 0xA5  # 帧尾
        data[16] = 0xA5  # 帧尾
    
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_spi_master_setmode usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_spi_master_setmode respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x0C:
            print("send_cmd_spi_master_setmode respond id Error")
            return -1


        if self.res_no_data[2] != 0:
            print("send_cmd_spi_master_setmode cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x000D: 获取指定通道的ADC的值
    # 0~3300 代表 0~3.3V
    def send_cmd_adc_getvol(self, index:int = 0):
        data = bytearray(10)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x0D  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x01  # 数据长度
        data[6] = index&0xFF
        
        crcval = 0
        for i in range(2,6):
            crcval = crcval ^ data[i]
        
        data[7] = crcval
        data[8] = 0xA5  # 帧尾
        data[9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_adc_getvol usb_send_buff error")
            return -1,-1
    
        # 等待响应
        if not self.res_datas_event.wait(0.1):
            print("send_cmd_adc_getvol respond error")
            return -1,-1

        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x0D:
            print("send_cmd_adc_getvol respond id Error")
            return -1,-1

        if self.res_have_data[2] != 0:
            print("send_cmd_adc_getvol cmd ret error:", self.res_have_data[2])

        return -self.res_have_data[2], (self.res_have_data[5] << 8) +  self.res_have_data[6]
    
    # ID:0x000E: 设置指定通道DAC的电压
    # 0~3300 代表 0~3.3V
    def send_cmd_dac_setvol(self, index:int = 0, vol:int = 0):
        data = bytearray(12)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x0E  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x03  # 数据长度
        data[6] = index&0xFF
        data[7] = (vol>>8)&0xFF
        data[8] = (vol>>0)&0xFF
        
        crcval = 0
        for i in range(2,8):
            crcval = crcval ^ data[i]
        
        data[9] = crcval
        data[10] = 0xA5  # 帧尾
        data[11] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_dac_setvol usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_dac_setvol respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x0E:
            print("send_cmd_dac_setvol respond id Error")
            return -1

        if self.res_no_data[2] != 0:
            print("send_cmd_dac_setvol cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x000F: 发送数据帧到CAN
    # tx_len: can帧数据不能大于8
    def send_cmd_can_put_oneframe(self, id: int, tx_buffer: bytearray, tx_len: int):
        if tx_len > 8:
            tx_len = 8
        data = bytearray(tx_len+11)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x0F  # 设置引脚指令
    
        data[4] = 0x00              # 数据长度
        data[5] = (tx_len+2)&0xFF       # 数据长度
        
        data[6] = (id>>8)&0xFF
        data[7] = id&0xFF
        
        for i in range(tx_len):
            data[i+8] = tx_buffer[i];
        
        crcval = 0
        for i in range(2,tx_len+7):
            crcval = crcval ^ data[i]
    
        data[tx_len+8] = crcval
        data[tx_len+9] = 0xA5  # 帧尾
        data[tx_len+10] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_can_put_oneframe usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(1):    # 打印数据比较耗时,打印完成后MCU才会返回结果,所以这里等待1S,防止超时错误.
            print("send_cmd_can_put_oneframe respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x0F:
            print("send_cmd_can_put_oneframe respond id Error")
            return -1


        if self.res_no_data[2] != 0:
            print("send_cmd_can_put_oneframe cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x0010: 从CAN接收缓冲区获取帧
    # return: ret, id, len, buff
    def send_cmd_can_get_oneframe(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x10  # 设置引脚指令
    
        data[4] = 0x00      # 数据长度
        data[5] = 0x00      # 数据长度
        
        crcval = 0
        for i in range(2,5):
            crcval = crcval ^ data[i]
    
        data[6] = crcval
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_can_get_oneframe usb_send_buff error")
            return -1,bytearray()
    
        # 等待响应
        if not self.res_datas_event.wait(0.2):
            print("send_cmd_can_get_oneframe respond error")
            return -1,bytearray()
        
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x10:
            print("send_cmd_can_get_oneframe respond id Error")
            return -1,bytearray()
        
        rx_buffer = self.res_have_data[5:(len(self.res_have_data)-3)]
        ret = self.get_signalval(self.res_have_data[2])

        if ret != 0:
            print("send_cmd_can_get_oneframe cmd ret error:", ret)

        return ret, rx_buffer
    
    # ID:0x0011: 让MCU打印内部CAN信息到MCU串口上
    def send_cmd_can_showinfo(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x11  # 设置引脚指令
    
        data[4] = 0x00      # 数据长度
        data[5] = 0x00      # 数据长度
        
        crcval = 0
        for i in range(2,5):
            crcval = crcval ^ data[i]
    
        data[6] = crcval
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_can_showinfo usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(3):    # 打印数据比较耗时,打印完成后MCU才会返回结果,所以这里等待1S,防止超时错误.
            print("send_cmd_can_showinfo respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x11:
            print("send_cmd_can_showinfo respond id Error")
            return -1

        if self.res_no_data[2] != 0:
            print("send_cmd_can_showinfo cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]

    # ID:0x0012: 设置SPI Master的参数
    # cpol:    0:Low 1:Hight
    # clph:    0:1Edga 1:2Edga
    # nss:     0:soft 1:hw input 2:hw output
    # # devtype: 0:stream 1:reg_dev_r8v8 2:reg_dev_r16v8 3:reg_dev_r8v16  4:reg_dev_r16v16
    def send_cmd_spi_slave_setmode(self, index:int = 0, cpol: int = 0, clph: int = 0, nss: int = 1, devtype:int  = 1):
        data = bytearray(14)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x12  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x4  # 数据长度
        data[6] = index&0xFF
        data[7] = cpol&0xFF
        data[8] = clph&0xFF
        data[9] = nss&0xFF
        data[10] = devtype&0xFF
        crcval = 0
        for i in range(2,10):
            crcval = crcval ^ data[i]
        
        data[11] = crcval
        data[12] = 0xA5  # 帧尾
        data[13] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_spi_slave_setmode usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.5):
            print("send_cmd_spi_slave_setmode respond error")
            return -1
    
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x12:
            print("send_cmd_spi_slave_setmode respond id Error")
            return -1
    
        if self.res_no_data[2] != 0:
            print("send_cmd_spi_slave_setmode cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    # ID:0x0013: reset mcu
    def send_cmd_mcu_reset(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x13  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x00  # 数据长度
        crcval = 0
        for i in range(2,5):
            crcval = crcval ^ data[i]
        
        data[6] = crcval
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
    
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_mcu_reset usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.2):
            print("send_cmd_mcu_reset respond error")
            return -1
    
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x13:
            print("send_cmd_mcu_reset respond id Error")
            return -1
        
        if self.res_no_data[2] != 0:
            print("send_cmd_mcu_reset cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]

    # ID:0x0014: 让MCU打印内部I2C信息到MCU串口上
    def send_cmd_i2c_showinfo(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x14  # 设置引脚指令
    
        data[4] = 0x00      # 数据长度
        data[5] = 0x00      # 数据长度
        
        crcval = 0
        for i in range(2,5):
            crcval = crcval ^ data[i]
    
        data[6] = crcval
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_i2c_showinfo usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(3):    # 打印数据比较耗时,打印完成后MCU才会返回结果,所以这里等待1S,防止超时错误.
            print("send_cmd_i2c_showinfo respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x14:
            print("send_cmd_i2c_showinfo respond id Error")
            return -1
        
        if self.res_no_data[2] != 0:
            print("send_cmd_i2c_showinfo cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x0015:获取设备的版本号
    def send_cmd_get_version(self):
        data = bytearray(16)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x15  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x00  # 数据长度
    
        data[6] = data[2] ^ data[3] ^ data[4] ^ data[5]
    
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_get_version usb_send_buff error")
            return -1, 0, 0
    
        # 等待响应
        if not self.res_datas_event.wait(0.3):
            print("send_cmd_get_version respond error")
            return -1, 0, 0
        
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x15:
            print("send_cmd_get_version respond id Error")
            return -1, 0, 0

        if self.res_have_data[2] != 0:
            print("send_cmd_get_version cmd ret error:", self.res_have_data[2])
            return -1, 0, 0
        else:
            return -self.res_have_data[2], (self.res_have_data[5]*10000) + (self.res_have_data[6]*100) + (self.res_have_data[7]), (self.res_have_data[8]*10000) + (self.res_have_data[9]*100) + (self.res_have_data[10])
    
    # ID:0x0016: 让MCU将SD卡信息打印到串口
    def send_cmd_sdcard_showinfo(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x16  # 设置引脚指令
    
        data[4] = 0x00      # 数据长度
        data[5] = 0x00      # 数据长度
        
        crcval = 0
        for i in range(2,5):
            crcval = crcval ^ data[i]
    
        data[6] = crcval
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_sdcard_showinfo usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(3):    # 打印数据比较耗时,打印完成后MCU才会返回结果,所以这里等待1S,防止超时错误.
            print("send_cmd_sdcard_showinfo respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x16:
            print("send_cmd_sdcard_showinfo respond id Error")
            return -1

        if self.res_no_data[2] != 0:
            print("send_cmd_sdcard_showinfo cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]


    # ID:0x0017: 清除gpio对应的中断次数
    def send_cmd_clear_gpio_ticount(self, index:int = 0):
        data = bytearray(10)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x17  # 设置引脚指令
    
        data[4] = 0x00      # 数据长度
        data[5] = 0x01      # 数据长度
        data[6] = index&0xFF
        
        crcval = 0
        for i in range(2,6):
            crcval = crcval ^ data[i]
    
        data[7] = crcval
        data[8] = 0xA5  # 帧尾
        data[9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_clear_gpio_ticount usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(1):    # 打印数据比较耗时,打印完成后MCU才会返回结果,所以这里等待1S,防止超时错误.
            print("send_cmd_clear_gpio_ticount respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x17:
            print("send_cmd_clear_gpio_ticount respond id Error")
            return -1

        if self.res_no_data[2] != 0:
            print("send_cmd_clear_gpio_ticount cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]

    # ID:0x0018: 获取gpio对应的中断次数
    def send_cmd_get_gpio_ticount(self, index:int = 0):
        data = bytearray(10)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x18  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x01  # 数据长度
        data[6] = index&0xFF
        
        crcval = 0
        for i in range(2,6):
            crcval = crcval ^ data[i]
        
        data[7] = crcval
        data[8] = 0xA5  # 帧尾
        data[9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_get_gpio_ticount usb_send_buff error")
            return -1,-1
    
        # 等待响应
        if not self.res_datas_event.wait(0.1):
            print("send_cmd_get_gpio_ticount respond error")
            return -1,-1

        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x18:
            print("send_cmd_get_gpio_ticount respond id Error")
            return -1,-1

        if self.res_have_data[2] != 0:
            print("send_cmd_get_gpio_ticount cmd ret error:", self.res_have_data[2])

        return -self.res_have_data[2], (self.res_have_data[5] << 24) +  (self.res_have_data[6] << 16) + (self.res_have_data[7] << 8) +  self.res_have_data[8]


    # ID:0x0019:批量设置I2C寄存器的值
    # mode:0:r8v8 1:r16v8
    def send_cmd_i2c_muilt_set(self, mode: int, bus: int, dev_addr: int, reg_addr: int, reg_vals: list, count: int):
        if count > 100 or count <= 0:
            print(f"send_cmd_i2cbuf_set count should < 100 and > 0, but now count is {count}")
            return -1
    
        data = bytearray(15+count*2)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x19  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x05+count*2  # 数据长度
        data[6] = mode
        data[7] = bus
        data[8] = dev_addr
        data[9] = reg_addr >> 8 & 0xFF
        data[10] = reg_addr & 0xFF
        data[11] = count & 0xFF
        for idx in range(count):
            data[11+idx*2+1] = reg_vals[idx] >> 8 & 0xFF
            data[11+idx*2+2] = reg_vals[idx] & 0xFF
    
        crcval = 0
        for i in range(2,10+count*2):
            crcval = crcval ^ data[i]
        
        data[12+count*2] = crcval
        data[13+count*2] = 0xA5  # 帧尾
        data[14+count*2] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_i2c_set usb_send_buff error")
            return -1

        # 等待响应
        if not self.res_nodata_event.wait(2):
            print("send_cmd_i2c_set respond error")
            return -1
        
        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x19:
            print("send_cmd_i2c_set respond id Error")
            return -1
         
        if self.res_no_data[2] != 0:
            print("send_cmd_i2c_set cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x001A:批量获取I2C寄存器的值
    # return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
    def send_cmd_i2c_muilt_get(self, mode: int, bus: int, dev_addr: int, reg_addr: int, count: int):
        if count > 100 or count <= 0:
            print(f"send_cmd_i2cbuf_get count should < 100 and > 0, but now count is {count}")
            return -1,{}

        data = bytearray(15)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x1A  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x06  # 数据长度
        data[6] = mode
        data[7] = bus
        data[8] = dev_addr
        data[9] = reg_addr >> 8 & 0xFF
        data[10] = reg_addr & 0xFF
        data[11] = count & 0xFF
    
        data[12] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10] ^ data[11]
    
        data[13] = 0xA5  # 帧尾
        data[14] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        self.res_no_data.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_i2c_get usb_send_buff error")
            return -1, []
    
        # 等待响应
        if not self.res_datas_event.wait(2):
            print("send_cmd_i2c_get respond error")
            return -1, []
        
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x1A:
            print("send_cmd_i2c_get respond id Error")
            return -1, []
         
            
        if self.res_have_data[2] != 0:
            print("send_cmd_i2c_get cmd ret error:", self.res_have_data[2])
            return -1, []
        else:
            regVal: list = []
            for idx in range(count):
                regVal.append((self.res_have_data[5+idx*2] << 8) +  self.res_have_data[6+idx*2])
            return -self.res_have_data[2], regVal
    
    # ID:0x001B:Uart接口交互，传入需要发送的数据，将返回的数据接收回来
    # index：那一路Uart tx_buffer，需要发送的数据 
    # return: ret,rx_buffer,  第一个值是ret值，第二个值为获取的rx_buffer
    def send_cmd_uart_transfer(self, index:int, tx_buffer: list, count: int, rx_len:int=2, rx_timeout:int=0x10):
        if count > 200 or count < 0:
            print(f"send_cmd_uart_transfer count should < 100 and > 0, but now count is {count}")
            return -1
    
        data = bytearray(14+count)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x1B  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 2+count  # 数据长度
        data[6] = index
        data[7] = count & 0xFF
        data[8] = rx_len & 0xFF
        data[9] = rx_timeout>>8 & 0xFF
        data[10] = rx_timeout & 0xFF
        for idx in range(count):
            data[11+idx] = tx_buffer[idx] & 0xFF
    
        crcval = 0
        for i in range(2,10+count):
            crcval = crcval ^ data[i]
        
        data[11+count] = crcval
        data[12+count] = 0xA5  # 帧尾
        data[13+count] = 0xA5  # 帧尾

        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_uart_transfer usb_send_buff error")
            return -1, []
    
        # 等待响应
        if not self.res_datas_event.wait(1):
            print("send_cmd_uart_transfer respond error")
            return -1, []
        
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x1B:
            print("send_cmd_uart_transfer respond id Error")
            return -1, []
         
            
        if self.res_have_data[2] < 0:
            print("send_cmd_uart_transfer cmd ret error:", self.res_have_data[2])
            return -1, []
        else:
            rx_buffer: list = []
            count = (self.res_have_data[3] << 8) +  self.res_have_data[4]
            for idx in range(count):
                rx_buffer.append(self.res_have_data[5+idx])

            return -self.res_have_data[2], rx_buffer

    # ID:0x001C: 设置debug串口
    # debug_en:是否使能调试信息， uart_port:调试的串口选择
    def send_cmd_set_debug_status(self, debug_en: int, uart_port: int, debug_level:int):
        data = bytearray(12)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x1C  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x03  # 数据长度
        data[6] = debug_en
        data[7] = uart_port
        data[8] = debug_level
        data[9] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8]
    
        data[10] = 0xA5  # 帧尾
        data[11] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_set_debug_ctrl usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_set_debug_ctrl respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x1C:
            print("send_cmd_set_debug_ctrl respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_set_debug_ctrl cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x001D: 设置串口信息
    # default:115200, stop:1 parity:None hw:None
    # rate:(baudrate) stop(STOPBITS 0.5 1.0 1.5 2.0)  parity(0:None 1:EVEN 2:ODD) hwflow(0:None 1:RTS 2:CTS 3:RTS_CTS)
    def send_cmd_set_uart_mode(self, index:int, rate:int, stop:float, parity:int, hwflow:int):
        data = bytearray(17)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x1D  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x02  # 数据长度
        data[6] = index
        data[7] = rate >> 24 & 0xFF
        data[8] = rate >> 16 & 0xFF
        data[9] = rate >> 8 & 0xFF
        data[10] = rate & 0xFF
        data[11] = int(stop*10)
        data[12] = parity
        data[13] = hwflow
        data[14] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10] ^ data[11] ^ data[12] ^ data[13]
    
        data[15] = 0xA5  # 帧尾
        data[16] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_set_uart_info usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_set_uart_info respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x1D:
            print("send_cmd_set_uart_info respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_set_uart_info cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    
    
    # ID:0x001E: QSPI Master 发CMD
    def send_cmd_qspi_master_cmd(self, index: int, cmd: int, addr:int, mode:int, dmcycle:int):
        data = bytearray(17)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x1E  # 设置引脚指令
        data[4] = 0x00
        data[5] = 0x08
        data[6] = index
        data[7] = cmd
        data[8] = addr >> 24 & 0xFF
        data[9] = addr >> 16 & 0xFF
        data[10] = addr >> 8 & 0xFF
        data[11] = addr & 0xFF
        data[12] = mode
        data[13] = dmcycle
        crcval = 0
        for i in range(2,13):
            crcval = crcval ^ data[i]
        
        data[14] = crcval
        data[15] = 0xA5  # 帧尾
        data[16] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_qspi_master_cmd usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(5):
            print("send_cmd_qspi_master_cmd respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x1E:
            print("send_cmd_qspi_master_cmd respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_qspi_master_cmd cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]

    # ID:0x001F: QSPI Master 发数据
    def send_cmd_qspi_master_t(self, index: int, tx_buffer: bytearray, tx_len: int):
        data = bytearray(tx_len+9+1)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x1F  # 设置引脚指令
    
        data[4] = (tx_len+1)>>8 & 0xFF  # 数据长度
        data[5] = (tx_len+1) & 0xFF     # 数据长度
        data[6] = index
        for i in range(tx_len):
            data[i+7] = tx_buffer[i];
        
        crcval = 0
        for i in range(2,tx_len+6):
            crcval = crcval ^ data[i]
        
        data[tx_len+7] = crcval
        data[tx_len+8] = 0xA5  # 帧尾
        data[tx_len+9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_qspi_master_t usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(5):
            print("send_cmd_qspi_master_t respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x1F:
            print("send_cmd_qspi_master_t respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_qspi_master_t cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]

    # ID:0x0020: QSPI Master 发数据
    def send_cmd_qspi_master_r(self, index: int, rx_len: int):
        data = bytearray(12)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x20  # 设置引脚指令
    
        data[4] = 0x00
        data[5] = 0x03
        data[6] = index
        data[7] = (rx_len)>>8 & 0xFF  # 数据长度
        data[8] = (rx_len) & 0xFF     # 数据长度
        
        crcval = 0
        for i in range(2,8):
            crcval = crcval ^ data[i]
        
        data[9] = crcval
        data[10] = 0xA5  # 帧尾
        data[11] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_qspi_master_r usb_send_buff error")
            return -1,bytearray()
    
        # 等待响应
        if not self.res_datas_event.wait(5):
            print("send_cmd_qspi_master_r respond error")
            return -1,bytearray()

        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x20:
            print("send_cmd_qspi_master_r respond id Error")
            return -1,bytearray()   

        rx_buffer = self.res_have_data[5:(len(self.res_have_data)-3)]
        ret = self.get_signalval(self.res_have_data[2])
    
        if ret < 0:
            print("send_cmd_qspi_master_r cmd ret error:", ret)

        return ret, rx_buffer


    # ID:0x0021: QSPI Master Set Mode
    def send_cmd_qspi_master_set_mode(self, qspi_blk_mode, ClockPrescaler, FifoThreshold, SampleShifting, FlashSize, ChipSelectHighTime, ClockMode):
        data = bytearray(16)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x21  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x07     # 数据长度
        data[6] = qspi_blk_mode
        data[7] = ClockPrescaler
        data[8] = FifoThreshold
        data[9] = SampleShifting
        data[10] = FlashSize
        data[11] = ChipSelectHighTime
        data[12] = ClockMode
        crcval = 0
        for i in range(2,12):
            crcval = crcval ^ data[i]
        
        data[13] = crcval
        data[14] = 0xA5  # 帧尾
        data[15] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_qspi_master_set_mode usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(5):
            print("send_cmd_qspi_master_set_mode respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x21:
            print("send_cmd_qspi_master_set_mode respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_qspi_master_set_mode cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]


    # ID:0x0022: QSPI Master Set Mode
    def send_cmd_qspi_master_set_test_times(self, times:int, hdlow:int, read_before:int=1000, read_after:int=1000, read_funmode:int=0):
        data = bytearray(24)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x22  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x0F     # 数据长度
        data[6] = (times>>8)&0xFF
        data[7] = (times>>0)&0xFF
        data[8] = hdlow >> 24 & 0xFF
        data[9] = hdlow >> 16 & 0xFF
        data[10] = hdlow >> 8 & 0xFF
        data[11] = hdlow & 0xFF
        data[12] = read_before >> 24 & 0xFF
        data[13] = read_before >> 16 & 0xFF
        data[14] = read_before >> 8 & 0xFF
        data[15] = read_before & 0xFF
        data[16] = read_after >> 24 & 0xFF
        data[17] = read_after >> 16 & 0xFF
        data[18] = read_after >> 8 & 0xFF
        data[19] = read_after & 0xFF
        data[20] = read_funmode;
        crcval = 0
        for i in range(2,20):
            crcval = crcval ^ data[i]
        
        data[21] = crcval
        data[22] = 0xA5  # 帧尾
        data[23] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_qspi_master_set_test_times usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(5):
            print("send_cmd_qspi_master_set_test_times respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x22:
            print("send_cmd_qspi_master_set_test_times respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_qspi_master_set_test_times cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]

    # ID:0x0023: QSPI Master get  test status
    def send_cmd_qspi_master_get_test_status(self):
        data = bytearray(9)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x23  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x00  # 数据长度
        crcval = 0
        for i in range(2,5):
            crcval = crcval ^ data[i]
        
        data[6] = crcval
        data[7] = 0xA5  # 帧尾
        data[8] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_qspi_master_get_test_status usb_send_buff error")
            return -1, -1, -1
    
        # 等待响应
        if not self.res_datas_event.wait(0.1):
            print("send_cmd_qspi_master_get_test_status respond error")
            return -1, -1, -1
        
        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x23:
            print("send_cmd_qspi_master_get_test_status respond id Error")
            return -1, -1, -1
    
        if self.res_have_data[2] != 0:
            print("send_cmd_qspi_master_get_test_status cmd ret error:", self.res_have_data[2])
            return -1, -1, -1
        else:
            return -self.res_have_data[2], self.res_have_data[5],(self.res_have_data[6] << 8) +  self.res_have_data[7]
    

    # ID:0x0024: QSPI Master Get Test Buffer
    def send_cmd_qspi_master_get_test_buffer(self, pos_start, length):
        data = bytearray(13)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x24  # 设置引脚指令
    
        data[4] = 0x00
        data[5] = 0x04
        data[6] = (pos_start>>8)&0xFF
        data[7] = (pos_start>>0)&0xFF
        data[8] = (length>>8)&0xFF
        data[9] = (length>>0)&0xFF
        
        crcval = 0
        for i in range(2,9):
            crcval = crcval ^ data[i]
        
        data[10] = crcval
        data[11] = 0xA5  # 帧尾
        data[12] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_datas_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_qspi_master_get_test_buffer usb_send_buff error")
            return -1,bytearray()
    
        # 等待响应
        if not self.res_datas_event.wait(5):
            print("send_cmd_qspi_master_get_test_buffer respond error")
            return -1,bytearray()

        # 确认是响应此cmd的信息
        if self.res_have_data[0] != 0x00 or self.res_have_data[1] != 0x24:
            print("send_cmd_qspi_master_get_test_buffer respond id Error")
            return -1,bytearray()   

        rx_buffer = self.res_have_data[5:(len(self.res_have_data)-3)]
        ret = self.get_signalval(self.res_have_data[2])
    
        if ret < 0:
            print("send_cmd_qspi_master_get_test_buffer cmd ret error:", ret)

        return ret, rx_buffer


    # ID:0x0025: Set I2C read stop flag
    def send_cmd_i2c_set_readstop_flag(self, stop_flag:int=0):
        data = bytearray(24)
        data[0] = 0xAA  # 帧头
        data[1] = 0x55  # 帧头
        data[2] = 0x00  # 设置引脚指令
        data[3] = 0x25  # 设置引脚指令
    
        data[4] = 0x00  # 数据长度
        data[5] = 0x01     # 数据长度
        data[6] = stop_flag
        crcval = 0
        for i in range(2,6):
            crcval = crcval ^ data[i]
        
        data[7] = crcval
        data[8] = 0xA5  # 帧尾
        data[9] = 0xA5  # 帧尾
        
        # 先清空事件，防止发送之后，对方马上返回数据，导致响应的事件被清楚掉
        self.res_nodata_event.clear()
        ret = self.usbDataDeal.usb_send_buff(data)
        if ret != 0:
            print("send_cmd_set_i2c_read_stopflag usb_send_buff error")
            return -1
    
        # 等待响应
        if not self.res_nodata_event.wait(0.1):
            print("send_cmd_set_i2c_read_stopflag respond error")
            return -1

        # 确认是响应此cmd的信息
        if self.res_no_data[0] != 0x00 or self.res_no_data[1] != 0x25:
            print("send_cmd_set_i2c_read_stopflag respond id Error")
            return -1    
        
        if self.res_no_data[2] != 0:
            print("send_cmd_set_i2c_read_stopflag cmd ret error:", self.res_no_data[2])

        return -self.res_no_data[2]
    ###########################################################################
    
    
    ###########################################################################
    # 处理接收到的信息
    
    # 不携带数据的响应指令
    def respond_cmd_nodata_fun(self, cmd_buff: bytearray, cmd_len: int):
        self.res_no_data = cmd_buff[4:7]
        self.res_nodata_event.set()
    
    # 携带数据的响应指令
    def respond_cmd_datas_fun(self, cmd_buff: bytearray, cmd_len: int):
        self.res_have_data = cmd_buff[4:]
        self.res_datas_event.set()
    
    # 处理对方发过来的指令：ID=0x1001
    def receive_cmd_1001_fun(self, cmd_buff: bytearray, cmd_len: int):
        if cmd_buff[6] == 0:
            print("cmd_1001_fun ic init success")
        else:
            print("cmd_1001_fun ic init error")
        self.send_cmd_respond_nodata(0x1001, 0)
    
    # 处理MCU对指令的响应
    def receive_cmd_ffff_fun(self, cmd_buff: bytearray, cmd_len: int):
        target_function = self.function_cmd_respond_map.get(cmd_buff[4]<<8 | cmd_buff[5])
        if target_function is not None:
            target_function(cmd_buff, cmd_len)
        else:
            print("cmd_ffff respond cmd_id fun no found:", hex(cmd_buff[4]<<8 | cmd_buff[5]))
    
    # 按照指令ID, 分发到对应的函数处理
    def publish_cmd(self, cmd_buff: bytearray, cmd_len: int):
        target_function = self.function_cmd_map.get(cmd_buff[2]<<8 | cmd_buff[3])
        if target_function is not None:
            target_function(cmd_buff, cmd_len)
        else:
            print("publish_cmd cmd_id fun no found:", hex(cmd_buff[2]<<8 | cmd_buff[3]))
    
    def get_signalval(self, byteval):
        return struct.unpack('b', bytes([byteval]))[0]
    ###########################################################################
