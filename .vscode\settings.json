{"python.testing.pytestArgs": ["."], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}\\api\\m65q68_a0\\;${workspaceFolder}\\api\\bridge_brd_dev\\bridge_mcu;${workspaceFolder}\\api\\bridge_brd_dev\\bridge_orin;${workspaceFolder}\\api\\m66s68_a0\\;${workspaceFolder}\\api\\instr\\;${workspaceFolder}\\test\\;${env:PYTHONPATH}"}, "python.analysis.extraPaths": ["./api/m65q68_a0", "./api/m66s68_a0", "./api/instr", "./test", "./api/m65d68_a0"]}