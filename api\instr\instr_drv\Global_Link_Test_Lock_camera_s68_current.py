'''
Revision2 modification:
    1. change the S68 M2CMIPIInit(), dlysetting=18 to dlysetting=5, for 031 sensor
    2. removed all dt_pass_en configuration. default is 0, not bypass. 
    3. add another sensor enable before S68 M2CMIPIInit() for all sensors?
Revision3 modification:
    1. 移除两次sensor init配置
    2. 将dco clk setting改为36， 130MHz

Note:
    1. dlysetting set to 5 for all sensor
    2. dt_pass_en only for multi-date type sensor, soc display issue need to try the ISP or redraw function by <PERSON><PERSON>qi<PERSON>
'''


# import wxP.1ytho.1nxFF
import sys
import csv
import os
import time
import datetime
# import wx

from Common.M65Q68_Common_Fuction_A0 import *
from Common_var.M66S68_Common_Fuction_A0 import *

# from instr_drv.Keysight_53230A_pyth3 import *
# from instr_drv.SiglentSDS5Xserial import *
instr_sel = True  # False: instr not selected
if instr_sel:
    # from HWInterface import HWImpl
    from instr_drv.<PERSON><PERSON>2230_py3 import *
    # from instr_drv.IT6332B import *
    # from instr_drv.oven_haituo import *
    # from instr_drv.DC5508U_TempMeter import *
    # from instr_drv.gpp_4323_py3 import *
    from instr_drv.IT6322B import *
    from instr_drv.Keysight_53230A_pyth3 import *
    from instr_drv.SiglentSDS5Xserial_20250605 import *
    

# oven = OVEN(port='com23')
#
# "Q68: TT85, s68_0: tt, s68_1: tt, s68_2: tt, s68_3: tt"
# Gpp433 = GPP_4323('com19')
power_camera = IT6322B(usb_addr="USB0::0x2EC7::0x6300::800068020757210071::INSTR")
power_q68 = Keithley2230(usb_addr="USB0::0x05E6::0x2230::9211136::INSTR")

# USB0::0x0957::0x1907::MY50003536::INSTR
meter = Keysight_53230A()
pngsave = SiglentSDS5034X(porttype='usb')
#messgebox to input part id & chip id
from tkinter import *
BOARDID = 0 #define global parameter to set boardid
CHIPID = 0 #define global parameter to set chipid
root=Tk()
root.geometry('300x300')

l1=Label(root,text='BOARD ID')
l1.pack()
boardid_text = StringVar()
boardid_set = Entry(root,textvariable=boardid_text)
boardid_text.set("")
boardid_set.pack()

l2=Label(root,text='CHIP ID')
l2.pack()
chipid_text = StringVar()
chipid_set = Entry(root,textvariable=chipid_text)
chipid_text.set("")
chipid_set.pack()

def on_click():
    global BOARDID
    global CHIPID
    BOARDID = boardid_text.get()
    CHIPID = chipid_text.get()
    string=str("sheet is:%s"%(BOARDID))
    string1=str("chipid is:%s"%(CHIPID))
    print(string)
    print(string1)
    root.destroy()
    
Button(root,text='press',command=on_click).pack()
root.mainloop()

SKEW                = ('R5_TT5',)
STEMP                = (25,)
VOLTAGE     =   [(1.2,1.8,1.8),]
VOLTYP              = ('Keithley2230', ) # ('LDO','Keithley2230',) # power supply type, on board LDO or 4 sense power supply
PLLINTERNALLDO      = (6, ) # PLL internal LDO to provide PLL power including aldo_V & dldo_V, 0:0.85V, 1:0.9V, 2:0.95V, 3:1V
PLLINTERNALLDO_DICT = {0:0.7, 1:0.9, 2:0.95, 3:1, 4:1}
ANAMUX              = (2, ) # Routed out PLL analog test signal, 0:vdddig09, 1:vddana09, 2:cp_vtrack, 3:vctrl
ANAMUX_DICT         = {0:'vcm_ahp',1:'vcm_ctle', 2:'vcm_driver', 3:'vcm_rxbuffer', 4:'vcm_t0', 5:'vcm_vga', 6:'vcm_afe_out', 7:'avdd_afe' }
DRIFT               = (25,-40,85,)
Drift_type          = {-40:(-40,25,85), 25:(25,-40,85),85:(85,25,-40)}
LOCK_MODE           = (1,)

# frame_std = 20
h0_std = 20
# VIDEO_WIN = [0,1,6,7]
#==================================================================================/
KLINK        =   [0,1,2,3]              # 表示接几个c3的lane link0~3

RATE         =   [2,2,2,2]              # link rate,  initial(default:cfg1), 1-3G, 2-6G; link0, link1, link2, link3
RATE_final   =   [2,2,2,2]              # link rate,  finally set(change when Linked)
BCRATE       =   [0,0,0,0]              # q68 -> S68 data rate: 0:150M, 1:187.5M, 2/3:200M  
DTbypass     =   [0,0,0,0]              # 多data type时必须改为1; link0, link1, link2, link3
vcid         =   [0,1,2,3]              # set vcid of each link(active when Camera's vcid=0)

pcs_set      =   [0,0,0,0]              # 0:8b/10b (0,0,0,0),  1: 66/64d [(1,1,1,1)
fec_bypass   =   [0,0,0,0]              # 1: fec bypass
#==================================================================================================================/

link_camera = {0:'Huayang_031', 1:'Huayang_031', 2:'Huayang_031', 3:'Huayang_031',}     #需要根据link上接的camera进行修改
# link_camera  = {0:'s68_VG',1: 's68_VG',2:'s68_VG',3:'s68_VG',}                       # S68 video generator, with Fsync
# link_camera = {0:'q68_VG',1: 'q68_VG',2:'q68_VG',3:'q68_VG',}                       # Q68 video generator, instead of Link data
                                        
q68_iic_addr =  0x73                        # Q68地址, 0x31#0x73
s68_iic_dev  =  [0x40, 0x40, 0x40, 0x40]    # s68地址，需要根据实际s68地址进行改动
#==================================================================================================================/
aggregation_mode = 'RoundRobin_2csi'        # 'RoundRobin_1csi' '4W1H_2csi'  #'1W4H_2csi' #'2W1H_2csi'  '3W1H_2csi' #'RoundRobin_4csi_1video'  '2W1H_4csi'
csi_mode = 'dphy_2csi_1c4d'                 # 'dphy_4csi_1c2d'  #'dphy_2csi_1c4d' dphy_1csi_1c4d 'dphy_cphy_2csi_1c4d' # 'cphy_2csi_4trio'  'cphy_4csi_2trio'
#==================================================================================================================/
MIPI_timing_dterm    = [0,0,0,0]            # S68 MIPI timing: d-term-en
MIPI_timing_hssettle = [0,0,0,0]            # S68 MIPI timing: hs-settle
#==================================================================================================================/
s68_res_dev        = [0x20, 0x21, 0x22, 0x23]           # s68转译地址可自行定义，link0,link1,link2,link3
s68_res_sensor_dev = [0x24, 0x25, 0x26, 0x27]           # sensor转译地址可自行定义，link0,link1,link2,link3
#===================================================================================================================/
sensor_dev = {'NIO_031':0, 'PHYbrd_NIO_031':0, 'xiaopeng_031_RAW12': 0x1A,'NIO_1Mega':0,'NIO_2Mega':0x36,'NIO_8Mega':16, 'x8B':0x36, 'SG2_IMX390C':0, 'Huayang_031':0, 's68_VG':0, 'q68_VG':0}    # sensor iic address, set to '0' if no register access needed
s68_sensor_dev = [sensor_dev[link_camera[0]], sensor_dev[link_camera[1]], sensor_dev[link_camera[2]], sensor_dev[link_camera[3]]] 
#==================================================================================================================/

q68 =  M65Q68_A0(dongle='stm32', id=0, bus='i2c')# G9PH DS board  
q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')
s680 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link0
s681 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link1
s682 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link2
s683 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link3

camera_id = {0:'N1X01G02_S143',1:'N1X01G02_TT',2:'X3X03105_F200',3:'NBX08B03_S138',}
PHY_TEST = 0
link_en = 1
#==================================================================================/
link_cable = ['15m','1m','2m','1m',]
# Gpp433.VoltageSet(channel=3, voltage=0)
# time.sleep(1)
# Gpp433.PowerOn(channel=4, power='OFF')
# time.sleep(1)
# Keithley2230G.TurnOutputsOff()      
# time.sleep(1)
# Gpp433.PowerOn(channel=3, power='ON')
# time.sleep(1)
# Keithley2230G.TurnOutputsOn()
# time.sleep(2)
# Gpp433.VoltageSet(channel=3, voltage=1.8)
# time.sleep(3)
# q68.c2m.wr_tx_link0_phy_ana_ctrl0_fields(reset_async_txbc=1)
# q68.c2m.wr_tx_link1_phy_ana_ctrl0_fields(reset_async_txbc=1)
# q68.c2m.wr_tx_link2_phy_ana_ctrl0_fields(reset_async_txbc=1)
# q68.c2m.wr_tx_link3_phy_ana_ctrl0_fields(reset_async_txbc=1)

data_address = "D:\\project\\m65q68_r5\\Raw_Data\\01_test\\Global\\Link_Lock\\"+str(BOARDID)+"_"+str(CHIPID)+"_"+"link_lock_test"+"_"+datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')+".csv"     
                 
def PRBSCheck():
    
        name=['time','BOARDID','CHIPID','starttemp','forwardrate','lock_read_pre','lock_ctrl_en','rd_lock_ctrl_en','link0_rate','link1_rate','link2_rate','link3_rate','rd_lockmode','lockpin_read_loss','relock_lockpin_read','error_flag','trigger_num_expect','testcase','caseloss','caselock','low_duration','rd_low_duration', 'countloss', \
           'countloslock', 'tiggerlowmin_relock', 'triggerlowmax_relock', 'high_duration', 'rd_high_duration', 'tiggerhighmin_relock', 'triggerhighmax_relock','vdd10','vdd18','vddio',\
           'h0_std','s680_error','s681_error','s682_error','s683_error','flag','camera_id[0]','camera_id[1]','camera_id[2]','camera_id[3]','cable_length[0]','cable_length[1]','cable_length[2]','cable_length[3]']
        Log_Q68_Status_name = q68.Log_Q68_Status( title = 1)
        Log_S68_Status_name = q68_remote.Log_S68_Status(title = 1)
        Log_FaultHandler_name = q68.Log_FaultHandler(title = 1)
        q68.Logging(filename=data_address, data=name+Log_Q68_Status_name+Log_S68_Status_name+Log_FaultHandler_name)
        for skew in SKEW:
            for vdd10,vddio,vdd18 in VOLTAGE:
                for starttemp in STEMP:
                    
                    # Gpp433.VoltageSet(channel=1, voltage=0)
                    # time.sleep(0.2)
                    # oven.settemp(starttemp)
                    #     # oven.start_stop()
                    # temp_measure=oven.tempread()
                    # while ((abs((temp_measure-starttemp)))>2):
                    #     time.sleep(30)
                    #     temp_measure=oven.tempread()
                    # time.sleep(10)              
                    #
                    
                    """ config Q68 + S68 """
                    link0_status = q68.c2m.rd_test_fsm_status1_link0()                        
                    link1_status = q68.c2m.rd_test_fsm_status1_link1()                        
                    link2_status = q68.c2m.rd_test_fsm_status2_link2()                        
                    link3_status = q68.c2m.rd_test_fsm_status2_link3()                        
                    print('Link Status:',link0_status,link1_status,link2_status,link3_status) 
                    for forwardrate in [1,2,]: #forward channel rate sweep; 1->3G, 2->6G
                        """ power down -> on """    
                        power_q68.TurnOutputsOff()
                        power_camera.TurnOutputsOff()
                    
                        time.sleep(2)
                        power_q68.TurnOutputsOn()
                        power_camera.TurnOutputsOn()
                        time.sleep(2)    
                        # Gpp433.VoltageSet(channel=3, voltage=0)
                        # time.sleep(1)
                        # Gpp433.PowerOn(channel=4, power='OFF')
                        # time.sleep(1)
                        # Keithley2230G.TurnOutputsOff()      
                        # time.sleep(1)
                        # Keithley2230G.SetVoltages(v1=vdd10, v2=vddio, v3=vdd18)  # provide supply
                        # time.sleep(1)
                        # Gpp433.PowerOn(channel=3, power='ON')
                        # time.sleep(1)
                        # Keithley2230G.TurnOutputsOn()
                        # time.sleep(2)
                        # Gpp433.VoltageSet(channel=3, voltage=1.8)
                        # time.sleep(3)
                        # q68.CommonInit() #configure q68 i2c to slave mode and modify q68 parameters
                        # Gpp433.PowerOn(channel=1, power='ON')
                        # time.sleep(2)
                        # Gpp433.PowerOn(channel=2, power='ON')
                        # time.sleep(2)
                        # Gpp433.PowerOn(channel=4, power='ON')
                        # time.sleep(2)
                        # Step0: default value change
                        
                        flag=0
                        q68.c2m.wr_sys_cfg_link_ctrl0_fields(lock_mode = 0, lock_high_duration = 1, lock_low_duration = 1)
                        
                        '''# Step1: 设置 Q68 link速率，以匹配S68速率'''
                
                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])  # set forward data rate
                        q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=BCRATE[0], bc_rate1=BCRATE[1], bc_rate2=BCRATE[2], bc_rate3=BCRATE[3])   # set backward data rate
                
                        '''# Step2： 设置 link参数、编码方式(FEC, 8b10b, 64b66b)'''
                        
                        q68.Q68_C3_6G_Init(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3]) #link parameters
                        
                        for link in KLINK:
                            q68.FECcoding(link=link, pcs_set=pcs_set[link], fec_bypass=fec_bypass[link])
                        
                        '''# Step3： 设置 4路S68、sensor I2C转译地址'''                    
  
                        for link in KLINK:
                            q68_remote.S68_AddrTrans(link=link, q68_iic_addr=q68_iic_addr ,s68_iic_addr=s68_iic_dev[link], s68_retrans_addr=s68_res_dev[link], sensor_addr=s68_sensor_dev[link], sensor_retrans_addr=s68_res_sensor_dev[link])               
          
                        for link in KLINK:
                            q68_remote.S68_FECCoding(s68_res_dev=s68_res_dev[link], pcs_set=pcs_set[link],fec_bypass=fec_bypass[link], RATE_final=forwardrate)       
        
                        '''# Step5: 设置 link速率(final)，参数 '''  
                            
                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=forwardrate, rate1=forwardrate, rate2=forwardrate, rate3=forwardrate)
                        q68.Q68_C3_6G_Init(rate0=forwardrate, rate1=forwardrate, rate2=forwardrate, rate3=forwardrate)  #for 6G init
        
                        print('link0 status is: ', q68.c2m.rd_test_fsm_status1_link0()) 
                        print('link1 status is: ', q68.c2m.rd_test_fsm_status1_link1()) 
                        print('link2 status is: ', q68.c2m.rd_test_fsm_status2_link2()) 
                        print('link3 status is: ', q68.c2m.rd_test_fsm_status2_link3())
                        
                        meter.TriggerCountMesSet() #configure frequency instrument to 
                        (max,min) = meter.InputLevelTest() #meaure error pin voltage
                        if (max>1):
                            lock_read_pre = 1
                        else:
                            lock_read_pre = 0
                        high_duration=0
                        rd_high_duration = q68.c2m.rd_sys_cfg_link_ctrl0_lock_high_duration()
                        tiggerhighmin_relock = 0
                        triggerhighmax_relock = 0  
                        error_flag=0 
                        lockpin_read_loss=0
                        relock_lockpin_read=0
                        for lock_ctrl_en in [15,]: #sweep lock pin enable bit
                        # for lock_ctrl_en in [15,]: #sweep lock pin enable bit
                            q68.c2m.wr_fh_lock_ctrl0_fields(en = lock_ctrl_en) #set lock pin enable  
                            for lock_mode in LOCK_MODE:
                                q68.c2m.wr_sys_cfg_link_ctrl0_fields(lock_mode=lock_mode) #set lock mode 0:pull up if all link locks,1:using falling edge as link lock/loss

                                # for link0_rate,link1_rate,link2_rate,link3_rate  in [(2,2,1,2),]:
                                for link0_rate in [1,]:    
                                    for link1_rate in [1,]:
                                        for link2_rate in [1,]:
                                            for link3_rate in [1,]:
                                                
                                                if lock_mode==0:
                                                    meter.TriggerCountMesSet() #configure frequency counter to DC mode and set trigger level to 1.1V
                                                    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=link0_rate, rate1=link1_rate, rate2=link2_rate, rate3=link3_rate)
                                                    (max,min) = meter.InputLevelTest()
                                                    if (max>1):
                                                        lockpin_read_loss = 1
                                                    else:
                                                        lockpin_read_loss = 0
                                                    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=forwardrate,rate1=forwardrate, rate2=forwardrate, rate3=forwardrate)
                                                    (max,min) = meter.InputLevelTest()
                                                    if (max>1):
                                                        relock_lockpin_read = 1
                                                    else:
                                                        relock_lockpin_read = 0
                                                    if ((not(lock_ctrl_en & 0x01))|(forwardrate==link0_rate))&((not((lock_ctrl_en & 0x02)>1))|(forwardrate==link1_rate))&((not((lock_ctrl_en & 0x04)>>2))|(forwardrate==link2_rate))&((not((lock_ctrl_en & 0x08)>>3))|(forwardrate==link3_rate)):   
                                                        expect_lock_loss = 1
                                                    else:
                                                        expect_lock_loss = 0
                                                    if (expect_lock_loss == lockpin_read_loss):
                                                        error_flag=0
                                                    else:
                                                        error_flag=1
                                                    rd_lockmode = q68.c2m.rd_sys_cfg_link_ctrl0_lock_mode()
                                                    rd_low_duration = q68.c2m.rd_sys_cfg_link_ctrl0_lock_low_duration()
                                                    rd_lock_ctrl_en = q68.c2m.rd_fh_lock_ctrl0_en()
                                                    testcase=0
                                                    caselock=0
                                                    caseloss=0
                                                    low_duration=0
                                                    countloss=0
                                                    tiggerlowmin_loss=0
                                                    triggerlowmax_loss=0
                                                    countloslock=0 
                                                    tiggerlowmin_relock=0
                                                    triggerlowmax_relock=0
                                                    
                                                    q68.EfhRouterStatusCheck()             # enable fault detect: pipe0~7          
                                                    q68.EfhStatusClr()                     # clear fault(link/router/com/gpio..) one time
                                                    for i in range(4):
                                                        q68_remote.dongle.devAddr = s68_res_dev[i]   
                                                        q68_remote.EfhStatusClr()          # clear s68 fault
                                                        q68.EfhTRxStatusCheck(i=link)      # enable fault detect: link
    
                                                    Log_Q68_Status = q68.Log_Q68_Status()
                                                    Log_S68_Status = q68_remote.Log_S68_Status()
                                                    Log_FaultHandler = q68.Log_FaultHandler()
                                                    
                                                    trigger_num_expect=0
                                                    data_row=[datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'),str(BOARDID),str(CHIPID),starttemp,forwardrate,lock_read_pre,lock_ctrl_en,rd_lock_ctrl_en,link0_rate,link1_rate,link2_rate,link3_rate,\
                                                        rd_lockmode,lockpin_read_loss,relock_lockpin_read,error_flag,trigger_num_expect,testcase,caselock,caseloss,low_duration,rd_low_duration, countloss, countloslock, tiggerlowmin_relock, triggerlowmax_relock, high_duration, rd_high_duration, tiggerhighmin_relock, triggerhighmax_relock,\
                                                        vdd10,vdd18,vddio,h0_std,flag,camera_id[0],camera_id[1],camera_id[2],camera_id[3],link_cable[0],link_cable[1],link_cable[2],link_cable[3]]
                                                    q68.Logging(filename=data_address, data=data_row+Log_Q68_Status+Log_S68_Status+Log_FaultHandler)              
                                                else:
                                                    rd_lockmode = q68.c2m.rd_sys_cfg_link_ctrl0_lock_mode()
                                                    rd_low_duration = q68.c2m.rd_sys_cfg_link_ctrl0_lock_low_duration()
                                                    rd_lock_ctrl_en = q68.c2m.rd_fh_lock_ctrl0_en()
                                                    # testcase=0
                                                    # caselock=0
                                                    # caseloss=0
                                                    # low_duration=0
                                                    countloss=0
                                                    # tiggerlowmin_loss=0
                                                    # triggerlowmax_loss=0
                                                    # countloslock=0 
                                                    tiggerlowmin_relock=0
                                                    triggerlowmax_relock=0
                                                    for low_duration in [0,]:
                                                        q68.c2m.wr_sys_cfg_link_ctrl0_fields(lock_low_duration = low_duration)
                                                        for testcase in ['neg_trigger_num',]: #1表示测试low trigger的宽度, 0表示测试low trigger的个数
                                                            
                                                            for caseloss in ['setonebyone','wholeset',]:
                                                                for caselock in ['setonebyone','wholeset',]:
                                                                    
                                                                    trigger_num_expect=0
                                                                    if testcase == 'neg_trigger_num':
                                                                        meter.TriggerCountMesSet()
                                                                        time.sleep(0.1)
                                                                        meter.TriggerCountMesStart()


                                                                    
                                                                    if caseloss=='setonebyone':
                                                                        pngsave.Set_TrigMode(trig_mode='SINGLE', timebase_scale='5ms') 
                                                                    else:
                                                                        pngsave.Set_TrigMode(trig_mode='SINGLE', timebase_scale='1ms')                                                                   
                                                                    # pngsave.Set_TrigMode(trig_mode='SINGLE')
                                                                    time.sleep(0.8)

                                                                  
                                                                  
                                                                    if caseloss=='setonebyone':
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=link0_rate)
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=link1_rate)
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=link2_rate)
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=link3_rate)
                                                                    else:
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=link0_rate, rate1=link1_rate, rate2=link2_rate, rate3=link3_rate)
                                                                    
                                                                    time.sleep(0.2)
                                                                    filepath = "U-disk0/trigger_20250610/"+"lowdu"+str(low_duration)+"_lock_en_"+str(lock_ctrl_en)+"_forwardR_"+str(forwardrate)+"_link0_"+ str(link0_rate)+"_link1_"+str(link1_rate)+"_link2_"+str(link2_rate)+"_link3_"+str(link3_rate)+"_caseloss"+"_"+caseloss+"_"+caselock+'_'+"voltage_"+str(vdd10)+"_temp"+str(starttemp)+".png"
                                                                    pngsave.Save_Image(filepath)
                                                                    time.sleep(0.7)
                                                                    # if testcase=='neg_trigger_num':
                                                                    #     meter.TriggerCountMesStop()
                                                                    #     time.sleep(0.2)
                                                                    #     countloss = meter.TriggerCountMeas()
                                                                    #     meter.TriggerCountMesStart()
                                                                    #
                                                                    #
                                                                    # time.sleep(0.2)
                                                                    if caselock=='setonebyone':
                                                                        pngsave.Set_TrigMode(trig_mode='SINGLE', timebase_scale='5ms') 
                                                                    else:
                                                                        pngsave.Set_TrigMode(trig_mode='SINGLE', timebase_scale='1ms')   
                                                                    time.sleep(0.8)
                                                                    if caselock=='setonebyone':
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=forwardrate)
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=forwardrate)
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=forwardrate)
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=forwardrate)
                                                                    else:
                                                                        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=forwardrate,rate1=forwardrate, rate2=forwardrate, rate3=forwardrate)
                                                                    time.sleep(0.2) 
                                                                    filepath = "U-disk0/trigger_20250610/"+"lowdu"+str(low_duration)+"_lock_en_"+str(lock_ctrl_en)+"_forwardR_"+str(forwardrate)+"_link0_"+ str(link0_rate)+"_link1_"+str(link1_rate)+"_link2_"+str(link2_rate)+"_link3_"+str(link3_rate)+"_caselock"+"_"+caseloss+"_"+caselock+'_'+"voltage"+str(vdd10)+"_temp"+str(starttemp)+".png"
                                                                    pngsave.Save_Image(filepath)
                                                                    time.sleep(0.7) 
                                                                    if testcase=='neg_trigger_num':
                                                                        meter.TriggerCountMesStop()
                                                                        time.sleep(0.2)
                                                                        countlock = meter.TriggerCountMeas()
                                                                        countloslock = countloss + countlock
                                                                        
                                                                    
                                                                    # time.sleep(0.3)
                                                                    if ((lock_ctrl_en & 0x01)==1)&(forwardrate!=link0_rate):
                                                                        if caseloss=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                        if caselock=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                        
                                                                    if ((lock_ctrl_en & 0x02)==2)&(forwardrate!=link1_rate):
                                                                        if caseloss=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                        if caselock=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                            
                                                                    if ((lock_ctrl_en & 0x04)==4)&(forwardrate!=link2_rate):
                                                                        if caseloss=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                        if caselock=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                            
                                                                    if ((lock_ctrl_en & 0x08)==8)&(forwardrate!=link3_rate):
                                                                        if caseloss=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                            
                                                                        if caselock=='setonebyone':
                                                                            trigger_num_expect += 1
                                                                            
                                                                    if ((not(lock_ctrl_en & 0x01))|(forwardrate==link0_rate))&((not((lock_ctrl_en & 0x02)>1))|(forwardrate==link1_rate))&((not((lock_ctrl_en & 0x04)>>2))|(forwardrate==link2_rate))&((not((lock_ctrl_en & 0x08)>>3))|(forwardrate==link3_rate)):
                                                                        pass
                                                                    else:
                                                                        if caseloss=='wholeset':
                                                                            trigger_num_expect += 1
                                                                        
                                                                        if caselock=='wholeset':
                                                                            trigger_num_expect += 1
                                                                        
                                                                    if (trigger_num_expect == countloslock):  
                                                                        error_flag=0
                                                                    else:
                                                                        error_flag=1  
                                                                    rd_lockmode = q68.c2m.rd_sys_cfg_link_ctrl0_lock_mode()
                                                                    rd_low_duration = q68.c2m.rd_sys_cfg_link_ctrl0_lock_low_duration()
                                                                    rd_lock_ctrl_en = q68.c2m.rd_fh_lock_ctrl0_en()
                                                                    
                                                                    q68.EfhRouterStatusCheck()             # enable fault detect: pipe0~7          
                                                                    q68.EfhStatusClr()                     # clear fault(link/router/com/gpio..) one time
                                                                    for i in range(4):
                                                                        q68_remote.dongle.devAddr = s68_res_dev[i]   
                                                                        q68_remote.EfhStatusClr()          # clear s68 fault
                                                                        q68.EfhTRxStatusCheck(i=link)      # enable fault detect: link
                    
                                                                    Log_Q68_Status = q68.Log_Q68_Status()
                                                                    Log_S68_Status = q68_remote.Log_S68_Status()
                                                                    Log_FaultHandler = q68.Log_FaultHandler()
                                                                    data_row=[datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'),str(BOARDID),str(CHIPID),starttemp,forwardrate,lock_read_pre,lock_ctrl_en,rd_lock_ctrl_en,link0_rate,link1_rate,link2_rate,link3_rate,\
                                                                              rd_lockmode,lockpin_read_loss,relock_lockpin_read,error_flag,trigger_num_expect,testcase,caselock,caseloss,low_duration,rd_low_duration, countloss, countloslock, tiggerlowmin_relock, triggerlowmax_relock, high_duration, rd_high_duration, tiggerhighmin_relock, triggerhighmax_relock,\
                                                                              vdd10,vdd18,vddio,h0_std,flag,camera_id[0],camera_id[1],camera_id[2],camera_id[3],link_cable[0],link_cable[1],link_cable[2],link_cable[3]]
                                                                    q68.Logging(filename=data_address, data=data_row+Log_Q68_Status+Log_S68_Status+Log_FaultHandler) 
                                                                    
                        # test low duration time
                        for low_duration in range(0,8):
                            q68.c2m.wr_fh_lock_ctrl0_fields(en = 15) 
                            q68.c2m.wr_sys_cfg_link_ctrl0_fields(lock_mode = 1)
                            q68.c2m.wr_sys_cfg_link_ctrl0_fields(lock_low_duration = low_duration)
                            rd_lockmode = q68.c2m.rd_sys_cfg_link_ctrl0_lock_mode()
                            rd_low_duration = q68.c2m.rd_sys_cfg_link_ctrl0_lock_low_duration()
                            rd_lock_ctrl_en = q68.c2m.rd_fh_lock_ctrl0_en()
                            time.sleep(0.2)
                            meter.SingleChannelTimeInter()
                            meter.Histogram()
                            time.sleep(0.2)
                            if forwardrate==1:
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=2)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=2)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=2)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=2) 
                            else:
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=1)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=1)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=1)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=1) 
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=forwardrate)
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=forwardrate)
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=forwardrate)
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=forwardrate)
                            time.sleep(0.2)
                            (tiggerlowmin_relock, triggerlowmax_relock) = meter.HistogramRead()
                            q68.EfhRouterStatusCheck()             # enable fault detect: pipe0~7          
                            q68.EfhStatusClr()                     # clear fault(link/router/com/gpio..) one time
                            for i in range(4):
                                q68_remote.dongle.devAddr = s68_res_dev[i]   
                                q68_remote.EfhStatusClr()          # clear s68 fault
                                q68.EfhTRxStatusCheck(i=link)      # enable fault detect: link

                            Log_Q68_Status = q68.Log_Q68_Status()
                            Log_S68_Status = q68_remote.Log_S68_Status()
                            Log_FaultHandler = q68.Log_FaultHandler()
                            
                            data_row=[datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'),str(BOARDID),str(CHIPID),starttemp,forwardrate,lock_read_pre,lock_ctrl_en,rd_lock_ctrl_en,link0_rate,link1_rate,link2_rate,link3_rate,\
                                      rd_lockmode,lockpin_read_loss,relock_lockpin_read,error_flag,trigger_num_expect,testcase,caselock,caseloss,low_duration,rd_low_duration, countloss, countloslock, tiggerlowmin_relock, triggerlowmax_relock, high_duration, rd_high_duration, tiggerhighmin_relock, triggerhighmax_relock,\
                                      vdd10,vdd18,vddio,h0_std,flag,camera_id[0],camera_id[1],camera_id[2],camera_id[3],link_cable[0],link_cable[1],link_cable[2],link_cable[3]]
                            q68.Logging(filename=data_address, data=data_row+Log_Q68_Status+Log_S68_Status+Log_FaultHandler) 
                                                                       
                        # test high duration time
                        for high_duration in range(0,8): 
                            q68.c2m.wr_fh_lock_ctrl0_fields(en = 15) 
                            q68.c2m.wr_sys_cfg_link_ctrl0_fields(lock_mode = 1, lock_high_duration = high_duration)
                            rd_high_duration = q68.c2m.rd_sys_cfg_link_ctrl0_lock_high_duration()
                            rd_lockmode = q68.c2m.rd_sys_cfg_link_ctrl0_lock_mode()
                            rd_lock_ctrl_en = q68.c2m.rd_fh_lock_ctrl0_en()
                            time.sleep(0.1)
                            if forwardrate==1:
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=2)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=2)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=2)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=2) 
                            else:
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=1)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=1)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=1)
                                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=1) 
                            
                            meter.SingleChannelTimeInterPOS()
                            meter.Histogram()
                            time.sleep(0.3)    
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=forwardrate)
                            # q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=forwardrate)
                            # q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=forwardrate)
                            # q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=forwardrate)
                            (tiggerhighmin_relock, triggerhighmax_relock) = meter.HistogramRead()
                            # time.sleep(0.2)
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate1=forwardrate)
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate2=forwardrate)
                            q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate3=forwardrate)                     

                         
                            q68.EfhRouterStatusCheck()             # enable fault detect: pipe0~7          
                            q68.EfhStatusClr()                     # clear fault(link/router/com/gpio..) one time
                            for i in range(4):
                                q68_remote.dongle.devAddr = s68_res_dev[i]   
                                q68_remote.EfhStatusClr()          # clear s68 fault
                                q68.EfhTRxStatusCheck(i=link)      # enable fault detect: link

                            Log_Q68_Status = q68.Log_Q68_Status()
                            Log_S68_Status = q68_remote.Log_S68_Status()
                            Log_FaultHandler = q68.Log_FaultHandler()
                            
                            data_row=[datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'),str(BOARDID),str(CHIPID),starttemp,forwardrate,lock_read_pre,lock_ctrl_en,rd_lock_ctrl_en,link0_rate,link1_rate,link2_rate,link3_rate,rd_lockmode,lockpin_read_loss,relock_lockpin_read,\
                                      error_flag,trigger_num_expect,testcase,caselock,caseloss,low_duration,rd_low_duration, countloss, countloslock, tiggerlowmin_relock, triggerlowmax_relock, high_duration, rd_high_duration, tiggerhighmin_relock, triggerhighmax_relock,\
                                      vdd10,vdd18,vddio,h0_std,flag,camera_id[0],camera_id[1],camera_id[2],camera_id[3],link_cable[0],link_cable[1],link_cable[2],link_cable[3]]
                            q68.Logging(filename=data_address, data=data_row+Log_Q68_Status+Log_S68_Status+Log_FaultHandler) 
                    
                    
            
if __name__ == "__main__":
    
    
    PRBSCheck()

