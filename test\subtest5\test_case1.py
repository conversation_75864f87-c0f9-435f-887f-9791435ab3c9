import sys
import csv
import os
import time

from Common.M65Q68_Common_Fuction_A0 import *
 
q68 = M65Q68_A0(dongle='stm32', id=0, bus='i2c')# G9PH DS board  

def test_5a():                                           
    RouterClk = 4     #when VG used,select clock from VG
    aggregation_mode = 'RoundRobin_1csi'
    vcid         =   [0,1,2,3]              # set vcid of each link(active when Camera's vcid=0)
    DTbypass     =   [0,0,0,0]              # 多data type时必须改为1; link0, link1, link2, link3
    KLINK        =   [0,1,2,3]              # 表示接几个c3的lane link0~3
    q68.Router_Config(vcid=vcid, aggregation_mode=aggregation_mode, DTbypass=DTbypass, RouterClk=RouterClk, KLINK=KLINK)  # '2W1H'
    
    csi_mode = 'dphy_2csi_1c4d'                 # 'dphy_4csi_1c2d'  #'dphy_2csi_1c4d' dphy_1csi_1c4d 'dphy_cphy_2csi_1c4d' # 'cphy_2csi_4trio'  'cphy_4csi_2trio'
    q68.Q68_MIPI_Init(csi_mode= csi_mode)       #'cphy_2csi_4trio'
    q68.Q68_VG_Gen(HV_res=19201280)        # generate VideGen directly,3840:3840x2160, 19201280:1920x1280,
    print("[Q68] Generate Q68 VG")
    
    print('status=', q68.Log_FaultHandler())
    