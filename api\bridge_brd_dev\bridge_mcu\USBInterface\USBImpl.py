#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from USBInterface import USBDataDeal
from USBInterface import CmdFuncation

# 获取连接的设备列表
def usbdev_scan():
    return USBDataDeal.ser_ee_dev_scan()


# 开启指定的连接设备
def usbdev_open(port_name):
    USBDataDeal.serial_open(port_name)


# 关闭指令的连接设备
def usbdev_close():
    USBDataDeal.serial_close()


# ID:0x0000: 获取设备SN号
def usbdev_get_sn():
    return CmdFuncation.send_cmd_get_sn()


# ID:0x0001: 设置指定输出引脚的值
def usbdev_set_gpio(pin_num: int, pin_val: int):
    return CmdFuncation.send_cmd_set_gpio(pin_num, pin_val)


# ID:0x0002: 获取指定输入引脚的值
def usbdev_get_gpio(pin_num: int):
    return CmdFuncation.send_cmd_get_gpio(pin_num)


# ID:0x0003:设置I2C寄存器的值
# mode:0:r8v8 1:r8v16
def usbdev_i2c_set(mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):
    return CmdFuncation.send_cmd_i2c_set(mode, bus, dev_addr, reg_addr, reg_val)


# ID:0x0004:获取I2C寄存器的值
# mode 0:r8v8 1:r16v8
# return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
def usbdev_i2c_get(mode: int, bus: int, dev_addr: int, reg_addr: int):
    return CmdFuncation.send_cmd_i2c_get(mode, bus, dev_addr, reg_addr)

# ID:0x0005:设置数据的模式
# mode 0:真实数据(默认) 1:虚拟数据
def usbdev_set_mode(mode:int):
    return CmdFuncation.send_cmd_set_mode(mode)

# ID:0x0006:设置I2C速率
# bus:需要设置的总线
# speed：需要设置的频率(单位Kbps)
def usbdev_i2c_setbusspeed(bus: int, speed: int):
    return CmdFuncation.send_cmd_i2c_setbusspeed(bus, speed)