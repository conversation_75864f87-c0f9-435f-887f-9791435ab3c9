import os
import sys
import pytest
import time

# 项目路径设置
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.extend([
    PROJECT_ROOT,
    os.path.join(PROJECT_ROOT, 'api', 'm65q68_a0'),
    os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'),
    os.path.join(PROJECT_ROOT, 'api', 'instr')
])

# 导入硬件驱动
try:
    from Common.M65Q68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    from instr_drv.IT6322B import IT6322B
    from instr_drv.Keithley2230_py3 import <PERSON><PERSON>2230
    from instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
    print("✅ 硬件驱动导入成功")
except ImportError as e:
    print(f"⚠️ 硬件驱动导入失败: {e}")
    class IT6322B: pass
    class Keithley2230: pass
    class SiglentSDS5034X: pass
    class M65Q68_A0: pass
    class M65Q68_A0_Remote_M66S68: pass

# 硬件配置常量
VOLTAGE = [1.2, 1.8, 1.8]
KLINK = [0,]
RATE = [2, 2, 2, 2]
BCRATE = [0, 0, 0, 0]
DTbypass = [0, 0, 0, 0]
vcid = [0, 1, 2, 3]
pcs_set = [0, 0, 0, 0]
fec_bypass = [0, 0, 0, 0]
q68_iic_addr = 0x73
s68_iic_dev = [0x40, 0x40, 0x40, 0x40]

# 全局设备对象
power_camera = None
power_q68 = None
q68 = None
q68_remote = None
s68_res_dev = None
oscilloscope = None

# 硬件控制函数
def init_global_objects():
    """初始化全局设备对象"""
    print("🔧 初始化全局设备对象...")

    global q68, q68_remote, s68_res_dev, q68_i2c2, q68_I2C2remote, s680, oscilloscope
    

    # 初始化Q68设备
    q68 = M65Q68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual')
    print("✅ q68 init")

    q68_remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00Q\x00$41Q\x197997', dongle='stm32', id=0, bus='i2c', optype='manual')
    print("✅ q68_remote init")
    
    q68_i2c2 = M65Q68_A0(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual')  # G9PH DS board, bus_chan=1 for I2C1 (SDA1, SCL1)
    print("✅ q68_i2c2 init")
   
    q68_I2C2remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', optype='manual')
    print("✅ q68_I2C2remote init")
    
    # S68转译地址
    s68_res_dev = [0x20, 0x21, 0x22, 0x23]

    # 初始化示波器 (可选，如果连接失败不影响其他测试)
    try:
        oscilloscope = SiglentSDS5034X(porttype='usb')
        print("✅ 示波器初始化成功")
    except Exception as e:
        print(f"⚠️ 示波器初始化失败: {e}")
        oscilloscope = None

def power_off():
    """电源关闭函数"""
    print("🔌 关闭电源...")
    power_q68.TurnOutputsOff()
    power_camera.TurnOutputsOff()
    time.sleep(2)

def power_on():
    """电源控制函数"""
    print("🔌 电源控制: 关闭 -> 开启")

    global power_camera, power_q68

    # 初始化电源设备
    try:
        power_camera = IT6322B(usb_addr="USB0::0x2EC7::0x6300::800068020757210071::INSTR")
        power_q68 = Keithley2230(usb_addr="USB0::0x05E6::0x2230::9211112::INSTR")
        print("✅ 电源设备初始化成功")
    except Exception as e:
        print(f"⚠️ 电源设备初始化失败: {e}")
        raise

    power_q68.TurnOutputsOff()
    power_camera.TurnOutputsOff()
    time.sleep(1)

    power_q68.SetCurrents(i1=1.2, i2=0.1, i3=0.2)
    power_q68.SetVoltages(VOLTAGE[0], VOLTAGE[1], VOLTAGE[2])
    power_q68.TurnOutputsOn()
    power_camera.TurnOutputsOn()
    time.sleep(1)

def setup_q68_s68_communication(active_links=None):
    """设置Q68和S68之间的通信"""
    if active_links is None:
        active_links = KLINK

    print(f"🔗 设置Q68和S68通信 (激活Links: {active_links})...")

    # 设置Q68链路速率
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])
    q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=BCRATE[0], bc_rate1=BCRATE[1], bc_rate2=BCRATE[2], bc_rate3=BCRATE[3])
    q68.Q68_C3_6G_Init(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])

    # 检查链路状态
    result = {
        'link0': q68.c2m.rd_test_fsm_status1_link0(),
        'link1': q68.c2m.rd_test_fsm_status1_link1(),
        'link2': q68.c2m.rd_test_fsm_status2_link2(),
        'link3': q68.c2m.rd_test_fsm_status2_link3()
    }

    print('🔗 链路状态:', result['link0'], result['link1'], result['link2'], result['link3'])
    print(f"✅ Q68和S68通信设置完成")
    return result

def configure_specific_links(target_links):
    """为特定的测试用例配置指定的Links

    Args:
        target_links: 要配置的link列表，如[0], [1], [0,1,2,3]等

    Returns:
        dict: 各link的状态
    """
    print(f"\n🔧 配置特定Links: {target_links}")

    # 验证link范围
    valid_links = [link for link in target_links if 0 <= link <= 3]
    if len(valid_links) != len(target_links):
        invalid_links = [link for link in target_links if link not in valid_links]
        print(f"⚠️ 警告: 无效的link {invalid_links}，将被忽略")

    if not valid_links:
        raise ValueError("没有有效的link可配置")

    # 检查配置后的状态
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    status_result = {}
    for link in valid_links:
        status = link_status_funcs[link]()
        status_result[f'link{link}'] = status
        print(f"  - Link{link} 状态: {status}")

    print(f"✅ 特定Links {valid_links} 配置完成\n")
    return status_result

def get_link_status(link_id=None):
    """获取指定link或所有link的状态

    Args:
        link_id: 指定的link ID (0-3)，如果为None则返回所有link状态

    Returns:
        int or dict: 单个link状态或所有link状态字典
    """
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    if link_id is not None:
        if 0 <= link_id <= 3:
            return link_status_funcs[link_id]()
        else:
            raise ValueError(f"无效的link_id: {link_id}，应该在0-3范围内")

    # 返回所有link状态
    return {
        'link0': link_status_funcs[0](),
        'link1': link_status_funcs[1](),
        'link2': link_status_funcs[2](),
        'link3': link_status_funcs[3](),
    }

# GPIO操作函数
def safe_gpio_remote_tx(q68_remote, gpio, tx_id, s68_dev_addr=None):
    """GPIO远程发送配置"""
    if s68_dev_addr is not None:
        original_addr = q68_remote.dongle.devAddr
        q68_remote.dongle.devAddr = s68_dev_addr

    try:
        q68_remote.M2CMFNSet(gpio=gpio, mfn=0)
        q68_remote.GPIORemoteTx(gpio=gpio, tx_id=tx_id)
        return True
    except Exception as e:
        print(f"❌ GPIO{gpio} 远程发送配置失败: {e}")
        return False
    finally:
        if s68_dev_addr is not None:
            q68_remote.dongle.devAddr = original_addr

def safe_gpio_remote_rx(q68_remote, gpio, rx_id, s68_dev_addr=None):
    """GPIO远程接收配置"""
    if s68_dev_addr is not None:
        original_addr = q68_remote.dongle.devAddr
        q68_remote.dongle.devAddr = s68_dev_addr

    try:
        q68_remote.M2CMFNSet(gpio=gpio, mfn=0)
        q68_remote.M2CGPIORemoteRx(gpio=gpio, rx_id=rx_id)
        return True
    except Exception as e:
        print(f"❌ GPIO{gpio} 远程接收配置失败: {e}")
        return False
    finally:
        if s68_dev_addr is not None:
            q68_remote.dongle.devAddr = original_addr

def safe_link_status_check():
    """链路状态检查"""
    try:
        return {
            'link0': q68.c2m.rd_test_fsm_status1_link0(),
            'link1': q68.c2m.rd_test_fsm_status1_link1(),
            'link2': q68.c2m.rd_test_fsm_status2_link2(),
            'link3': q68.c2m.rd_test_fsm_status2_link3(),
        }
    except Exception as e:
        print(f"❌ 链路状态检查失败: {e}")
        return {'link0': -1, 'link1': -1, 'link2': -1, 'link3': -1}

# Session-scope hardware fixture
@pytest.fixture(scope="session")
def hardware_setup():
    """基础硬件环境设置 - 只包含共性配置

    包含:
    1. 电源控制
    2. 基础设备对象初始化
    3. 基础通信设置
    """
    print("\n" + "="*60)
    print("🚀 基础硬件环境设置")
    print("="*60)
    # 电源控制
    power_on()

    # 初始化设备对象
    init_global_objects()

    # 基础Q68-S68通信设置 (使用默认KLINK配置)
    setup_q68_s68_communication()

    # 返回设备对象字典和配置函数
    devices = {
        'q68': q68,
        'q68_remote': q68_remote,
        'q68_i2c2': q68_i2c2,
        'q68_I2C2remote': q68_I2C2remote,
        's68_res_dev': s68_res_dev,
        'oscilloscope': oscilloscope,
        'configure_links': configure_specific_links,
        'get_link_status': get_link_status,
        'setup_communication': setup_q68_s68_communication,
        'power_off': power_off,
        'power_on': power_on,
        'safe_gpio_remote_tx': safe_gpio_remote_tx,
        'safe_gpio_remote_rx': safe_gpio_remote_rx,
        'safe_link_status_check': safe_link_status_check
    }

    yield devices

    print("\n🔧 基础硬件环境清理完成")


# 简化的设备访问fixtures
@pytest.fixture(scope="session")
def devices(hardware_setup):
    """返回所有设备对象的字典"""
    return hardware_setup


# ============================================================================
# 通用示波器截图方法
# ============================================================================

def create_universal_oscilloscope_screenshot_method():
    """创建通用的示波器截图方法类"""

    class UniversalOscilloscopeScreenshot:
        """通用示波器截图类 - 基于test_gpio_case6_8_s2d_auto2.py验证的逻辑"""

        def __init__(self, oscilloscope):
            import time
            import os
            self.oscilloscope = oscilloscope
            self.time = time
            self.os = os

        def create_screenshot_folders(self, base_folder, active_links, gpio_range):
            """创建截图文件夹结构"""
            # 创建基础文件夹 - 固定频率和扫频都放在同一个文件夹
            for link in active_links:
                for gpio in gpio_range:
                    # 创建主文件夹（固定频率和扫频都使用） - 使用os.path.join确保路径正确
                    folder_path = self.os.path.join(base_folder, f"link{link}_gpio{gpio}")
                    self.os.makedirs(folder_path, exist_ok=True)

        def screenshot_fixed_frequencies(self, gpio_num, active_links, osc_config):
            """固定频率截图模式 - 基于验证的逻辑"""
            try:
                if not self.oscilloscope:
                    return True

                frequency_list = osc_config['frequency_list']
                timebase_list = osc_config['timebase_list']

                # 为每个活跃Link分别截图
                for link_id in active_links:
                    print(f"    🔗 为Link{link_id}进行固定频率截图...")
                    print(f"    * 开始固定频率测试: {len(frequency_list)}个频率点")

                    for i, (frequency, timebase) in enumerate(zip(frequency_list, timebase_list)):
                        freq_display = f"{frequency}Hz" if frequency < 1000 else f"{frequency/1000:.0f}kHz"
                        print(f"      -> 测试频率: {freq_display}, 时基: {timebase} [{i+1}/{len(frequency_list)}]")

                        # 配置示波器
                        self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
                        self.oscilloscope.Set_Trigger_Source(osc_config['trigger_source'])
                        self.oscilloscope.Set_Display_Persistence(time=osc_config['persistence_mode'])

                        # 频率切换逻辑
                        if i == 0:
                            # 第一个频率特殊处理
                            self.oscilloscope.Set_Wavegen_Basic(
                                waveform=osc_config['waveform_type'],
                                frequency=max(frequency-10000, 10),
                                amplitude=osc_config['amplitude'],
                                offset=osc_config['offset'],
                                output_state='ON',
                                load=50
                            )
                            self.time.sleep(0.8)
                            self.oscilloscope.Set_Wavegen_Frequency(frequency=frequency)
                            self.time.sleep(0.8)
                            self.oscilloscope.Clear_Display_Waveform()
                            self.time.sleep(0.8)
                        else:
                            # 非第一个频率正常处理
                            self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                            self.time.sleep(0.8)
                            self.oscilloscope.Set_Wavegen_Frequency(frequency=frequency)
                            self.time.sleep(0.8)
                            self.oscilloscope.Clear_Display_Waveform()
                            self.time.sleep(0.8)

                        # 等待信号稳定和余晖累积
                        self.time.sleep(osc_config['freq_observation_time'])

                        # 截图 - 使用简化文件名避免特殊字符
                        timestamp = self.time.strftime('%m%d_%H%M%S')
                        # 简化active_links格式，避免方括号和逗号
                        links_str = "_".join(map(str, active_links))

                        # RVS测试使用特殊文件名格式
                        if osc_config.get('rvs_dly') is not None:
                            rvs_dly = osc_config['rvs_dly']
                            screenshot_filename = f"RVS_GPIO{gpio_num}_LINK{links_str}_{freq_display}_{timebase}_rvs{rvs_dly}_{timestamp}.png"
                        else:
                            screenshot_filename = f"FIXED_{i+1:02d}_GPIO{gpio_num}_LINK{links_str}_{freq_display}_{timebase}_{timestamp}.png"

                        # 创建文件夹路径 - 模仿s2q成功模式，使用gpio{n}格式
                        base_folder = osc_config['screenshot_folder_base']
                        screenshot_folder = f"{base_folder}/link{link_id}_gpio{gpio_num}"
                        # 确保文件夹存在
                        self.os.makedirs(screenshot_folder, exist_ok=True)
                        screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

                        self.oscilloscope.Save_Image(
                            filepath=screenshot_path,
                            image_format="PNG",
                            invert="OFF",
                            menu="MOF"
                        )
                        self.time.sleep(1.3)

                    print(f"    * 固定频率测试完成，共测试 {len(frequency_list)} 个频率点")
                    # 不再自动关闭示波器信号
                    
                return True

            except Exception:
                return False

        def screenshot_sweep_frequencies(self, gpio_num, active_links, osc_config):
            """扫频截图模式 - 基于验证的逻辑"""
            try:
                if not self.oscilloscope:
                    return True

                freq_range = osc_config['frequency_range']
                sweep_timebase = osc_config['sweep_timebase']

                # 计算扫频参数
                start_freq = freq_range['start']
                end_freq = freq_range['end']
                step_freq = freq_range['step']
                frequencies = list(range(start_freq, end_freq + step_freq, step_freq))

                print(f"    🌊 扫频范围: {start_freq}Hz - {end_freq}Hz, 步进: {step_freq}Hz")
                print(f"    📊 扫频点数: {len(frequencies)} 个频率点")

                # 为每个活跃Link分别扫频截图
                for link_id in active_links:
                    print(f"    🔗 为Link{link_id}进行扫频截图...")
                    print(f"    * 开始频率扫描: {start_freq/1000}-{end_freq/1000}kHz, 步进{step_freq/1000}kHz")

                    # 配置示波器基础设置
                    self.oscilloscope.Set_Timebase_Scale(timebase_scale=sweep_timebase)
                    self.oscilloscope.Set_Trigger_Source(osc_config['trigger_source'])
                    self.oscilloscope.Set_Display_Persistence(time=osc_config['persistence_mode'])

                    # 设置初始波形参数
                    target_first_freq = start_freq
                    temp_freq = target_first_freq + 50000  # 临时频率

                    self.oscilloscope.Set_Wavegen_Basic(
                        waveform=osc_config['waveform_type'],
                        frequency=temp_freq,  # 先设置临时频率
                        amplitude=osc_config['amplitude'],
                        offset=osc_config['offset'],
                        output_state='OFF',  # 初始不开启输出
                        load=50
                    )

                    for i, frequency in enumerate(frequencies):
                        freq_khz = frequency / 1000
                        print(f"      -> 测试频率: {freq_khz}kHz [{i+1}/{len(frequencies)}]")

                        # 频率切换逻辑
                        if i == 0:
                            # 第一个频率特殊处理
                            self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                            self.time.sleep(0.8)
                            self.oscilloscope.Set_Wavegen_Frequency(frequency=frequency-10000)
                            self.time.sleep(0.8)
                            self.oscilloscope.Set_Wavegen_Frequency(frequency=frequency)
                            self.oscilloscope.Clear_Display_Waveform()
                            self.time.sleep(0.8)
                        else:
                            # 非第一个频率正常处理
                            self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                            self.time.sleep(0.8)
                            self.oscilloscope.Set_Wavegen_Frequency(frequency=frequency)
                            self.time.sleep(0.8)
                            self.oscilloscope.Clear_Display_Waveform()
                            self.time.sleep(0.8)

                        # 等待信号稳定和余晖累积
                        self.time.sleep(osc_config['freq_observation_time'])

                        # 截图 - 使用简化文件名避免特殊字符
                        timestamp = self.time.strftime('%m%d_%H%M%S')
                        # 简化active_links格式，避免方括号和逗号
                        links_str = "_".join(map(str, active_links))
                        screenshot_filename = f"SWEEP_{i+1:02d}_GPIO{gpio_num}_LINK{links_str}_{freq_khz}kHz_{timestamp}.png"

                        # 创建文件夹路径 - 模仿s2q成功模式，使用gpio{n}格式
                        base_folder = osc_config['screenshot_folder_base']
                        screenshot_folder = f"{base_folder}/link{link_id}_gpio{gpio_num}"
                        # 确保文件夹存在
                        self.os.makedirs(screenshot_folder, exist_ok=True)
                        screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

                        self.oscilloscope.Save_Image(
                            filepath=screenshot_path,
                            image_format="PNG",
                            invert="OFF",
                            menu="MOF"
                        )
                        self.time.sleep(1.3)

                    print(f"    * 频率扫描完成，共测试 {len(frequencies)} 个频率点")
                    self.oscilloscope.Set_Wavegen_Basic(
                                output_state='OFF',
                            )                    

                return True

            except Exception:
                return False

        def execute_screenshot(self, gpio_num, active_links, test_mode, osc_config):
            """执行截图 - 主入口方法"""
            try:
                if not osc_config.get('enable_screenshot', False):
                    return True

                # 不再预创建文件夹，改为在保存时动态创建（模仿原始扫频代码的成功模式）

                # 根据测试模式执行相应的截图
                if test_mode == 'fixed':
                    return self.screenshot_fixed_frequencies(gpio_num, active_links, osc_config)
                elif test_mode == 'sweep':
                    return self.screenshot_sweep_frequencies(gpio_num, active_links, osc_config)
                elif test_mode == 'combined':
                    fixed_result = self.screenshot_fixed_frequencies(gpio_num, active_links, osc_config)
                    if fixed_result:
                        sweep_result = self.screenshot_sweep_frequencies(gpio_num, active_links, osc_config)
                        return sweep_result
                    return False
                else:
                    return False

            except Exception:
                return False

    return UniversalOscilloscopeScreenshot


def get_universal_oscilloscope_screenshot(oscilloscope):
    """获取通用示波器截图实例的便捷函数

    使用示例:
    # 在test_gpio_case6_8_s2d_auto2.py中使用
    from conftest import get_universal_oscilloscope_screenshot

    # 创建截图实例
    screenshot_tool = get_universal_oscilloscope_screenshot(self.oscilloscope)

    # 执行截图
    result = screenshot_tool.execute_screenshot(
        gpio_num=s68_gpio,
        active_links=TEST_CONFIG['active_links'],
        test_mode=TEST_CONFIG['oscilloscope_config']['test_mode'],
        osc_config=TEST_CONFIG['oscilloscope_config']
    )
    """
    screenshot_class = create_universal_oscilloscope_screenshot_method()
    return screenshot_class(oscilloscope)