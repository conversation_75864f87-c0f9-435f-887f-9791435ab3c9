# -*- coding: utf-8 -*-
"""
Q68 GPIO13 特殊配置工具函数

GPIO13是Q68的特殊引脚，需要特殊的寄存器配置才能用作GPIO功能。
这个模块提供了GPIO13的配置和控制函数。
"""
import time


def configure_q68_gpio13_as_gpio(q68):
    """
    配置Q68 GPIO13为GPIO功能
    
    GPIO13默认可能有其他功能（如I2C），需要特殊配置才能用作GPIO。
    
    Args:
        q68: Q68设备对象
        
    Returns:
        bool: 配置是否成功
    """
    try:
        print("配置Q68 GPIO13为GPIO功能...")
        
        # 步骤1: 禁用I2C选择 (disable i2c sel)
        print("  - 禁用I2C选择...")
        q68.M65Q68I2CLocalWrite(regAddr=0x4479, value=0x00)
        
        # 步骤2: 设置GPIO控制寄存器
        print("  - 设置GPIO控制寄存器...")
        q68.M65Q68I2CLocalWrite(regAddr=0x440d, value=0xff)
        
        # 步骤3: 配置为GPIO功能 (初始设置为输出低)
        print("  - 配置GPIO13为GPIO功能...")
        q68.M65Q68I2CLocalWrite(regAddr=0x4420, value=0x02)  # output=L
        
        # 等待配置生效
        time.sleep(0.1)
        
        print("  - GPIO13配置完成")
        return True
        
    except Exception as e:
        print(f"  - 错误: GPIO13配置失败: {e}")
        return False


def set_q68_gpio13_output(q68, level):
    """
    设置Q68 GPIO13的输出电平
    
    Args:
        q68: Q68设备对象
        level: 输出电平，'H'或'L'，True或False，1或0
        
    Returns:
        bool: 设置是否成功
    """
    try:
        # 转换输入参数为标准格式
        if level in ['H', 'h', True, 1, '1']:
            level_str = 'H'
            reg_value = 0x82  # output=H
        elif level in ['L', 'l', False, 0, '0']:
            level_str = 'L'
            reg_value = 0x02  # output=L
        else:
            raise ValueError(f"无效的电平值: {level}，应该是 'H'/'L', True/False, 1/0")
        
        print(f"  - 设置GPIO13输出{level_str}电平...")
        q68.M65Q68I2CLocalWrite(regAddr=0x4420, value=reg_value)
        
        return True
        
    except Exception as e:
        print(f"  - 错误: GPIO13电平设置失败: {e}")
        return False


def test_q68_gpio13_toggle(q68, cycles=5, delay=1.0):
    """
    测试Q68 GPIO13的高低电平切换
    
    Args:
        q68: Q68设备对象
        cycles: 切换次数
        delay: 每次切换的延时(秒)
        
    Returns:
        bool: 测试是否成功
    """
    try:
        print(f"开始GPIO13高低电平切换测试 ({cycles}次循环)...")
        
        for i in range(cycles):
            print(f"  - 第{i+1}次切换:")
            
            # 输出高电平
            if not set_q68_gpio13_output(q68, 'H'):
                return False
            time.sleep(delay)
            
            # 输出低电平
            if not set_q68_gpio13_output(q68, 'L'):
                return False
            time.sleep(delay)
        
        print("  - GPIO13高低电平切换测试完成")
        return True
        
    except Exception as e:
        print(f"  - 错误: GPIO13切换测试失败: {e}")
        return False


def configure_q68_gpio13_for_remote_tx(q68, signal_id, link_id):
    """
    配置Q68 GPIO13用于远程传输
    
    这个函数结合了GPIO13的特殊配置和远程传输配置
    
    Args:
        q68: Q68设备对象
        signal_id: 信号ID
        link_id: Link ID
        
    Returns:
        bool: 配置是否成功
    """
    try:
        print("配置Q68 GPIO13用于远程传输...")
        
        # 步骤1: 特殊GPIO配置
        if not configure_q68_gpio13_as_gpio(q68):
            return False
        
        # 步骤2: 标准GPIO功能配置
        print("  - 设置GPIO13为GPIO功能...")
        q68.MFNSet(gpio=13, mfn=0)
        
        # 步骤3: 远程传输配置
        print(f"  - 配置GPIO13为远程发送 (信号ID: {signal_id}, Link: {link_id})...")
        q68.GPIORemoteTx(gpio=13, tx_id=signal_id, link_id=link_id, dly_comp_en=0)
        
        print("  - GPIO13远程传输配置完成")
        return True
        
    except Exception as e:
        print(f"  - 错误: GPIO13远程传输配置失败: {e}")
        return False


def get_q68_gpio13_register_status(q68):
    """
    读取Q68 GPIO13相关寄存器的状态
    
    Args:
        q68: Q68设备对象
        
    Returns:
        dict: 寄存器状态字典
    """
    try:
        status = {}
        
        # 读取相关寄存器
        status['reg_0x4479'] = q68.M65Q68I2CLocalRead(regAddr=0x4479, CRC=False)  # I2C选择
        status['reg_0x440d'] = q68.M65Q68I2CLocalRead(regAddr=0x440d, CRC=False)  # GPIO控制
        status['reg_0x4420'] = q68.M65Q68I2CLocalRead(regAddr=0x4420, CRC=False)  # GPIO输出
        
        print("GPIO13寄存器状态:")
        print(f"  - 0x4479 (I2C选择): 0x{status['reg_0x4479']:02X}")
        print(f"  - 0x440d (GPIO控制): 0x{status['reg_0x440d']:02X}")
        print(f"  - 0x4420 (GPIO输出): 0x{status['reg_0x4420']:02X}")
        
        return status
        
    except Exception as e:
        print(f"  - 错误: 读取GPIO13寄存器状态失败: {e}")
        return {}


# 使用示例
if __name__ == "__main__":
    """
    使用示例:
    
    # 基础配置
    configure_q68_gpio13_as_gpio(q68)
    
    # 设置输出电平
    set_q68_gpio13_output(q68, 'H')  # 输出高电平
    set_q68_gpio13_output(q68, 'L')  # 输出低电平
    
    # 高低电平切换测试
    test_q68_gpio13_toggle(q68, cycles=10, delay=0.5)
    
    # 远程传输配置
    configure_q68_gpio13_for_remote_tx(q68, signal_id=11, link_id=0)
    
    # 查看寄存器状态
    get_q68_gpio13_register_status(q68)
    """
    print("Q68 GPIO13 工具函数模块")
    print("请在测试代码中导入并使用这些函数")
