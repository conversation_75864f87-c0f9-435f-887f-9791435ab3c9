"""
Module for accessing functions from FTD2XX in an easier to use
_pythonic_ way. For full documentation please refer to the FTDI
Programming Guide. This module is based on Pablo Bleyers d2xx module,
except this uses ctypes instead of an extension approach.
"""
import logging
import sys
import ctypes
import time

from enum import unique, IntFlag, IntEnum
from builtins import range
from contextlib import AbstractContextManager
from types import TracebackType
from typing import Any, Callable, List, Optional, Tuple, Type, Union

if sys.platform == "win32":
    from . import _ftd2xx as _ft
else:
    raise Exception("Unknown platform")

if sys.version_info >= (3, 8, 0):
    from typing import TypedDict
else:
    from typing_extensions import TypedDict

@unique
class Status(IntEnum):
    OK = 0
    INVALID_HANDLE = 1
    DEVICE_NOT_FOUND = 2
    DEVICE_NOT_OPENED = 3
    IO_ERROR = 4
    INSUFFICIENT_RESOURCES = 5
    INVALID_PARAMETER = 6
    INVALID_BAUD_RATE = 7
    DEVICE_NOT_OPENED_FOR_ERASE = 8
    DEVICE_NOT_OPENED_FOR_WRITE = 9
    FAILED_TO_WRITE_DEVICE = 10
    EEPROM_READ_FAILED = 11
    EEPROM_WRITE_FAILED = 12
    EEPROM_ERASE_FAILED = 13
    EEPROM_NOT_PRESENT = 14
    EEPROM_NOT_PROGRAMMED = 15
    INVALID_ARGS = 16
    NOT_SUPPORTED = 17
    OTHER_ERROR = 18

@unique
class Device(IntEnum):
    FT_232BM = 0
    FT_232AM = 1
    FT_100AX = 2
    UNKNOWN = 3
    FT_2232C = 4
    FT_232R = 5
    FT_2232H = 6
    FT_4232H = 7
    FT_232H = 8
    FT_X_SERIES = 9
    FT_4222H = 10

@unique
class ModemStatus(IntFlag):
    CTS = 0x10  #: Clear to Send
    DSR = 0x20  #: Data Set Ready
    RI  = 0x40  #: Ring Indicator
    DCD = 0x80  #: Data Carrier Detect
    DR = 0x100  #: Data Ready
    OE = 0x200  #: Overrun Error
    PE = 0x400  #: Parity Error
    FE = 0x800  #: Framing Error
    BI = 0x1000 #: Break Interrupt
    THRE = 0x2000 #: Transmitter Holding Register
    TEMT = 0x4000 #: Transmitter Empty
    RCVE = 0x8000 #: Receiver FIFO Error

@unique
class OpenExFlags(IntFlag):
    OPEN_BY_SERIAL_NUMBER = 1
    OPEN_BY_DESCRIPTION   = 2
    OPEN_BY_LOCATION = 4

# List Devices flags
LIST_NUMBER_ONLY = 0x80000000
LIST_BY_INDEX = 0x40000000
LIST_ALL = 0x20000000

# Driver Types
DRIVER_TYPE_D2XX = 0
DRIVER_TYPE_VCP  = 1

# Word Lengths
BITS_8 = 8
BITS_7 = 7

# Stop Bits
STOP_BITS_1 = 0
STOP_BITS_2 = 2

# Parity
PARITY_NONE = 0
PARITY_ODD = 1
PARITY_EVEN = 2
PARITY_MARK = 3
PARITY_SPACE = 4

# Flow Control
FLOW_NONE = 0x0000
FLOW_RTS_CTS = 0x0100
FLOW_DTR_DSR = 0x0200
FLOW_XON_XOFF = 0x0400

# Purge RX and TX Buffers
PURGE_RX = 1
PURGE_TX = 2

# Notification Events
EVENT_RXCHAR = 1
EVENT_MODEM_STATUS = 2
EVENT_LINE_STATUS = 4

# Description size
MAX_DESCRIPTION_SIZE = 256

# program data type
ft_program_data = _ft.ft_program_data

# logger
logger = logging.getLogger("ftd2xx")

class DeviceError(Exception):
    """Exception class for status messages"""

    def __init__(self, message: Union[int, Any]):
        super().__init__()
        if isinstance(message, int):
            self.message = Status(message).name
        else:
            self.message = str(message)

    def __str__(self):
        return self.message

class DeviceInfoDetail(TypedDict):
    index: int
    flags: int
    type: int
    id: int
    location: int
    serial: bytes
    description: bytes
    handle: _ft.FT_HANDLE

class DeviceInfo(TypedDict):
    type: int
    id: int
    description: bytes
    serial: bytes

class ProgramData(TypedDict, total=False):
    Signature1: Union[_ft.DWORD, int]
    Signature2: Union[_ft.DWORD, int]
    Version: Union[_ft.DWORD, int]
    VendorId: Union[_ft.WORD, int]
    ProductId: Union[_ft.WORD, int]
    Manufacturer: Union[_ft.STRING, int]
    ManufacturerId: Union[_ft.STRING, int]
    Description: Union[_ft.STRING, int]
    SerialNumber: Union[_ft.STRING, int]
    MaxPower: Union[_ft.WORD, int]
    PnP: Union[_ft.WORD, int]
    SelfPowered: Union[_ft.WORD, int]
    RemoteWakeup: Union[_ft.WORD, int]
    Rev4: Union[_ft.UCHAR, int]
    IsoIn: Union[_ft.UCHAR, int]
    IsoOut: Union[_ft.UCHAR, int]
    PullDownEnable: Union[_ft.UCHAR, int]
    SerNumEnable: Union[_ft.UCHAR, int]
    USBVersionEnable: Union[_ft.UCHAR, int]
    USBVersion: Union[_ft.WORD, int]
    Rev5: Union[_ft.UCHAR, int]
    IsoInA: Union[_ft.UCHAR, int]
    IsoInB: Union[_ft.UCHAR, int]
    IsoOutA: Union[_ft.UCHAR, int]
    IsoOutB: Union[_ft.UCHAR, int]
    PullDownEnable5: Union[_ft.UCHAR, int]
    SerNumEnable5: Union[_ft.UCHAR, int]
    USBVersionEnable5: Union[_ft.UCHAR, int]
    USBVersion5: Union[_ft.WORD, int]
    AIsHighCurrent: Union[_ft.UCHAR, int]
    BIsHighCurrent: Union[_ft.UCHAR, int]
    IFAIsFifo: Union[_ft.UCHAR, int]
    IFAIsFifoTar: Union[_ft.UCHAR, int]
    IFAIsFastSer: Union[_ft.UCHAR, int]
    AIsVCP: Union[_ft.UCHAR, int]
    IFBIsFifo: Union[_ft.UCHAR, int]
    IFBIsFifoTar: Union[_ft.UCHAR, int]
    IFBIsFastSer: Union[_ft.UCHAR, int]
    BIsVCP: Union[_ft.UCHAR, int]
    UseExtOsc: Union[_ft.UCHAR, int]
    HighDriveIOs: Union[_ft.UCHAR, int]
    EndpointSize: Union[_ft.UCHAR, int]
    PullDownEnableR: Union[_ft.UCHAR, int]
    SerNumEnableR: Union[_ft.UCHAR, int]
    InvertTXD: Union[_ft.UCHAR, int]
    InvertRXD: Union[_ft.UCHAR, int]
    InvertRTS: Union[_ft.UCHAR, int]
    InvertCTS: Union[_ft.UCHAR, int]
    InvertDTR: Union[_ft.UCHAR, int]
    InvertDSR: Union[_ft.UCHAR, int]
    InvertDCD: Union[_ft.UCHAR, int]
    InvertRI: Union[_ft.UCHAR, int]
    Cbus0: Union[_ft.UCHAR, int]
    Cbus1: Union[_ft.UCHAR, int]
    Cbus2: Union[_ft.UCHAR, int]
    Cbus3: Union[_ft.UCHAR, int]
    Cbus4: Union[_ft.UCHAR, int]
    RIsVCP: Union[_ft.UCHAR, int]

def call_ft(function: Callable, *args):
    """Call an FTDI function and check the status. Raise exception on error"""
    status = function(*args)
    if status != Status.OK:
        raise DeviceError(status)

def listDevices(flags: int = 0) -> Optional[List[bytes]]:
    """Return a list of serial numbers(default), descriptions or locations (Windows only) of the connected FTDI devices depending on value of flags"""
    n = _ft.DWORD()
    call_ft(_ft.FT_ListDevices, ctypes.byref(n), None, _ft.DWORD(LIST_NUMBER_ONLY))
    devcount = n.value
    logger.debug("Found %i devices", devcount)
    if devcount:
        # since ctypes has no pointer arithmetic
        bd = [
            ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
            for i in range(devcount)
        ]

        # array of pointers to those strings, initially all NULL
        ba = (ctypes.c_char_p * (devcount + 1))(*[ctypes.addressof(x) for x in bd], None)
        call_ft(_ft.FT_ListDevices, ba, ctypes.byref(n), _ft.DWORD(LIST_ALL | flags))
        return [res for res in ba[:devcount]]
    return None

def getLibraryVersion() -> int:
    """Return a long representing library version"""
    m = _ft.DWORD()
    call_ft(_ft.FT_GetLibraryVersion, ctypes.byref(m))
    return m.value

def createDeviceInfoList() -> int:
    """Create the internal device info list and return number of entries"""
    m = _ft.DWORD()
    call_ft(_ft.FT_CreateDeviceInfoList, ctypes.byref(m))
    return m.value

def getDeviceInfoDetail(devnum: int = 0, update: bool = True) -> DeviceInfoDetail:
    """Get an entry from the internal device info list. Set update to False to avoid a slow call to createDeviceInfoList."""
    flags = _ft.DWORD()
    typ = _ft.DWORD()
    dev_id = _ft.DWORD()
    location = _ft.DWORD()
    handle = _ft.FT_HANDLE()
    name = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
    description = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
    # createDeviceInfoList is slow, only run if update is True
    if update:
        createDeviceInfoList()
    call_ft(_ft.FT_GetDeviceInfoDetail, _ft.DWORD(devnum), ctypes.byref(flags), ctypes.byref(typ), ctypes.byref(dev_id), ctypes.byref(location), name, description, ctypes.byref(handle),)
    return {"index": devnum, "flags": flags.value, "type": typ.value, "id": dev_id.value, "location": location.value, "serial": name.value, "description": description.value, "handle": handle,}

def open(dev: int = 0, update: bool = True):  # pylint: disable=redefined-builtin
    """Open a handle to a usb device by index and return an FTD2XX instance for
    it. Set update to False to avoid a slow call to createDeviceInfoList.

    Args:
        dev (int): Device number
        update (bool): Set False to disable automatic call to createDeviceInfoList

    Returns:
        instance of FTD2XX instance if successful
    """
    h = _ft.FT_HANDLE()
    call_ft(_ft.FT_Open, dev, ctypes.byref(h))
    return FTD2XX(h, update=update)

def openEx(id_str: bytes, flags: int = OpenExFlags.OPEN_BY_SERIAL_NUMBER, update: bool = True):
    """Open a handle to a usb device by serial number(default), description or
    location(Windows only) depending on value of flags and return an FTD2XX
    instance for it. Set update to False to avoid a slow call to createDeviceInfoList.

    Args:
        id_str (bytes): ID string from listDevices
        flags (int) = FLAG (consult D2XX Guide). Defaults to OpenExFlags.OPEN_BY_SERIAL_NUMBER
        update (bool): Set False to disable automatic call to createDeviceInfoList

    Returns:
        instance of FTD2XX instance if successful
    """
    h = _ft.FT_HANDLE()
    call_ft(_ft.FT_OpenEx, id_str, _ft.DWORD(flags), ctypes.byref(h))
    return FTD2XX(h, update=update)

from win32con import GENERIC_READ, GENERIC_WRITE, OPEN_EXISTING
def w32CreateFile(name: bytes, access: int = GENERIC_READ | GENERIC_WRITE, flags: int = OpenExFlags.OPEN_BY_SERIAL_NUMBER,):
    return FTD2XX(_ft.FT_W32_CreateFile(_ft.STRING(name), _ft.DWORD(access), _ft.DWORD(0), None, _ft.DWORD(OPEN_EXISTING), _ft.DWORD(flags), _ft.HANDLE(0),))

class FTD2XX(AbstractContextManager):
    """Class for communicating with an FTDI device"""

    handle: _ft.FT_HANDLE
    status: int

    # spi settings
    spiMode  = 0 # spi mode 0/1/2/3
    spiCs    = 3 # 3-DBUS3, 4-DBUS4, 5-DBUS5, 6-DBUS6, 7-DBUS7 
    spiCsPol = 1 # 0-active low, 1-active high
    spiLsb   = 0 # 1-lsb first,  0-msb first

    def __init__(self, handle: _ft.FT_HANDLE, update: bool = True):
        """
        Create an instance of the FTD2XX class with the given device handle and populate the device info in the instance dictionary.
        update (bool): Set False to disable automatic (slow) call to createDeviceInfoList
        """
        self.handle = handle
        self.status = 1
        if update: # createDeviceInfoList is slow, only run if update is True
            createDeviceInfoList()
        self.__dict__.update(self.getDeviceInfo())

    def close(self) -> None:
        """Close the device handle"""
        call_ft(_ft.FT_Close, self.handle)
        self.status = 0

    def read(self, nchars: int, raw: bool = True) -> bytes:
        """Read up to nchars bytes of data from the device. Can return fewer if timedout. Use getQueueStatus to find how many bytes are available"""
        b_read = _ft.DWORD()
        b = ctypes.create_string_buffer(nchars)
        call_ft(_ft.FT_Read, self.handle, b, nchars, ctypes.byref(b_read))
        return b.raw[: b_read.value] if raw else b.value[: b_read.value]

    def write(self, data: bytes):
        """Send the data to the device. Data must be a string representing the bytes to be sent"""
        w = _ft.DWORD()
        call_ft(_ft.FT_Write, self.handle, data, len(data), ctypes.byref(w))
        return w.value

    def ioctl(self):
        """Not implemented"""
        raise NotImplementedError

    def setBaudRate(self, baud: int) -> None:
        """Set the baud rate"""
        call_ft(_ft.FT_SetBaudRate, self.handle, _ft.DWORD(baud))

    def setDivisor(self, div: int):
        """Set the clock divider. The clock will be set to 6e6/(div + 1)."""
        call_ft(_ft.FT_SetDivisor, self.handle, _ft.USHORT(div))

    def setDataCharacteristics(self, wordlen: int, stopbits: int, parity: int):
        """Set the data characteristics for UART"""
        call_ft(_ft.FT_SetDataCharacteristics, self.handle, _ft.UCHAR(wordlen), _ft.UCHAR(stopbits), _ft.UCHAR(parity),)

    def setFlowControl(self, flowcontrol: int, xon: int = -1, xoff: int = -1):
        '''
        flowcontrol: Must be one (FLOW_NONE, FLOW_RTS_CTS, FLOW_DTR_DSR, FLOW_XON_XOFF)
        xon:  Character used to signal Xon.  Only used if flow control is FLOW_XON_XOFF.
        xoff: Character used to signal Xoff. Only used if flow control is FLOW_XON_XOFF.
        '''
        if flowcontrol == FLOW_XON_XOFF and (xon == -1 or xoff == -1):
            raise ValueError
        call_ft(_ft.FT_SetFlowControl, self.handle, _ft.USHORT(flowcontrol), _ft.UCHAR(xon), _ft.UCHAR(xoff),)

    def resetDevice(self):
        """Reset the device"""
        call_ft(_ft.FT_ResetDevice, self.handle)

    def setDtr(self):
        call_ft(_ft.FT_SetDtr, self.handle)

    def clrDtr(self):
        call_ft(_ft.FT_ClrDtr, self.handle)

    def setRts(self):
        call_ft(_ft.FT_SetRts, self.handle)

    def clrRts(self):
        call_ft(_ft.FT_ClrRts, self.handle)

    def getModemStatus(self) -> ModemStatus:
        m = _ft.DWORD()
        call_ft(_ft.FT_GetModemStatus, self.handle, ctypes.byref(m))
        return ModemStatus(m.value & 0xFFFF)

    def setChars(self, evch: int, evch_en: int, erch: int, erch_en: int):
        call_ft(_ft.FT_SetChars, self.handle, _ft.UCHAR(evch), _ft.UCHAR(evch_en), _ft.UCHAR(erch), _ft.UCHAR(erch_en),)

    def purge(self):
        '''
        purges (clear) receive and transmit buffers in the device
        '''
        mask = PURGE_RX | PURGE_TX
        call_ft(_ft.FT_Purge, self.handle, _ft.DWORD(mask))

    def setTimeouts(self, read: int, write: int):
        '''
        read: Read timeout in milliseconds.
        write: Write timeout in milliseconds
        '''
        call_ft(_ft.FT_SetTimeouts, self.handle, _ft.DWORD(read), _ft.DWORD(write))

    def setDeadmanTimeout(self, timeout: int):
        '''
        allows the maximum time in milliseconds that a USB request can remain outstanding to be set
        '''
        call_ft(_ft.FT_SetDeadmanTimeout, self.handle, _ft.DWORD(timeout))

    def getQueueStatus(self) -> int:
        """Get number of bytes in receive queue."""
        rxQAmount = _ft.DWORD()
        call_ft(_ft.FT_GetQueueStatus, self.handle, ctypes.byref(rxQAmount))
        return rxQAmount.value

    def setEventNotification(self, evtmask: int, evthandle):
        '''Sets conditions for event notification.'''
        call_ft(_ft.FT_SetEventNotification, self.handle, _ft.DWORD(evtmask), _ft.HANDLE(evthandle),)

    def getStatus(self):
        """Return a 3-tuple of rx queue bytes, tx queue bytes and event status"""
        rxQAmount = _ft.DWORD()
        txQAmount = _ft.DWORD()
        evtStatus = _ft.DWORD()
        call_ft(_ft.FT_GetStatus, self.handle, ctypes.byref(rxQAmount), ctypes.byref(txQAmount), ctypes.byref(evtStatus),)
        return (rxQAmount.value, txQAmount.value, evtStatus.value)

    def setBreakOn(self):
        """Sets the BREAK condition for the device."""
        call_ft(_ft.FT_SetBreakOn, self.handle)

    def setBreakOff(self):
        """Resets the BREAK condition for the device."""
        call_ft(_ft.FT_SetBreakOff, self.handle)

    def setWaitMask(self, mask: int):
        call_ft(_ft.FT_SetWaitMask, self.handle, _ft.DWORD(mask))

    def waitOnMask(self):
        mask = _ft.DWORD()
        call_ft(_ft.FT_WaitOnMask, self.handle, ctypes.byref(mask))
        return mask.value

    def getEventStatus(self):
        evtStatus = _ft.DWORD()
        call_ft(_ft.FT_GetEventStatus, self.handle, ctypes.byref(evtStatus))
        return evtStatus.value

    def setLatencyTimer(self, latency: int):
        '''latency: Required value, in milliseconds, of latency timer. Valid range is 2~255.'''
        call_ft(_ft.FT_SetLatencyTimer, self.handle, _ft.UCHAR(latency))

    def getLatencyTimer(self) -> int:
        latency = _ft.UCHAR()
        call_ft(_ft.FT_GetLatencyTimer, self.handle, ctypes.byref(latency))
        return latency.value

    def setBitMode(self, mask: int, mode: int):
        '''
        mask: 
            Required value for bit mode mask (not needed by MPSSE mode). This sets up which bits are inputs and outputs.
            0: input, 1 output for each bit.
            CBUS Bit Bang mode: the upper nibble of this value controls which pins are inputs and outputs,
            while the lower nibble controls which of the outputs are high and low.
        mode:
            0x00 = Reset.
            0x01 = Asynchronous Bit Bang.
            0x02 = MPSSE (FT2232, FT2232H, FT4232H and FT232H devices only).
            0x04 = Synchronous Bit Bang (FT232R, FT245R, FT2232, FT2232H, FT4232H and FT232H devices only).
            0x08 = MCU Host Bus Emulation Mode (FT2232, FT2232H, FT4232H and FT232H devices only).
            0x10 = Fast Opto-Isolated Serial Mode (FT2232, FT2232H, FT4232H and FT232H devices only).
            0x20 = CBUS Bit Bang Mode (FT232R and FT232H devices only).
            0x40 = Single Channel Synchronous 245 FIFO Mode (FT2232H and FT232H devices only).
        '''
        call_ft(_ft.FT_SetBitMode, self.handle, _ft.UCHAR(mask), _ft.UCHAR(mode))

    def getBitMode(self) -> int:
        '''
        Gets the instantaneous value of the data bus.
        '''
        mode = _ft.UCHAR()
        call_ft(_ft.FT_GetBitMode, self.handle, ctypes.byref(mode))
        return mode.value

    def setUSBParameters(self, in_tx_size: int, out_tx_size: int = 0):
        '''
        This function can be used to change the transfer sizes from the default transfer size of 4K bytes to
        better suit the application requirements. Transfer sizes must be set to a multiple of 64 bytes between 64
        bytes and 64k bytes.
        '''
        call_ft(_ft.FT_SetUSBParameters, self.handle, _ft.ULONG(in_tx_size), _ft.ULONG(out_tx_size),)

    def getDeviceInfo(self) -> DeviceInfo:
        """
        Returns a dictionary describing the device.
        type: Device.FT_232BM (0), Device.FT_232AM (1), Device.FT_100AX (2), Device.FT_2232C (4), 
              Device.FT_232R  (5), Device.FT_2232H (6), Device.FT_4232H (7), Device.FT_232H  (8),
              Device.FT_X_SERIES (9), Device.FT_4222H (10)
        """
        deviceType = _ft.DWORD()
        deviceId = _ft.DWORD()
        desc = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
        serial = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
        call_ft(_ft.FT_GetDeviceInfo, self.handle, ctypes.byref(deviceType), ctypes.byref(deviceId), serial, desc, None,)
        return {"type": deviceType.value, "id": deviceId.value, "description": desc.value, "serial": serial.value,}

    def stopInTask(self):
        call_ft(_ft.FT_StopInTask, self.handle)

    def restartInTask(self):
        call_ft(_ft.FT_RestartInTask, self.handle)

    def setRestPipeRetryCount(self, count):
        call_ft(_ft.FT_SetResetPipeRetryCount, self.handle, _ft.DWORD(count))

    def resetPort(self):
        """Send a reset command to the port."""
        call_ft(_ft.FT_ResetPort, self.handle)

    def cyclePort(self):
        """Send a cycle command to the USB port."""
        call_ft(_ft.FT_CyclePort, self.handle)

    def getDriverVersion(self) -> int:
        drvver = _ft.DWORD()
        call_ft(_ft.FT_GetDriverVersion, self.handle, ctypes.byref(drvver))
        return drvver.value

    def getComPortNumber(self) -> int:
        """Return a long representing the COM port number"""
        m = _ft.LONG()
        try:
            call_ft(_ft.FT_GetComPortNumber, self.handle, ctypes.byref(m))
        except AttributeError as exc:
            raise Exception("FT_GetComPortNumber is only available on windows") from exc
        return m.value

    def eeProgram(self, progdata: Optional[ft_program_data] = None, **kwds) -> None:
        """Program the EEPROM with custom data. If SerialNumber is null, a new serial number is generated from ManufacturerId"""
        if progdata is None:
            progdata = ft_program_data(**kwds)
        progdata.Signature1 = _ft.DWORD(0)
        progdata.Signature2 = _ft.DWORD(0xFFFFFFFF)
        progdata.Version = _ft.DWORD(2)
        call_ft(_ft.FT_EE_Program, self.handle, progdata)

    def eeRead(self) -> ft_program_data:
        """Get the program information from the EEPROM"""
        Manufacturer = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
        ManufacturerId = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
        Description = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
        SerialNumber = ctypes.create_string_buffer(MAX_DESCRIPTION_SIZE)
        progdata = ft_program_data(
            **ProgramData(
                Signature1=0,
                Signature2=0xFFFFFFFF,
                Version=2,
                Manufacturer=ctypes.addressof(Manufacturer),
                ManufacturerId=ctypes.addressof(ManufacturerId),
                Description=ctypes.addressof(Description),
                SerialNumber=ctypes.addressof(SerialNumber),
            )
        )
        call_ft(_ft.FT_EE_Read, self.handle, ctypes.byref(progdata))
        return progdata

    def eeUASize(self) -> int:
        """Get the EEPROM user area size"""
        uasize = _ft.DWORD()
        call_ft(_ft.FT_EE_UASize, self.handle, ctypes.byref(uasize))
        return uasize.value

    def eeUAWrite(self, data: bytes) -> None:
        """Write data to the EEPROM user area. data must be a string with appropriate byte values"""
        buf = ctypes.create_string_buffer(data)
        call_ft(_ft.FT_EE_UAWrite, self.handle, buf, len(data))

    def eeUARead(self, b_to_read: int) -> bytes:
        """Read b_to_read bytes from the EEPROM user area"""
        b_read = _ft.DWORD()
        buf = (ctypes.c_ubyte * (b_to_read + 1))()
        call_ft(_ft.FT_EE_UARead, self.handle, buf, b_to_read, ctypes.byref(b_read),)
        return bytes(buf[: b_read.value])

    def __exit__(self, __exc_type: Optional[Type[BaseException]], __exc_value: Optional[BaseException], __traceback: Optional[TracebackType],) -> Optional[bool]:
        self.close()
        return super().__exit__(__exc_type, __exc_value, __traceback)

    def setChanClk(self, clkHz):
        # TODO: set div for 2232C
        # for 232H, 2232H, 4232H
        if clkHz <= 6e6:
            sentbuffer = (0x8b,) # enable /5 clk divider -> 12MHz clk
            self.write(bytes(sentbuffer))
            div = int((6e6 / clkHz) - 1)
        else:
            sentbuffer = (0x8a,) # disable /5 clk divider -> 60MHz clk
            self.write(bytes(sentbuffer))
            div = int((30e6 / clkHz) - 1) # -> 6MHz SPI clk
        # set clk freq
        sentbuffer = (0x86, div & 0xff, (div >> 8) & 0xff)
        self.write(bytes(sentbuffer))

    def initChan(self, clkRateHz, latencyTimer, mode):
        '''
        latency:
            Required value, in milliseconds, of latency timer. Valid range is 2~255.
        mode:
            0x00 = Reset
            0x01 = Asynchronous Bit Bang
            0x02 = MPSSE (FT2232, FT2232H, FT4232H and FT232H devices only)
            0x04 = Synchronous Bit Bang (FT232R, FT245R, FT2232, FT2232H, FT4232H and FT232H devices only)
            0x08 = MCU Host Bus Emulation Mode (FT2232, FT2232H, FT4232H and FT232H devices only)
            0x10 = Fast Opto-Isolated Serial Mode (FT2232, FT2232H, FT4232H and FT232H devices only)
            0x20 = CBUS Bit Bang Mode (FT232R and FT232H devices only)
            0x40 = Single Channel Synchronous 245 FIFO Mode (FT2232H and FT232H devices only)
        '''
        self.resetDevice()
        self.purge()
        self.setUSBParameters(65536, 65536)
        self.setChars(0, 0, 0, 0)
        self.setTimeouts(5000, 5000)
        self.setLatencyTimer(latencyTimer)
        self.setBitMode(0x00, 0x00) # reset
        time.sleep(0.05)
        self.setBitMode(0x00, mode)
        time.sleep(0.05)
        self.setChanClk(clkRateHz)
        time.sleep(0.02)
        self.enableLoopBack(False)
        self.emptyInputBuf()

    def spiInit(self, clkRateHz, spiMode, spiCs, spiCsPol, spiLsb):
        '''
        clkRateHz: spi clk rate in Hz
        spiMode:   0/1/2/3 - mode0/1/2/3 (mode0:  CPOL=0, CPHA=0, mode1: CPOL=0, CPHA=1, mode2: CPOL=1, CPHA=0, mode3: CPOL=1, CPHA=1)
        spiCs:     3/4/5/6/7 - dbus3/4/5/6/7
        spiCsPol : 0-active low, 1-active high
        spiLsb:    0: msb, 1:lsb
        '''
        self.initChan(clkRateHz, 2, 0x02)
        self.spiMode  = spiMode
        self.spiCs    = spiCs 
        self.spiCsPol = 1 if spiCsPol > 0 else 0
        self.spiLsb   = spiLsb
        self.changeSpiCs(1 - spiCsPol) # deselect spi slave

    def i2cInit(self, clkRateHz, options):
        '''
        options: [0]: enable 3phase clocking
        '''
        if options & 0x01 > 0:
            clkRateHz = (clkRateHz * 3 ) / 2
        self.initChan(clkRateHz, 2, 0x02)
        if options & 0x01 > 0:
            sentbuffer = (0x8c,)
            self.write(bytes(sentbuffer))
        self.setGpioL(0x03, 0x03) # output high on sda & scl
        # TODO: enable drive only zero for 232H

    def gpioInit(self):
        '''
        only used in async bit bang mode
        '''
        self.initChan(100e3, 2, 0x01)
        self.setBaudRate(9600)

    def setGpioDir(self, dir):
        '''
        only used in async bit bang mode
        dir: 0-input, 1-output, for each bit
        '''
        self.setBitMode(dir, 0x01)

    def setGpioVal(self, val):
        '''
        only used in async bit bang mode
        val: 0-low, 1-high, for each bit
        '''
        self.write(bytes((val & 0xff,)))

    def getGpioVal(self):
        '''
        only used in async bit bang mode
        '''
        return self.getBitMode()

    def setGpioL(self, val, dir):
        '''
        only used when channel is in MPSSE mode
        val: 0-high,  0-low    for each bit
        dir: 0-input, 1-output for each bit
        '''
        sentbuffer = (0x80, val, dir) # 0x80 - setup dir and value on low 8bits, like adbus[7:0]
        self.write(bytes(sentbuffer))

    def setGpioH(self, val, dir):
        '''
        only used when channel is in MPSSE mode
        val: 0-high,  0-low    for each bit
        dir: 0-input, 1-output for each bit
        '''
        sentbuffer = (0x82, val, dir) # 0x82 - setup dir and value on high 8bits, like acbus[7:0]
        self.write(bytes(sentbuffer))

    def getGpioL(self):
        '''
        only used when channel is in MPSSE mode
        '''
        sentbuffer = (0x81, 0x87) # 0x81 - read data bits on low 8bits, like adbus[7:0]
        self.write(bytes(sentbuffer))
        rdBuf = self.read(1, True)
        return rdBuf[0]

    def getGpioH(self):
        '''
        only used when channel is in MPSSE mode
        '''
        sentbuffer = (0x83, 0x87) # 0x83 - read data bits on high 8bits, like acbus[7:0]
        self.write(bytes(sentbuffer))
        rdBuf = self.read(1, True)
        return rdBuf[0]

    def enableLoopBack(self, enable):
        if enable:
            sentbuffer = (0x84,) # turn on  loopback
        else:
            sentbuffer = (0x85,) # turn off loopback
        self.write(bytes(sentbuffer))

    def emptyInputBuf(self):
        bytesNum = self.getQueueStatus()
        while bytesNum > 0:
            toReadNum = 4096 if bytesNum > 4096 else bytesNum
            readNum  = len(self.read(toReadNum, True))
            bytesNum = bytesNum - readNum

    def changeSpiCs(self, newCsLevel):
        '''
        newCsLevel: output spiCs level
        '''
        gpioVal  = (newCsLevel << self.spiCs) | (0x01 if self.spiMode >= 2 else 0x00) # set default output level
        gpioDir  = (1 << self.spiCs) | 0x03 # [2]: miso, [1]: mosi, [0]: sck
        self.setGpioL(gpioVal, gpioDir)

    def spiGetReadCmd(self):
        lsb = 0x00
        if self.spiLsb == 1:
            lsb = 0x08
        cmd = 0x00
        if self.spiMode == 0 or self.spiMode == 3:
            cmd = 0x20 | lsb
        elif self.spiMode == 1 or self.spiMode == 2:
            cmd = 0x24 | lsb
        else:
            raise ValueError
        return cmd

    def spiGetWriteCmd(self):
        lsb = 0x00
        if self.spiLsb == 1:
            lsb = 0x08
        cmd = 0x00
        if self.spiMode == 0 or self.spiMode == 3:
            cmd = 0x11 | lsb
        elif self.spiMode == 1 or self.spiMode == 2:
            cmd = 0x10 | lsb
        else:
            raise ValueError
        return cmd

    def spiWrite(self, sentBuffer, options):
        '''
        options: [2]: disable spi cs when end, [1]: enable spi cs when start
        '''
        if options & 0x2 > 0:
            self.changeSpiCs(self.spiCsPol)
        size   = len(sentBuffer) - 1
        header = (self.spiGetWriteCmd(), (size & 0xff), (size >> 8) & 0xff)
        txBuf  = bytes(header) + sentBuffer
        self.write(txBuf)
        if options & 0x4 > 0:
            self.changeSpiCs(1 - self.spiCsPol)

    def spiRead(self, size, options):
        '''
        options: [2]: disable spi cs when end, [1]: enable spi cs when start
        '''
        if options & 0x2 > 0:
            self.changeSpiCs(self.spiCsPol)
        size = size - 1
        sentbuffer = (self.spiGetReadCmd(), (size & 0xff), (size >> 8) & 0xff, 0x87)
        self.write(bytes(sentbuffer))
        rdBuf = self.read(size, True)
        if options & 0x4 > 0:
            self.changeSpiCs(1 - self.spiCsPol)
        return rdBuf

    def i2cStart(self):
        sentbuffer = ()
        # scl high, sda high
        for i in range(10): 
            sentbuffer = sentbuffer + (0x80,)
            sentbuffer = sentbuffer + (0x03,) # val
            sentbuffer = sentbuffer + (0x03,) # dir
        # scl high, sda low
        for i in range(20): 
            sentbuffer = sentbuffer + (0x80,)
            sentbuffer = sentbuffer + (0x01,)
            sentbuffer = sentbuffer + (0x03,)
        # scl low, sda low
        sentbuffer = sentbuffer + (0x80,)
        sentbuffer = sentbuffer + (0x00,)
        sentbuffer = sentbuffer + (0x03,)
        self.write(bytes(sentbuffer))

    def i2cStop(self):
        sentbuffer = ()
        # scl low, sda low
        for i in range(10):
            sentbuffer = sentbuffer + (0x80,)
            sentbuffer = sentbuffer + (0x00,)
            sentbuffer = sentbuffer + (0x03,)
        # scl high, sda low
        for i in range(10):
            sentbuffer = sentbuffer + (0x80,)
            sentbuffer = sentbuffer + (0x01,)
            sentbuffer = sentbuffer + (0x03,)
        # scl high, sda high
        for i in range(10): 
            sentbuffer = sentbuffer + (0x80,)
            sentbuffer = sentbuffer + (0x03,)
            sentbuffer = sentbuffer + (0x03,)
        # tristate scl & sda
        sentbuffer = sentbuffer + (0x80,)
        sentbuffer = sentbuffer + (0x03,)
        sentbuffer = sentbuffer + (0x00,)
        self.write(bytes(sentbuffer))

    def i2cWriteByteAndGetAck(self, data):
        sentbuffer = ()
        # set direction
        sentbuffer = sentbuffer + (0x80,)
        sentbuffer = sentbuffer + (0x00,)
        sentbuffer = sentbuffer + (0x03,)
        # write data bits
        sentbuffer = sentbuffer + (0x13,)
        sentbuffer = sentbuffer + (0x07,)
        sentbuffer = sentbuffer + (data,)
        # set sda to input mode before reading ack
        sentbuffer = sentbuffer + (0x80,)
        sentbuffer = sentbuffer + (0x00,)
        sentbuffer = sentbuffer + (0x01,)
        # read data bits from tdo/di
        sentbuffer = sentbuffer + (0x22,)
        sentbuffer = sentbuffer + (0x00,)
        sentbuffer = sentbuffer + (0x87,)
        self.write(bytes(sentbuffer))
        rdBuf = self.read(1, True)
        ack = rdBuf[0] & 0x01
        return ack

    def i2cReadByteAndGiveAck(self, ack):
        sentbuffer = ()
        # scl low, sda input
        sentbuffer = sentbuffer + (0x80,)
        sentbuffer = sentbuffer + (0x00,)
        sentbuffer = sentbuffer + (0x01,)
        # read data bits from tdo/di
        sentbuffer = sentbuffer + (0x22,)
        sentbuffer = sentbuffer + (0x07,)
        sentbuffer = sentbuffer + (0x87,)
        if ack == 0: # send ack
            sentbuffer = sentbuffer + (0x80,)
            sentbuffer = sentbuffer + (0x00,)
            sentbuffer = sentbuffer + (0x03,)
            # write data bits
            sentbuffer = sentbuffer + (0x13,)
            sentbuffer = sentbuffer + (0x00,)
            sentbuffer = sentbuffer + (0x00,)
        else: # send nack
            sentbuffer = sentbuffer + (0x80,)
            sentbuffer = sentbuffer + (0x02,)
            sentbuffer = sentbuffer + (0x03,)
            # write data bits
            sentbuffer = sentbuffer + (0x13,)
            sentbuffer = sentbuffer + (0x00,)
            sentbuffer = sentbuffer + (0x80,)
        # back to idle
        sentbuffer = sentbuffer + (0x80,)
        sentbuffer = sentbuffer + (0x00,)
        sentbuffer = sentbuffer + (0x01,)
        self.write(bytes(sentbuffer))
        rdBuf = self.read(1, True)
        return rdBuf[0]

    def i2cWriteDevAddr(self, devAddr, dir):
        fullAddr = devAddr << 1
        if dir == 1: # read
            fullAddr = fullAddr | 0x01
        else:
            fullAddr = fullAddr & 0xfe
        ack = self.i2cWriteByteAndGetAck(fullAddr)
        return ack

    def i2cRead(self, devAddr, sizeToXfer, options):
        '''
        options: [1]: has stop, [0]: has start
        '''
        self.purge()
        b = ctypes.create_string_buffer(sizeToXfer)
        if options & 0x01 > 0: # bit0: start
            self.i2cStart()
        ack = self.i2cWriteDevAddr(devAddr, 1)
        if ack == 0:
            for i in range(sizeToXfer):
                ack = 1 if i == (sizeToXfer - 1) else 0
                b[i] = self.i2cReadByteAndGiveAck(ack)
            if options & 0x02 > 0: #bit1: stop
                self.i2cStop()
            return b.raw[: sizeToXfer]
        else:
            self.i2cStop()
            raise ValueError("Device address not acked")

    def i2cWrite(self, devAddr, data:bytes, options):
        '''
        options: [1]: has stop, [0]: has start
        '''
        self.purge()
        if options & 0x01 > 0: # bit0: start, bit1: stop
            self.i2cStart()
        ack = self.i2cWriteDevAddr(devAddr, 0)
        if ack == 0:
            for i in range(len(data)):
                ack = self.i2cWriteByteAndGetAck(data[i])
                if ack == 1:
                    self.i2cStop()
                    raise ValueError("Nacked when writting data byte")
            if options & 0x02 > 0: #bit1: stop
                self.i2cStop()
        else:
            self.i2cStop()
            raise ValueError("Device address not acked")

__all__ = [
    "call_ft",
    "listDevices",
    "getLibraryVersion",
    "createDeviceInfoList",
    "getDeviceInfoDetail",
    "open",
    "openEx",
    "FTD2XX",
    "DeviceError",
    "ft_program_data",
]

__all__ += ["w32CreateFile"]
