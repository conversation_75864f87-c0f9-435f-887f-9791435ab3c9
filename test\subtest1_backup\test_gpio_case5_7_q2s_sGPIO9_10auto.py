# -*- coding: utf-8 -*-
"""

q68 0-14(除了13) -> s68 GPIO9/10
需要接上另一个mcu接iic2(4 14)或 iic1 (11 12)
灵活的GPIO9/10测试用例
展示如何在测试用例内部灵活配置不同的Link

特点:
1. 测试用例内部可以动态选择Link
2. 不需要修改top.py的全局配置
3. 支持单Link和多Link测试
4. 最大化模块灵活性

注意引脚上不用接上拉，也不能接探头，跑完再接
"""


import time
import pytest
import os
import tkinter as tk
from tkinter import messagebox


class GPIO9_10_FlexibleTester:
    """GPIO9/10灵活测试器"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_I2C2remote = devices['q68_I2C2remote']
        self.configure_links = devices['configure_links']
        self.get_link_status = devices['get_link_status']
        self.oscilloscope = devices.get('oscilloscope')

        # 获取top模块的函数
        self.power_off = devices.get('power_off')
        self.power_on = devices.get('power_on')
        self.setup_communication = devices.get('setup_communication')

        # 示波器自动截图配置
        self.oscilloscope_config = {
            'enable_screenshot': True,              # 是否启用自动截图
            'digital_trigger_channel': 9,           # 数字通道D1作为触发源
            'waveform_type': 'SQUARE',              # 方波
            'frequency_range': {
                'start': 380000,                    # 300kHz
                'end': 450000,                      # 400kHz
                'step': 10000                       # 10kHz步进
            },
            'amplitude': 1.8,                      # 1.26Vpp
            'offset': 0.9,                          # 900mVdc偏移
            'timebase_scale': '1us',                # 时基设置 2us/div
            'persistence_mode': 'INFinite',         # 余晖模式 (该机型唯一选项)
            'screenshot_folder': 'U-disk0/gpiotest/s68toq681',  # 截图保存文件夹
            'freq_observation_time': 1,             # 每个频率的观察时间(秒)
            'probe_wait_time': 6,                  # 第一次截图前等待插探头时间(秒)
        }
    
    def test_single_link(self, link_id, signal_id=11, q68_gpio=2, s68_gpios=[0, 9, 10], duration=3):
        """测试单个Link的GPIO9/10功能
        
        Args:
            link_id: 要测试的Link ID (0-3)
            signal_id: GPIO信号ID
            q68_gpio: Q68发送GPIO
            s68_gpios: S68接收GPIO列表
            duration: 测试持续时间
        """
        print(f"\n{'='*50}")
        print(f"测试单个Link: Link{link_id}")
        print(f"配置: 信号ID={signal_id}, Q68_GPIO{q68_gpio} → S68_GPIO{s68_gpios}")
        print(f"{'='*50}")
        
        # 步骤1: 配置指定Link
        print(f"\n步骤1: 配置Link{link_id}")
        link_status = self.configure_links([link_id])
        initial_status = link_status.get(f'link{link_id}', 0)
        print(f"  - Link{link_id} 初始状态: {initial_status}")
        
        # 步骤2: 配置Q68发送端
        print(f"\n步骤2: 配置Q68 GPIO{q68_gpio}为发送端")
        self.q68.MFNSet(gpio=q68_gpio, mfn=0)
        self.q68.GPIORemoteTx(gpio=q68_gpio, tx_id=signal_id, link_id=link_id, dly_comp_en=0)
        print(f"  - Q68 GPIO{q68_gpio} 配置完成 (信号ID={signal_id}, Link={link_id})")
        
        # 步骤3: 配置S68接收端
        print(f"\n步骤3: 配置S68 GPIO{s68_gpios}为接收端")
        self.q68_I2C2remote.dongle.devAddr = 0x40
        
        for gpio_pin in s68_gpios:
            # 引脚功能切换
            self.q68_I2C2remote.M2CMFNSet(gpio=gpio_pin, mfn=0)
            # 远程接收配置
            self.q68_I2C2remote.M2CGPIORemoteRx(gpio=gpio_pin, rx_id=signal_id)
            print(f"  - GPIO{gpio_pin}: I2C功能 → GPIO功能, 接收信号ID={signal_id}")
        
        # 步骤4: 等待和验证 + 示波器自动截图
        print(f"\n步骤4: 等待{duration}秒进行信号传输...")

        # 示波器频率扫描截图功能 (可选)
        self.oscilloscope_frequency_sweep_screenshot(link_id, q68_gpio, f"GPIO9_10")

        # 如果没有示波器或截图禁用，则执行原有的等待逻辑
        if not self.oscilloscope_config['enable_screenshot'] or self.oscilloscope is None:
            time.sleep(duration)
        
        # 步骤5: 验证结果
        final_status = self.get_link_status(link_id)
        print(f"\n步骤5: 验证结果")
        print(f"  - Link{link_id} 最终状态: {final_status}")
        
        # 断言验证
        assert final_status == 5, f"Link{link_id} 测试失败，状态: {final_status} (期望: 5)"
        
        print(f"✅ Link{link_id} GPIO9/10测试通过!")
        return True
    
    def test_multiple_links(self, link_configs):
        """测试多个Link的GPIO9/10功能
        
        Args:
            link_configs: Link配置列表，每个配置包含link_id, signal_id等
        """
        print(f"\n{'='*60}")
        print(f"测试多个Links: {[cfg['link_id'] for cfg in link_configs]}")
        print(f"{'='*60}")
        
        results = {}
        
        for i, config in enumerate(link_configs):
            print(f"\n--- 测试配置 {i+1}/{len(link_configs)} ---")
            
            try:
                result = self.test_single_link(
                    link_id=config['link_id'],
                    signal_id=config.get('signal_id', 11 + config['link_id']),
                    q68_gpio=config.get('q68_gpio', config['link_id']),
                    s68_gpios=config.get('s68_gpios', [9, 10]),
                    duration=config.get('duration', 3)
                )
                results[f"link{config['link_id']}"] = result
                
            except Exception as e:
                print(f"❌ Link{config['link_id']} 测试失败: {e}")
                results[f"link{config['link_id']}"] = False
                # 继续测试其他Link，不中断
        
        # 总结结果
        print(f"\n{'='*60}")
        print("多Link测试总结:")
        for link, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  - {link}: {status}")
        print(f"{'='*60}")
        
        return results

    def full_system_reinit(self, link_id, q68_gpio):
        """完整的系统重新初始化 - 模拟断电重新开机流程"""
        print(f"\n🔄 开始完整系统重新初始化...")
        print(f"   目标: Link{link_id} - Q68 GPIO{q68_gpio}")

        try:
            # 步骤1: 断电
            print(f"  🔌 步骤1: 系统断电...")
            if self.power_off:
                self.power_off()
                print(f"    - 断电完成")
            else:
                print(f"    - 警告: power_off函数不可用，跳过断电")

            # 等待断电稳定
            print(f"  ⏳ 等待2秒断电稳定...")
            time.sleep(2)

            # 步骤2: 重新上电
            print(f"  ⚡ 步骤2: 系统重新上电...")
            if self.power_on:
                self.power_on()
                print(f"    - 上电完成")
            else:
                print(f"    - 警告: power_on函数不可用，跳过上电")

            # 等待上电稳定
            print(f"  ⏳ 等待3秒上电稳定...")
            time.sleep(3)

            # 步骤3: 重新建立通信
            print(f"  📡 步骤3: 重新建立Q68-S68通信...")
            if self.setup_communication:
                self.setup_communication()
                print(f"    - 通信建立完成")
            else:
                print(f"    - 警告: setup_communication函数不可用，跳过通信建立")

            # 等待通信稳定
            print(f"  ⏳ 等待2秒通信稳定...")
            time.sleep(2)

            # 步骤4: 重新配置指定Link
            print(f"  🔗 步骤4: 重新配置Link{link_id}...")
            link_status = self.configure_links([link_id])
            print(f"    - Link{link_id}配置完成: {link_status}")

            print(f"✅ 系统重新初始化完成，准备测试GPIO{q68_gpio}")
            return True

        except Exception as e:
            print(f"❌ 系统重新初始化失败: {e}")
            return False

    def show_gpio_confirmation_dialog(self, link_id, q68_gpio):
        """显示GPIO测试确认对话框"""
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            root.attributes('-topmost', True)  # 置顶显示

            # 显示确认对话框
            title = f"GPIO测试确认 - Link{link_id} GPIO{q68_gpio}"
            message = f"""即将测试:
Link{link_id} - Q68 GPIO{q68_gpio} → S68 GPIO9/10

⚠️ 注意：每个GPIO测试前会执行完整的系统重新初始化
包括：断电 → 重新上电 → 重建通信 → 重新配置Link

预计耗时：约15-20秒 (初始化) + 测试时间

是否继续测试此GPIO?

[是] = 继续测试
[否] = 跳过此GPIO
[取消] = 停止所有测试"""

            result = messagebox.askyesnocancel(
                title=title,
                message=message,
                icon='question'
            )

            root.destroy()  # 销毁窗口

            # 返回结果：True=继续, False=跳过, None=取消全部
            return result

        except Exception as e:
            print(f"    - 弹窗显示失败: {e}")
            print(f"    - 自动继续测试...")
            return True  # 如果弹窗失败，默认继续

    def test_all_links_all_gpios(self, gpio_range=None, links_range=None, enable_dialog=True):
        """自动遍历所有Link的所有GPIO进行测试

        Args:
            gpio_range: GPIO范围，默认[0,1,2,3,4, 5,6,7,8,9,10,11,12,14,]
            links_range: Link范围，默认[0,1,2,3]
            enable_dialog: 是否启用确认对话框
        """
        if gpio_range is None:
            # 默认测试GPIO 0-14，排除GPIO13 (GPIO13通常用于特殊用途)
            gpio_range = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14]

        if links_range is None:
            links_range = [0, 1, 2, 3]

        print(f"\n{'='*80}")
        print(f"开始自动遍历测试:")
        print(f"  - Links: {links_range}")
        print(f"  - Q68 GPIOs: {gpio_range}")
        print(f"  - 确认对话框: {'启用' if enable_dialog else '禁用'}")
        print(f"  - 总测试数量: {len(links_range)} Links × {len(gpio_range)} GPIOs = {len(links_range) * len(gpio_range)} 个测试")
        print(f"{'='*80}")

        results = {}
        total_tests = len(links_range) * len(gpio_range)
        current_test = 0
        skipped_tests = []
        failed_tests = []

        for link_id in links_range:
            print(f"\n🔗 开始测试 Link{link_id} (共{len(gpio_range)}个GPIO)")
            print(f"{'='*60}")

            link_results = {}

            for q68_gpio in gpio_range:
                current_test += 1
                test_key = f"Link{link_id}_GPIO{q68_gpio}"

                print(f"\n📍 测试进度: {current_test}/{total_tests}")
                print(f"当前测试: Link{link_id} - Q68 GPIO{q68_gpio} → S68 GPIO9/10")

                # 显示确认对话框 (如果启用)
                if enable_dialog:
                    dialog_result = self.show_gpio_confirmation_dialog(link_id, q68_gpio)

                    if dialog_result is None:  # 用户点击取消
                        print(f"❌ 用户取消测试，停止所有后续测试")
                        results[test_key] = "cancelled"
                        return {
                            'results': results,
                            'summary': {
                                'total': current_test - 1,
                                'completed': len([r for r in results.values() if r == True]),
                                'failed': len([r for r in results.values() if r == False]),
                                'skipped': len(skipped_tests),
                                'cancelled': True
                            }
                        }

                    elif dialog_result is False:  # 用户选择跳过
                        print(f"⏭️  跳过测试: Link{link_id} GPIO{q68_gpio}")
                        skipped_tests.append(test_key)
                        results[test_key] = "skipped"
                        link_results[f"gpio{q68_gpio}"] = "skipped"
                        continue

                # 执行完整的系统重新初始化
                print(f"🔄 执行完整系统重新初始化 (模拟断电重新开机)...")
                reinit_success = self.full_system_reinit(link_id, q68_gpio)

                if not reinit_success:
                    print(f"❌ 系统重新初始化失败，跳过此GPIO测试")
                    results[test_key] = False
                    link_results[f"gpio{q68_gpio}"] = False
                    failed_tests.append(test_key)
                    continue

                # 执行测试
                try:
                    print(f"🚀 开始执行GPIO测试...")
                    result = self.test_single_link(
                        link_id=link_id,
                        signal_id=11 + link_id,  # 每个Link使用不同的信号ID
                        q68_gpio=q68_gpio,
                        s68_gpios=[0, 9, 10],  # 固定测试S68的GPIO0,9,10
                        duration=2  # 缩短单个测试时间
                    )

                    results[test_key] = result
                    link_results[f"gpio{q68_gpio}"] = result

                    if result:
                        print(f"✅ 测试通过: {test_key}")
                    else:
                        print(f"❌ 测试失败: {test_key}")
                        failed_tests.append(test_key)

                except Exception as e:
                    print(f"💥 测试异常: {test_key} - {e}")
                    results[test_key] = False
                    link_results[f"gpio{q68_gpio}"] = False
                    failed_tests.append(test_key)

                # 测试间隔
                if current_test < total_tests:
                    print(f"⏳ 等待1秒后进行下一个测试...")
                    time.sleep(1)

            # Link测试总结
            link_passed = len([r for r in link_results.values() if r == True])
            link_total = len([r for r in link_results.values() if r != "skipped"])
            print(f"\n📊 Link{link_id} 测试完成: {link_passed}/{link_total} 通过")

        # 最终总结
        print(f"\n{'='*80}")
        print(f"🎯 自动遍历测试完成!")

        completed_tests = [r for r in results.values() if r in [True, False]]
        passed_tests = [r for r in results.values() if r == True]

        summary = {
            'total': total_tests,
            'completed': len(completed_tests),
            'passed': len(passed_tests),
            'failed': len(failed_tests),
            'skipped': len(skipped_tests),
            'cancelled': False
        }

        print(f"📈 测试统计:")
        print(f"  - 总计划测试: {summary['total']}")
        print(f"  - 实际完成: {summary['completed']}")
        print(f"  - 测试通过: {summary['passed']}")
        print(f"  - 测试失败: {summary['failed']}")
        print(f"  - 跳过测试: {summary['skipped']}")

        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test}")

        if skipped_tests:
            print(f"\n⏭️  跳过的测试:")
            for test in skipped_tests:
                print(f"  - {test}")

        print(f"{'='*80}")

        return {
            'results': results,
            'summary': summary,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests
        }

    def oscilloscope_frequency_sweep_screenshot(self, link_id, q68_gpio, test_name="GPIO9_10"):
        """示波器频率扫描截图功能"""
        if not self.oscilloscope_config['enable_screenshot'] or self.oscilloscope is None:
            if not self.oscilloscope_config['enable_screenshot']:
                print("  - 示波器自动截图已禁用")
            elif self.oscilloscope is None:
                print("  - 示波器不可用")
            return

        try:
            print("  - 开始示波器频率扫描截图...")

            # 配置示波器
            osc_config = self.oscilloscope_config

            # 设置数字通道触发
            trigger_source = self.oscilloscope.Set_Digital_Trigger(digital_channel=osc_config['digital_trigger_channel'])
            print(f"    * 数字触发设置: {trigger_source.strip()}")

            # 设置余晖模式
            persistence_time = self.oscilloscope.Set_Display_Persistence(time=osc_config['persistence_mode'])
            print(f"    * 余晖模式设置: {persistence_time.strip()}")

            # 设置时基
            current_timebase = self.oscilloscope.Set_Timebase_Scale(timebase_scale=osc_config['timebase_scale'])
            print(f"    * 时基设置: {osc_config['timebase_scale']} ({current_timebase:.2e}s/div)")

            # 创建截图保存目录
            screenshot_folder = osc_config['screenshot_folder']
            os.makedirs(screenshot_folder, exist_ok=True)

            # 设置初始波形参数 (使用临时频率，为第一个目标频率做准备)
            waveform = osc_config['waveform_type']
            amplitude = osc_config['amplitude']
            offset = osc_config['offset']
            target_first_freq = osc_config['frequency_range']['start']
            temp_freq = target_first_freq + 50000  # 临时频率：比第一个目标频率高50kHz

            print(f"    * 设置初始波形参数...")
            self.oscilloscope.Set_Wavegen_Basic(
                waveform=waveform,
                frequency=temp_freq,  # 先设置临时频率
                amplitude=amplitude,
                offset=offset,
                output_state='OFF',  # 初始不开启输出
                load=50
            )
            print(f"      - 波形类型: {waveform}, 幅值: {amplitude}Vpp, 偏移: {offset}Vdc")
            print(f"      - 临时频率: {temp_freq/1000}kHz (为第一个目标频率{target_first_freq/1000}kHz做准备)")

            # 第一次截图前等待插探头
            probe_wait_time = osc_config['probe_wait_time']
            print(f"    * 等待{probe_wait_time}秒供您插探头...")
            time.sleep(probe_wait_time)

            # 频率扫描
            freq_config = osc_config['frequency_range']
            frequency_list = range(freq_config['start'], freq_config['end'] + freq_config['step'], freq_config['step'])

            print(f"    * 开始频率扫描: {freq_config['start']/1000}-{freq_config['end']/1000}kHz, 步进{freq_config['step']/1000}kHz")

            for i, freq_hz in enumerate(frequency_list):
                freq_khz = freq_hz / 1000
                print(f"      -> 测试频率: {freq_khz}kHz [{i+1}/{len(frequency_list)}]")

                # 第一个频率的特殊处理：需要先开启输出，然后进行频率切换
                if i == 0:
                    print(f"        - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")

                    # 开启输出
                    output_on_status = self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                    print(f"        - 临时频率输出已开启: {output_on_status.strip()}")
                    time.sleep(0.2)

                    # 准备切换频率
                    self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz-10000)
                    print(f"        - 准备切换到目标频率")
                    time.sleep(0.2)

                    # 切换到目标频率
                    self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                    print(f"        - 频率切换完成: {freq_khz}kHz")

                    # 清除扫描并重新开始采集
                    self.oscilloscope.Clear_Display_Waveform()
                    print(f"        - 已清除扫描，重新开始采集目标频率")
                    time.sleep(0.1)

                else:
                    # 非第一个频率的正常处理流程
                    # 开启输出
                    output_on_status = self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                    print(f"        - 输出已开启: {output_on_status.strip()}")
                    time.sleep(0.2)

                    # 设置新频率
                    self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                    print(f"        - 频率设置完成: {freq_khz}kHz")
                    time.sleep(0.2)

                    self.oscilloscope.Clear_Display_Waveform()
                    print(f"        - 已清除扫描，重新开始采集目标频率")
                    time.sleep(0.1)

                # 等待信号稳定和余晖累积
                print(f"        - 等待{osc_config['freq_observation_time']}秒进行余晖观察...")
                time.sleep(osc_config['freq_observation_time'])

                # 自动截图
                active_links = link_id
                gpio_num = q68_gpio
                timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒，不包含年份
                screenshot_filename = f"{i+1:02d}_GPIO{gpio_num}_LINK{active_links}_{freq_khz}kHz_{timestamp}.png"
                screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

                # 使用正确的截图参数：PNG格式，正常色彩，隐藏菜单
                self.oscilloscope.Save_Image(
                    filepath=screenshot_path,
                    image_format="PNG",
                    invert="OFF",      # 正常色彩
                    menu="MOF"         # 隐藏菜单
                )
                print(f"        - 截图已保存: {screenshot_filename} (PNG格式，隐藏菜单)")

                # 短暂延时确保截图完成
                time.sleep(1)

            print(f"    * 频率扫描完成，共测试 {len(frequency_list)} 个频率点")

        except Exception as e:
            print(f"    - 示波器频率扫描截图失败: {e}")
            print(f"    - 继续原有测试流程...")


@pytest.mark.fast
def test_gpio9_10_link0(devices):
    """测试Link0的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=0, signal_id=11)


@pytest.mark.fast  
def test_gpio9_10_link1(devices):
    """测试Link1的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=1, signal_id=12)


@pytest.mark.fast
def test_gpio9_10_link2(devices):
    """测试Link2的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=2, signal_id=13)


@pytest.mark.fast
def test_gpio9_10_link3(devices):
    """测试Link3的GPIO9/10"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(link_id=3, signal_id=14)


@pytest.mark.slow
def test_gpio9_10_all_links(devices):
    """测试所有Link的GPIO9/10 (如果硬件支持)"""
    tester = GPIO9_10_FlexibleTester(devices)
    
    # 定义所有Link的测试配置
    link_configs = [
        {'link_id': 0, 'signal_id': 11, 'q68_gpio': 0},
        {'link_id': 1, 'signal_id': 12, 'q68_gpio': 1},
        {'link_id': 2, 'signal_id': 13, 'q68_gpio': 2},
        {'link_id': 3, 'signal_id': 14, 'q68_gpio': 3},
    ]
    
    results = tester.test_multiple_links(link_configs)
    
    # 验证至少有一个Link测试通过
    passed_links = [link for link, result in results.items() if result]
    assert len(passed_links) > 0, f"所有Link测试都失败了: {results}"
    
    print(f"✅ 多Link测试完成，通过的Links: {passed_links}")


@pytest.mark.parametrize("link_id", [0, 1, 2, 3])
def test_gpio9_10_parametrized(devices, link_id):
    """参数化测试 - 可以选择性运行特定Link"""
    tester = GPIO9_10_FlexibleTester(devices)
    tester.test_single_link(
        link_id=link_id,
        signal_id=10 + link_id,
        duration=2  # 缩短测试时间
    )


@pytest.mark.fast
def test_gpio9_10_auto_traverse_all(devices):
    """自动遍历测试所有Link的所有GPIO (带确认对话框)"""
    tester = GPIO9_10_FlexibleTester(devices)

    print(f"\n🚀 开始自动遍历测试 - 所有Link所有GPIO")
    print(f"⚠️  注意：此测试将遍历Link0-3的GPIO0-15 (排除GPIO4)")
    print(f"📱 每个测试前会弹窗确认，可选择跳过或取消")

    # 执行自动遍历测试
    result = tester.test_all_links_all_gpios(
        gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14],  # 排除GPIO4
        links_range=[0, 1, 2, 3],
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    if summary['passed'] == 0 and summary['completed'] > 0:
        print(f"⚠️  警告：所有完成的测试都失败了")

    print(f"✅ 自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.slow
def test_gpio9_10_auto_traverse_no_dialog(devices):
    """自动遍历测试所有Link的所有GPIO (无确认对话框，快速模式)"""
    tester = GPIO9_10_FlexibleTester(devices)

    print(f"\n🚀 开始快速自动遍历测试 - 所有Link所有GPIO")
    print(f"⚡ 快速模式：无确认对话框，自动执行所有测试")

    # 执行自动遍历测试 (无对话框)
    result = tester.test_all_links_all_gpios(
        gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14],  # 排除GPIO13
        links_range=[0, 1, 2, 3],
        enable_dialog=False  # 禁用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ 快速自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_gpio9_10_auto_traverse_single_link(devices):
    """自动遍历测试单个Link的所有GPIO (带确认对话框)"""
    tester = GPIO9_10_FlexibleTester(devices)

    print(f"\n🚀 开始单Link自动遍历测试")
    print(f"📱 每个GPIO测试前会弹窗确认")

    # 只测试Link0的所有GPIO
    result = tester.test_all_links_all_gpios(
        gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14],  # 排除GPIO13
        links_range=[2],  # 只测试一个Link
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ 单Link自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


if __name__ == "__main__":
    """
    使用说明:

    1. 测试单个Link:
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py::test_gpio9_10_link0 -v -s

    2. 测试所有Link:
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py::test_gpio9_10_all_links -v -s

    3. 参数化测试特定Link:
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py::test_gpio9_10_parametrized[0] -v -s

    4. 🆕 自动遍历测试所有Link所有GPIO (带确认对话框):
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py::test_gpio9_10_auto_traverse_all -v -s

    5. 🆕 快速自动遍历测试 (无确认对话框):
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py::test_gpio9_10_auto_traverse_no_dialog -v -s

    6. 🆕 单Link自动遍历测试 (带确认对话框):
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py::test_gpio9_10_auto_traverse_single_link -v -s

    7. 运行所有快速测试:
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py -m fast -v -s

    8. 运行所有慢速测试 (包括自动遍历):
       pytest test_gpio_case5_7_q2s_sGPIO9_10auto.py -m slow -v -s

    🔥 新功能特点:
    - 自动遍历Link0-3的GPIO0-15 (排除GPIO4)
    - 弹窗确认每个GPIO测试，可选择跳过或取消
    - 详细的测试进度和统计信息
    - 支持快速模式 (无确认对话框)
    - 智能错误处理和测试总结
    """
    print("GPIO9/10自动遍历测试用例")
    print("请使用pytest运行测试")
