# -*- coding: utf-8 -*-
"""
GPIO Oscilloscope Auto Screenshot Test - 优化重构版本

基于test_gpio_case5_7.py模板，使用示波器自动截图功能测试GPIO信号。
配置数字通道触发，产生方波信号，并在不同频率下自动截图。

📋 文件说明：
这是经过优化重构的GPIO自动遍历测试版本，提供了更好的代码结构和错误处理。

🚀 与 test_gpio_case5_7auto.py 的主要改进：

1. 📊 配置结构优化：
   - 分层配置：gpio、links、timing、oscilloscope 分类管理
   - 参数集中：相关配置参数分组，便于维护
   - 默认值：提供合理的默认配置

2. 🛡️ 错误处理增强：
   - 统一错误处理：safe_execute() 包装器统一处理异常
   - 错误恢复：更好的错误恢复机制
   - 详细日志：更详细的错误信息和调试输出

3. 🔧 代码结构改进：
   - 方法拆分：将长方法拆分为更小的功能单元
   - 代码复用：提取公共方法，减少重复代码
   - 可读性：更清晰的方法命名和注释

4. ⚙️ 功能扩展：
   - 更多测试模式：支持更灵活的测试配置
   - 更好的进度跟踪：详细的测试进度和统计
   - 更强的容错性：更好地处理硬件异常

🎯 适用场景：
- 复杂的GPIO测试需求
- 需要高可靠性的自动化测试
- 大规模的GPIO遍历测试
- 需要详细错误处理和日志的场景
"""
import logging
import time
import pytest
import os
import tkinter as tk
from tkinter import messagebox

# 优化后的测试配置 - 统一管理所有配置参数
TEST_CONFIG = {
    # GPIO配置
    'gpio': {
        'q68_default_range': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16],  # 排除GPIO13
        's68_default_range': [0, 1, 2, 3, 4, 5, 6, 7, 8],
        'special_gpios': [15, 16],  # 需要特殊I2C配置的GPIO
        'signal_id': 11,
    },

    # Link配置
    'links': {
        'default_active': [0],
        'all_links': [0, 1, 2, 3],
        'i2c_addresses': {
            'q68_iic_addr': 0x73,
            's68_iic_addr': 0x40,
            'sensor_addr': 0x24,
        }
    },

    # 时间配置
    'timing': {
        'observation_time': 1,
        'power_off_wait': 3,
        'power_on_wait': 5,
        'communication_wait': 2,
        'address_setup_wait': 0.1,
        'test_interval': 1,
        'screenshot_wait': 2,
    },

    # 示波器配置
    'oscilloscope': {
        'enable_screenshot': True,
        'use_fixed_frequencies': True,
        'digital_trigger_channel': 1,
        'waveform_type': 'SQUARE',
        'frequency_list': [30, 1000, 10000, 50000, 100000],
        'timebase_list': ['20ms', '1ms', '100us', '20us', '10us'],
        'frequency_range': {'start': 300000, 'end': 420000, 'step': 10000},
        'sweep_timebase': '1us',
        'amplitude': 1.8,
        'offset': 0.9,
        'screenshot_folder': 'U-disk0/gpiotest/q68tos68_0_8',
        'persistence_mode': 'INFinite',
        'probe_wait_time': 5,
        'freq_observation_time': 3,
    }
}


class GPIO_Q68_S68_AutoTester:
    """优化后的Q68到S68 GPIO自动遍历测试器"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.get_link_status = devices['get_link_status']
        self.oscilloscope = devices.get('oscilloscope')

        # 系统控制函数
        self.power_off = devices.get('power_off')
        self.power_on = devices.get('power_on')
        self.setup_communication = devices.get('setup_communication')

        # 配置缓存
        self.config = TEST_CONFIG

    def safe_execute(self, func, *args, error_msg="操作失败", allow_none_return=True, **kwargs):
        """统一的错误处理包装器

        Args:
            func: 要执行的函数
            *args: 函数参数
            error_msg: 错误消息
            allow_none_return: 是否允许None返回值（硬件控制函数通常返回None但执行成功）
            **kwargs: 函数关键字参数

        Returns:
            (result, error): 成功时返回(result, None)，失败时返回(None, error_msg)
        """
        _ = allow_none_return  # 保持接口兼容性，硬件控制函数默认允许None返回值
        try:
            result = func(*args, **kwargs)
            # 对于硬件控制函数，None返回值通常表示成功执行
            return result, None
        except Exception as e:
            print(f"❌ {error_msg}: {e}")
            return None, str(e)

    def safe_execute_with_validation(self, func, *args, error_msg="操作失败", **kwargs):
        """需要验证返回值的安全执行包装器（用于需要非None返回值的函数）"""
        try:
            result = func(*args, **kwargs)
            if result is None:
                print(f"❌ {error_msg}: 函数返回None")
                return None, "函数返回None"
            return result, None
        except Exception as e:
            print(f"❌ {error_msg}: {e}")
            return None, str(e)

    def wait_with_message(self, seconds, message):
        """带消息的等待函数"""
        print(f"  ⏳ {message}...")
        time.sleep(seconds)

    def get_i2c_bus_config(self, q68_gpio):
        """获取GPIO对应的I2C总线配置"""
        return 1 if q68_gpio in self.config['gpio']['special_gpios'] else 0

    def setup_address_translation(self, links, i2c_bus_config):
        """统一的地址转换设置"""
        print(f"  📡 设置Links地址转换 (i2c_bus={i2c_bus_config})...")
        addresses = self.config['links']['i2c_addresses']

        for link_id in links:
            if link_id < len(self.s68_res_dev):
                _, error = self.safe_execute(
                    self.q68_remote.S68_AddrTrans,
                    link=link_id,
                    q68_iic_addr=addresses['q68_iic_addr'],
                    s68_iic_addr=addresses['s68_iic_addr'],
                    s68_retrans_addr=self.s68_res_dev[link_id],
                    sensor_addr=addresses['sensor_addr'],
                    sensor_retrans_addr=addresses['sensor_addr'] + link_id,
                    i2c_bus=i2c_bus_config,
                    error_msg=f"Link{link_id}地址转换设置失败"
                )
                if error is None:  # 修正：检查error而不是result，S68_AddrTrans通常返回None但执行成功
                    print(f"    ✅ Link{link_id} 地址转换设置完成")
                else:
                    print(f"    ❌ Link{link_id} 地址转换设置失败: {error}")
                    return False
        return True

    def system_reinit(self, target_links, q68_gpio, s68_gpio_desc):
        """统一的系统重新初始化方法"""
        links_desc = f"Link{target_links[0]}" if len(target_links) == 1 else f"所有Links{target_links}"
        print(f"\n🔄 开始完整系统重新初始化...")
        print(f"   目标: {links_desc} - Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_desc}")

        timing = self.config['timing']

        # 系统断电
        print(f"  🔌 步骤1: 系统断电...")
        if self.power_off:
            result, error = self.safe_execute(self.power_off, error_msg="断电失败")
            if error is None:  # 修正：检查error而不是result，因为硬件函数可能返回None但执行成功
                print(f"    ✅ 断电完成")
            else:
                print(f"    ❌ 断电失败: {error}")
        else:
            print(f"    ⚠️ power_off函数不可用，跳过断电")

        self.wait_with_message(timing['power_off_wait'], "等待断电稳定")

        # 系统上电
        print(f"  ⚡ 步骤2: 系统重新上电...")
        if self.power_on:
            result, error = self.safe_execute(self.power_on, error_msg="上电失败")
            if error is None:  # 修正：检查error而不是result
                print(f"    ✅ 上电完成")
            else:
                print(f"    ❌ 上电失败: {error}")
        else:
            print(f"    ⚠️ power_on函数不可用，跳过上电")

        self.wait_with_message(timing['power_on_wait'], "等待上电稳定")

        # 重新建立通信
        print(f"  📡 步骤3: 重新建立Q68-S68通信...")
        if self.setup_communication:
            result, error = self.safe_execute(self.setup_communication, error_msg="通信建立失败")
            if error is None:  # 修正：检查error而不是result
                print(f"    ✅ 通信建立完成")
            else:
                print(f"    ❌ 通信建立失败: {error}")
                return False
        else:
            print(f"    ⚠️ setup_communication函数不可用，跳过通信建立")

        self.wait_with_message(timing['communication_wait'], "等待通信稳定")

        # 重新配置Links
        print(f"  🔗 步骤4: 重新配置{links_desc}...")
        result, error = self.safe_execute(self.configure_links, target_links, error_msg="Links配置失败")
        if error is None:  # 修正：检查error而不是result
            print(f"    ✅ {links_desc}配置完成: {result}")
            print(f"✅ 系统重新初始化完成")
            return True
        else:
            print(f"    ❌ {links_desc}配置失败: {error}")
            return False

    # 保持向后兼容的方法
    def full_system_reinit(self, link_id, q68_gpio, s68_gpio):
        """单Link系统重新初始化 - 向后兼容"""
        return self.system_reinit([link_id], q68_gpio, s68_gpio)

    def full_system_reinit_all_links(self, q68_gpio, s68_gpio):
        """全Link系统重新初始化 - 向后兼容"""
        return self.system_reinit(self.config['links']['all_links'], q68_gpio, s68_gpio)

    def show_confirmation_dialog(self, q68_gpio, test_scope="单Link"):
        """统一的GPIO测试确认对话框"""
        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)

            # 根据测试范围生成不同的消息
            if test_scope == "单Link":
                title = f"Q68 GPIO测试确认"
                target_desc = "指定Link"
                time_estimate = "15-20秒"
            else:
                title = f"全Link Q68 GPIO测试确认"
                target_desc = "所有Links(0-3)"
                time_estimate = "20-25秒"

            message = f"""即将测试:
{target_desc} - Q68 GPIO{q68_gpio} → S68 GPIO0-8

⚠️ 注意：每个Q68 GPIO测试前会执行完整的系统重新初始化
包括：断电 → 重新上电 → 重建通信 → 重新配置Links

预计耗时：约{time_estimate} (初始化) + 测试时间

是否继续测试此Q68 GPIO?

[是] = 继续测试
[否] = 跳过此Q68 GPIO
[取消] = 停止所有测试"""

            result = messagebox.askyesnocancel(title=title, message=message, icon='question')
            root.destroy()
            return result

        except Exception as e:
            print(f"    ⚠️ 弹窗显示失败: {e}, 自动继续测试")
            return True

    # 保持向后兼容的方法
    def show_gpio_confirmation_dialog(self, link_id, q68_gpio):
        """单Link GPIO测试确认对话框 - 向后兼容"""
        _ = link_id  # 保持接口兼容性
        return self.show_confirmation_dialog(q68_gpio, "单Link")

    def show_all_links_gpio_confirmation_dialog(self, q68_gpio):
        """全Link GPIO测试确认对话框 - 向后兼容"""
        return self.show_confirmation_dialog(q68_gpio, "全Link")

    def configure_q68_gpio(self, q68_gpio, signal_id, primary_link=None):
        """统一的Q68 GPIO配置方法"""
        print(f"  📤 配置Q68 GPIO{q68_gpio}为发送端...")

        # 设置MFN
        _, error = self.safe_execute(
            self.q68.MFNSet,
            gpio=q68_gpio,
            mfn=0,
            error_msg=f"Q68 GPIO{q68_gpio} MFN设置失败"
        )
        if error is not None:  # 修正：检查error而不是result
            return False

        # 配置远程发送
        if primary_link is not None:
            _, error = self.safe_execute(
                self.q68.GPIORemoteTx,
                gpio=q68_gpio,
                tx_id=signal_id,
                link_id=primary_link,
                dly_comp_en=0,
                error_msg=f"Q68 GPIO{q68_gpio} 远程发送配置失败"
            )
        else:
            _, error = self.safe_execute(
                self.q68.GPIORemoteTx,
                gpio=q68_gpio,
                tx_id=signal_id,
                error_msg=f"Q68 GPIO{q68_gpio} 远程发送配置失败"
            )

        if error is None:  # 修正：检查error而不是result
            link_desc = f", 主Link: {primary_link}" if primary_link is not None else ""
            print(f"    ✅ Q68 GPIO{q68_gpio} 发送端配置完成 (信号ID: {signal_id}{link_desc})")
            return True
        else:
            print(f"    ❌ Q68 GPIO{q68_gpio} 发送端配置失败: {error}")
            return False

    def configure_s68_gpio(self, link_id, s68_gpio, signal_id):
        """配置单个S68 GPIO"""
        try:
            # 设置MFN为0 (GPIO功能)
            self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
            # 设置远程接收
            self.q68_remote.M2CGPIORemoteRx(gpio=s68_gpio, rx_id=signal_id)
            print(f"        ✅ Link{link_id} S68 GPIO{s68_gpio} 配置完成")
            return True
        except Exception as e:
            print(f"        ❌ Link{link_id} S68 GPIO{s68_gpio} 配置失败: {e}")
            return False

    def configure_s68_gpios_for_links(self, target_links, s68_gpio_range, signal_id):
        """统一的S68 GPIO配置方法"""
        print(f"  📥 配置S68 GPIO{s68_gpio_range}为接收端...")

        success_count = 0
        total_count = 0

        for link_id in target_links:
            if link_id >= len(self.s68_res_dev):
                print(f"    ⚠️ Link{link_id} 超出s68_res_dev范围，跳过")
                continue

            target_addr = self.s68_res_dev[link_id]
            print(f"    🔗 配置Link{link_id} S68 (地址: 0x{target_addr:02X})...")

            # 设置设备地址
            self.q68_remote.dongle.devAddr = target_addr
            print(f"      📍 设备地址已设置为: 0x{target_addr:02X}")

            # 等待地址设置生效
            time.sleep(self.config['timing']['address_setup_wait'])

            # 配置所有S68 GPIO
            for s68_gpio in s68_gpio_range:
                total_count += 1
                if self.configure_s68_gpio(link_id, s68_gpio, signal_id):
                    success_count += 1

        print(f"    📊 S68 GPIO配置完成: {success_count}/{total_count} 成功")
        return success_count > 0

    def setup_oscilloscope_basic(self, osc_config):
        """示波器基础设置"""
        print(f"  🔧 配置示波器基础参数...")

        # 设置数字通道触发
        _, error = self.safe_execute(
            self.oscilloscope.Set_Digital_Trigger,
            digital_channel=osc_config['digital_trigger_channel'],
            error_msg="数字触发设置失败"
        )
        if error is None:  # 修正：检查error而不是result
            print(f"    ✅ 数字触发设置: D{osc_config['digital_trigger_channel']}")

        # 设置余晖模式
        _, error = self.safe_execute(
            self.oscilloscope.Set_Display_Persistence,
            time=osc_config['persistence_mode'],
            error_msg="余晖模式设置失败"
        )
        if error is None:  # 修正：检查error而不是result
            print(f"    ✅ 余晖模式设置: {osc_config['persistence_mode']}")

        # 创建截图目录
        os.makedirs(osc_config['screenshot_folder'], exist_ok=True)
        print(f"    ✅ 截图目录: {osc_config['screenshot_folder']}")

    def process_frequency_point(self, freq_hz, timebase, link_id, q68_gpio, index, total, osc_config):
        """处理单个频率点的测试"""
        freq_display = f"{freq_hz}Hz" if freq_hz < 1000 else f"{freq_hz/1000:.0f}kHz"
        print(f"      🎯 测试频率: {freq_display}, 时基: {timebase} [{index+1}/{total}]")

        # 设置时基
        self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)

        # 频率设置逻辑
        if index == 0:
            # 第一个频率特殊处理
            self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
            time.sleep(0.3)
            self.oscilloscope.Set_Wavegen_Frequency(frequency=max(freq_hz-10000, 10))
            time.sleep(0.3)
            self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
            self.oscilloscope.Clear_Display_Waveform()
            time.sleep(0.5)
        else:
            # 正常频率处理
            self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
            time.sleep(0.5)
            self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
            time.sleep(0.3)
            self.oscilloscope.Clear_Display_Waveform()
            time.sleep(0.5)

        # 等待信号稳定
        time.sleep(osc_config['freq_observation_time'])

        # 截图
        timestamp = time.strftime('%m%d_%H%M%S')
        screenshot_filename = f"{index+1:02d}_Link{link_id}_Q68GPIO{q68_gpio}_{freq_display}_{timebase}_{timestamp}.png"
        screenshot_path = f"{osc_config['screenshot_folder']}/{screenshot_filename}"

        self.oscilloscope.Save_Image(
            filepath=screenshot_path,
            image_format="PNG",
            invert="OFF",
            menu="MOF"
        )
        print(f"        📸 截图已保存: {screenshot_filename}")
        time.sleep(1)

    def full_system_reinit_all_links(self, q68_gpio, s68_gpio):
        """完整的系统重新初始化 - 模拟断电重新开机流程 (针对所有Links)"""
        print(f"\n🔄 开始完整系统重新初始化...")
        print(f"   目标: 所有Links(0-3) - Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio}")

        try:
            # 步骤1: 断电
            print(f"  🔌 步骤1: 系统断电...")
            if self.power_off:
                self.power_off()
                print(f"    - 断电完成")
            else:
                print(f"    - 警告: power_off函数不可用，跳过断电")

            # 等待断电稳定
            print(f"  ⏳ 等待3秒断电稳定...")
            time.sleep(3)

            # 步骤2: 重新上电
            print(f"  ⚡ 步骤2: 系统重新上电...")
            if self.power_on:
                self.power_on()
                print(f"    - 上电完成")
            else:
                print(f"    - 警告: power_on函数不可用，跳过上电")

            # 等待上电稳定
            print(f"  ⏳ 等待5秒上电稳定...")
            time.sleep(5)

            # 步骤3: 重新建立通信
            print(f"  📡 步骤3: 重新建立Q68-S68通信...")
            if self.setup_communication:
                self.setup_communication()
                print(f"    - 通信建立完成")
            else:
                print(f"    - 警告: setup_communication函数不可用，跳过通信建立")

            # 等待通信稳定
            print(f"  ⏳ 等待2秒通信稳定...")
            time.sleep(2)

            # 步骤4: 重新配置所有Links
            print(f"  🔗 步骤4: 重新配置所有Links(0-3)...")
            all_links = [0, 1, 2, 3]
            link_status = self.configure_links(all_links)
            print(f"    - 所有Links配置完成: {link_status}")

            print(f"✅ 系统重新初始化完成，准备测试Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio}")
            return True

        except Exception as e:
            print(f"❌ 系统重新初始化失败: {e}")
            return False

    def oscilloscope_frequency_sweep_screenshot(self, link_id, q68_gpio):
        """优化的示波器频率扫描截图功能"""
        osc_config = self.config['oscilloscope']

        # 检查示波器可用性
        if not osc_config['enable_screenshot']:
            print("  📷 示波器自动截图已禁用")
            return
        if self.oscilloscope is None:
            print("  📷 示波器不可用")
            return

        try:
            mode = "固定频率" if osc_config['use_fixed_frequencies'] else "扫频"
            print(f"  📷 开始示波器{mode}截图...")

            # 基础设置
            self.setup_oscilloscope_basic(osc_config)

            # 设置初始波形参数
            waveform = osc_config['waveform_type']
            amplitude = osc_config['amplitude']
            offset = osc_config['offset']

            if osc_config['use_fixed_frequencies']:
                # 固定频率模式
                frequency_list = osc_config['frequency_list']
                timebase_list = osc_config['timebase_list']

                print(f"    * 设置初始波形参数...")
                self.oscilloscope.Set_Wavegen_Basic(
                    waveform=waveform,
                    frequency=frequency_list[0] + 50000,  # 临时频率
                    amplitude=amplitude,
                    offset=offset,
                    output_state='OFF',  # 初始不开启输出
                    load=50
                )
                print(f"      - 波形类型: {waveform}, 幅值: {amplitude}Vpp, 偏移: {offset}Vdc")

                # 第一次截图前等待插探头
                probe_wait_time = osc_config['probe_wait_time']
                print(f"    * 等待{probe_wait_time}秒供您插探头...")
                time.sleep(probe_wait_time)

                print(f"    * 开始固定频率测试: {len(frequency_list)}个频率点")

                for i, (freq_hz, timebase) in enumerate(zip(frequency_list, timebase_list)):
                    freq_display = f"{freq_hz}Hz" if freq_hz < 1000 else f"{freq_hz/1000:.0f}kHz"
                    print(f"      -> 测试频率: {freq_display}, 时基: {timebase} [{i+1}/{len(frequency_list)}]")

                    # 设置时基
                    current_timebase = self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
                    print(f"        - 时基设置: {timebase} ({current_timebase:.2e}s/div)")

                    # 第一个频率的特殊处理
                    if i == 0:
                        print(f"        - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 临时频率输出已开启")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=max(freq_hz-10000, 10))
                        print(f"        - 准备切换到目标频率")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率切换完成: {freq_display}")
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)
                    else:
                        # 非第一个频率的正常处理流程
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 输出已开启")
                        time.sleep(0.5)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率设置完成: {freq_display}")
                        time.sleep(0.3)
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)

                    # 等待信号稳定和余晖累积
                    print(f"        - 等待{osc_config['freq_observation_time']}秒进行余晖观察...")
                    time.sleep(osc_config['freq_observation_time'])

                    # 自动截图
                    timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒
                    screenshot_filename = f"{i+1:02d}_Link{link_id}_Q68GPIO{q68_gpio}_{freq_display}_{timebase}_{timestamp}.png"
                    screenshot_path = f"{osc_config['screenshot_folder']}/{screenshot_filename}"

                    self.oscilloscope.Save_Image(
                        filepath=screenshot_path,
                        image_format="PNG",
                        invert="OFF",      # 正常色彩
                        menu="MOF"         # 隐藏菜单
                    )
                    print(f"        - 截图已保存: {screenshot_filename}")
                    time.sleep(1)

                print(f"    * 固定频率测试完成，共测试 {len(frequency_list)} 个频率点")

            else:
                # 扫频模式 (保留原有功能)
                freq_config = osc_config['frequency_range']
                timebase = osc_config['sweep_timebase']

                # 设置时基
                current_timebase = self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
                print(f"    * 时基设置: {timebase} ({current_timebase:.2e}s/div)")

                print(f"    * 设置初始波形参数...")
                target_first_freq = freq_config['start']
                temp_freq = target_first_freq + 50000  # 临时频率

                self.oscilloscope.Set_Wavegen_Basic(
                    waveform=waveform,
                    frequency=temp_freq,  # 先设置临时频率
                    amplitude=amplitude,
                    offset=offset,
                    output_state='OFF',  # 初始不开启输出
                    load=50
                )
                print(f"      - 波形类型: {waveform}, 幅值: {amplitude}Vpp, 偏移: {offset}Vdc")
                print(f"      - 临时频率: {temp_freq/1000}kHz (为第一个目标频率{target_first_freq/1000}kHz做准备)")

                # 第一次截图前等待插探头
                probe_wait_time = osc_config['probe_wait_time']
                print(f"    * 等待{probe_wait_time}秒供您插探头...")
                time.sleep(probe_wait_time)

                # 频率扫描
                frequency_list = range(freq_config['start'], freq_config['end'] + freq_config['step'], freq_config['step'])
                print(f"    * 开始频率扫描: {freq_config['start']/1000}-{freq_config['end']/1000}kHz, 步进{freq_config['step']/1000}kHz")

                for i, freq_hz in enumerate(frequency_list):
                    freq_khz = freq_hz / 1000
                    print(f"      -> 测试频率: {freq_khz}kHz [{i+1}/{len(frequency_list)}]")

                    # 第一个频率的特殊处理
                    if i == 0:
                        print(f"        - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 临时频率输出已开启")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz-10000)
                        print(f"        - 准备切换到目标频率")
                        time.sleep(0.3)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率切换完成: {freq_khz}kHz")
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)
                    else:
                        # 非第一个频率的正常处理流程
                        self.oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                        print(f"        - 输出已开启")
                        time.sleep(0.5)
                        self.oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                        print(f"        - 频率设置完成: {freq_khz}kHz")
                        time.sleep(0.3)
                        self.oscilloscope.Clear_Display_Waveform()
                        print(f"        - 已清除扫描，重新开始采集目标频率")
                        time.sleep(0.5)

                    # 等待信号稳定和余晖累积
                    print(f"        - 等待{osc_config['freq_observation_time']}秒进行余晖观察...")
                    time.sleep(osc_config['freq_observation_time'])

                    # 自动截图
                    timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒
                    screenshot_filename = f"{i+1:02d}_Link{link_id}_Q68GPIO{q68_gpio}_{freq_khz}kHz_{timestamp}.png"
                    screenshot_path = f"{osc_config['screenshot_folder']}/{screenshot_filename}"

                    self.oscilloscope.Save_Image(
                        filepath=screenshot_path,
                        image_format="PNG",
                        invert="OFF",      # 正常色彩
                        menu="MOF"         # 隐藏菜单
                    )
                    print(f"        - 截图已保存: {screenshot_filename}")
                    time.sleep(1)

                print(f"    * 频率扫描完成，共测试 {len(frequency_list)} 个频率点")

        except Exception as e:
            print(f"    - 示波器截图失败: {e}")
            print(f"    - 继续原有测试流程...")

    def test_gpio_communication(self, target_links, q68_gpio, s68_gpio_range, signal_id, duration=3):
        """统一的GPIO通信测试方法"""
        links_desc = f"Link{target_links[0]}" if len(target_links) == 1 else f"所有Links{target_links}"
        print(f"\n🔗 测试 {links_desc}: Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")

        try:
            # 步骤1: 配置Links
            print(f"  📋 步骤1: 配置{links_desc}...")
            _, error = self.safe_execute(self.configure_links, target_links, error_msg="Links配置失败")
            if error is not None:  # 修正：检查error而不是result
                return False
            print(f"    ✅ {links_desc}配置成功")

            # 步骤2: 设置地址转换
            i2c_bus_config = self.get_i2c_bus_config(q68_gpio)
            if q68_gpio in self.config['gpio']['special_gpios']:
                print(f"    ⚠️ 检测到特殊GPIO{q68_gpio} (I2C1引脚)，使用i2c_bus=1")

            if not self.setup_address_translation(target_links, i2c_bus_config):
                return False

            # 步骤3: 配置Q68 GPIO
            primary_link = target_links[0] if len(target_links) > 1 else None
            if not self.configure_q68_gpio(q68_gpio, signal_id, primary_link):
                return False

            # 步骤4: 配置S68 GPIOs
            if not self.configure_s68_gpios_for_links(target_links, s68_gpio_range, signal_id):
                return False

            # 步骤5: 信号传输和验证
            print(f"  🔄 步骤5: 信号传输测试...")

            # 示波器截图 (可选)
            self.oscilloscope_frequency_sweep_screenshot(target_links[0], q68_gpio)

            # 等待信号传输
            osc_config = self.config['oscilloscope']
            if not osc_config['enable_screenshot'] or self.oscilloscope is None:
                self.wait_with_message(duration, f"等待{duration}秒进行信号传输")

            # 验证链路状态
            print(f"  ✅ 步骤6: 验证{links_desc}状态...")
            final_status, error = self.safe_execute(self.get_link_status, error_msg="获取链路状态失败")
            if error is None:
                print(f"    📊 最终状态: {final_status}")
            else:
                print(f"    ⚠️ 无法获取链路状态: {error}")

            print(f"✅ 测试完成: {links_desc} Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")
            return True

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

    # 保持向后兼容的方法
    def test_single_q68_gpio_to_all_s68_gpios(self, link_id, q68_gpio, s68_gpio_range, signal_id, duration=3):
        """单Link GPIO测试 - 向后兼容"""
        return self.test_gpio_communication([link_id], q68_gpio, s68_gpio_range, signal_id, duration)

    def test_single_q68_gpio_to_all_links_all_s68_gpios(self, q68_gpio, s68_gpio_range, all_links, signal_id, duration=3):
        """全Link GPIO测试 - 向后兼容"""
        return self.test_gpio_communication(all_links, q68_gpio, s68_gpio_range, signal_id, duration)

    def run_gpio_test_suite(self, target_links, q68_gpio_range=None, s68_gpio_range=None, enable_dialog=True):
        """统一的GPIO测试套件执行方法"""
        # 使用默认配置
        if q68_gpio_range is None:
            q68_gpio_range = self.config['gpio']['q68_default_range']
        if s68_gpio_range is None:
            s68_gpio_range = self.config['gpio']['s68_default_range']

        # 测试描述
        test_scope = "单Link" if len(target_links) == 1 else "全Link"
        links_desc = f"Link{target_links[0]}" if len(target_links) == 1 else f"所有Links{target_links}"

        print(f"\n{'='*80}")
        print(f"开始{test_scope} Q68→S68 GPIO自动遍历测试:")
        print(f"  - Links: {links_desc}")
        print(f"  - Q68 GPIOs: {q68_gpio_range}")
        print(f"  - S68 GPIOs: {s68_gpio_range}")
        print(f"  - 确认对话框: {'启用' if enable_dialog else '禁用'}")
        print(f"  - 总测试数量: {len(q68_gpio_range)} 个Q68 GPIO")
        print(f"{'='*80}")

        # 测试状态跟踪
        results = {}
        skipped_tests = []
        failed_tests = []

        for current_test, q68_gpio in enumerate(q68_gpio_range, 1):
            test_key = f"{links_desc}_Q68GPIO{q68_gpio}_S68GPIO{s68_gpio_range}"

            print(f"\n📍 测试进度: {current_test}/{len(q68_gpio_range)}")
            print(f"当前测试: {links_desc} - Q68 GPIO{q68_gpio} → S68 GPIO{s68_gpio_range}")

            # 确认对话框
            if enable_dialog:
                dialog_result = self.show_confirmation_dialog(q68_gpio, test_scope)

                if dialog_result is None:  # 取消
                    print(f"❌ 用户取消测试，停止所有后续测试")
                    results[test_key] = "cancelled"
                    return self._generate_test_summary(results, skipped_tests, failed_tests, current_test-1, True)

                elif dialog_result is False:  # 跳过
                    print(f"⏭️ 跳过测试: {test_key}")
                    skipped_tests.append(test_key)
                    results[test_key] = "skipped"
                    continue

            # 系统重新初始化
            print(f"🔄 执行完整系统重新初始化...")
            if not self.system_reinit(target_links, q68_gpio, f"GPIO{s68_gpio_range}"):
                print(f"❌ 系统重新初始化失败，跳过此测试")
                results[test_key] = False
                failed_tests.append(test_key)
                continue

            # 执行GPIO测试
            try:
                print(f"🚀 开始执行Q68 GPIO{q68_gpio}测试...")
                signal_id = self.config['gpio']['signal_id'] + (target_links[0] if len(target_links) == 1 else 0)
                result = self.test_gpio_communication(target_links, q68_gpio, s68_gpio_range, signal_id, 2)

                results[test_key] = result
                if result:
                    print(f"✅ 测试通过: {test_key}")
                else:
                    print(f"❌ 测试失败: {test_key}")
                    failed_tests.append(test_key)

            except Exception as e:
                print(f"💥 测试异常: {test_key} - {e}")
                results[test_key] = False
                failed_tests.append(test_key)

            # 测试间隔
            if current_test < len(q68_gpio_range):
                time.sleep(self.config['timing']['test_interval'])

        return self._generate_test_summary(results, skipped_tests, failed_tests, len(q68_gpio_range), False)

    def _generate_test_summary(self, results, skipped_tests, failed_tests, total_tests, cancelled):
        """生成测试总结报告"""
        completed_tests = [r for r in results.values() if r in [True, False]]
        passed_tests = [r for r in results.values() if r == True]

        summary = {
            'total': total_tests,
            'completed': len(completed_tests),
            'passed': len(passed_tests),
            'failed': len(failed_tests),
            'skipped': len(skipped_tests),
            'cancelled': cancelled
        }

        print(f"\n{'='*80}")
        print(f"🎯 GPIO自动遍历测试完成!")
        print(f"📈 测试统计:")
        print(f"  - 总计划测试: {summary['total']}")
        print(f"  - 实际完成: {summary['completed']}")
        print(f"  - 测试通过: {summary['passed']}")
        print(f"  - 测试失败: {summary['failed']}")
        print(f"  - 跳过测试: {summary['skipped']}")

        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test}")

        if skipped_tests:
            print(f"\n⏭️ 跳过的测试:")
            for test in skipped_tests:
                print(f"  - {test}")

        print(f"{'='*80}")

        return {
            'results': results,
            'summary': summary,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests
        }

    # 保持向后兼容的方法
    def test_single_link_all_q68_gpios(self, q68_gpio_range=None, s68_gpio_range=None, link_id=0, enable_dialog=True):
        """单Link GPIO遍历测试 - 向后兼容"""
        return self.run_gpio_test_suite([link_id], q68_gpio_range, s68_gpio_range, enable_dialog)

    def test_all_links_all_q68_gpios(self, q68_gpio_range=None, s68_gpio_range=None, enable_dialog=True):
        """全Link GPIO遍历测试 - 向后兼容"""
        return self.run_gpio_test_suite(self.config['links']['all_links'], q68_gpio_range, s68_gpio_range, enable_dialog)


@pytest.mark.fast
def test_gpio_q68_s68_auto_traverse_all_links(devices):
    """自动遍历测试所有Links的所有Q68 GPIO (每个Q68 GPIO同时测试所有Links上的所有S68 GPIO 0-8)"""
    tester = GPIO_Q68_S68_AutoTester(devices)

    print(f"\n🚀 开始全Link Q68→S68 GPIO自动遍历测试")
    print(f"📱 每个Q68 GPIO测试前会弹窗确认")
    print(f"🔄 每个Q68 GPIO会同时测试所有Links(0-3)上的所有S68 GPIO 0-8")

    # 测试所有Links的所有Q68 GPIO
    result = tester.test_all_links_all_q68_gpios(
        q68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16],  # 排除GPIO13
        s68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8],
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ 全Link Q68→S68 GPIO自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_gpio_q68_s68_auto_traverse_single_link(devices):
    """自动遍历测试单个Link的所有Q68 GPIO (每个Q68 GPIO同时测试所有S68 GPIO 0-8)"""
    tester = GPIO_Q68_S68_AutoTester(devices)

    print(f"\n🚀 开始单Link Q68→S68 GPIO自动遍历测试")
    print(f"📱 每个Q68 GPIO测试前会弹窗确认")
    print(f"🔄 每个Q68 GPIO会同时测试所有S68 GPIO 0-8")

    # 测试Link0的所有Q68 GPIO
    result = tester.test_single_link_all_q68_gpios(
        q68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16],  # 排除GPIO13
        s68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8],
        link_id=0,  # 测试Link0
        enable_dialog=True  # 启用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ 单Link Q68→S68 GPIO自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.slow
def test_gpio_q68_s68_auto_traverse_no_dialog(devices):
    """自动遍历测试单个Link的所有Q68 GPIO (无确认对话框，快速模式)"""
    tester = GPIO_Q68_S68_AutoTester(devices)

    print(f"\n🚀 开始单Link Q68→S68 GPIO快速自动遍历测试")
    print(f"⚡ 快速模式：无确认对话框，自动执行所有测试")
    print(f"🔄 每个Q68 GPIO会同时测试所有S68 GPIO 0-8")

    # 测试Link0的所有Q68 GPIO (无对话框)
    result = tester.test_single_link_all_q68_gpios(
        q68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16],  # 排除GPIO13
        s68_gpio_range=[0, 1, 2, 3, 4, 5, 6, 7, 8],
        link_id=0,  # 测试Link0
        enable_dialog=False  # 禁用确认对话框
    )

    # 验证至少有一些测试通过
    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试: {summary}"

    print(f"✅ 单Link Q68→S68 GPIO快速自动遍历测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_gpio_oscilloscope_auto_screenshot(devices):
    """
    GPIO示波器自动截图测试
    
    测试步骤:
    1. 初始化示波器并配置数字触发、余晖模式和时基
    2. 配置Q68 GPIO为发送端
    3. 配置S68 GPIOs为接收端
    4. 在指定频率范围内扫描，每个频率点:
       - 清除前一频率的扫描并重新开始采集
       - 设置新的波形参数
       - 等待余晖累积
       - 自动截图保存
    5. 验证链路状态
    """
    print(f"\n[GPIO示波器测试] 开始执行: 自动截图功能测试")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")
    print(f"频率范围: {TEST_CONFIG['oscilloscope_config']['frequency_range']['start']/1000}kHz - {TEST_CONFIG['oscilloscope_config']['frequency_range']['end']/1000}kHz")
    
    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']
    oscilloscope = devices['oscilloscope']  # 从devices获取示波器对象

    # ---------------------------------------------------------------------
    # 步骤0: 检查示波器可用性并配置
    # ---------------------------------------------------------------------
    print(f"\n步骤0: 检查示波器可用性并配置...")

    if oscilloscope is None:
        print("  - 示波器不可用，跳过测试")
        pytest.skip("示波器不可用，跳过测试")

    try:
        print("  - 示波器已初始化")

        # 设置数字通道触发
        trigger_channel = TEST_CONFIG['oscilloscope_config']['digital_trigger_channel']
        trigger_source = oscilloscope.Set_Digital_Trigger(digital_channel=trigger_channel)
        print(f"  - 数字触发设置完成: {trigger_source}")

        # 设置余晖模式
        persistence_mode = TEST_CONFIG['oscilloscope_config']['persistence_mode']
        persistence_time = oscilloscope.Set_Display_Persistence(time=persistence_mode)
        print(f"  - 余晖模式设置完成: {persistence_time.strip()}")

        # 设置时基 (使用扫频模式的时基作为默认值)
        timebase_scale = TEST_CONFIG['oscilloscope_config']['sweep_timebase']
        current_timebase = oscilloscope.Set_Timebase_Scale(timebase_scale=timebase_scale)
        print(f"  - 时基设置完成: {timebase_scale} ({current_timebase:.2e}s/div)")

        # 设置初始波形参数 (使用一个临时频率，为第一个目标频率做准备)
        waveform = TEST_CONFIG['oscilloscope_config']['waveform_type']
        amplitude = TEST_CONFIG['oscilloscope_config']['amplitude']
        offset = TEST_CONFIG['oscilloscope_config']['offset']
        target_first_freq = TEST_CONFIG['oscilloscope_config']['frequency_range']['start']
        temp_freq = target_first_freq + 50000  # 临时频率：比第一个目标频率高50kHz

        print(f"  - 设置初始波形参数...")
        basic_wave_status, _ = oscilloscope.Set_Wavegen_Basic(
            waveform=waveform,
            frequency=temp_freq,  # 先设置临时频率
            amplitude=amplitude,
            offset=offset,
            output_state='OFF',  # 初始不开启输出
            load=50
        )
        print(f"    * 波形类型: {waveform}")
        print(f"    * 幅值: {amplitude}Vpp")
        print(f"    * 偏移: {offset}Vdc")
        print(f"    * 临时频率: {temp_freq/1000}kHz (为第一个目标频率{target_first_freq/1000}kHz做准备)")
        print(f"    * 基本波形状态: {basic_wave_status.strip()}")

        # 创建截图保存目录
        screenshot_folder = TEST_CONFIG['oscilloscope_config']['screenshot_folder']
        os.makedirs(screenshot_folder, exist_ok=True)
        print(f"  - 截图保存目录: {screenshot_folder}")

    except Exception as e:
        print(f"  - 示波器配置失败: {e}")
        pytest.skip("示波器配置失败，跳过测试")
    
    # ---------------------------------------------------------------------
    # 步骤1: 配置测试专用Links
    # ---------------------------------------------------------------------
    print(f"\n步骤1: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")
    
    # 检查GPIO15/16特殊配置
    q68_source_gpio = TEST_CONFIG['q68_source_gpio']
    
    # 根据GPIO类型确定i2c_bus配置
    if q68_source_gpio in [15, 16]:
        print(f"\n⚠️  检测到Q68 GPIO{q68_source_gpio} (I2C1引脚)")
        print("=" * 60)
        print("🔌 硬件连接提示:")
        print("   请确保已连接 Q68 GPIO 11/12 (SDA1/SCL1)")
        print("   - GPIO 11: SDA1 (I2C1数据线)")
        print("   - GPIO 12: SCL1 (I2C1时钟线)")
        print("=" * 60)
        i2c_bus_config = 1
        print(f"✅ 自动设置 i2c_bus = 1 (用于GPIO{q68_source_gpio})")
    else:
        i2c_bus_config = 0
        print(f"\n使用标准GPIO{q68_source_gpio}配置 (i2c_bus = 0)")
    
    # 设置地址转换
    print(f"\n步骤1.5: 设置激活Links的地址转换...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            q68_remote.S68_AddrTrans(
                link=link,
                q68_iic_addr=0x73,
                s68_iic_addr=0x40,
                s68_retrans_addr=s68_res_dev[link],
                sensor_addr=0x24,
                sensor_retrans_addr=0x24 + link,
                i2c_bus=i2c_bus_config
            )
            print(f"  - Link{link} 地址转换设置: 0x{s68_res_dev[link]:02X} -> 0x40 (i2c_bus={i2c_bus_config})")
    print("  - 地址转换设置完成")
    
    # ---------------------------------------------------------------------
    # 步骤2: 配置Q68 GPIO为发送端
    # ---------------------------------------------------------------------
    print(f"\n步骤2: 配置 Q68 GPIO-{q68_source_gpio} 为发送端 (Signal ID: {TEST_CONFIG['signal_id']})...")
    print(f"    - I2C总线配置: {i2c_bus_config}")
    
    q68.MFNSet(gpio=q68_source_gpio, mfn=0)
    
    # 使用第一个激活的Link作为发送Link
    primary_link = TEST_CONFIG['active_links'][0]
    q68.GPIORemoteTx(gpio=q68_source_gpio, tx_id=TEST_CONFIG['signal_id'],
                     link_id=primary_link, dly_comp_en=0)
    print(f"    - Q68 GPIO{q68_source_gpio} 配置完成，使用Link{primary_link}")
    
    # ---------------------------------------------------------------------
    # 步骤3: 配置S68 GPIOs为接收端
    # ---------------------------------------------------------------------
    print(f"\n步骤3: 配置激活Links上的S68 GPIOs {TEST_CONFIG['s68_target_gpios']} 为接收端...")
    for link in TEST_CONFIG['active_links']:
        if link < len(s68_res_dev):
            target_addr = s68_res_dev[link]
            print(f"    - 配置 S68 Link-{link} (地址: 0x{target_addr:02X})...")
            
            # 设置设备地址
            q68_remote.dongle.devAddr = target_addr
            print(f"      * 设备地址已设置为: 0x{target_addr:02X}")
            
            time.sleep(0.1)
            
            for s68_pin in TEST_CONFIG['s68_target_gpios']:
                print(f"      * 配置GPIO{s68_pin}...")
                
                try:
                    q68_remote.M2CMFNSet(gpio=s68_pin, mfn=0)
                    print(f"        - GPIO{s68_pin} MFN设置为0 (GPIO功能)")
                    
                    q68_remote.M2CGPIORemoteRx(gpio=s68_pin, rx_id=TEST_CONFIG['signal_id'])
                    print(f"        - GPIO{s68_pin} 远程接收设置完成 (信号ID: {TEST_CONFIG['signal_id']})")
                except Exception as e:
                    print(f"        - 错误: GPIO{s68_pin} 配置失败: {e}")
        else:
            print(f"    - 警告: Link{link} 超出s68_res_dev范围，跳过")
    print("    - 激活Links上的S68 GPIO 配置完成。")
    
    # ---------------------------------------------------------------------
    # 步骤4: 频率扫描和自动截图
    # ---------------------------------------------------------------------
    freq_config = TEST_CONFIG['oscilloscope_config']['frequency_range']

    print(f"\n步骤4: 开始频率扫描和自动截图...")
    print(f"    - 波形参数已在初始化时设定，现在只改变频率")

    frequency_list = range(freq_config['start'], freq_config['end'] + freq_config['step'], freq_config['step'])

    for i, freq_hz in enumerate(frequency_list):
        freq_khz = freq_hz / 1000
        print(f"\n  -> 测试频率: {freq_khz}kHz ({freq_hz}Hz) [{i+1}/{len(frequency_list)}]")

        try:
            # 第一个频率的特殊处理：需要先开启输出，然后进行频率切换
            if i == 0:
                print(f"    - 第一个频率特殊处理：先开启临时频率输出，然后切换到目标频率")

                # 开启输出
                output_on_status = oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                print(f"    - 临时频率输出已开启: {output_on_status.strip()}")
                time.sleep(0.3)

                # 准备切换频率
                oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz-10000)
                print(f"    - 准备切换到目标频率")
                time.sleep(0.3)

                # 切换到目标频率
                oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                print(f"    - 频率切换完成: {freq_khz}kHz")
                # time.sleep(0.3)

                # 清除扫描并重新开始采集
                oscilloscope.Clear_Display_Waveform()
                print(f"    - 已清除扫描，重新开始采集目标频率")
                time.sleep(0.5)

            else:
                # 非第一个频率的正常处理流程
                # 清除之前的扫描并重新开始采集

                # 开启输出
                output_on_status = oscilloscope.Set_Wavegen_Output(state='ON', load=50)
                print(f"    - 输出已开启: {output_on_status.strip()}")
                time.sleep(0.5)

                # 设置新频率
                oscilloscope.Set_Wavegen_Frequency(frequency=freq_hz)
                print(f"    - 频率设置完成: {freq_khz}kHz")
                time.sleep(0.3)

                oscilloscope.Clear_Display_Waveform()
                print(f"    - 已清除扫描，重新开始采集目标频率")
                time.sleep(0.5)

            # 等待信号稳定和余晖累积
            print(f"    - 等待{TEST_CONFIG['observation_time']}秒进行余晖观察...")
            time.sleep(TEST_CONFIG['observation_time'])

            # 自动截图
            gpio_num = TEST_CONFIG['q68_source_gpio']
            timestamp = time.strftime('%m%d_%H%M%S')  # 月日_时分秒，不包含年份
            screenshot_filename = f"{i+1:02d}_GPIO{gpio_num}_{freq_khz}kHz_{timestamp}.png"
            screenshot_path = f"{screenshot_folder}/{screenshot_filename}"

            # 使用正确的截图参数：PNG格式，正常色彩，隐藏菜单
            oscilloscope.Save_Image(
                filepath=screenshot_path,
                image_format="PNG",
                invert="OFF",      # 正常色彩
                menu="MOF"         # 隐藏菜单
            )
            print(f"    - 截图已保存: {screenshot_filename} (PNG格式，隐藏菜单)")

            # 短暂延时确保截图完成
            time.sleep(2)

        except Exception as e:
            print(f"    - 错误: 频率{freq_khz}kHz测试失败: {e}")
            continue
    
    print(f"\n  - 频率扫描完成，共测试 {len(frequency_list)} 个频率点")
    
    # ---------------------------------------------------------------------
    # 步骤5: 验证链路状态
    # ---------------------------------------------------------------------
    print(f"\n步骤5: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]
    
    active_statuses = {}
    for link_id in TEST_CONFIG['active_links']:
        if 0 <= link_id <= 3:
            status = link_status_funcs[link_id]()
            active_statuses[f'link{link_id}'] = status
            print(f"    - Link{link_id} 状态: {status}")
    
    # 验证激活Links的状态
    failed_links = [link for link, status in active_statuses.items() if status != 5]
    assert len(failed_links) == 0, \
        f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"
    
    print(f"\n==> [GPIO示波器测试] 测试通过!")
    print(f"    - GPIO信号路径: Q68 GPIO{TEST_CONFIG['q68_source_gpio']} -> S68 GPIOs{TEST_CONFIG['s68_target_gpios']}")
    print(f"    - 激活Links: {TEST_CONFIG['active_links']}")
    print(f"    - 频率范围: {freq_config['start']/1000}-{freq_config['end']/1000}kHz")
    print(f"    - 截图保存位置: {screenshot_folder}")
    print(f"    - 链路状态正常: {active_statuses}")


if __name__ == "__main__":
    """
    使用说明:

    1. 原有的示波器自动截图测试:
       pytest test_gpio_case5_7auto_fix.py::test_gpio_oscilloscope_auto_screenshot -v -s

    2. 🆕 全Link自动遍历测试 (带确认对话框) - 同时测试所有4个Links:
       pytest test_gpio_case5_7auto_fix.py::test_gpio_q68_s68_auto_traverse_all_links -v -s

    3. 🆕 单Link自动遍历测试 (带确认对话框):
       pytest test_gpio_case5_7auto_fix.py::test_gpio_q68_s68_auto_traverse_single_link -v -s

    4. 🆕 快速自动遍历测试 (无确认对话框):
       pytest test_gpio_case5_7auto_fix.py::test_gpio_q68_s68_auto_traverse_no_dialog -v -s

    5. 运行所有快速测试:
       pytest test_gpio_case5_7auto_fix.py -m fast -v -s

    6. 运行所有慢速测试 (包括自动遍历):
       pytest test_gpio_case5_7auto_fix.py -m slow -v -s

    🔥 新功能特点:
    - 🆕 全Link自动遍历: 同时测试所有4个Links的Q68 GPIO0-16到S68 GPIO0-8
    - 🆕 单Link自动遍历: 测试指定单个Link的所有GPIO组合
    - 弹窗确认每个GPIO测试，可选择跳过或取消
    - 每个GPIO测试前完整系统重新初始化 (断电→重新上电→重建通信)
    - 固定频率测试: 30Hz, 1kHz, 10kHz, 50kHz, 100kHz
    - 对应时基: 20ms, 1ms, 100us, 10us, 10us
    - 详细的测试进度和统计信息
    - 智能错误处理和测试总结
    - 支持示波器自动截图功能

    📊 测试配置:
    - 修改TEST_CONFIG中的参数来调整测试设置
    - frequency_list: 固定频率列表 [30, 1000, 10000, 50000, 100000] Hz
    - timebase_list: 对应时基 ['20ms', '1ms', '100us', '10us', '10us']
    - amplitude/offset: 调整波形参数
    - screenshot_folder: 修改截图保存路径
    - persistence_mode: 调整余晖模式 (该机型只支持INFinite)

    🔧 硬件要求:
    - Siglent SDS5034X示波器通过USB连接
    - Q68和S68硬件正确连接
    - 如使用GPIO15/16需要连接I2C1引脚
    - 支持完整的电源控制 (断电/重新上电)
    """
    print("Q68→S68 GPIO自动遍历测试用例")
    print("请使用pytest运行测试")
