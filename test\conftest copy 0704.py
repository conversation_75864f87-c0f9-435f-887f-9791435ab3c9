import os
import sys
import pytest
import time

# ------------------------------------------------------------------
#  项目路径设置和依赖导入
# ------------------------------------------------------------------
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 添加API路径
# sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65d68_a0'))  # Path to Common_d
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65q68_a0'))  # Path to Common
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'))  # Path to Common_var
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # Path to instr_drv

# 导入硬件驱动
try:
    # from Common_d.M65D68_Common_Fuction_A0 import *
    from Common.M65Q68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    from instr_drv.IT6322B import IT6322B
    from instr_drv.Keithley2230_py3 import Keithley2230
    from instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
    print("✅ 硬件驱动导入成功")
except ImportError as e:
    print(f"⚠️ 硬件驱动导入失败: {e}")
    # 如果导入失败，定义空类以避免错误
    class IT6322B: pass
    class Keithley2230: pass
    class SiglentSDS5034X: pass
    class M65Q68_A0: pass
    class M65Q68_A0_Remote_M66S68: pass

# ------------------------------------------------------------------
#  硬件配置常量
# ------------------------------------------------------------------
# 电源配置
VOLTAGE = [1.2, 1.8, 1.8]

# Link配置 - 支持所有4个Link的完整配置
ALL_LINKS = [0, 1, 2, 3]          # 所有可用的link
KLINK = [0,]                      # 默认激活的link
RATE = [2, 2, 2, 2]               # link rate: 1-3G, 2-6G
RATE_final = [2, 2, 2, 2]         # 最终设置的link rate
BCRATE = [0, 0, 0, 0]             # q68 -> S68 data rate: 0:150M, 1:187.5M, 2/3:200M
DTbypass = [0, 0, 0, 0]           # 多data type时必须改为1
vcid = [0, 1, 2, 3]               # set vcid of each link
pcs_set = [0, 0, 0, 0]            # 0:8b/10b, 1: 66/64d
fec_bypass = [0, 0, 0, 0]         # 1: fec bypass

# GPIO测试相关配置
q68_iic_addr = 0x73                        # Q68地址
s68_iic_dev = [0x40, 0x40, 0x40, 0x40]     # s68地址

# I2C通信错误检测配置
I2C_ERROR_DETECTION = {
    'enable': True,                        # 是否启用I2C错误检测
    'retry_count': 1,                      # 重试次数
    'retry_delay': 0.1,                    # 重试间隔(秒)
    'error_patterns': [                    # 错误模式匹配
        'send_cmd_i2c_get cmd ret error: 1',
        'i2c communication error',
        'I2C_ERROR',
        'timeout'
    ]
}

# ------------------------------------------------------------------
#  全局设备对象
# ------------------------------------------------------------------
# 电源设备 (延迟初始化)
power_camera = None
power_q68 = None

# 全局设备对象
d68 = None
q68 = None
q68_remote = None
s68_res_dev = None
q68_i2c2 = None
q68_I2C2remote = None
s680 = None
oscilloscope = None

# ------------------------------------------------------------------
#  I2C错误检测和处理函数
# ------------------------------------------------------------------
import re
import logging
from contextlib import redirect_stdout, redirect_stderr
from io import StringIO

class I2CErrorDetector:
    """I2C通信错误检测器"""

    def __init__(self):
        self.error_count = 0
        self.last_error = None
        self.error_history = []

    def check_for_errors(self, output_text):
        """检查输出文本中是否包含I2C错误"""
        if not I2C_ERROR_DETECTION['enable']:
            return False

        for pattern in I2C_ERROR_DETECTION['error_patterns']:
            if pattern.lower() in output_text.lower():
                self.error_count += 1
                self.last_error = pattern
                self.error_history.append({
                    'pattern': pattern,
                    'text': output_text,
                    'timestamp': time.time()
                })
                print(f"🚨 检测到I2C错误: {pattern}")
                return True
        return False

    def reset_error_count(self):
        """重置错误计数"""
        self.error_count = 0
        self.last_error = None

    def get_error_summary(self):
        """获取错误摘要"""
        return {
            'error_count': self.error_count,
            'last_error': self.last_error,
            'total_errors': len(self.error_history)
        }

# 全局I2C错误检测器实例
i2c_error_detector = I2CErrorDetector()

def execute_with_i2c_error_detection(func, *args, **kwargs):
    """执行函数并检测I2C错误

    Args:
        func: 要执行的函数
        *args: 函数参数
        **kwargs: 函数关键字参数

    Returns:
        tuple: (result, has_error)
    """
    if not I2C_ERROR_DETECTION['enable']:
        return func(*args, **kwargs), False

    # 捕获标准输出和错误输出
    stdout_capture = StringIO()
    stderr_capture = StringIO()

    try:
        with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
            result = func(*args, **kwargs)

        # 检查输出中是否有错误
        stdout_text = stdout_capture.getvalue()
        stderr_text = stderr_capture.getvalue()

        has_error = (i2c_error_detector.check_for_errors(stdout_text) or
                    i2c_error_detector.check_for_errors(stderr_text))

        if has_error:
            print(f"📝 捕获的输出: {stdout_text}")
            print(f"📝 捕获的错误: {stderr_text}")

        return result, has_error

    except Exception as e:
        error_text = str(e)
        has_error = i2c_error_detector.check_for_errors(error_text)
        print(f"❌ 函数执行异常: {e}")
        raise

def retry_on_i2c_error(func, *args, **kwargs):
    """在I2C错误时重试执行函数

    Args:
        func: 要执行的函数
        *args: 函数参数
        **kwargs: 函数关键字参数

    Returns:
        函数执行结果

    Raises:
        Exception: 重试次数用尽后仍有错误
    """
    max_retries = I2C_ERROR_DETECTION['retry_count']
    retry_delay = I2C_ERROR_DETECTION['retry_delay']

    for attempt in range(max_retries + 1):
        try:
            result, has_error = execute_with_i2c_error_detection(func, *args, **kwargs)

            if not has_error:
                if attempt > 0:
                    print(f"✅ 重试成功 (第{attempt}次重试)")
                return result
            else:
                if attempt < max_retries:
                    print(f"🔄 检测到I2C错误，准备重试 ({attempt + 1}/{max_retries + 1})")
                    time.sleep(retry_delay)
                else:
                    print(f"❌ 重试次数用尽，I2C错误持续存在")
                    raise Exception(f"I2C通信错误: {i2c_error_detector.last_error}")

        except Exception as e:
            if attempt < max_retries:
                print(f"🔄 执行异常，准备重试 ({attempt + 1}/{max_retries + 1}): {e}")
                time.sleep(retry_delay)
            else:
                print(f"❌ 重试次数用尽，异常持续存在: {e}")
                raise

# ------------------------------------------------------------------
#  硬件控制函数
# ------------------------------------------------------------------
def init_global_objects():
    """初始化全局设备对象"""
    print("🔧 初始化全局设备对象...")

    global q68, q68_remote, s68_res_dev, q68_i2c2, q68_I2C2remote, s680, oscilloscope
    

    # 初始化Q68设备
    q68 = M65Q68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual')
    print("✅ q68 init")

    q68_remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00Q\x00$41Q\x197997', dongle='stm32', id=0, bus='i2c', optype='manual')
    print("✅ q68_remote init")

    # q68_i2c2 = M65Q68_A0(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', bus_chan=1, optype='manual')  # G9PH DS board, bus_chan=1 for I2C1 (SDA1, SCL1)
    # print("✅ q68_i2c2 init")
    # q68_I2C2remote = M65Q68_A0_Remote_M66S68(dongle_id=b'\x00:\x00&41Q\x024590',dongle='stm32', id=0, bus='i2c', optype='manual')
    # print("✅ q68_I2C2remote init")
    
    # S68转译地址
    s68_res_dev = [0x20, 0x21, 0x22, 0x23]

    # 初始化示波器 (可选，如果连接失败不影响其他测试)
    try:
        oscilloscope = SiglentSDS5034X(porttype='usb')
        print("✅ 示波器初始化成功")
    except Exception as e:
        print(f"⚠️ 示波器初始化失败: {e}")
        oscilloscope = None

def power_off():
    """电源关闭函数"""
    print("🔌 关闭电源...")
    power_q68.TurnOutputsOff()
    power_camera.TurnOutputsOff()
    time.sleep(2)

def power_on():
    """电源控制函数"""
    print("🔌 电源控制: 关闭 -> 开启")

    global power_camera, power_q68

    # 初始化电源设备
    try:
        power_camera = IT6322B(usb_addr="USB0::0x2EC7::0x6300::800068020757210071::INSTR")
        power_q68 = Keithley2230(usb_addr="USB0::0x05E6::0x2230::9211112::INSTR")
        print("✅ 电源设备初始化成功")
    except Exception as e:
        print(f"⚠️ 电源设备初始化失败: {e}")
        raise

    power_q68.TurnOutputsOff()
    power_camera.TurnOutputsOff()
    time.sleep(2)

    power_q68.SetCurrents(i1=1.2, i2=0.1, i3=0.2)
    power_q68.SetVoltages(VOLTAGE[0], VOLTAGE[1], VOLTAGE[2])
    power_q68.TurnOutputsOn()
    power_camera.TurnOutputsOn()
    time.sleep(3)

def setup_q68_s68_communication(active_links=None):
    """设置Q68和S68之间的通信 (带I2C错误检测)

    Args:
        active_links: 要激活的link列表，如[0, 1]。如果为None，使用全局KLINK配置
    """
    if active_links is None:
        active_links = KLINK

    print(f"\n🔗 设置Q68和S68通信 (激活Links: {active_links})...")

    # 重置错误计数器
    i2c_error_detector.reset_error_count()

    def _setup_communication():
        # 设置Q68链路速率 - 始终配置所有4个link
        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])
        q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=BCRATE[0], bc_rate1=BCRATE[1], bc_rate2=BCRATE[2], bc_rate3=BCRATE[3])

        # 设置链路参数和编码方式 - 始终初始化所有4个link
        q68.Q68_C3_6G_Init(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])

        # 设置最终链路速率 - 始终设置所有4个link
        q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])
        q68.Q68_C3_6G_Init(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])

        # 检查所有链路状态
        link0_status = q68.c2m.rd_test_fsm_status1_link0()
        link1_status = q68.c2m.rd_test_fsm_status1_link1()
        link2_status = q68.c2m.rd_test_fsm_status2_link2()
        link3_status = q68.c2m.rd_test_fsm_status2_link3()

        return {
            'link0': link0_status,
            'link1': link1_status,
            'link2': link2_status,
            'link3': link3_status
        }

    # 使用I2C错误检测执行通信设置
    try:
        result = retry_on_i2c_error(_setup_communication)

        print('🔗 所有链路状态:', result['link0'], result['link1'], result['link2'], result['link3'])
        print(f"✅ Q68和S68通信设置完成 (激活Links: {active_links})")

        # 显示错误统计
        error_summary = i2c_error_detector.get_error_summary()
        if error_summary['error_count'] > 0:
            print(f"⚠️ 通信过程中检测到 {error_summary['error_count']} 次I2C错误，已自动重试解决")

        print()
        return result

    except Exception as e:
        print(f"❌ Q68和S68通信设置失败: {e}")
        error_summary = i2c_error_detector.get_error_summary()
        print(f"📊 I2C错误统计: {error_summary}")
        raise

def configure_specific_links(target_links):
    """为特定的测试用例配置指定的Links

    Args:
        target_links: 要配置的link列表，如[0], [1], [0,1,2,3]等

    Returns:
        dict: 各link的状态
    """
    print(f"\n🔧 配置特定Links: {target_links}")

    # 验证link范围
    valid_links = [link for link in target_links if 0 <= link <= 3]
    if len(valid_links) != len(target_links):
        invalid_links = [link for link in target_links if link not in valid_links]
        print(f"⚠️ 警告: 无效的link {invalid_links}，将被忽略")

    if not valid_links:
        raise ValueError("没有有效的link可配置")

    # 检查配置后的状态
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    status_result = {}
    for link in valid_links:
        status = link_status_funcs[link]()
        status_result[f'link{link}'] = status
        print(f"  - Link{link} 状态: {status}")

    print(f"✅ 特定Links {valid_links} 配置完成\n")
    return status_result

def get_link_status(link_id=None):
    """获取指定link或所有link的状态

    Args:
        link_id: 指定的link ID (0-3)，如果为None则返回所有link状态

    Returns:
        int or dict: 单个link状态或所有link状态字典
    """
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]

    if link_id is not None:
        if 0 <= link_id <= 3:
            return link_status_funcs[link_id]()
        else:
            raise ValueError(f"无效的link_id: {link_id}，应该在0-3范围内")

    # 返回所有link状态
    return {
        'link0': link_status_funcs[0](),
        'link1': link_status_funcs[1](),
        'link2': link_status_funcs[2](),
        'link3': link_status_funcs[3](),
    }

# ------------------------------------------------------------------
#  GPIO操作的I2C错误检测包装函数
# ------------------------------------------------------------------
def safe_gpio_remote_tx(q68_remote, gpio, tx_id, s68_dev_addr=None):
    """安全的GPIO远程发送配置 (带I2C错误检测)

    Args:
        q68_remote: Q68远程对象
        gpio: GPIO编号
        tx_id: 发送ID
        s68_dev_addr: S68设备地址 (可选)

    Returns:
        bool: 操作是否成功
    """
    def _gpio_tx_operation():
        if s68_dev_addr is not None:
            original_addr = q68_remote.dongle.devAddr
            q68_remote.dongle.devAddr = s68_dev_addr

        try:
            # 设置GPIO为发送模式
            q68_remote.M2CMFNSet(gpio=gpio, mfn=0)
            q68_remote.GPIORemoteTx(gpio=gpio, tx_id=tx_id)
            return True
        finally:
            if s68_dev_addr is not None:
                q68_remote.dongle.devAddr = original_addr

    try:
        result = retry_on_i2c_error(_gpio_tx_operation)
        print(f"✅ GPIO{gpio} 远程发送配置成功 (tx_id={tx_id})")
        return result
    except Exception as e:
        print(f"❌ GPIO{gpio} 远程发送配置失败: {e}")
        return False

def safe_gpio_remote_rx(q68_remote, gpio, rx_id, s68_dev_addr=None):
    """安全的GPIO远程接收配置 (带I2C错误检测)

    Args:
        q68_remote: Q68远程对象
        gpio: GPIO编号
        rx_id: 接收ID
        s68_dev_addr: S68设备地址 (可选)

    Returns:
        bool: 操作是否成功
    """
    def _gpio_rx_operation():
        if s68_dev_addr is not None:
            original_addr = q68_remote.dongle.devAddr
            q68_remote.dongle.devAddr = s68_dev_addr

        try:
            # 设置GPIO为接收模式
            q68_remote.M2CMFNSet(gpio=gpio, mfn=0)
            q68_remote.M2CGPIORemoteRx(gpio=gpio, rx_id=rx_id)
            return True
        finally:
            if s68_dev_addr is not None:
                q68_remote.dongle.devAddr = original_addr

    try:
        result = retry_on_i2c_error(_gpio_rx_operation)
        print(f"✅ GPIO{gpio} 远程接收配置成功 (rx_id={rx_id})")
        return result
    except Exception as e:
        print(f"❌ GPIO{gpio} 远程接收配置失败: {e}")
        return False

def safe_link_status_check():
    """安全的链路状态检查 (带I2C错误检测)

    Returns:
        dict: 链路状态字典
    """
    def _check_status():
        return {
            'link0': q68.c2m.rd_test_fsm_status1_link0(),
            'link1': q68.c2m.rd_test_fsm_status1_link1(),
            'link2': q68.c2m.rd_test_fsm_status2_link2(),
            'link3': q68.c2m.rd_test_fsm_status2_link3(),
        }

    try:
        result = retry_on_i2c_error(_check_status)
        return result
    except Exception as e:
        print(f"❌ 链路状态检查失败: {e}")
        return {'link0': -1, 'link1': -1, 'link2': -1, 'link3': -1}

# ------------------------------------------------------------------
#  Session-scope hardware fixture (yield-style)
# ------------------------------------------------------------------
@pytest.fixture(scope="session")
def hardware_setup():
    """基础硬件环境设置 - 只包含共性配置

    包含:
    1. 电源控制
    2. 基础设备对象初始化
    3. 基础通信设置
    """
    print("\n" + "="*60)
    print("🚀 基础硬件环境设置")
    print("="*60)
    # 电源控制
    power_on()

    # 初始化设备对象
    init_global_objects()

    # 基础Q68-S68通信设置 (使用默认KLINK配置)
    setup_q68_s68_communication()

    # 返回设备对象字典和配置函数
    devices = {
        'q68': q68,
        'q68_remote': q68_remote,
        'q68_i2c2': q68_i2c2,
        'q68_I2C2remote': q68_I2C2remote,
        's68_res_dev': s68_res_dev,
        'oscilloscope': oscilloscope,  # 添加示波器对象
        # 添加配置函数，让测试用例可以灵活配置
        'configure_links': configure_specific_links,
        'get_link_status': get_link_status,
        'setup_communication': setup_q68_s68_communication,
        # 添加电源控制函数，支持完整的系统重新初始化
        'power_off': power_off,
        'power_on': power_on,
        # 添加I2C错误检测相关函数
        'i2c_error_detector': i2c_error_detector,
        'safe_gpio_remote_tx': safe_gpio_remote_tx,
        'safe_gpio_remote_rx': safe_gpio_remote_rx,
        'safe_link_status_check': safe_link_status_check,
        'retry_on_i2c_error': retry_on_i2c_error,
        'execute_with_i2c_error_detection': execute_with_i2c_error_detection
    }

    yield devices

    print("\n🔧 基础硬件环境清理完成")


# ------------------------------------------------------------------
#  简化的设备访问fixtures
# ------------------------------------------------------------------
@pytest.fixture(scope="session")
def devices(hardware_setup):
    """返回所有设备对象的字典"""
    return hardware_setup