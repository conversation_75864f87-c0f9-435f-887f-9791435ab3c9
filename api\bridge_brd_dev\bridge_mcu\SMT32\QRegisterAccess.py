import csv
import time
from USBInterface import USBImpl
import atexit

isInit:bool  = False
save_data =  False # False
data_addr = "./save_data.csv"

# 全局变量，记录当前模块对应的设备和总线状态
g_snSerial  = None
g_deviceIndex  = 0
g_bus:int  = 0
g_devAddr:int  = 0x69

# 模块在退出的时候，清理连接接口的状态
def on_exit():
    global isInit
    print("QRegisterAccess on_exit index -", g_deviceIndex)
    if isInit == True:
        print("QRegisterAccess close serial")
        USBImpl.usbdev_close()
        isInit = False

def on_load(deviceIndex):
    global isInit
    try:
        devlist = USBImpl.usbdev_scan()
        print("探测到 USBCOMM 设备列表:")
        for i in range(0, len(devlist)):
            print("    ", devlist[i])
    
        # 使用第一个设备
        USBImpl.usbdev_open(devlist[deviceIndex].name)
        ret,sndata = USBImpl.usbdev_get_sn()
        print("return=", ret, " get serial number result: ", ''.join(format(byte, '02x') for byte in sndata))
    except Exception as e:
        print("QRegisterAccess", e)
        # 捕获所有其他类型的异常
        isInit = False
    else:
        # 如果没有发生异常，则执行该代码块
        isInit = True
    finally:
        print("USBImpl init end")

    # 如果使能了保存数据的功能，程序运行时，应处理文件的创建
    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            name = ['DeviceAddr','Reg_Addr', 'R_or_W', 'Write_or_Read_Value']
            csv_writer.writerow(name)

# 注册程序退出时，执行的模块清理函数
atexit.register(on_exit)

# 作为模块加载到程序中时，自动打开连接接口连接
if __name__ == "SMT32.QRegisterAccess":
    print("QRegisterAccess on_load index -", g_deviceIndex)
    on_load(g_deviceIndex)


# 设置设备信息
def selectDevice(snSerial=None, deviceIndex=None):
    global g_snSerial
    global g_deviceIndex

    if snSerial != None and g_snSerial != snSerial:
        g_snSerial = snSerial

    if deviceIndex != None and g_deviceIndex != deviceIndex:
        print("QRegisterAccess index -", g_deviceIndex, " change to index -", deviceIndex)
        g_deviceIndex = deviceIndex
        # 处理启动的端口状态(如果打开就关闭，如果失败就不管)，重新打开指定的端口
        on_exit()
        on_load(g_deviceIndex)


# 设置设备信息
def selectI2CDevice(bus=None, devAddr=None):
    global g_bus
    global g_devAddr

    if bus != None:
        g_bus = bus

    if devAddr != None:
        g_devAddr = devAddr
    
    print('Using MCU I2C',g_bus, ',and Slave address set to', hex(g_devAddr))

def iicAddInit(devaddr):
    global g_devAddr
    g_devAddr = devaddr

def findGMSLSpiChan():
    # init spi master
    return True

def findMeritechSpiChan():
    # init spi master
    return True

def findMeritechTDC65nmSingleSpiChan():
    '''
    mode Attributes:
        SPI_IO_NONE: No IOs
        SPI_IO_SINGLE: Single mode
        SPI_IO_DUAL: Dual mode
        SPI_IO_QUAD: Quad mode
    clock Attributes:
        NONE:
        DIV_2: 1/2 System Clock
        DIV_4: 1/4 System Clock
        DIV_8: 1/8 System Clock
        DIV_16: 1/16 System Clock
        DIV_32: 1/32 System Clock
        DIV_64: 1/64 System Clock
        DIV_128: 1/128 System Clock
        DIV_256: 1/256 System Clock
        DIV_512: 1/512 System Clock
    slave select Attributes:
        SS0: Slave select 0
        SS1: Slave select 1
        SS2: Slave select 2
        SS3: Slave select 3
    Cpol Attributes:
        IDLE_LOW: Idle low
        IDLE_HIGH: Idle high
        IDLE_LOW  = 0
        IDLE_HIGH = 1
    Cpha Attributes:
        CLK_LEADING: Leading phase
        CLK_TRAILING: Trailing phase
        CLK_LEADING  = 0
        CLK_TRAILING = 1
    '''
    # init spi master
    return True

# 兼容FT4XXX接口
def findMeritechTDC65nmQSpiChan():
    ''''
    This overrides the mode passed to FT4222_SPIMaster_init. 
    This might be needed if a device accepts commands in single mode but data transfer is to use dual or quad mode.'''
    return True

def findMeritechDACSpiChan():
    # init spi master

    '''
        NONE:
        DIV_2: 1/2 System Clock
        DIV_4: 1/4 System Clock
        DIV_8: 1/8 System Clock
        DIV_16: 1/16 System Clock
        DIV_32: 1/32 System Clock
        DIV_64: 1/64 System Clock
        DIV_128: 1/128 System Clock
        DIV_256: 1/256 System Clock
        DIV_512: 1/512 System Clock
        
        Cpol: IDLE_LOW  = 0, IDLE_HIGH = 1
        Cpha: CLK_LEADING: Leading phase, CLK_TRAILING: Trailing phase; CLK_LEADING  = 0, CLK_TRAILING = 1
    '''
    
    return True

def findMeritechADCSpiChan():
    # init spi master
    return True

def findMeritechI2cChan():
    # init i2c master
    # TODO:查找mt的i2c
    return True

def findMeritechI2CSlave(): 
    return True

def findMeritechSPISlave(): 
    return True


def initGpio23(dir2, dir3):
    pass

def setGpio(index, value):
    v = 0 if value == 0 else 1
    USBImpl.usbdev_set_gpio(index, v)

def getGpio(index):
    v = 0
    if isInit:
        v = USBImpl.usbdev_get_gpio(index)
    return v

def spiRead1860Reg(addr):
    value = 0
    return value

def spiWrite1860Reg(addr, value):
    return True

def BitReverse(bitwidth, value):
    addrbin=bin(value)
    addrlsbf=0
    addrbin=addrbin[2:]
    #print (addrbin)
    #      lenabs=len(addrbin)%8
    lenabs=len(addrbin)
   
    #print (lenabs)
    if lenabs == 0:
        pass
    else:
        for i in range(bitwidth-lenabs):
            addrbin='0'+addrbin
    #print (addrbin)
    for i in range(0,len(addrbin),1):
        addrlsbf+=int(addrbin[len(addrbin)-1-i])*(2**(len(addrbin)-1-i))
    return addrlsbf


''' the following module for TDC SPI w/r......................'''
def singleSpiReadTDCReg(addr):
    value = 0x00
    return value

def singleSpiWriteTDCReg(addr, value):
    return True

def QSpiReadTDCReg(addr):

    '''
    spiMaster_MultiReadWrite(singleWrite, multiWrite, bytesToRead)
        Write and read data to and from a SPI slave in dual- or quad-mode (multi-mode).
        
        Parameters:
        singleWrite (bytes, bytearray, int) �C Data to write to slave in signle-line mode (max. 15 bytes)
        
        multiWrite (bytes, bytearray, int) �C Data to write to slave in multi-line mode (max. 65535 bytes)
        
        bytesToRead (int) �C Number of bytes to read on multi-line (max. 65535 bytes)
        
        Returns:
        Bytes read from slave in multi-line mode
        
        Return type:
        bytes
        
        Raises:
        FT4222DeviceError �C on error
    '''
    Instruction=0x40
    value=0x00
    return value

'''.................end......................'''

def spiReadADCReg(addr):
    value = 0x00
    return value

def spiWriteADCReg(addr, value):
    return True

def spiReadDACReg(addr):
    value = 0x00
    return value

def spiWriteDACReg(addr, value):
    return True

def i2cRead96717Reg(addr):
    value = 0
    mode = 1
    if isInit:
        ret, value = USBImpl.usbdev_i2c_get(mode, g_bus, 0x35, addr)
    else:
        print("USB Impl Not Open")

    return value

def i2cWrite96717Reg(addr, value):
    mode = 1
    if isInit:
        ret = USBImpl.usbdev_i2c_set(mode, g_bus, 0x35, addr, value)
    else:
        print("USB Impl Not Open")

    return True

def i2cReadC3TxTestReg(regAddr):
    value = 0x00
    mode = 0
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, g_devAddr, regAddr, value)
    else:
        print("USB Impl Not Open")
        
    return value

def i2cWriteC3TxTestReg(regAddr, value):
    mode = 0
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, g_devAddr, regAddr, value)
    else:
        print("USB Impl Not Open")
        
    return True

def MAX5395I2CWrite(devAddr5, regAddr, value):
    mode = 0
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, g_devAddr, regAddr, value)
    else:
        print("USB Impl Not Open")
    return True

def MAX5395I2CRead(devAddr5, regAddr):
    value = 0x00
    mode = 0
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, devAddr5, regAddr, value)
    else:
        print("USB Impl Not Open")
    return value

#...............this module to w/r register from C3 tested chip with FT4222&py37................start.........................

def i2cReadC3TxTestReg_ft4222py37(devi_addr,regAddr):
    value = 0
    return value

def i2cWriteC3TxTestReg_ft4222py37(devi_addr,regAddr, value):
    return True


def M66S68_Read_Register(devi_addr,regi_addr):
    ''' 
    descriptions: this function to read register value from C3 tested chip with FT4222&py3.7
    '''
    mode = 1
    value = 0
    if isInit:
        ret, value = USBImpl.usbdev_i2c_get(mode, g_bus, devi_addr, regi_addr)
    else:
        print("USB Impl Not Open")

    return value

def M66S68_Write_Register(devi_addr, regi_addr,value):
    ''' 
    descriptions: this function to write register value from C3 tested chip with FT4222&py3.7
    '''
    mode = 1
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, devi_addr, regi_addr, value)
    else:
        print("USB Impl Not Open")
    return True

def M66S68_Write_Bitfield(devi_addr,regi_addr, bitnum, startbit,value):
    ''' 
    descriptions: this function to read register filed value from C3 tested chip with FT4222&py3.7
    '''
    bitsToSave = 2 ** 8 - 2 ** (startbit + bitnum) + 2 ** startbit - 1
    reg_data = M66S68_Read_Register(devi_addr,regi_addr) & bitsToSave
    data1 = reg_data + (value << startbit)
    M66S68_Write_Register(devi_addr,regi_addr,data1)

def M66S68_Read_Bitfield(devi_addr,regi_addr, bitnum, startbit,):
    ''' 
    descriptions: this function to write register filed value from C3 tested chip with FT4222&py3.7
    '''
    bitsToGet = 0xff-(2**8-2**(startbit+bitnum)+2**startbit-1) 
        
    value = (M66S68_Read_Register(devi_addr,regi_addr) & bitsToGet) >> startbit
    
    return value
    
#...............this module to w/r register from C3 tested chip with FT4222&py37................end.........................
    

def M66S68I2CWrite_normal(regAddr, value):
    mode = 1
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, g_devAddr, regAddr, value)
    else:
        print("USB Impl Not Open")

    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(g_devAddr), hex(regAddr), 'W', hex(value)]
            csv_writer.writerow(row) 
    return True

def M66S68I2CRead_normal(regAddr):
    mode = 1
    value = 0
    if isInit:
        ret, value = USBImpl.usbdev_i2c_get(mode, g_bus, g_devAddr, regAddr)
    else:
        print("USB Impl Not Open")

    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(g_devAddr), hex(regAddr), 'R', hex(value)]
            csv_writer.writerow(row) 
    return value

def M66S68I2CWrite_CRC(regAddr, value):
    mode = 1
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, g_devAddr, regAddr, value)
    else:
        print("USB Impl Not Open")
    return True

def M66S68I2CRead_CRC(regAddr):
    mode = 1
    value = 0
    if isInit:
        ret, value = USBImpl.usbdev_i2c_get(mode, g_bus, g_devAddr, regAddr)
    else:
        print("USB Impl Not Open")
    return value

def M65Q68I2CWrite_normal(regAddr, value):
    mode = 1
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, g_devAddr, regAddr, value)
    else:
        print("USB Impl Not Open")

    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(g_devAddr), hex(regAddr), 'W', hex(value)]
            csv_writer.writerow(row) 
    return True

def M65Q68I2CRead_normal(regAddr):
    mode = 1
    value = 0
    if isInit:
        ret, value = USBImpl.usbdev_i2c_get(mode, g_bus, g_devAddr, regAddr)
    else:
        print("USB Impl Not Open")

    if save_data:
        with open(data_addr, "a", encoding="utf-8", newline="") as f:
            csv_writer = csv.writer(f)
            row = [hex(g_devAddr), hex(regAddr), 'R', hex(value)]
            csv_writer.writerow(row)
    return value

def M65Q68I2CWrite_CRC(regAddr, value):
    mode = 1
    if isInit:
        USBImpl.usbdev_i2c_set(mode, g_bus, g_devAddr, regAddr, value)
    else:
        print("USB Impl Not Open")

    return True

def M65Q68I2CRead_CRC(regAddr):
    mode = 1
    value = 0
    if isInit:
        ret, value = USBImpl.usbdev_i2c_get(mode, g_bus, g_devAddr, regAddr)
    else:
        print("USB Impl Not Open")

    return value

def spiGMSLInBandControl(value):
    pass
    
def spiGMSLReadLocal():
    value = 0x00
    return value

def spiSendReadCmdToMcp2515(addr):
    MCP2515_READ_CMD  = 0x03
    
def spiSendWriteCmdToMcp2515(addr, value):
    MCP2515_WRITE_CMD = 0x02
    
def spiReadMcp2515Reg(addr):
    MCP2515_READ_CMD  = 0x03
    value = 0
    return value

def spiWriteMcp2515Reg(addr, value):
    MCP2515_WRITE_CMD = 0x02
    return True

def spiReadC3(addr, size):
    value = 0x00
    return value

def i2cReadAxiMasterReg(addr):
    value = 0
    return value

def i2cWriteAxiMasterReg(addr, value):
    return True

def axiI2cMasterEnable():
    i2cWriteAxiMasterReg(0x0100, 0x02) # CR, reset tx fifo
    i2cWriteAxiMasterReg(0x0100, 0x01) # CR, enable axi-iic
    return

def axiI2cMasterWrite(devAddraxi, regAddr, regValue):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:            
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddraxi << 1)) # tx fifo, start + dev addr
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x200 | regValue)       # tx fifo, reg value + stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    return True

def axiI2cMasterRead(devAddraxi, regAddr):
    while i2cReadAxiMasterReg(0x0104) & 0xc4 != 0xc0:    # tx fifo emtpy, rx fifo emtpy, not busy:
        continue
    i2cWriteAxiMasterReg(0x0108, 0x100 | (devAddraxi << 1)) # tx fifo, start + dev addr (w)
    i2cWriteAxiMasterReg(0x0108, regAddr)                # tx fifo, reg addr
    i2cWriteAxiMasterReg(0x0108, 0x101 | (devAddraxi << 1)) # tx fifo, start + dev addr (r)
    i2cWriteAxiMasterReg(0x0108, 0x200)                  # tx fifo, stop
    while i2cReadAxiMasterReg(0x0104) & 0x80 != 0x80:    # SR, tx fifo emtpy
        continue
    value = i2cReadAxiMasterReg( 0x010c) # rx fifo
    return value

def I2CSlaveAddressSet(addr,):
    return True

def I2CSlaveAddressGet():
    value = 0x00
    return value
       
def I2CSalveWrite(data,size): # write i2c slave , it will cache the data and wait for i2c master access
    return True

def SPISalveWrite(addr,value): # write spi slave , it will cache the data and wait for i2c master access
    return True

def SPISlaveRead(): # read spi slave, it will get data from spi master
    value = 0x00
    return value
    
def readReg(moduleName, addr):
    # devAddr=0x69
    CRC=False
    if moduleName=='c2m':
        if CRC==False:
            value = M65Q68I2CRead_normal(addr)
        else:
            value = M65Q68I2CRead_CRC(addr)
    elif moduleName=='m2c':
        if CRC==False:
            value = M66S68I2CRead_normal(addr)
        else:
            value = M66S68I2CRead_CRC(addr)   
    
    # if save_data:
    #     with open(data_addr, "a", encoding="utf-8", newline="") as f:
    #         csv_writer = csv.writer(f)
    #         row = [hex(devAddr), hex(addr), 'R', hex(value)]
    #         csv_writer.writerow(row) 
    
    return value

def writeReg(moduleName, addr, value):
    
    CRC=False
    status=False
    if moduleName=='c2m':
        
        if CRC==False:
            M65Q68I2CWrite_normal(addr, value) 
            status=True
        else:
            M65Q68I2CWrite_CRC(addr, value)
            status=True
    elif moduleName=='m2c':
        if CRC==False:
            M66S68I2CWrite_normal(addr, value) 
            status=True
        else:
            M66S68I2CWrite_CRC(addr, value)
            status=True
    # if save_data:
    #     with open(data_addr, "a", encoding="utf-8", newline="") as f:
    #         csv_writer = csv.writer(f)
    #         row = [hex(devAddr), hex(addr), 'W', hex(value)]
    #         csv_writer.writerow(row) 
    return status

def readSingleData(pollingName):
    data  = []
    return data

def readBulkData(pollingName, dataLen):
    data  = []
    return data
