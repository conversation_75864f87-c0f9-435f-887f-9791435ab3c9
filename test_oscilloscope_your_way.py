#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按照用户方式测试示波器Save_Image功能
完全模仿test_gpio_case6_8_s2d_auto2.py中的示波器使用方式
"""

import os
import sys
import time

# 按照用户的方式添加路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # 示波器模块路径

def test_oscilloscope_save_image_your_way():
    """按照用户的方式测试示波器Save_Image"""
    
    print("🔬 按照用户方式测试示波器Save_Image")
    print("="*60)
    
    # 按照用户的方式初始化示波器
    try:
        print("  📊 尝试初始化示波器...")
        from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
        oscilloscope = SiglentSDS5034X()
        print("    ✅ 示波器初始化完成")
    except Exception as e:
        print(f"    ⚠️ 示波器初始化失败: {e}")
        oscilloscope = None
        return False
    
    # 模拟用户的配置
    test_config = {
        'screenshot_folder_base': 'U-disk0/gpiotest',  # 用户当前的配置
        'active_links': [0, 2],
    }
    
    # 测试参数
    s68_gpio = 8
    active_links = test_config['active_links']
    
    print(f"\n📋 测试配置:")
    print(f"   S68 GPIO: {s68_gpio}")
    print(f"   活跃Links: {active_links}")
    print(f"   基础路径: {test_config['screenshot_folder_base']}")
    
    # 测试不同的路径格式，从简单到复杂
    test_cases = [
        {
            'name': '最简单路径',
            'path': 'U-disk0/test_simple.png'
        },
        {
            'name': '一级目录',
            'path': 'U-disk0/gpiotest/test_level1.png'
        },
        {
            'name': '二级目录',
            'path': 'U-disk0/gpiotest/s68tod68_0_2/test_level2.png'
        },
        {
            'name': '完整路径 - 简化文件名',
            'path': 'U-disk0/gpiotest/s68tod68_0_2/link0_gpio8/test_simple_name.png'
        },
        {
            'name': '完整路径 - 原始文件名格式',
            'path': f'U-disk0/gpiotest/s68tod68_0_2/link0_gpio8/FIXED_01_GPIO{s68_gpio}_LINK{active_links}_30Hz_20ms_test.png'
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}/{total_count}: {test_case['name']}")
        print(f"   路径: {test_case['path']}")
        
        # 创建文件夹
        folder = os.path.dirname(test_case['path'])
        if folder:
            try:
                os.makedirs(folder, exist_ok=True)
                print(f"   📁 文件夹创建: ✅")
            except Exception as e:
                print(f"   📁 文件夹创建: ❌ {e}")
                continue
        
        # 按照用户的方式调用Save_Image
        try:
            print(f"   📸 调用Save_Image...")
            
            # 完全按照conftest.py中的方式调用
            result = oscilloscope.Save_Image(
                filepath=test_case['path'],
                image_format="PNG",
                invert="OFF",      # 正常色彩
                menu="MOF"         # 隐藏菜单
            )
            
            print(f"   📋 Save_Image返回: {result}")
            
            # 等待保存完成
            print(f"   ⏳ 等待保存完成...")
            time.sleep(2)
            
            # 检查文件是否存在
            if os.path.exists(test_case['path']):
                file_size = os.path.getsize(test_case['path'])
                print(f"   ✅ 保存成功! 文件大小: {file_size} 字节")
                success_count += 1
                
                if file_size == 0:
                    print(f"   ⚠️ 警告: 文件大小为0")
            else:
                print(f"   ❌ 文件未找到")
                
        except Exception as e:
            print(f"   ❌ Save_Image失败: {e}")
            
            # 详细分析错误
            error_str = str(e)
            print(f"   🔍 错误分析:")
            
            if "存储空间不足" in error_str or "space" in error_str.lower():
                print(f"      - 确认是存储空间不足问题")
                print(f"      - 可能原因: U盘空间真的不足")
            elif "路径" in error_str or "path" in error_str.lower():
                print(f"      - 路径格式问题")
                print(f"      - 可能原因: 路径格式不被示波器支持")
            elif "文件名" in error_str or "filename" in error_str.lower():
                print(f"      - 文件名问题")
                print(f"      - 可能原因: 文件名包含不支持的字符")
            else:
                print(f"      - 其他问题: {error_str}")
    
    # 测试总结
    print(f"\n📊 测试总结:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   失败: {total_count - success_count}/{total_count}")
    
    if success_count > 0:
        print(f"   💡 建议: 使用成功的路径格式")
    else:
        print(f"   💡 建议: 检查示波器U盘空间和连接")
    
    return success_count > 0

def test_conftest_integration():
    """测试conftest.py中的通用方法"""
    
    print(f"\n🔧 测试conftest.py通用方法")
    print("="*60)
    
    try:
        # 导入conftest中的通用方法
        sys.path.append('test')
        from conftest import get_universal_oscilloscope_screenshot
        
        # 初始化示波器
        from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X
        oscilloscope = SiglentSDS5034X()
        
        # 创建通用截图工具
        screenshot_tool = get_universal_oscilloscope_screenshot(oscilloscope)
        
        # 测试配置
        test_config = {
            'screenshot_folder_base': 'U-disk0/gpiotest',
            'enable_screenshot': True,
            'trigger_source': 'D1',
            'waveform_type': 'SQUARE',
            'frequency_list': [30, 1000],  # 只测试2个频率
            'timebase_list': ['20ms', '1ms'],
            'amplitude': 1.8,
            'offset': 0.9,
            'persistence_mode': 'INFinite',
            'freq_observation_time': 1,
        }
        
        print(f"📋 测试通用方法:")
        print(f"   GPIO: 8")
        print(f"   Links: [0]")
        print(f"   模式: fixed")
        
        # 调用通用方法
        result = screenshot_tool.execute_screenshot(
            gpio_num=8,
            active_links=[0],  # 只测试一个Link
            test_mode='fixed',  # 只测试固定频率
            osc_config=test_config
        )
        
        if result:
            print(f"   ✅ 通用方法测试成功")
        else:
            print(f"   ❌ 通用方法测试失败")
            
        return result
        
    except Exception as e:
        print(f"   ❌ 通用方法测试异常: {e}")
        return False

def analyze_results():
    """分析测试结果"""
    
    print(f"\n🔍 结果分析")
    print("="*60)
    
    print(f"如果所有测试都失败:")
    print(f"   1. 检查示波器U盘是否插入")
    print(f"   2. 检查U盘剩余空间")
    print(f"   3. 检查示波器网络连接")
    print(f"   4. 尝试手动在示波器上保存截图")
    
    print(f"\n如果简单路径成功，复杂路径失败:")
    print(f"   1. 使用更简单的文件夹结构")
    print(f"   2. 简化文件名格式")
    print(f"   3. 避免特殊字符 [, ], :, 等")
    
    print(f"\n如果部分成功:")
    print(f"   1. 找出成功的路径模式")
    print(f"   2. 修改conftest.py使用成功的格式")
    print(f"   3. 更新文件名生成逻辑")

if __name__ == "__main__":
    print("🎯 按照用户方式测试示波器Save_Image功能")
    print("📝 目标: 找出存储空间不足的真正原因")
    print("")
    
    # 运行基础测试
    basic_success = test_oscilloscope_save_image_your_way()
    
    if basic_success:
        print(f"\n✅ 基础测试有成功案例，继续测试通用方法...")
        # 运行通用方法测试
        universal_success = test_conftest_integration()
    else:
        print(f"\n❌ 基础测试全部失败，跳过通用方法测试")
    
    # 分析结果
    analyze_results()
    
    print(f"\n🎉 测试完成!")
    print(f"📝 请根据成功的测试案例调整路径格式")
