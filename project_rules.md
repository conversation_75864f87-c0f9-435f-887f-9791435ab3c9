# 項目開發與測試規範

## 1. 概述

本文檔旨在為基於 Python 和 Pytest 的 GPIO 芯片測試框架提供一套統一的開發與測試規範。所有項目成員應嚴格遵守本規範，以確保代碼質量、可讀性、可維護性和團隊協作效率。

## 2. 目錄結構

項目採用分層的目錄結構，確保代碼職責分離，清晰明了。

```
/
├── api/                  # 存放與硬體設備交互的底層 API 封裝
├── top/                  # 頂層業務邏輯和測試用例的組合與封裝
├── test/                 # Pytest 測試文件目錄
│   ├── conftest.py       # Pytest 的主要配置文件，存放全局 Fixture
│   └── test_*.py        # 測試用例文件
├── mytestforupdata/      # 用於開發和調試過程中的臨時測試目錄（不應提交到主分支）
├── test_reports/         # 存放測試報告的目錄
├── .gitignore            # Git 忽略文件配置
└── README.md             # 項目說明文檔
```

- **`api/`**: 核心硬體接口層。此目錄下的代碼負責直接與硬體通信，如通過 `ftdi`、`pyserial` 等庫操作 GPIO、I2C、SPI 等。此層應封裝複雜的硬體協議，向上提供簡潔的函數接口。
- **`top/`**: 業務邏輯層。此目錄的代碼調用 `api/` 層提供的接口，組合實現具體的業務功能或複雜的測試場景。例如，一個完整的"點亮 LED"測試可能需要調用多個 GPIO API，這些組合邏輯應在 `top/` 中實現。
- **`test/`**: 測試用例層。此目錄存放所有 `pytest` 測試腳本。
    - 測試文件名必須以 `test_` 開頭。
    - 測試函數名必須以 `test_` 開頭。
    - `conftest.py` 用於定義全局的、可共享的 `fixture`，例如硬體初始化和清理操作。
- **`mytestforupdata/`**: 開發調試目錄。開發人員可以在此目錄下創建臨時腳本進行功能驗證和調試，但此目錄的內容不應合併到 `main` 或 `develop` 等主要分支。

## 3. Python 編碼規範

遵循 **PEP 8** 風格指南。推薦使用 `black` 和 `isort` 工具自動格式化代碼。

- **命名規範**:
    - 模塊名：全小寫，如 `gpio_control.py`。
    - 包名：全小寫，如 `api`。
    - 類名：駝峰式命名（PascalCase），如 `GpioDevice`。
    - 函數與變量名：蛇形命名（snake_case），如 `set_gpio_direction`。
    - 常量名：全大寫，下劃線分隔，如 `PIN_NUMBER`。
- **文檔字符串 (Docstrings)**:
    - 每個模塊、類、函數都必須包含 Docstring。
    - 推薦使用 Google 風格的 Docstring。
    - Docstring 應清晰說明其功能、參數、返回值和可能引發的異常。
- **類型提示 (Type Hinting)**:
    - 所有函數的參數和返回值都必須添加類型提示。
    - 這有助於靜態分析工具發現潛在錯誤，並提高代碼可讀性。
- **日誌**:
    - 使用 `logging` 模塊記錄日誌，而不是 `print()`。
    - 根據場景選擇合適的日誌級別（DEBUG, INFO, WARNING, ERROR, CRITICAL）。

## 4. Pytest 測試框架規範

- **Fixture**:
    - **核心原則**: 使用 Fixture 管理測試的依賴和狀態（例如硬體資源）。**嚴禁在測試用例模塊的全局範圍內實例化硬體對象**。
    - **作用域 (Scope)**: 根據資源的生命週期合理選擇 Fixture 的作用域 (`function`, `class`, `module`, `session`)。對於硬體初始化等昂貴操作，應使用 `session` 作用域，確保在整個測試會話中只執行一次。
    - **命名**: Fixture 名稱應清晰地描述其提供的資源或狀態，如 `gpio_device`。
    - **位置**: 全局共享的 Fixture 應定義在 `test/conftest.py` 中。
- **斷言 (Assertions)**:
    - 使用 `assert` 關鍵字進行斷言，充分利用 Pytest 的詳細報錯信息。
    - 斷言表達式應清晰直觀。例如，使用 `assert result is True` 而不是 `assert result`。
- **標記 (Markers)**:
    - 對測試進行分類，例如 `@pytest.mark.smoke`（煙霧測試）、`@pytest.mark.regression`（回歸測試）。
    - 使用 `@pytest.mark.skip` 跳過暫時無法運行的測試。
    - 使用 `@pytest.mark.parametrize` 進行參數化測試，以相同的邏輯測試多組數據。
- **測試結構**:
    - 遵循 **Arrange-Act-Assert (3A)** 模式編寫測試函數。
        1.  **Arrange**: 準備測試環境，初始化對象，設置測試數據（通常由 Fixture 完成）。
        2.  **Act**: 執行被測代碼。
        3.  **Assert**: 驗證結果是否符合預期。

## 5. 版本控制 (Git)

- **分支策略**:
    - `main`: 主分支，存放穩定、可發布的代碼。
    - `develop`: 開發分支，集成分支，用於合併新功能。
    - `feature/xxx`: 功能分支，從 `develop` 分支出來，用於開發新功能。
- **提交信息**:
    - 每次提交都應有清晰的提交信息，描述本次提交的內容。
    - 格式建議：`<類型>: <主題>`，例如 `feat: 添加 GPIO 上拉電阻配置功能` 或 `fix: 修復 I2C 通信超時問題`。

---
*此文檔為項目的核心規則，所有成員必須遵守。* 