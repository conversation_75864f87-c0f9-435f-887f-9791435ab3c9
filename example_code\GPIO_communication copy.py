'''
Revision:
    1. update to A0
    2. puts log into 'Common_Fucntion'

Note:
    1. no power supply control.
    
'''

# import wxP.1ytho.1nxFF
import sys
import csv
import os
import time
import datetime
# import wx

from Common.M65Q68_Common_Fuction_A0 import *
from Common_var.M66S68_Common_Fuction_A0 import *
# from constants import TRUE

from instr_drv.IT6322B import *
power_camera = IT6322B(usb_addr="USB0::0x2EC7::0x6300::800068020757210071::INSTR")
from instr_drv.Keithley2230_py3 import *
power_q68 = <PERSON>ley2230(usb_addr="USB0::0x05E6::0x2230::9211136::INSTR")

# from oven_doaho import *  #温箱
# oven=OVEN(port='com6')
#==================================================================================/
from tkinter import *
BOARDID = 0 #define global parameter to set boardid
CHIPID = 0 #define global parameter to set chipid
root=Tk()
root.geometry('300x300')

l1=Label(root,text='BOARD ID')
l1.pack()
boardid_text = StringVar()
boardid_set = Entry(root,textvariable=boardid_text)
boardid_text.set("")
boardid_set.pack()

l2=Label(root,text='CHIP ID')
l2.pack()
chipid_text = StringVar()
chipid_set = Entry(root,textvariable=chipid_text)
chipid_text.set("")
chipid_set.pack()

def on_click():
    global BOARDID
    global CHIPID
    BOARDID = boardid_text.get()
    CHIPID = chipid_text.get()
    string=str("sheet is:%s"%(BOARDID))
    string1=str("chipid is:%s"%(CHIPID))
    print(string)
    print(string1)
    root.destroy()
    
Button(root,text='press',command=on_click).pack()
root.mainloop()

#==================================================================================/
STEMP               =   (25,)
VOLTAGE             =   [(1.14,1.71,1.71),(1.2,1.8,1.8)]
DRIFT               =   (25,)
Drift_type          =   {-40:(-40,85,25), 25:(25,-40,85),85:(85,25,-40)}
VIDEO_WIN = [0,5,10,15]     # orin video window

cable_length = ['5m_coax', '5m_coax', '5m_coax', '5m_coax']      # information: link/cable 
camera_id = {0:'1#',1:'2#',2:'3#',3:'4#',}                          # information: camera SN#
#==================================================================================/
KLINK        =   [0,1,2,3]              # 表示接几个c3的lane link0~3

RATE         =   [2,2,2,2]              # link rate,  initial(default:cfg1), 1-3G, 2-6G; link0, link1, link2, link3
RATE_final   =   [2,2,2,2]              # link rate,  finally set(change when Linked)
BCRATE       =   [0,0,0,0]              # q68 -> S68 data rate: 0:150M, 1:187.5M, 2/3:200M  
DTbypass     =   [0,0,0,0]              # 多data type时必须改为1; link0, link1, link2, link3
vcid         =   [0,1,2,3]              # set vcid of each link(active when Camera's vcid=0)

pcs_set      =   [0,0,0,0]              # 0:8b/10b (0,0,0,0),  1: 66/64d [(1,1,1,1)
fec_bypass   =   [0,0,0,0]              # 1: fec bypass
#==================================================================================================================/

link_camera = {0:'Huayang_031', 1:'Huayang_031', 2:'Huayang_031', 3:'Huayang_031',}     #需要根据link上接的camera进行修改
# link_camera  = {0:'s68_VG',1: 's68_VG',2:'s68_VG',3:'s68_VG',}                       # S68 video generator, with Fsync
# link_camera = {0:'q68_VG',1: 'q68_VG',2:'q68_VG',3:'q68_VG',}                       # Q68 video generator, instead of Link data
                                        
q68_iic_addr =  0x73                        # Q68地址, 0x31#0x73
s68_iic_dev  =  [0x40, 0x40, 0x40, 0x40]    # s68地址，需要根据实际s68地址进行改动
#==================================================================================================================/
aggregation_mode = 'RoundRobin_2csi'        # 'RoundRobin_1csi' '4W1H_2csi'  #'1W4H_2csi' #'2W1H_2csi'  '3W1H_2csi' #'RoundRobin_4csi_1video'  '2W1H_4csi'
csi_mode = 'dphy_2csi_1c4d'                 # 'dphy_4csi_1c2d'  #'dphy_2csi_1c4d' dphy_1csi_1c4d 'dphy_cphy_2csi_1c4d' # 'cphy_2csi_4trio'  'cphy_4csi_2trio'
#==================================================================================================================/
MIPI_timing_dterm    = [0,0,0,0]            # S68 MIPI timing: d-term-en
MIPI_timing_hssettle = [0,0,0,0]            # S68 MIPI timing: hs-settle
#==================================================================================================================/
s68_res_dev        = [0x20, 0x21, 0x22, 0x23]           # s68转译地址可自行定义，link0,link1,link2,link3
s68_res_sensor_dev = [0x24, 0x25, 0x26, 0x27]           # sensor转译地址可自行定义，link0,link1,link2,link3
#===================================================================================================================/
sensor_dev = {'NIO_031':0, 'PHYbrd_NIO_031':0, 'xiaopeng_031_RAW12': 0x1A,'NIO_1Mega':0,'NIO_2Mega':0x36,'NIO_8Mega':16, 'x8B':0x36, 'SG2_IMX390C':0, 'Huayang_031':0, 's68_VG':0, 'q68_VG':0}    # sensor iic address, set to '0' if no register access needed
s68_sensor_dev = [sensor_dev[link_camera[0]], sensor_dev[link_camera[1]], sensor_dev[link_camera[2]], sensor_dev[link_camera[3]]] 
#==================================================================================================================/

q68 =  M65Q68_A0(dongle='stm32', id=0, bus='i2c')# G9PH DS board  
q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')
s680 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link0
s681 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link1
s682 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link2
s683 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link3
# s68_0 = M66S68_A0(dongle='stm32', id=1, bus='i2c',acc='L',dongle_id=b'\x00N\x00$41Q\x197997', bus_chan=2, optype='auto')

# data_address = "D:\\project\\m65q68_a0\\Raw_Data\\01_test\\Camera\\" + str(BOARDID) + "_" + "Camera" + "_" + datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S') + ".csv"  # ('%Y-%m-%d_%H-%M-%S') 

       
def TEST():
    
    global s68_iic_dev  # 添加这行声明使用全局变量
    
    for vdd10,vddio,vdd18 in VOLTAGE:
        # Keithley2230G1.SetVoltages(v1=vdd10, v2=vddio, v3=vdd18)
        for starttemp in STEMP:     
              
            for powercycle in range(1):
                                
                '''# Step1: 设置 Q68 link速率，以匹配S68速率'''
                
                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])  # set forward data rate
                q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=BCRATE[0], bc_rate1=BCRATE[1], bc_rate2=BCRATE[2], bc_rate3=BCRATE[3])   # set backward data rate
        
                '''# Step2： 设置 link参数、编码方式(FEC, 8b10b, 64b66b)'''
                
                q68.Q68_C3_6G_Init(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3]) #link parameters
                
                for link in KLINK:
                    q68.FECcoding(link=link, pcs_set=pcs_set[link], fec_bypass=fec_bypass[link])
                
                '''# Step3： 设置 4路S68、sensor I2C转译地址'''                    
  
                for link in KLINK:
                    q68_remote.S68_AddrTrans(link=link, q68_iic_addr=q68_iic_addr ,s68_iic_addr=s68_iic_dev[link], s68_retrans_addr=s68_res_dev[link], sensor_addr=s68_sensor_dev[link], sensor_retrans_addr=s68_res_sensor_dev[link])

                '''# Step4: 设置 S68 编码方式(FEC, 8b10b, 64b66b) '''                 
            
                for link in KLINK:
                    q68_remote.S68_FECCoding(s68_res_dev=s68_res_dev[link], pcs_set=pcs_set[link],fec_bypass=fec_bypass[link], RATE_final=RATE_final[link])       

                '''# Step5: 设置 link速率(final)，参数 '''  
                    
                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])
                q68.Q68_C3_6G_Init(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])  #for 6G init

                print('link0 status is: ', q68.c2m.rd_test_fsm_status1_link0()) 
                print('link1 status is: ', q68.c2m.rd_test_fsm_status1_link1()) 
                print('link2 status is: ', q68.c2m.rd_test_fsm_status2_link2()) 
                print('link3 status is: ', q68.c2m.rd_test_fsm_status2_link3())
                
                ''' 
                case1:frame sync test with 3 frame sync gen: Q68->4S68
                for link in KLINK:
                    s68_iic_dev = s68_res_dev[link]
                    q68_remote.dongle.devAddr = s68_iic_dev
                    # q68_remote.M2CGPIORemoteRx(gpio=0)       #enable frame sync output to camera
                    q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=11)       #enable frame sync output to camera
                    q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=13)       #enable frame sync output to camera
                    q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=15)       #enable frame sync output to camera
                
                //30 50 12
                q68.FrameSyncOutConifg(i=0, per_div=0x0B, duty_cycle=4, period=17361, fs_tx_id=11, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data
                q68.FrameSyncOutConifg(i=1, per_div=0x0A, duty_cycle=4, period=15625, fs_tx_id=13, auto_fs=1, outen=1)     #configure and enable fsync, 50 Hz , camera send out data
                q68.FrameSyncOutConifg(i=2, per_div=0x0B, duty_cycle=4, period=43403, fs_tx_id=15, auto_fs=1, outen=1)     #configure and enable fsync, 12 Hz , camera send out data
                
                
                case2:q68传一个s68的不同gpio
                for link in KLINK:
                    s68_iic_dev = s68_res_dev[link]
                    q68_remote.dongle.devAddr = s68_iic_dev
                    
                s68_iic_dev = s68_res_dev[3]
                q68_remote.M2CMFNSet(gpio=3, mfn=0)//复用功能
                q68_remote.M2CGPIORemoteRx(gpio=3, rx_id=11)       #enable frame sync output to camera
                s68_iic_dev = s68_res_dev[0]   
                q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=11)       #enable frame sync output to camera 
                s68_iic_dev = s68_res_dev[1]
                q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=11)       #enable frame sync output to camera
                s68_iic_dev = s68_res_dev[2]
                q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=11)       #enable frame sync output to camera
                
                q68.FrameSyncOutConifg(i=0, per_div=0x0B, duty_cycle=4, period=17361, fs_tx_id=11, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data

                
                case3:test占空比 duty_cycle 0-9
                for link in KLINK:
                    s68_iic_dev = s68_res_dev[link]
                    q68_remote.dongle.devAddr = s68_iic_dev
                    q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=11)       #enable frame sync output to camera
                    
                q68.FrameSyncOutConifg(i=0, per_div=0x0B, duty_cycle=4, period=17361, fs_tx_id=11, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data


                case4:外部信号给 q68 gpio 0 -> s68 gpio 3   测试外部信号最大频率和占空比(脉宽最小)
                for link in KLINK:
                    s68_iic_dev = s68_res_dev[link]
                    q68_remote.dongle.devAddr = s68_iic_dev
                    q68_remote.M2CMFNSet(gpio=3, mfn=0)
                    q68_remote.M2CGPIORemoteRx(gpio=3, rx_id=11)       #enable frame sync output to camera

                q68.MFNSet(gpio=0, mfn=0)
                q68.GPIORemoteTx(gpio=0, tx_id = 11, link_id=1, dly_comp_en=0)
                print('done')
                '''
                
                # '''
                # case5:外部信号给 s68 gpio3 ->q68 gpio0
                for link in KLINK:
                    s68_iic_dev = s68_res_dev[link]
                    q68_remote.dongle.devAddr = s68_iic_dev
                    q68_remote.M2CMFNSet(gpio=3, mfn=0)
                    q68_remote.M2CGPIORemoteTx(gpio=3, tx_id=11)

                q68.MFNSet(gpio=0, mfn=0)
                q68.GPIORemoteRx(gpio=0, rx_id=11)
                print('done')

        
if __name__ == "__main__":


    for starttemp in STEMP:        
                       

        
        """ power down -> on """    
        power_q68.TurnOutputsOff()
        power_camera.TurnOutputsOff()
    
        time.sleep(5)
        power_q68.TurnOutputsOn()
        power_camera.TurnOutputsOn()
        time.sleep(5)    
        
        """ config Q68 + S68 """
        link0_status = q68.c2m.rd_test_fsm_status1_link0()                        
        link1_status = q68.c2m.rd_test_fsm_status1_link1()                        
        link2_status = q68.c2m.rd_test_fsm_status2_link2()                        
        link3_status = q68.c2m.rd_test_fsm_status2_link3()                        
        print('Link Status:',link0_status,link1_status,link2_status,link3_status) 
        
        TEST()      # main config



