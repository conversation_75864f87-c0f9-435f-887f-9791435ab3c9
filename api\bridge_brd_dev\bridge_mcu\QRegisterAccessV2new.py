#!python3

import time
import csv

# package for supporting stm32
from stm32 import USBImpl
#from USBInterface import *
import ft4222  
# package for supporting ft4222
# import ft4222

# global ft4222 usb hardware instances list, as some silicon may share same instance
ft4222_inst_list = None

# global stm32 usb hardware instances list, as some silicon may share same instance
stm32_inst_list = None

q68_dev = None
q68_search = True
devlist = 0
dutsearch = None
save_data = 1
if save_data:
    data_addr = "C:\\project\\ssctest.csv"
    with open(data_addr, "a", encoding="utf-8", newline="") as f:
        csv_writer = csv.writer(f)
        name = ['DeviceAddr','Reg_Addr', 'R_or_W', 'Write_or_Read_Value']
        csv_writer.writerow(name)
        
class QRegisterAccess:

    # i2c device address
    devAddr = 0x00
    
    # dongle name, 'stm32'/'ft4222'/'ft4232'
    dongle = None

    # ft4222 i2c, spi & gpio
    ft4222_dev_location = None
    ft4222_devA = None
    ft4222_devB = None
    ft4222_bus  = None

    # stm32 support
    stm32_dev = None
    stm32_bus = None
    stm32_dev_index = 0x00
    stm32_dev_sn    = 0x00
    stm32_bus_chan  = 0x00 # 1: i2c1, 2:i2c2, 3:i2c3
    
    # todo: ft4222 support
    
    # todo: ft4232 support
    def __init__(self, dongle='stm32', dev=None, i2c_addr=None, dongle_id=None, bus='i2c', bus_chan=1, product='m2c', id=0, optype='auto'):
        global stm32_inst_list
        global q68_search 
        global q68_dev
        global devlist
        global ft4222_devA
        global dutsearch
        
        if optype=='auto':
            self.dongle = dongle
            if dongle == 'stm32':
            
                #find all device
                if stm32_inst_list is None:
                    stm32_inst_list = []
                    self.stm32_dev = USBImpl.USBImpl()
                    devlist = self.stm32_dev.usbdev_scan()
                    self.stm32_dev = None
                    print("Find %d STM32 Devices:"%(len(devlist)))
                    for i in range(len(devlist)):
                        dev = USBImpl.USBImpl()
                        dev.usbdev_open(devlist[i].name)
                        dev.usbdev_i2c_setbusspeed(1, 400)
                        dev.usbdev_i2c_setbusspeed(2, 400)
                        dev.usbdev_i2c_setbusspeed(3, 400)
                        (ret, sn) = dev.usbdev_get_sn()
                        stm32_inst_list.append((dev, sn))
                        print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                        
                i = 0
                for inst in stm32_inst_list:
                    if len(devlist)==1:  # means 1 Q68, 1 s8 or 1 Q68+1s68
                    # if inst[1] == dongle_id:
                        self.stm32_dev = inst[0]
                        self.stm32_dev_sn = dongle_id
                        self.stm32_bus = bus
                        self.stm32_bus_chan = bus_chan
                        if self.stm32_dev == None:
                            raise BaseException('unable to find specified stm32 dongle')
                        if id<=1:
                            if bus == 'i2c':
                                self.stm32_bus = bus
                                if product=='c2m':
                                    if id==0: 
                                        if i2c_addr is None:
                                            print('sn: ', inst[1])
                                            
                                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                            #for devAddr in list(range(256)): 
                                                # self.dongle.stm32_dev = inst[0]
                                                # self.dongle.stm32_dev_sn = inst[1]
                                                self.devAddr = devAddr
                                                
                                                self.stm32_bus_chan = 1
                                                data = self.readReg(moduleName=product, addr=0x1001)
                                                print(('data = ',data,'and slave_addr = ',hex(devAddr)))
                                                if data == 0x5:
                                                    i2c_addr = devAddr
                                                    # self.dongle.devAddr = i2c_addr
                                                    print('find q68 device 0x%x'%(devAddr))
                                                    q68_dev = inst
                                                    break
                                            print('done!')
                                    #             if i2c_addr is None:
                                    #                 raise('q68 not found')
                                    # else:
                                    #     raise('please connect Q68 to MCU channel 1 !!!')
                                if product=='m2c' and id==1:
                                    if i2c_addr is None:
                                        # for devAddr in [0x40, 0x44]: 
                                        for devAddr in [0x40,0x44]: 
                                            # self.dongle.stm32_dev = inst[0]
                                            # self.dongle.stm32_dev_sn = inst[1]
                                            self.devAddr = devAddr
                                            self.stm32_bus_chan = 2
                                            data = self.readReg(moduleName=product, addr=0x0101)
                                            if data == 0x55:
                                                i2c_addr = devAddr
                                                # self.dongle.devAddr = i2c_addr
                                                print('find s68 device 0x%x'%(devAddr))
                                                break
                                        # if i2c_addr is None:
                                        #     raise('s68 not found')
                        if len(devlist)>1:  # means q68 + s681, s682 or more
                            
                            self.stm32_dev = inst[0]
                            self.stm32_dev_sn = dongle_id
                            self.stm32_bus = bus
                            self.stm32_bus_chan = bus_chan
                            if self.stm32_dev == None:
                                raise BaseException('unable to find specified stm32 dongle')
                            if id<=1:
                                if bus == 'i2c':
                                    self.stm32_bus = bus
                                    if product=='c2m':
                                        if id==0: 
                                            if i2c_addr is None:
                                                print('sn: ', inst[1])
                                                if q68_search:
                                                    for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                                        # self.stm32_dev = inst[0]
                                                        # self.stm32_dev_sn = inst[1]
                                                        self.devAddr = devAddr
                                                        
                                                        self.stm32_bus_chan = 1
                                                        data = self.readReg(moduleName=product, addr=0x1001)
                                                        if data == 0x5:
                                                            i2c_addr = devAddr
                                                            # self.dongle.devAddr = i2c_addr
                                                            print('find q68 device 0x%x'%(devAddr))
                                                            q68_dev = inst
                                                            q68_search = False
                                                            
                                                            break
                                        #                 if i2c_addr is None:
                                        #                     raise('q68 not found')
                                        # else:
                                        #     raise('please connect Q68 to MCU channel 1 !!!')
                                    
                                    if product=='m2c' and id==1:
                                        if i2c_addr is None:
                                            for devAddr in [0x40, 0x44]: 
                                                self.stm32_dev = q68_dev[0]
                                                # self.stm32_dev_sn = inst[1]
                                                self.devAddr = devAddr
                                                self.stm32_bus_chan = 2
                                                data = self.readReg(moduleName=product, addr=0x0101)
                                                if data == 0x55:
                                                    i2c_addr = devAddr
                                                    # self.devAddr = i2c_addr
                                            #         print('find s68 device 0x%x'%(devAddr))
                                            #         break
                                            # if i2c_addr is None:
                                            #     raise('s68 not found')
                        if q68_search ==False:
                            break
                if product=='m2c' and id>1:
                    for inst in stm32_inst_list:
                        if inst[1] != q68_dev[1]:
                            if i2c_addr is None:
                                for devAddr in [0x40, 0x44]: 
                                    self.stm32_dev = inst[0]
                                    # self.stm32_dev_sn = inst[1]
                                    self.devAddr = devAddr
                                    self.stm32_bus_chan = id-1
                                    data = self.readReg(moduleName=product, addr=0x0101)
                                    if data == 0x55:
                                        i2c_addr = devAddr
                                        # self.dongle.devAddr = i2c_addr
                                        print('find s68 device 0x%x'%(devAddr))
                                        break
                        
                                    # if i2c_addr is None:
                                    #     raise('s68 not found')
                                    
                                    
                        
            elif dongle == 'ft4222':
                  
            
                location0=4881  #801#12817
                location1=4882  #802#12818
            
                devNum = ft4222.createDeviceInfoList()
                add = [0 for i in range(devNum)]
                for i in range(devNum):
                    add[i]=ft4222.getDeviceInfoDetail(i, False)
                    print(ft4222.getDeviceInfoDetail(i, False))
                
                for useadd in range(int(devNum/2)):
                    location0 = add[2*useadd]['location']
                    location1 = add[2*useadd+1]['location']
                    if location0 != dutsearch:
                        ft4222_devA=ft4222.openByLocation(location0)
                        ft4222_devB=ft4222.openByLocation(location1)
                        ft4222_devA.setClock(ft4222.SysClock.CLK_80)
                    # if MCU=='ft4222':
                    #     from QRegisterAccess import *
                    # elif MCU=='ftd2xx':
                    #     from ftd2xx.QRegisterAccess import *
                    
                        if self.findMeritechI2cChan():
                            print ('Bridge board i2c is found!')
                        else:
                            print ('Can not find bridge i2c!') 
                            raise 
                        if product=='m2c':
                            #for i in list(range(256)):
                            for devAddr in [0x40,0x44]:
                                self.devAddr = devAddr
                               
                                #for reg in (0x30,0x31,0x32,0x33):
                                data = self.readReg(moduleName=product, addr=0x0101)
                                #data = self.readReg(moduleName=product, addr=reg)
                                #print('FPGA address = ',i, ' ,and register address = ',hex(reg), ' ,and value = ',data)
                                # if data !=255:
                                #     print('find MCU and read register OK !')
                                #     break
                                if data == 0x55:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find s68 device 0x%x'%(devAddr))
                                    break
                            if data == 0x55:
                                dutsearch = location0
                                break
                        if product=='c2m':
                            for devAddr in [0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71,]: 
                                # self.stm32_dev = inst[0]
                                # self.stm32_dev_sn = inst[1]
                                self.devAddr = devAddr
                                data = self.readReg(moduleName=product, addr=0x1001)
                                if data == 0x5:
                                    i2c_addr = devAddr
                                    # self.dongle.devAddr = i2c_addr
                                    print('find q68 device 0x%x'%(devAddr))
                                    break
                            if data == 0x5:
                                dutsearch = location0
                                break
                            
                        # if product=='m2c':
                        #     for devAddr in list(range(256)):
                        #         self.devAddr = devAddr
                        #         data = self.readReg(moduleName=product, addr=0x21)
                        #         print('devAddr = ',devAddr,'and data=',data)
                        #         if data == 0x012:
                        #             i2c_addr = devAddr
                        #             # self.dongle.devAddr = i2c_addr
                        #             print('find s68 device 0x%x'%(devAddr))
                        #             break
                        #     if data == 0x55:
                        #         dutsearch = location0
                        #         break
                #raise BaseException('not support')
            elif dongle == 'ft4232':
                raise BaseException('not support')
            else:
                raise BaseException('unknown dongle')
        elif optype=='manual':
            self.dongle = dongle
            if dongle == 'stm32':
                
                # find all device
                if stm32_inst_list is None:
                    stm32_inst_list = []
                    self.stm32_dev = USBImpl.USBImpl()
                    devlist = self.stm32_dev.usbdev_scan()
                    self.stm32_dev = None
                    print("Find %d STM32 Devices:"%(len(devlist)))
                    for i in range(len(devlist)):
                        dev = USBImpl.USBImpl()
                        dev.usbdev_open(devlist[i].name)
                        dev.usbdev_i2c_setbusspeed(1, 400)
                        dev.usbdev_i2c_setbusspeed(2, 400)
                        dev.usbdev_i2c_setbusspeed(3, 400)
                        (ret, sn) = dev.usbdev_get_sn()
                        stm32_inst_list.append((dev, sn))
                        print("id=%s,"%i, devlist[i], ", sn=%s"%sn)
                for inst in stm32_inst_list:
                    if inst[1] == dongle_id:
                        self.stm32_dev = inst[0]
                        self.stm32_dev_sn = dongle_id
                        self.stm32_bus = bus
                        self.stm32_bus_chan = bus_chan
                        
                if self.stm32_dev == None:
                    raise BaseException('unable to find specified stm32 dongle')

        
    def __del__(self):
        global stm32_inst_list
        if stm32_inst_list != None:
            if self.dongle == 'stm32':
                for i in range(len(stm32_inst_list)):
                    print("closing stm32 device %d (sn = %s)"%(i, stm32_inst_list[i][1]))
                    stm32_inst_list[i][0].usbdev_close()
                stm32_inst_list = None
    
    def readReg(self, moduleName, addr, crc = False):
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, self.devAddr, addr)
            if self.dongle == 'ft4222':
                wrBuf = [(addr >> 8) & 0xFF, (addr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
            if save_data:
                with open(data_addr, "a", encoding="utf-8", newline="") as f:
                    csv_writer = csv.writer(f)
                    row = [hex(self.devAddr), hex(addr), 'R', hex(value)]
                    csv_writer.writerow(row) 
        else:
            raise BaseException('unknown module')
        return value
    
    '''............new add I2C_brust and without CRC mode............................'''
    def readMuiltReg(self, dev_addr, addr, count, moduleName,crc = False):
        
        self.devAddr=dev_addr
        
        if crc==False:
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values = self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
            return values
        
    def readMuiltReg_CRC(self,dev_addr, addr, count, moduleName,crc = True):
    
        self.devAddr=dev_addr
        #print('s68_real_addr = ',s68_real_addr)
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x73:
                addr_crc=0xE6
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            elif self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x24:
                addr_crc=0x68
            elif self.devAddr==0x25:
                addr_crc=0x6a
            elif self.devAddr==0x26:
                addr_crc=0x6c
            elif self.devAddr==0x27:
                addr_crc=0x6e
            elif self.devAddr==0x35:
                addr_crc=0x6A
            elif self.devAddr==0x51:
                addr_crc=0xA2
            elif self.devAddr==0x53:
                addr_crc=0xA6
            elif self.devAddr==0x55:
                addr_crc=0xAA
            elif self.devAddr==0x71:
                addr_crc=0xE2
              
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
            
        return values
    
    def writeMuiltS68Reg_CRC_crc0is8bitdevaddr(self, s68_real_addr,dev_addr, addr, values:list, moduleName, crc = True):
        
        self.devAddr=dev_addr
        
        if s68_real_addr == 0x44:
            s68_real_addr_crc0 = 0x88
        elif s68_real_addr == 0x40:
            s68_real_addr_crc0 = 0x80
        
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            
            if len(values)  <2:
                
                crc0=self.crc8([s68_real_addr_crc0,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
                return True
                  
            if len(values)  ==3:
                
                crc0=self.crc8([s68_real_addr_crc0,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                # crc3=self.crc8([values[3]])
                # crc4=self.crc8([values[4]])
                # crc5=self.crc8([values[5]])
                # crc6=self.crc8([values[6]])
                # crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2
            
            if len(values)  ==8:
                
                crc0=self.crc8([s68_real_addr_crc0,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                crc3=self.crc8([values[3]])
                crc4=self.crc8([values[4]])
                crc5=self.crc8([values[5]])
                crc6=self.crc8([values[6]])
                crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
            
        else:
                raise BaseException('unknown module')
    
    def writeMuiltS68Reg_CRC(self, dev_addr, addr, values:list, moduleName, crc = True):
        
        self.devAddr=dev_addr
        
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            
            if len(values)  <2:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
                return True
                  
            if len(values)  ==8:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                crc3=self.crc8([values[3]])
                crc4=self.crc8([values[4]])
                crc5=self.crc8([values[5]])
                crc6=self.crc8([values[6]])
                crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
            
        else:
                raise BaseException('unknown module')
    
    def readMuiltS68Reg_CRC(self, dev_addr, addr, count:list, moduleName, crc = True):
    
        self.devAddr=dev_addr
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x68
              
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
            
        return values


    def writeMuiltReg(self, dev_addr, addr, values:list, moduleName,crc = False):
        
        self.devAddr = dev_addr
        if crc==False:
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, values)
            else:
                raise BaseException('unknown module')
        return True
        
    def writeMuiltReg_CRC(self, dev_addr, addr, values:list, moduleName,crc = True):
        
        self.devAddr = dev_addr
        if crc==True:
            packetcounter=0
            
            if self.devAddr==0x73:
                addr_crc=0xE6
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x35:
                addr_crc=0x6A
            elif self.devAddr==0x51:
                addr_crc=0xA2
            elif self.devAddr==0x53:
                addr_crc=0xA6
            elif self.devAddr==0x55:
                addr_crc=0xAA
            elif self.devAddr==0x71:
                addr_crc=0xE2
            elif self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x30:
                addr_crc=0x60
            elif self.devAddr==0x31:
                addr_crc=0x62
            elif self.devAddr==0x32:
                addr_crc=0x64
            elif self.devAddr==0x33:
                addr_crc=0x66
            
            if len(values)  <2:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
                return True
                  
            if len(values)  ==8:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                crc3=self.crc8([values[3]])
                crc4=self.crc8([values[4]])
                crc5=self.crc8([values[5]])
                crc6=self.crc8([values[6]])
                crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
            
            if len(values)  ==3:
                
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                crc1=self.crc8([values[1]])
                crc2=self.crc8([values[2]])
                #crc3=self.crc8([values[3]])
                # crc4=self.crc8([values[4]])
                # crc5=self.crc8([values[5]])
                # crc6=self.crc8([values[6]])
                # crc7=self.crc8([values[7]])
                
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,]
        
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
                else:
                    raise BaseException('unknown module')
                
                return packetcounter,crc0,crc1,crc2
    
        else:
                raise BaseException('unknown module')
    
    ''' .....................end..................................'''
    
    def writeReg(self,moduleName, addr, value, crc=False):
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, self.devAddr, addr, value)
            if self.dongle == 'ft4222':
                wrBuf = [(addr >> 8) & 0xFF, (addr & 0xFF), value & 0xFF]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
            if save_data:
                with open(data_addr, "a", encoding="utf-8", newline="") as f:
                    csv_writer = csv.writer(f)
                    row = [hex(self.devAddr), hex(addr), 'W', hex(value)]
                    csv_writer.writerow(row)
        else:
            raise BaseException('unknown module')
        return True
    
    ''' s68 remote'''
    def M66S68I2CWrite_normal_without_crc(self, regAddr, values:list, moduleName,crc = False):
    
        if moduleName =='m2c' or moduleName =='c2m' :
    
            if self.dongle == 'stm32':
                self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, regAddr, values)
    
            if self.dongle == 'ft4222':
                if ft4222_devA != None:
                    wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                    ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        return True
    
    def M66S68I2CRead_normal_without_crc(self, regAddr, count:int, moduleName,crc = False):
    
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, values = self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, regAddr, count)
            if self.dongle == 'ft4222':
                value = 0
                if ft4222_devA != None:
                    wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                    ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                    rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                    value = rdBuf[0]
        return value
    
    def M66S68I2CRead_normal_with_crc(self, addr, count:int ,moduleName, crc = True):
    
        if crc==True:
            packetcounter=0
    
            if self.devAddr==0x40:
                addr_crc=0x80
            elif self.devAddr==0x44:
                addr_crc=0x88
            elif self.devAddr==0x30:
                addr_crc=0xc0
            elif self.devAddr==0x31:
                addr_crc=0xc2
            elif self.devAddr==0x32:
                addr_crc=0xc4
            elif self.devAddr==0x33:
                addr_crc=0xc8
    
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, addr, count)
            else:
                raise BaseException('unknown module')
    
        return values
    
    
    # def writeMuiltReg(self, moduleName, addr, values:list, crc = False):
    #
    #     if crc==False:
    #         if moduleName=='c2m' or moduleName == 'm2c':
    #             if self.dongle == 'stm32':
    #                 self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, values)
    #         else:
    #             raise BaseException('unknown module')
    #     return True
    #
    #
    # def writeMuiltReg_CRC(self, moduleName, addr, values:list, crc = True):
    #
    #     if crc==True:
    #         packetcounter=0
    #
    #         if self.devAddr==0x73:
    #             addr_crc=0xE6
    #         elif self.devAddr==0x31:
    #             addr_crc=0x62
    #         elif self.devAddr==0x32:
    #             addr_crc=0x64
    #         elif self.devAddr==0x35:
    #             addr_crc=0x6A
    #         elif self.devAddr==0x51:
    #             addr_crc=0xA2
    #         elif self.devAddr==0x53:
    #             addr_crc=0xA6
    #         elif self.devAddr==0x55:
    #             addr_crc=0xAA
    #         elif self.devAddr==0x71:
    #             addr_crc=0xE2
    #
    #         if len(values)  <2:
    #
    #             crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
    #             wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
    #             if moduleName=='c2m' or moduleName == 'm2c':
    #                 if self.dongle == 'stm32':
    #                     self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
    #                 else:
    #                     raise BaseException('unknown module')
    #             return True
    #
    #         if len(values)  ==8:
    #
    #             crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
    #             crc1=self.crc8([values[1]])
    #             crc2=self.crc8([values[2]])
    #             crc3=self.crc8([values[3]])
    #             crc4=self.crc8([values[4]])
    #             crc5=self.crc8([values[5]])
    #             crc6=self.crc8([values[6]])
    #             crc7=self.crc8([values[7]])
    #
    #             wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7]
    #
    #             if moduleName=='c2m' or moduleName == 'm2c':
    #                 if self.dongle == 'stm32':
    #                     self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
    #             else:
    #                 raise BaseException('unknown module')
    #
    #             return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7
    #
    #         if len(values)  ==11:
    #
    #             crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
    #             crc1=self.crc8([values[1]])
    #             crc2=self.crc8([values[2]])
    #             crc3=self.crc8([values[3]])
    #             crc4=self.crc8([values[4]])
    #             crc5=self.crc8([values[5]])
    #             crc6=self.crc8([values[6]])
    #             crc7=self.crc8([values[7]])
    #             crc8=self.crc8([values[7]])
    #             crc9=self.crc8([values[8]])
    #             crc10=self.crc8([values[10]])
    #
    #             wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,values[1],crc1,values[2],crc2,values[3],crc3,values[4],crc4,values[5],crc5,values[6],crc6,values[7],crc7,values[8],crc8,values[9],crc9,values[10],crc10]
    #
    #             if moduleName=='c2m' or moduleName == 'm2c':
    #                 if self.dongle == 'stm32':
    #                     self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, addr, reg_vals=wrBuf)
    #             else:
    #                 raise BaseException('unknown module')
    #
    #             return packetcounter,crc0,crc1,crc2,crc3,crc4,crc5,crc6,crc7,crc8,crc9,crc10
    #
    #     else:
    #             raise BaseException('unknown module')
    #


    
        
    '''......end......'''
    
    def M66S68I2CWrite_normal(self, regAddr, value):
        
        if self.dongle == 'stm32':
            self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, self.devAddr, regAddr, value)
            
        if self.dongle == 'ft4222':
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        return True
    
    def M66S68I2CRead_normal(self, regAddr):
        
        #if moduleName=='c2m' or moduleName == 'm2c':
        if self.dongle == 'stm32':
            ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, self.devAddr, regAddr)
        if self.dongle == 'ft4222':
            value = 0
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
        return value
    
    def M66S68I2CWrite_CRC(self, regAddr, value):
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF), value)
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        return True
    
    def M66S68I2CRead_CRC(self, regAddr):
        value = 0
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF))
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
            rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 2)
            value = rdBuf[0]
        return value
    
    def M65Q68I2CWrite_normal(self, regAddr, value):
    
        if self.dongle == 'stm32':
            self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, self.devAddr, regAddr, value)
            
        if self.dongle == 'ft4222':
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        
        return True
    
    def M65Q68I2CRead_normal(self, regAddr):
       
        if self.dongle == 'stm32':
            ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, self.devAddr, regAddr)
            
        if self.dongle == 'ft4222':
            
            value = 0
            if ft4222_devA != None:
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
                
        return value
    
    def M65Q68I2CWrite_CRC(self, regAddr, packetcounter,value):
        '''
            Q68 dev_addr(7bit) = 0x73, 0x31, 0x32, 0x35, 0x51, 0x53, 0x55, 0x71
            addr_crc(8bit) syntax: remove bit7, and add 0 into bit0
        '''
        if self.devAddr==0x73:
            addr_crc=0xE6
        elif self.devAddr==0x31:
            addr_crc=0x62
        elif self.devAddr==0x32:
            addr_crc=0x64
        elif self.devAddr==0x35:
            addr_crc=0x6A
        elif self.devAddr==0x51:
            addr_crc=0xA2
        elif self.devAddr==0x53:
            addr_crc=0xA6
        elif self.devAddr==0x55:
            addr_crc=0xAA
        elif self.devAddr==0x71:
            addr_crc=0xE2
        crc=self.crc8([addr_crc,(regAddr >> 8) & 0xFF, (regAddr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), value])
        
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF),value,crc)
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
        return True
    
    def M65Q68I2CRead_CRC(self, regAddr):
            
        value = 0
        if ft4222_devA != None:
            wrBuf = ((regAddr >> 8) & 0xFF, (regAddr & 0xFF))
            ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
            rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 4)
            value = rdBuf[2]
        return (rdBuf[0],rdBuf[1],rdBuf[2],rdBuf[3])
    
    def cal_crc(self, data):
        '''
        计算1字节数据的CRC8
        '''
        crc = data
        poly = 0x07    # 多项式x^8 + x^2 + x^1 + 1，即0x107，（根据原理）省略了最高位1而得0x07
        for i in range(8,0,-1):
            if ((crc & 0x80) >> 7) == 1:    # 判断最高位是否为1，如果是需要异或，否则仅左移
                crc = (crc << 1) ^ poly;
            else:
                crc = (crc << 1)
        return crc & 0xFF    # 计算后需要进行截取
     
    def crc8(self, datas):
        '''
        计算数据的CRC8校验码
        '''
        length = len(datas)
        crc = 0xFF;
        for i in range(length):
            # if i == 0:
            #     crc = crc ^ datas[0]     # 先计算第1个数据的CRC8
            #     crc = cal_crc(crc)
            # else:
            crc = (crc ^ datas[i]) & 0xFF    # 其余的均将上次的CRC8结果与本次数据异或
            crc = self.cal_crc(crc)               # 再计算CRC8
        return crc & 0xFF
    
    
    def initGpio(self, index, mode, pull):
        if self.dongle == 'stm32':
            # index: [0~23]
            # mode: [0:input 1:output_pp 2:output_od 3:ad_pp 4:ad_od]
            # pull: [0:nopull 1:pullup 2:pulldown]
            self.stm32_dev.usbdev_init_gpio(index, mode, pull)
    
    def setGpio(self, index, value):
        if self.dongle == 'stm32':
            # index: [0~23]
            self.stm32_dev.usbdev_set_gpio(index, value)
    
    def getGpio(self, index):
        value = 0
        if self.dongle == 'stm32':
            # index: [0~23]
            value = self.stm32_dev.usbdev_get_gpio(index)
        return value
    
    def resetDongle(self):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_mcu_reset()
            if ret < 0:
                print("failed to reset stm32.")
            else:
                print("reset stm32 successfully.")
                self.stm32_dev.uninitUsbDev()
        return ret

    def setBusSpeed(self, bus, speed):
        ret = 0
        if self.dongle == 'stm32':
            ret = self.stm32_dev.usbdev_i2c_setbusspeed(bus, speed)
            if ret < 0:
                print("failed to set stm32 bus speed.")
            else:
                print("")
        return ret
    
    def findMeritechI2cChan(self):
        # init i2c master
        ft4222_devA.i2cMaster_Init(500)  # 10kHz
        return True
    def M66S68PCII2CWrite_normal(self, pcidevAddr,regAddr, value):
        # print('pcidevAddr is: ', pcidevAddr) 
       
        moduleName = 'm2c'
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                self.stm32_dev.usbdev_i2c_set(1, self.stm32_bus_chan, pcidevAddr, regAddr, value)
            if self.dongle == 'ft4222':
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF), value & 0xFF]
                self.ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, bytearray(wrBuf))
            # if save_data:
            #     with open(data_addr, "a", encoding="utf-8", newline="") as f:
            #         csv_writer = csv.writer(f)
            #         row = [hex(pcidevAddr), hex(regAddr), 'W', hex(value)]
            #         csv_writer.writerow(row) 
        else:
            raise BaseException('unknown module')
        return True
    
    def M66S68PCII2CRead_normal(self, pcidevAddr, regAddr):
        
        moduleName = 'm2c'
        if moduleName=='c2m' or moduleName == 'm2c':
            if self.dongle == 'stm32':
                ret, value = self.stm32_dev.usbdev_i2c_get(1, self.stm32_bus_chan, pcidevAddr, regAddr)
            if self.dongle == 'ft4222':
                wrBuf = [(regAddr >> 8) & 0xFF, (regAddr & 0xFF)]
                ft4222_devA.i2cMaster_WriteEx(self.devAddr, ft4222.I2CMaster.Flag.START, bytearray(wrBuf))
                rdBuf = ft4222_devA.i2cMaster_ReadEx(self.devAddr, ft4222.I2CMaster.Flag.START_AND_STOP, 1)
                value = rdBuf[0]
        else:
            raise BaseException('unknown module')
        return value
    
    def M66S68PCII2CWrite_crc(self, pcidevAddr,regAddr, value,crcmoduleName, addr, values:list, crc = True):
        # print('pcidevAddr is: ', pcidevAddr) 
       
        moduleName = 'm2c'
        self.devAddr=pcidevAddr
        if crc==True:
            packetcounter=0
            
            if self.pcidevAddr==0x30:
                addr_crc=0x60
            elif self.pcidevAddr==0x31:
                addr_crc=0x62
            elif self.pcidevAddr==0x32:
                addr_crc=0x64
            elif self.pcidevAddr==0x33:
                addr_crc=0x66
            elif self.pcidevAddr==0x24:
                addr_crc=0x48
            elif self.devAddr==0x25:
                addr_crc=0x4a
            elif self.devAddr==0x26:
                addr_crc=0x4c
            elif self.devAddr==0x27:
                addr_crc=0x4e
            
            if len(values)  <2:
                crc0=self.crc8([addr_crc,(addr >> 8) & 0xFF, (addr & 0xFF),(packetcounter>>8)&0xFF,(packetcounter & 0xFF), values[0]])
                wrBuf = [(packetcounter>>8)&0xFF,(packetcounter & 0xFF),values[0],crc0,]
                if moduleName=='c2m' or moduleName == 'm2c':
                    if self.dongle == 'stm32':
                        self.stm32_dev.usbdev_i2c_muilt_set(1, self.stm32_bus_chan, self.devAddr, reg_addr=addr, reg_vals=wrBuf)
                    else:
                        raise BaseException('unknown module')
        else:
            raise BaseException('unknown module')
        
        return True
    
    def M66S68PCII2CRead_crc(self, moduleName,pcidevAddr, addr,count,crc):
        
        self.devAddr=pcidevAddr
        reg_addr=addr
        if crc==True:
            packetcounter=0
            
            if pcidevAddr==0x30:
                addr_crc=0x60
            elif pcidevAddr==0x31:
                addr_crc=0x62
            elif pcidevAddr==0x32:
                addr_crc=0x64
            elif pcidevAddr==0x33:
                addr_crc=0x66
            elif pcidevAddr==0x24:
                addr_crc=0x48
            elif pcidevAddr==0x25:
                addr_crc=0x4a
            elif pcidevAddr==0x26:
                addr_crc=0x4c
            elif pcidevAddr==0x27:
                addr_crc=0x4e
                
            elif pcidevAddr==0x36:
                addr_crc=0x6c
            elif pcidevAddr==0x1A:
                addr_crc=0x34
            elif pcidevAddr==0x10:
                addr_crc=0x20
              
            if moduleName=='c2m' or moduleName == 'm2c':
                if self.dongle == 'stm32':
                    ret, values=self.stm32_dev.usbdev_i2c_muilt_get(1, self.stm32_bus_chan, self.devAddr, reg_addr, count)
            else:
                raise BaseException('unknown module')
            
        return values
        