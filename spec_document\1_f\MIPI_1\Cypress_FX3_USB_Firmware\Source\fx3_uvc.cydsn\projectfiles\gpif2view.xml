﻿<?xml version="1.0" encoding="UTF-8"?>
<Root version="4">
  <CyStates>
    <CyNormalState>
      <Left>230</Left>
      <Top>16.8466666666667</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE0</Name>
      <DisplayName>IDLE_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>847.4</Left>
      <Top>20.2</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE1</Name>
      <DisplayName>IDLE_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>407</Left>
      <Top>22.2</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE2</Name>
      <DisplayName>WAIT_FOR_FRAME_START_0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>655</Left>
      <Top>22.8</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE3</Name>
      <DisplayName>WAIT_FOR_FRAME_START_1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>425.6</Left>
      <Top>236.2</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE4</Name>
      <DisplayName>PUSH_DATA_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>656.6</Left>
      <Top>222.8</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE5</Name>
      <DisplayName>PUSH_DATA_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>54.2</Left>
      <Top>219.8</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE6</Name>
      <DisplayName>LINE_END_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>1023</Left>
      <Top>226.8</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE7</Name>
      <DisplayName>LINE_END_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>38</Left>
      <Top>380.8</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE8</Name>
      <DisplayName>WAIT_TO_FILL_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>855.4</Left>
      <Top>397.6</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE9</Name>
      <DisplayName>WAIT_TO_FILL_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>240.8</Left>
      <Top>396.6</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE10</Name>
      <DisplayName>WAIT_FULL_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>605.6</Left>
      <Top>371.2</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE11</Name>
      <DisplayName>WAIT_FULL_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>46.2</Left>
      <Top>491.2</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE12</Name>
      <DisplayName>PARTIAL_BUF_IN_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>825.4</Left>
      <Top>497.2</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE13</Name>
      <DisplayName>PARTIAL_BUF_IN_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>231.6</Left>
      <Top>493</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE14</Name>
      <DisplayName>FULL_BUF_IN_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>622.2</Left>
      <Top>479.6</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE15</Name>
      <DisplayName>FULL_BUF_IN_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>134.8</Left>
      <Top>618.2</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE16</Name>
      <DisplayName>FRAME_END_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyNormalState>
      <Left>724.8</Left>
      <Top>624.6</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STATE17</Name>
      <DisplayName>FRAME_END_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyNormalState>
    <CyStartState>
      <Left>29</Left>
      <Top>18.4466666666667</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STARTSTATE0</Name>
      <DisplayName>START_SCK0</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyStartState>
    <CyStartState>
      <Left>1021</Left>
      <Top>20.4</Top>
      <Width>83</Width>
      <Height>70</Height>
      <Name>STARTSTATE1</Name>
      <DisplayName>START_SCK1</DisplayName>
      <zIndex>1</zIndex>
      <IsGroup>False</IsGroup>
      <ParentID>00000000-0000-0000-0000-000000000000</ParentID>
    </CyStartState>
  </CyStates>
  <CyTransitions>
    <CyTransition>
      <Name>TRANSITION27</Name>
      <TransitionEquation>!FW_TRG</TransitionEquation>
      <SourceName>STATE17</SourceName>
      <SinkName>STATE0</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION26</Name>
      <TransitionEquation>!FW_TRG</TransitionEquation>
      <SourceName>STATE16</SourceName>
      <SinkName>STATE1</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION25</Name>
      <TransitionEquation>FW_TRG</TransitionEquation>
      <SourceName>STATE13</SourceName>
      <SinkName>STATE17</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION24</Name>
      <TransitionEquation>FW_TRG</TransitionEquation>
      <SourceName>STATE15</SourceName>
      <SinkName>STATE17</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION23</Name>
      <TransitionEquation>FW_TRG</TransitionEquation>
      <SourceName>STATE14</SourceName>
      <SinkName>STATE16</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION22</Name>
      <TransitionEquation>FW_TRG</TransitionEquation>
      <SourceName>STATE12</SourceName>
      <SinkName>STATE16</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION21</Name>
      <TransitionEquation>!FV</TransitionEquation>
      <SourceName>STATE9</SourceName>
      <SinkName>STATE13</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION20</Name>
      <TransitionEquation>!FV</TransitionEquation>
      <SourceName>STATE11</SourceName>
      <SinkName>STATE15</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION19</Name>
      <TransitionEquation>!FV</TransitionEquation>
      <SourceName>STATE10</SourceName>
      <SinkName>STATE14</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION18</Name>
      <TransitionEquation>!FV</TransitionEquation>
      <SourceName>STATE8</SourceName>
      <SinkName>STATE12</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION17</Name>
      <TransitionEquation>LV</TransitionEquation>
      <SourceName>STATE11</SourceName>
      <SinkName>STATE4</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION16</Name>
      <TransitionEquation>LV</TransitionEquation>
      <SourceName>STATE10</SourceName>
      <SinkName>STATE5</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION15</Name>
      <TransitionEquation>ADDR_CNT_HIT</TransitionEquation>
      <SourceName>STATE7</SourceName>
      <SinkName>STATE11</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION14</Name>
      <TransitionEquation>DATA_CNT_HIT</TransitionEquation>
      <SourceName>STATE6</SourceName>
      <SinkName>STATE10</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION13</Name>
      <TransitionEquation>LV</TransitionEquation>
      <SourceName>STATE9</SourceName>
      <SinkName>STATE5</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION12</Name>
      <TransitionEquation>LV</TransitionEquation>
      <SourceName>STATE8</SourceName>
      <SinkName>STATE4</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION11</Name>
      <TransitionEquation>!ADDR_CNT_HIT</TransitionEquation>
      <SourceName>STATE7</SourceName>
      <SinkName>STATE9</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION10</Name>
      <TransitionEquation>!DATA_CNT_HIT</TransitionEquation>
      <SourceName>STATE6</SourceName>
      <SinkName>STATE8</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION9</Name>
      <TransitionEquation>!LV</TransitionEquation>
      <SourceName>STATE5</SourceName>
      <SinkName>STATE7</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION8</Name>
      <TransitionEquation>!LV</TransitionEquation>
      <SourceName>STATE4</SourceName>
      <SinkName>STATE6</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION7</Name>
      <TransitionEquation>LV&amp;ADDR_CNT_HIT</TransitionEquation>
      <SourceName>STATE5</SourceName>
      <SinkName>STATE4</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION6</Name>
      <TransitionEquation>LV&amp;DATA_CNT_HIT</TransitionEquation>
      <SourceName>STATE4</SourceName>
      <SinkName>STATE5</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION5</Name>
      <TransitionEquation>FV&amp;LV</TransitionEquation>
      <SourceName>STATE3</SourceName>
      <SinkName>STATE5</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION4</Name>
      <TransitionEquation>FV&amp;LV</TransitionEquation>
      <SourceName>STATE2</SourceName>
      <SinkName>STATE4</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION3</Name>
      <TransitionEquation>!FV</TransitionEquation>
      <SourceName>STATE1</SourceName>
      <SinkName>STATE3</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION2</Name>
      <TransitionEquation>!FV</TransitionEquation>
      <SourceName>STATE0</SourceName>
      <SinkName>STATE2</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION1</Name>
      <TransitionEquation>LOGIC_ONE</TransitionEquation>
      <SourceName>STARTSTATE1</SourceName>
      <SinkName>STATE1</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
    <CyTransition>
      <Name>TRANSITION0</Name>
      <TransitionEquation>LOGIC_ONE</TransitionEquation>
      <SourceName>STARTSTATE0</SourceName>
      <SinkName>STATE0</SinkName>
      <SourceConnectorName>Connector</SourceConnectorName>
      <SinkConnectorName>Connector</SinkConnectorName>
      <SourceArrowSymbol>None</SourceArrowSymbol>
      <SinkArrowSymbol>Arrow</SinkArrowSymbol>
      <zIndex>0</zIndex>
    </CyTransition>
  </CyTransitions>
</Root>