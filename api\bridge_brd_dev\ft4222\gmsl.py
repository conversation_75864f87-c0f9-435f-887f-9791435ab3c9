# packages
import time
import ft4222_py3.QRegisterAccess1 as QRegisterAccess1

def initGpio():
    QRegisterAccess1.initGpio23(0, 1) # GPIO2 as RO output, GPIO3 as BNE input

def setROHigh():
    QRegisterAccess1.setGpio(2, 1)  # set RO as high 

def setROLow():
    QRegisterAccess1.setGpio(2, 0)  # set RO as low 

def isBNEHigh():
    return (QRegisterAccess1.getGpio(3) > 0) # BNE value

def assertRemoteSS0():
    setROHigh()
    QRegisterAccess1.spiGMSLInBandControl(0xa4)

def assertRemoteSS1():
    setROHigh()
    QRegisterAccess1.spiGMSLInBandControl(0xa5)

def deassertRemoteAllSS():
    setROHigh()
    QRegisterAccess1.spiGMSLInBandControl(0xa6)

def send0xFFToRemote():
    setROHigh()
    QRegisterAccess1.spiGMSLInBandControl(0xa7)

def readMcp2515(addr:int):
    # assert remote spi ss0
    assertRemoteSS0()
    # assert RO low to start write
    setROLow()
    # toggle local spi waveform
    print("Start to read register: 0x%x" % (addr))
    QRegisterAccess1.spiSendReadCmdToMcp2515(addr)
    # finish write
    setROHigh()
    # deassert remote spi ss
    deassertRemoteAllSS()

    delayCount = 0
    while True:
        time.sleep(0.01) #delay 10ms
        delayCount = delayCount + 1
        if delayCount > 20:
            break
        if isBNEHigh():
            print("    read value is: 0x%x" % (QRegisterAccess1.spiGMSLReadLocal()))

def ReadDACReg(addr:int):
    setROLow()
    # assert remote spi ss0
    setROHigh()
    # QRegisterAccess1.spiGMSLInBandControl(0xa0)
    QRegisterAccess1.spiGMSLInBandControl(0xa5)
    # assert RO low to start write
    setROLow()
    # toggle local spi waveform
    print("Start to write register: 0x%x = 0x%x" % (addr, val)) 
    QRegisterAccess1.spiSendWriteCmdToDAC(addr, val)            
    # finish write
    setROHigh()
    # deassert remote spi ss
    deassertRemoteAllSS()
    
    delayCount = 0
    while True:
        time.sleep(0.01) #delay 10ms
        delayCount = delayCount + 1
        if delayCount > 20:
            break
        if isBNEHigh():
            print("    read value is: 0x%x" % (QRegisterAccess1.spiC3ReadLocal()))
    
def writeDACReg(addr:int, val:int):
    # assert remote spi ss0
    setROLow()
    setROHigh()
    # QRegisterAccess1.spiGMSLInBandControl(0xa0)
    QRegisterAccess1.spiGMSLInBandControl(0xa5)
    setROLow()
    time.sleep(0.5)
    # toggle local spi waveform
    print("Start to write register: 0x%x = 0x%x" % (addr, val))
    QRegisterAccess1.spiSendWriteCmdToDAC(addr, val)
    time.sleep(2)
    # finish write
    setROHigh()
    # deassert remote spi ss
    
    delayCount = 0
    while True:
        time.sleep(0.1) #delay 10ms
        delayCount = delayCount + 1
        if delayCount > 20:
            break
        print('bne is:', isBNEHigh())
        if isBNEHigh():
            
            print("    local buffer value is: " , hex(QRegisterAccess1.spiC3ReadLocal()))
    setROLow()
    deassertRemoteAllSS()
            
def writeMcp2515(addr:int, val:int):
    # assert remote spi ss0
    assertRemoteSS0()
    # assert RO low to start write
    setROLow()
    # toggle local spi waveform
    print("Start to write register: 0x%x = 0x%x" % (addr, val))
    QRegisterAccess1.spiSendWriteCmdToMcp2515(addr, val)
    QRegisterAccess1.spiSendWriteCmdToMcp2515(addr, val)
    time.sleep(1)
    # finish write
    setROHigh()
    # deassert remote spi ss
    deassertRemoteAllSS()

    delayCount = 0
    while True:
        time.sleep(0.1) #delay 10ms
        delayCount = delayCount + 1
        if delayCount > 20:
            break
        if isBNEHigh():
            print("    local buffer value is: 0x%x" % (QRegisterAccess1.spiGMSLReadLocal()))

if QRegisterAccess1.findMeritechDACSpiChan():
    
    initGpio()
    # QRegisterAccess1.spiSendWriteCmdToDAC(addr=0x23, value=0x010203040506070809)
    for i in range(100):
        writeDACReg(0x23, 0x010203040506070809)
        time.sleep(1)
    print ('done')
    
    
    

# if QRegisterAccess1.findSpiChan(div = QRegisterAccess1.SPI_MASTER_CLK_DIV16):
#
#     # read test
#     for i in range(10):
#         print("read  reg 0x%02x = 0x%02x"%(i, QRegisterAccess1.spiReadMcp2515Reg(i)))
#
#     # read-write test
#     for i in range(10):
#         print("write reg 0x%02x = 0x%02x"%(0x00, i))
#         QRegisterAccess1.spiWriteMcp2515Reg(0x00, i)
#         print("read reg  0x%02x = 0x%02x"%(0x00, QRegisterAccess1.spiReadMcp2515Reg(0x00)))
#
#     exit(0)
#
#     initGpio()
#     for i in range(10):
#         readMcp2515(i)




