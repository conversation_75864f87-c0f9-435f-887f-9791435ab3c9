#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import socket
import time
import threading
from HWInterface import Recv<PERSON><PERSON><PERSON><PERSON>
from threading import Lock

ip_addr = "*************"
PORT = 4444
is_exit: bool = False
netdev_Lock = Lock()
client_socket = None


def is_socket_connected(sock):
    return (sock != None) and (sock.fileno() != -1)


def connect_to_server(server_ip, server_port):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        s.connect((server_ip, server_port))
        s.settimeout(2)  # 设置超时时间为1秒
        return s
    except ConnectionRefusedError as e:
        print(e)
        return None
    except OSError as e:
        print(e)
        return None

# 等待服务器链接
def check_server(server_ip, server_port):
    sock = None

    print("wait connect server....")
    while not is_exit:
        sock = connect_to_server(server_ip, server_port)
        if sock is not None:
            print("连接成功")
            break
        else:
            print("连接失败，等待3秒后重试...")
            time.sleep(3)

    return sock


def check_cmd(cmd_buff: bytearray, cmd_len: int):
    return True


# 处理完整指令
def deal_cmd(cmd_buff: bytearray, cmd_len: int):
    if not check_cmd(cmd_buff, cmd_len):
        return False

    RecvDealCenter.publish_cmd(cmd_buff, cmd_len)


cmd_data = []
cmd_idx: int = 0


# 解析数据缓冲区
def deal_cmd_buff(data_all):
    global cmd_idx
    global cmd_data

    for byte in data_all:
        if cmd_idx != 0:
            cmd_data.append(byte)
            cmd_idx += 1
            # 判断cmd_data中是否存储了一条完整的cmd数据，如果是一条完整的数据，就进行cmd处理。
            if cmd_data[cmd_idx - 1] == 0xA5 and cmd_data[cmd_idx - 2] == 0xA5:
                hex_data = ' '.join(f'{byte:02x}' for byte in bytearray(cmd_data))
                # print("tcp_recv_cmd_buff:", hex_data)
                deal_cmd(bytearray(cmd_data), cmd_idx)
                cmd_data.clear()
                cmd_idx = 0

            # 当接收到1024个字节，还是没有找到cmd结束标记，说明数据已经出问题了，这里直接将数据丢弃，重新解析。
            if cmd_idx >= 1024:
                cmd_idx = 0
        else:
            # 一条新的数据，必须由AA开头，否则就丢弃异常数据。
            if byte == 0xAA:
                cmd_data.append(byte)
                cmd_idx += 1


# 接收net传递过来的数据，放入到缓冲区中
def net_receive_data_thread():
    global client_socket
    print("Net Port Receive Thread Start....")
    while not is_exit:
        try:
            while is_socket_connected(client_socket):
                # 获取当前可读的数据
                datas = client_socket.recv(1024)

                # 如果长度为0,就是远程断开
                if len(datas) == 0:
                    break

                deal_cmd_buff(datas)

            print("Net socket invalid....")
            client_socket = None
        except socket.timeout:
            print("Net socket timeout ....")
            client_socket = None
            time.sleep(0.5)
        except ConnectionResetError:
            print("Net socket ResetError ....")
            client_socket = None
            time.sleep(0.5)

        if not is_socket_connected(client_socket):
            # 如果链接终端，就等待链接恢复
            client_socket = check_server(ip_addr, PORT)

    print("net Port Receive Thread Exit....\n")


# 打开设备
def network_open(ipaddr):
    global client_socket
    global is_exit
    global ip_addr

    ip_addr = ipaddr
    client_socket = connect_to_server(ip_addr, PORT)
    if is_socket_connected(client_socket):  # 判断网络是否成功打开
        print("打开网络成功: ", ip_addr, PORT)  # 输出网络端口
    else:
        print("打开网络失败: ", ip_addr, PORT)  # 输出网络端口

    is_exit = False
    run_thread = threading.Thread(target=net_receive_data_thread, name="network_receive")
    # 设置为daemon线程，程序在退出的时候，线程自动退出
    run_thread.daemon = True
    run_thread.start()

    return 0


# 关闭设备
def network_close():
    global is_exit

    time.sleep(0.1)
    is_exit = True
    time.sleep(0.1)
    if is_socket_connected(client_socket):
        client_socket.close()
        print("关闭网络成功: ", ip_addr)  # 输出串口号
    else:
        print("网络已处于关闭状态: ", ip_addr)


# 发送函数
def net_send_buff(data):
    if not is_socket_connected(client_socket):    # 串口是打开的，才进行数据传递
        print("net socket is not connected")
        return -1
    netdev_Lock.acquire()
    client_socket.send(data)
    netdev_Lock.release()
    return 0
