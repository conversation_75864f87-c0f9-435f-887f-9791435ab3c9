address,default,name
0x00000000,0x00000000,m2c_sys_cfg_dev_info0
0x00000001,0x00000000,m2c_sys_cfg_dev_info1
0x00000002,0x00000000,m2c_sys_cfg_dev
0x00000003,0x00000007,m2c_sys_cfg_i2c_uart
0x00000004,0x00000000,m2c_sys_cfg_link_ctrl
0x00000005,0x00000000,m2c_sys_cfg_mipi_ctrl
0x00000006,0x00000001,m2c_sys_cfg_video_ctrl
0x00000020,0x00000000,m2c_sys_cfg_power_save
0x00000060,0x00000000,m2c_sys_cfg_link_status
0x00000070,0x00000001,m2c_rcc_ck_tx_ctrl
0x00000071,0x00000001,m2c_rcc_ck_rx_ctrl
0x00000073,0x00000003,m2c_rcc_ck_video_ctrl
0x00000074,0x00000000,m2c_rcc_ck_peri_ctrl
0x00000075,0x00000002,m2c_rcc_ck_mipi
0x00000078,0x00000001,m2c_rcc_rst_glb
0x00000079,0x00000003,m2c_rcc_rst_logic
0x0000007a,0x00000003,m2c_rcc_rst_link
0x0000007b,0x00000000,m2c_rcc_pll_status
0x0000007c,0x00000000,m2c_rcc_xtal_status
0x0000007d,0x00000000,m2c_rcc_v_clk_det_ctrl
0x0000007e,0x00000000,m2c_rcc_v_clk_det_config0
0x0000007f,0x00000000,m2c_rcc_v_clk_det_config1
0x00000080,0x00000000,m2c_rcc_spi_mst_clk_ctrl0
0x00000081,0x0000007f,m2c_rcc_spi_mst_clk_ctrl1
0x00000090,0x00000000,m2c_pinmux_pin_io_ow_en_0
0x00000091,0x000000ff,m2c_pinmux_pin_io_ow_en_1
0x00000092,0x000000ff,m2c_pinmux_pin_io_ow_en_2
0x00000093,0x00000000,m2c_pinmux_pin_io_ow_en_3
0x00000094,0x00000000,m2c_pinmux_pin_io_ow_en_4
0x00000095,0x00000000,m2c_pinmux_pin_io_ow_en_5
0x00000096,0x00000000,m2c_pinmux_pin_io_ow_en_6
0x00000097,0x00000000,m2c_pinmux_pin_io_ow_en_7
0x00000098,0x00000000,m2c_pinmux_pin_io_ow_en_8
0x00000099,0x00000000,m2c_pinmux_pin_io_ow_en_9
0x0000009a,0x00000000,m2c_pinmux_pin_io_ow_en_10
0x0000009b,0x00000001,m2c_pinmux_pin_io_ow_ctrl_0
0x0000009c,0x00000050,m2c_pinmux_pin_io_ow_ctrl_1
0x0000009d,0x00000050,m2c_pinmux_pin_io_ow_ctrl_2
0x0000009e,0x00000001,m2c_pinmux_pin_io_ow_ctrl_3
0x0000009f,0x00000001,m2c_pinmux_pin_io_ow_ctrl_4
0x000000a0,0x00000001,m2c_pinmux_pin_io_ow_ctrl_5
0x000000a1,0x00000001,m2c_pinmux_pin_io_ow_ctrl_6
0x000000a2,0x00000001,m2c_pinmux_pin_io_ow_ctrl_7
0x000000a3,0x00000001,m2c_pinmux_pin_io_ow_ctrl_8
0x000000a4,0x00000001,m2c_pinmux_pin_io_ow_ctrl_9
0x000000a5,0x00000001,m2c_pinmux_pin_io_ow_ctrl_10
0x000000a6,0x00000000,m2c_pinmux_mixb_io_ow_en[0]
0x000000a7,0x00000000,m2c_pinmux_mixb_io_ow_en[1]
0x000000a8,0x00000000,m2c_pinmux_mixb_io_ow_en[2]
0x000000a9,0x00000000,m2c_pinmux_mixb_io_ow_en[3]
0x000000aa,0x00000000,m2c_pinmux_mixb_io_ow_en[4]
0x000000ab,0x00000000,m2c_pinmux_mixb_io_ow_en[5]
0x000000ac,0x00000000,m2c_pinmux_mixb_io_ow_en[6]
0x000000ad,0x00000000,m2c_pinmux_mixb_io_ow_en[7]
0x000000ae,0x00000000,m2c_pinmux_mixb_io_ow_en[8]
0x000000af,0x00000000,m2c_pinmux_mixb_io_ow_en[9]
0x000000b0,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[0]
0x000000b1,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[1]
0x000000b2,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[2]
0x000000b3,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[3]
0x000000b4,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[4]
0x000000b5,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[5]
0x000000b6,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[6]
0x000000b7,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[7]
0x000000b8,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[8]
0x000000b9,0x00000000,m2c_pinmux_mixb_io_ow_ctrl[9]
0x000000ba,0x00000000,m2c_pinmux_pin_ctrl0
0x000000bb,0x00000000,m2c_pinmux_pin_ctrl1
0x000000bc,0x00000000,m2c_pinmux_pin_ctrl2
0x000000bd,0x00000000,m2c_pinmux_pin_ctrl3
0x000000be,0x00000000,m2c_pinmux_pin_ctrl4
0x000000bf,0x00000000,m2c_pinmux_pin_ctrl5
0x000000c0,0x00000000,m2c_pinmux_pin_ctrl6
0x000000c1,0x00000000,m2c_pinmux_pin_ctrl7
0x000000c2,0x00000000,m2c_pinmux_pin_ctrl8
0x000000c3,0x00000001,m2c_pinmux_pin_ctrl9
0x000000c4,0x00000001,m2c_pinmux_pin_ctrl10
0x000000c5,0x00000000,m2c_pinmux_parallel_pin_ie
0x00000100,0x00000027,m2c_i2c0_ctrl0
0x00000101,0x00000055,m2c_i2c0_ctrl1
0x00000102,0x00000077,m2c_i2c0_ctrl2
0x00000103,0x00000007,m2c_i2c0_ctrl3
0x00000104,0x00000000,m2c_i2c0_ctrl4[0]
0x00000105,0x00000000,m2c_i2c0_ctrl4[1]
0x00000109,0x0000007f,m2c_i2c0_ctrl5[0]
0x0000010a,0x0000007f,m2c_i2c0_ctrl5[1]
0x0000010e,0x00000003,m2c_i2c0_ctrl6
0x00000112,0x00000000,m2c_i2c0_pkt_cnt0[0]
0x00000113,0x00000000,m2c_i2c0_pkt_cnt0[1]
0x00000117,0x00000000,m2c_i2c0_pkt_cnt1[0]
0x00000118,0x00000000,m2c_i2c0_pkt_cnt1[1]
0x0000011c,0x00000000,m2c_i2c0_dev_addr_tran0
0x00000120,0x00000000,m2c_i2c0_dev_addr_tran1
0x00000124,0x00000000,m2c_i2c0_dev_addr_tran2
0x00000128,0x00000000,m2c_i2c0_dev_addr_tran3
0x00000140,0x00000027,m2c_i2c1_ctrl0
0x00000141,0x00000055,m2c_i2c1_ctrl1
0x00000142,0x00000077,m2c_i2c1_ctrl2
0x00000143,0x00000007,m2c_i2c1_ctrl3
0x00000144,0x00000000,m2c_i2c1_ctrl4[0]
0x00000145,0x00000000,m2c_i2c1_ctrl4[1]
0x00000149,0x0000007f,m2c_i2c1_ctrl5[0]
0x0000014a,0x0000007f,m2c_i2c1_ctrl5[1]
0x0000014e,0x00000003,m2c_i2c1_ctrl6
0x00000152,0x00000000,m2c_i2c1_pkt_cnt0[0]
0x00000153,0x00000000,m2c_i2c1_pkt_cnt0[1]
0x00000157,0x00000000,m2c_i2c1_pkt_cnt1[0]
0x00000158,0x00000000,m2c_i2c1_pkt_cnt1[1]
0x0000015c,0x00000000,m2c_i2c1_dev_addr_tran0
0x00000160,0x00000000,m2c_i2c1_dev_addr_tran1
0x00000164,0x00000000,m2c_i2c1_dev_addr_tran2
0x00000168,0x00000000,m2c_i2c1_dev_addr_tran3
0x00000180,0x00000027,m2c_i2c2_ctrl0
0x00000181,0x00000055,m2c_i2c2_ctrl1
0x00000182,0x00000077,m2c_i2c2_ctrl2
0x00000183,0x00000007,m2c_i2c2_ctrl3
0x00000184,0x00000000,m2c_i2c2_ctrl4[0]
0x00000185,0x00000000,m2c_i2c2_ctrl4[1]
0x00000189,0x0000007f,m2c_i2c2_ctrl5[0]
0x0000018a,0x0000007f,m2c_i2c2_ctrl5[1]
0x0000018e,0x00000003,m2c_i2c2_ctrl6
0x00000192,0x00000000,m2c_i2c2_pkt_cnt0[0]
0x00000193,0x00000000,m2c_i2c2_pkt_cnt0[1]
0x00000197,0x00000000,m2c_i2c2_pkt_cnt1[0]
0x00000198,0x00000000,m2c_i2c2_pkt_cnt1[1]
0x0000019c,0x00000000,m2c_i2c2_dev_addr_tran0
0x000001a0,0x00000000,m2c_i2c2_dev_addr_tran1
0x000001a4,0x00000000,m2c_i2c2_dev_addr_tran2
0x000001a8,0x00000000,m2c_i2c2_dev_addr_tran3
0x00000200,0x00000000,m2c_uart0_ctrl0
0x00000201,0x00000000,m2c_uart0_ctrl1
0x00000202,0x00000092,m2c_uart0_ck_ctrl0
0x00000203,0x000000df,m2c_uart0_ck_ctrl1
0x00000208,0x00000000,m2c_uart0_rt_pkt_counter0
0x00000209,0x00000000,m2c_uart0_rt_pkt_counter1
0x0000020a,0x00000000,m2c_uart0_tr_pkt_counter0
0x0000020b,0x00000000,m2c_uart0_tr_pkt_counter1
0x00000220,0x00000000,m2c_uart1_ctrl0
0x00000221,0x00000000,m2c_uart1_ctrl1
0x00000222,0x00000092,m2c_uart1_ck_ctrl0
0x00000223,0x000000df,m2c_uart1_ck_ctrl1
0x00000228,0x00000000,m2c_uart1_rt_pkt_counter0
0x00000229,0x00000000,m2c_uart1_rt_pkt_counter1
0x0000022a,0x00000000,m2c_uart1_tr_pkt_counter0
0x0000022b,0x00000000,m2c_uart1_tr_pkt_counter1
0x00000240,0x00000000,m2c_uart2_ctrl0
0x00000241,0x00000000,m2c_uart2_ctrl1
0x00000242,0x00000092,m2c_uart2_ck_ctrl0
0x00000243,0x000000df,m2c_uart2_ck_ctrl1
0x00000248,0x00000000,m2c_uart2_rt_pkt_counter0
0x00000249,0x00000000,m2c_uart2_rt_pkt_counter1
0x0000024a,0x00000000,m2c_uart2_tr_pkt_counter0
0x0000024b,0x00000000,m2c_uart2_tr_pkt_counter1
0x00000300,0x00000000,m2c_spi0_ctrl0
0x00000301,0x00000000,m2c_spi0_ctrl1
0x00000302,0x00000000,m2c_spi0_ctrl2
0x00000303,0x00000000,m2c_spi0_ctrl3
0x00000304,0x00000000,m2c_spi0_ctrl4
0x00000305,0x00000000,m2c_spi0_ctrl5
0x00000306,0x00000000,m2c_spi0_ctrl6
0x00000400,0x00000000,m2c_tx_link_phy_phy_ctrl0
0x00000401,0x00000008,m2c_tx_link_phy_sync_ctrl0
0x00000402,0x00000000,m2c_tx_link_phy_sync_ctrl1
0x00000403,0x00000000,m2c_tx_link_phy_sync_ctrl2
0x00000404,0x00000000,m2c_tx_link_phy_comp_ctrl0
0x00000405,0x00000000,m2c_tx_link_phy_comp_ctrl1
0x00000406,0x00000000,m2c_tx_link_phy_comp_ctrl2
0x00000407,0x00000002,m2c_tx_link_phy_fec_ctrl0
0x00000408,0x00000002,m2c_tx_link_phy_psch_ctrl0
0x00000409,0x00000000,m2c_tx_link_phy_ipg_ctrl0
0x0000040a,0x00000040,m2c_tx_link_phy_ipg_ctrl1
0x0000040b,0x0000007f,m2c_tx_link_phy_idle_ctrl0
0x0000040c,0x0000000c,m2c_tx_link_phy_errinj_ctrl0
0x0000040d,0x00000000,m2c_tx_link_phy_errinj_ctrl1
0x0000040e,0x00000019,m2c_tx_link_phy_ana_ctrl0
0x0000040f,0x00000019,m2c_tx_link_phy_ana_ctrl1
0x00000410,0x000000d8,m2c_tx_link_phy_ana_ctrl2
0x00000411,0x00000090,m2c_tx_link_phy_ana_ctrl3
0x00000412,0x000000c4,m2c_tx_link_phy_ana_ctrl4
0x00000413,0x00000039,m2c_tx_link_phy_ana_ctrl5
0x00000414,0x00000039,m2c_tx_link_phy_ana_ctrl6
0x00000415,0x00000019,m2c_tx_link_phy_ana_ctrl7
0x00000416,0x000000c4,m2c_tx_link_phy_ana_ctrl8
0x00000417,0x00000003,m2c_tx_link_phy_ana_ctrl9
0x00000418,0x00000019,m2c_tx_link_phy_ana_ctrl10
0x00000419,0x00000019,m2c_tx_link_phy_ana_ctrl11
0x0000041a,0x000000c4,m2c_tx_link_phy_ana_ctrl12
0x0000041b,0x00000019,m2c_tx_link_phy_ana_ctrl13
0x0000041c,0x00000019,m2c_tx_link_phy_ana_ctrl14
0x0000041d,0x00000099,m2c_tx_link_phy_ana_ctrl15
0x0000041e,0x00000008,m2c_tx_link_phy_ana_ctrl16
0x0000041f,0x000000ff,m2c_tx_link_phy_ana_ctrl17
0x00000420,0x00000007,m2c_tx_link_phy_ana_ctrl18
0x00000430,0x00000003,m2c_tx_link_i2c0_ctrl0
0x00000431,0x00000028,m2c_tx_link_i2c0_ctrl1
0x00000432,0x00000010,m2c_tx_link_i2c0_ctrl2
0x00000433,0x00000000,m2c_tx_link_i2c0_ctrl3
0x00000434,0x00000001,m2c_tx_link_i2c0_ctrl4
0x00000435,0x00000000,m2c_tx_link_i2c0_ctrl5
0x00000436,0x00000000,m2c_tx_link_i2c0_ctrl6
0x00000437,0x00000000,m2c_tx_link_i2c0_ctrl7
0x00000438,0x00000000,m2c_tx_link_i2c0_status0
0x0000043c,0x00000003,m2c_tx_link_i2c1_ctrl0
0x0000043d,0x00000028,m2c_tx_link_i2c1_ctrl1
0x0000043e,0x00000015,m2c_tx_link_i2c1_ctrl2
0x0000043f,0x00000000,m2c_tx_link_i2c1_ctrl3
0x00000440,0x00000001,m2c_tx_link_i2c1_ctrl4
0x00000441,0x00000000,m2c_tx_link_i2c1_ctrl5
0x00000442,0x00000000,m2c_tx_link_i2c1_ctrl6
0x00000443,0x00000000,m2c_tx_link_i2c1_ctrl7
0x00000444,0x00000000,m2c_tx_link_i2c1_status0
0x00000448,0x00000003,m2c_tx_link_i2c2_ctrl0
0x00000449,0x00000028,m2c_tx_link_i2c2_ctrl1
0x0000044a,0x0000001a,m2c_tx_link_i2c2_ctrl2
0x0000044b,0x00000000,m2c_tx_link_i2c2_ctrl3
0x0000044c,0x00000001,m2c_tx_link_i2c2_ctrl4
0x0000044d,0x00000000,m2c_tx_link_i2c2_ctrl5
0x0000044e,0x00000000,m2c_tx_link_i2c2_ctrl6
0x0000044f,0x00000000,m2c_tx_link_i2c2_ctrl7
0x00000450,0x00000000,m2c_tx_link_i2c2_status0
0x00000454,0x00000003,m2c_tx_link_i2c_ack_ctrl0
0x00000455,0x00000000,m2c_tx_link_i2c_ack_ctrl1
0x00000456,0x00000000,m2c_tx_link_i2c_ack_ctrl2
0x00000457,0x00000000,m2c_tx_link_i2c_ack_ctrl3
0x00000458,0x00000000,m2c_tx_link_i2c_ack_status0
0x0000045c,0x00000003,m2c_tx_link_uart0_ctrl0
0x0000045d,0x00000028,m2c_tx_link_uart0_ctrl1
0x0000045e,0x00000010,m2c_tx_link_uart0_ctrl2
0x0000045f,0x00000000,m2c_tx_link_uart0_ctrl3
0x00000460,0x00000001,m2c_tx_link_uart0_ctrl4
0x00000461,0x00000000,m2c_tx_link_uart0_ctrl5
0x00000462,0x00000000,m2c_tx_link_uart0_ctrl6
0x00000463,0x00000000,m2c_tx_link_uart0_ctrl7
0x00000464,0x00000000,m2c_tx_link_uart0_status0
0x00000468,0x00000003,m2c_tx_link_uart1_ctrl0
0x00000469,0x00000028,m2c_tx_link_uart1_ctrl1
0x0000046a,0x00000015,m2c_tx_link_uart1_ctrl2
0x0000046b,0x00000000,m2c_tx_link_uart1_ctrl3
0x0000046c,0x00000001,m2c_tx_link_uart1_ctrl4
0x0000046d,0x00000000,m2c_tx_link_uart1_ctrl5
0x0000046e,0x00000000,m2c_tx_link_uart1_ctrl6
0x0000046f,0x00000000,m2c_tx_link_uart1_ctrl7
0x00000470,0x00000000,m2c_tx_link_uart1_status0
0x00000474,0x00000003,m2c_tx_link_uart2_ctrl0
0x00000475,0x00000028,m2c_tx_link_uart2_ctrl1
0x00000476,0x0000001a,m2c_tx_link_uart2_ctrl2
0x00000477,0x00000000,m2c_tx_link_uart2_ctrl3
0x00000478,0x00000001,m2c_tx_link_uart2_ctrl4
0x00000479,0x00000000,m2c_tx_link_uart2_ctrl5
0x0000047a,0x00000000,m2c_tx_link_uart2_ctrl6
0x0000047b,0x00000000,m2c_tx_link_uart2_ctrl7
0x0000047c,0x00000000,m2c_tx_link_uart2_status0
0x00000480,0x00000003,m2c_tx_link_uart_ack_ctrl0
0x00000481,0x00000000,m2c_tx_link_uart_ack_ctrl1
0x00000482,0x00000000,m2c_tx_link_uart_ack_ctrl2
0x00000483,0x00000000,m2c_tx_link_uart_ack_ctrl3
0x00000484,0x00000000,m2c_tx_link_uart_ack_status0
0x00000488,0x00000003,m2c_tx_link_spi_ctrl0
0x00000489,0x00000028,m2c_tx_link_spi_ctrl1
0x0000048a,0x00000010,m2c_tx_link_spi_ctrl2
0x0000048b,0x00000000,m2c_tx_link_spi_ctrl3
0x0000048c,0x00000001,m2c_tx_link_spi_ctrl4
0x0000048d,0x00000000,m2c_tx_link_spi_ctrl5
0x0000048e,0x00000000,m2c_tx_link_spi_ctrl6
0x0000048f,0x00000000,m2c_tx_link_spi_ctrl7
0x00000490,0x00000000,m2c_tx_link_spi_status0
0x00000494,0x00000003,m2c_tx_link_spi_ack_ctrl0
0x00000495,0x00000000,m2c_tx_link_spi_ack_ctrl1
0x00000496,0x00000000,m2c_tx_link_spi_ack_ctrl2
0x00000497,0x00000000,m2c_tx_link_spi_ack_ctrl3
0x00000498,0x00000000,m2c_tx_link_spi_ack_status0
0x0000049c,0x00000003,m2c_tx_link_gpio_ctrl0
0x0000049d,0x00000028,m2c_tx_link_gpio_ctrl1
0x0000049e,0x00000000,m2c_tx_link_gpio_ctrl2
0x0000049f,0x00000000,m2c_tx_link_gpio_ctrl3
0x000004a0,0x00000001,m2c_tx_link_gpio_ctrl4
0x000004a1,0x00000000,m2c_tx_link_gpio_ctrl5
0x000004a2,0x00000000,m2c_tx_link_gpio_ctrl6
0x000004a3,0x00000000,m2c_tx_link_gpio_ctrl7
0x000004a4,0x00000000,m2c_tx_link_gpio_status0
0x000004a8,0x00000003,m2c_tx_link_gpio_ack_ctrl0
0x000004a9,0x00000000,m2c_tx_link_gpio_ack_ctrl1
0x000004aa,0x00000000,m2c_tx_link_gpio_ack_ctrl2
0x000004ab,0x00000000,m2c_tx_link_gpio_ack_ctrl3
0x000004ac,0x00000000,m2c_tx_link_gpio_ack_status0
0x000004b0,0x00000003,m2c_tx_link_i2s_ctrl0
0x000004b1,0x00000028,m2c_tx_link_i2s_ctrl1
0x000004b2,0x00000000,m2c_tx_link_i2s_ctrl2
0x000004b3,0x00000000,m2c_tx_link_i2s_ctrl3
0x000004b4,0x00000002,m2c_tx_link_i2s_ctrl4
0x000004b5,0x00000000,m2c_tx_link_i2s_ctrl5
0x000004b6,0x00000000,m2c_tx_link_i2s_ctrl6
0x000004b7,0x00000000,m2c_tx_link_i2s_ctrl7
0x000004b8,0x00000000,m2c_tx_link_i2s_status0
0x000004bc,0x00000003,m2c_tx_link_i2s_ack_ctrl0
0x000004bd,0x00000000,m2c_tx_link_i2s_ack_ctrl1
0x000004be,0x00000000,m2c_tx_link_i2s_ack_ctrl2
0x000004bf,0x00000000,m2c_tx_link_i2s_ack_ctrl3
0x000004c0,0x00000000,m2c_tx_link_i2s_ack_status0
0x000004c5,0x00000002,m2c_tx_link_video_ctrl0
0x000004c6,0x00000000,m2c_tx_link_video_ctrl1
0x000004c7,0x00000000,m2c_tx_link_video_ctrl2
0x000004c8,0x00000000,m2c_tx_link_video_ctrl3
0x000004c9,0x00000001,m2c_tx_link_video_ctrl4
0x000004ca,0x00000000,m2c_tx_link_video_ctrl5
0x000004cb,0x00000000,m2c_tx_link_video_ctrl6
0x000004cc,0x00000000,m2c_tx_link_video_status0
0x00000501,0x00000080,m2c_rx_link_phy_sync_ctrl0
0x00000502,0x00000000,m2c_rx_link_phy_sync_ctrl1
0x00000503,0x00000020,m2c_rx_link_phy_sync_ctrl2
0x00000504,0x00000004,m2c_rx_link_phy_sync_ctrl3
0x00000505,0x00000000,m2c_rx_link_phy_sync_ctrl4
0x00000509,0x00000010,m2c_rx_link_phy_idle_ctrl0
0x0000050a,0x00000003,m2c_rx_link_phy_dec_ctrl0
0x0000050b,0x00000005,m2c_rx_link_phy_dec_ctrl1
0x0000050c,0x00000000,m2c_rx_link_phy_dec_ctrl2
0x0000050d,0x00000005,m2c_rx_link_phy_dec_ctrl3
0x00000520,0x00000007,m2c_rx_link_pp_i2c_ctrl0
0x00000521,0x000000ff,m2c_rx_link_pp_i2c_ctrl1
0x00000522,0x00000007,m2c_rx_link_pp_uart_ctrl0
0x00000523,0x000000ff,m2c_rx_link_pp_uart_ctrl1
0x00000524,0x00000003,m2c_rx_link_pp_spi_ctrl0
0x00000525,0x000000ff,m2c_rx_link_pp_spi_ctrl1
0x00000526,0x00000003,m2c_rx_link_pp_gpio_ctrl0
0x00000527,0x000000ff,m2c_rx_link_pp_gpio_ctrl1
0x00000528,0x00000007,m2c_rx_link_pp_i2s_ctrl0
0x00000529,0x000000ff,m2c_rx_link_pp_i2s_ctrl1
0x0000052a,0x00000001,m2c_rx_link_pp_timeout_ctrl0
0x0000052b,0x000000ff,m2c_rx_link_pp_timeout_ctrl1
0x0000052c,0x000000ff,m2c_rx_link_pp_timeout_ctrl2
0x00000600,0x00000000,m2c_gpios_ctrl0
0x00000601,0x00000000,m2c_gpios_ctrl1
0x00000610,0x00000091,m2c_gpios_gpio_a_0
0x00000611,0x000000a0,m2c_gpios_gpio_b_0
0x00000612,0x00000000,m2c_gpios_gpio_c_0
0x00000613,0x00000000,m2c_gpios_gpio_d_0
0x00000614,0x00000081,m2c_gpios_gpio_a_1
0x00000615,0x00000021,m2c_gpios_gpio_b_1
0x00000616,0x00000001,m2c_gpios_gpio_c_1
0x00000617,0x00000000,m2c_gpios_gpio_d_1
0x00000618,0x00000091,m2c_gpios_gpio_a_2
0x00000619,0x00000022,m2c_gpios_gpio_b_2
0x0000061a,0x00000002,m2c_gpios_gpio_c_2
0x0000061b,0x00000000,m2c_gpios_gpio_d_2
0x0000061c,0x00000081,m2c_gpios_gpio_a_3
0x0000061d,0x000000a3,m2c_gpios_gpio_b_3
0x0000061e,0x00000003,m2c_gpios_gpio_c_3
0x0000061f,0x00000000,m2c_gpios_gpio_d_3
0x00000620,0x00000091,m2c_gpios_gpio_a_4
0x00000621,0x000000a4,m2c_gpios_gpio_b_4
0x00000622,0x00000004,m2c_gpios_gpio_c_4
0x00000623,0x00000000,m2c_gpios_gpio_d_4
0x00000624,0x00000081,m2c_gpios_gpio_a_5
0x00000625,0x000000a5,m2c_gpios_gpio_b_5
0x00000626,0x00000005,m2c_gpios_gpio_c_5
0x00000627,0x00000000,m2c_gpios_gpio_d_5
0x00000628,0x00000091,m2c_gpios_gpio_a_6
0x00000629,0x000000a6,m2c_gpios_gpio_b_6
0x0000062a,0x00000006,m2c_gpios_gpio_c_6
0x0000062b,0x00000000,m2c_gpios_gpio_d_6
0x0000062c,0x00000083,m2c_gpios_gpio_a_7
0x0000062d,0x000000a7,m2c_gpios_gpio_b_7
0x0000062e,0x00000007,m2c_gpios_gpio_c_7
0x0000062f,0x00000010,m2c_gpios_gpio_d_7
0x00000630,0x00000094,m2c_gpios_gpio_a_8
0x00000631,0x00000028,m2c_gpios_gpio_b_8
0x00000632,0x00000008,m2c_gpios_gpio_c_8
0x00000633,0x00000000,m2c_gpios_gpio_d_8
0x00000634,0x00000081,m2c_gpios_gpio_a_9
0x00000635,0x000000a9,m2c_gpios_gpio_b_9
0x00000636,0x00000009,m2c_gpios_gpio_c_9
0x00000637,0x00000000,m2c_gpios_gpio_d_9
0x00000638,0x00000091,m2c_gpios_gpio_a_10
0x00000639,0x0000002a,m2c_gpios_gpio_b_10
0x0000063a,0x0000000a,m2c_gpios_gpio_c_10
0x0000063b,0x00000000,m2c_gpios_gpio_d_10
0x00000700,0x00000000,m2c_vg_config0
0x00000701,0x00000000,m2c_vg_config1
0x00000702,0x00000000,m2c_vg_v_id
0x00000703,0x00000000,m2c_vg_p_id
0x00000704,0x00000000,m2c_vg_ext_htotal_msb
0x00000705,0x00000000,m2c_vg_ext_htotal_lsb
0x00000706,0x00000000,m2c_vg_ext_hblank_msb
0x00000707,0x00000000,m2c_vg_ext_hblank_lsb
0x00000708,0x00000000,m2c_vg_ext_hfp_msb
0x00000709,0x00000000,m2c_vg_ext_hfp_lsb
0x0000070a,0x00000000,m2c_vg_ext_hbp_msb
0x0000070b,0x00000000,m2c_vg_ext_hbp_lsb
0x0000070c,0x00000000,m2c_vg_ext_vtotal_msb
0x0000070d,0x00000000,m2c_vg_ext_vtotal_lsb
0x0000070e,0x00000000,m2c_vg_ext_vblank_msb
0x0000070f,0x00000000,m2c_vg_ext_vblank_lsb
0x00000710,0x00000000,m2c_vg_ext_vfp_msb
0x00000711,0x00000000,m2c_vg_ext_vfp_lsb
0x00000712,0x00000000,m2c_vg_ext_vsync_msb
0x00000713,0x00000000,m2c_vg_ext_vsync_lsb
0x00000714,0x00000000,m2c_vg_fs_ctrl
0x00000715,0x00000000,m2c_vg_step_mode_cfg
0x00000716,0x00000000,m2c_vg_step_len0
0x00000717,0x00000000,m2c_vg_step_len1
0x00000718,0x00000000,m2c_vg_cur_frame0
0x00000719,0x00000000,m2c_vg_cur_frame1
0x0000071a,0x00000001,m2c_vg_yuv_config
0x0000071b,0x00000000,m2c_vg_pixel_cnt_0
0x0000071c,0x00000000,m2c_vg_pixel_cnt_1
0x0000071d,0x00000000,m2c_vg_line_cnt_0
0x0000071e,0x00000000,m2c_vg_line_cnt_1
0x0000071f,0x00000000,m2c_vg_fsm
0x00000800,0x00000000,m2c_i2s_ctrl0
0x00000900,0x00000000,m2c_pi_ctrl0
0x00000901,0x00000000,m2c_pi_h_active_len0
0x00000902,0x00000000,m2c_pi_h_active_len1
0x00000903,0x00000000,m2c_pi_v_active_len0
0x00000904,0x00000000,m2c_pi_v_active_len1
0x00000905,0x00000000,m2c_pi_v_bp_0
0x00000906,0x00000000,m2c_pi_v_bp_1
0x00000907,0x00000000,m2c_pi_h_bp_0
0x00000908,0x00000000,m2c_pi_h_bp_1
0x00000909,0x00000000,m2c_pi_bit_map[0]
0x0000090a,0x00000000,m2c_pi_bit_map[1]
0x0000090b,0x00000000,m2c_pi_bit_map[2]
0x0000090c,0x00000000,m2c_pi_bit_map[3]
0x0000090d,0x00000000,m2c_pi_bit_map[4]
0x0000090e,0x00000000,m2c_pi_bit_map[5]
0x0000090f,0x00000000,m2c_pi_bit_map[6]
0x00000910,0x00000000,m2c_pi_bit_map[7]
0x00000911,0x00000000,m2c_pi_bit_map[8]
0x00000912,0x00000000,m2c_pi_bit_map[9]
0x00000913,0x00000000,m2c_pi_bit_map[10]
0x00000914,0x00000000,m2c_pi_bit_map[11]
0x00000915,0x00000000,m2c_pi_bit_map[12]
0x00000916,0x00000000,m2c_pi_bit_map[13]
0x00000917,0x00000000,m2c_pi_bit_map[14]
0x00000918,0x00000000,m2c_pi_bit_map[15]
0x00000919,0x00000000,m2c_pi_pixel_cnt_0
0x0000091a,0x00000000,m2c_pi_pixel_cnt_1
0x0000091b,0x00000000,m2c_pi_line_cnt_0
0x0000091c,0x00000000,m2c_pi_line_cnt_1
0x0000091d,0x00000000,m2c_pi_fsm
0x00000a00,0x0000003f,m2c_efh_tx_en_ctrl0
0x00000a01,0x000000f7,m2c_efh_tx_en_ctrl1
0x00000a02,0x0000001f,m2c_efh_tx_en_ctrl2
0x00000a03,0x00000000,m2c_efh_tx_en_ctrl3
0x00000a05,0x0000003f,m2c_efh_rx_en_ctrl0
0x00000a06,0x00000001,m2c_efh_rx_en_ctrl1
0x00000a07,0x00000000,m2c_efh_rx_en_ctrl2
0x00000a08,0x00000000,m2c_efh_rx_en_ctrl3
0x00000a09,0x00000000,m2c_efh_rx_en_ctrl4
0x00000a0a,0x00000000,m2c_efh_rx_en_ctrl5
0x00000a0b,0x00000000,m2c_efh_rx_en_ctrl6
0x00000a0e,0x0000008b,m2c_efh_i2c_en_ctrl0[0]
0x00000a10,0x0000008b,m2c_efh_i2c_en_ctrl0[1]
0x00000a12,0x0000008b,m2c_efh_i2c_en_ctrl0[2]
0x00000a0f,0x0000000f,m2c_efh_i2c_en_ctrl1[0]
0x00000a11,0x0000000f,m2c_efh_i2c_en_ctrl1[1]
0x00000a13,0x0000000f,m2c_efh_i2c_en_ctrl1[2]
0x00000a16,0x000000ff,m2c_efh_uart_en_ctrl0[0]
0x00000a18,0x000000ff,m2c_efh_uart_en_ctrl0[1]
0x00000a1a,0x000000ff,m2c_efh_uart_en_ctrl0[2]
0x00000a17,0x00000003,m2c_efh_uart_en_ctrl1[0]
0x00000a19,0x00000003,m2c_efh_uart_en_ctrl1[1]
0x00000a1b,0x00000003,m2c_efh_uart_en_ctrl1[2]
0x00000a1d,0x00000003,m2c_efh_spi_en_ctrl0
0x00000a1f,0x00000003,m2c_efh_gpio_en_ctrl0
0x00000a21,0x00000007,m2c_efh_mipi_rx_en_ctrl0
0x00000a23,0x00000001,m2c_efh_lfm_err0_en_ctrl0
0x00000a24,0x00000001,m2c_efh_lfm_err1_en_ctrl0
0x00000a26,0x00000000,m2c_efh_adc_en_ctrl0
0x00000a27,0x00000000,m2c_efh_adc_en_ctrl1
0x00000a28,0x00000000,m2c_efh_adc_en_ctrl2
0x00000a29,0x00000000,m2c_efh_adc_en_ctrl3
0x00000a2a,0x00000001,m2c_efh_adc_en_ctrl4
0x00000a2c,0x00000001,m2c_efh_pll_en_ctrl0
0x00000a2e,0x00000001,m2c_efh_mbist_en_ctrl0
0x00000a30,0x00000001,m2c_efh_lbist_en_ctrl0
0x00000a32,0x0000000e,m2c_efh_efuse_en_ctrl0
0x00000a33,0x00000000,m2c_efh_cfg_crc_en_ctrl0
0x00000a34,0x00000000,m2c_efh_clr_ctrl0
0x00000a35,0x00000000,m2c_efh_clr_ctrl1
0x00000a36,0x00000000,m2c_efh_clr_ctrl2
0x00000a38,0x00000000,m2c_efh_l0_status0
0x00000a39,0x00000000,m2c_efh_l0_status1
0x00000a3a,0x00000000,m2c_efh_l0_status2
0x00000a3c,0x00000000,m2c_efh_tx_status0
0x00000a3d,0x00000000,m2c_efh_tx_status1
0x00000a3e,0x00000000,m2c_efh_tx_status2
0x00000a3f,0x00000000,m2c_efh_tx_status3
0x00000a40,0x00000000,m2c_efh_rx_status0
0x00000a41,0x00000000,m2c_efh_rx_status1
0x00000a42,0x00000000,m2c_efh_rx_status2
0x00000a43,0x00000000,m2c_efh_rx_status3
0x00000a44,0x00000000,m2c_efh_rx_status4
0x00000a45,0x00000000,m2c_efh_rx_status5
0x00000a46,0x00000000,m2c_efh_rx_status6
0x00000a48,0x00000000,m2c_efh_i2c_status0[0]
0x00000a4a,0x00000000,m2c_efh_i2c_status0[1]
0x00000a4c,0x00000000,m2c_efh_i2c_status0[2]
0x00000a49,0x00000000,m2c_efh_i2c_status1[0]
0x00000a4b,0x00000000,m2c_efh_i2c_status1[1]
0x00000a4d,0x00000000,m2c_efh_i2c_status1[2]
0x00000a50,0x00000000,m2c_efh_uart_status0[0]
0x00000a52,0x00000000,m2c_efh_uart_status0[1]
0x00000a54,0x00000000,m2c_efh_uart_status0[2]
0x00000a51,0x00000000,m2c_efh_uart_status1[0]
0x00000a53,0x00000000,m2c_efh_uart_status1[1]
0x00000a55,0x00000000,m2c_efh_uart_status1[2]
0x00000a57,0x00000000,m2c_efh_spi_status0
0x00000a59,0x00000000,m2c_efh_gpio_status0
0x00000a5b,0x00000000,m2c_efh_mipi_rx_status0
0x00000a5d,0x00000000,m2c_efh_lfm_err0_status0
0x00000a5f,0x00000000,m2c_efh_lfm_err1_status1
0x00000a61,0x00000000,m2c_efh_adc_status0
0x00000a62,0x00000000,m2c_efh_adc_status1
0x00000a63,0x00000000,m2c_efh_adc_status2
0x00000a64,0x00000000,m2c_efh_adc_status3
0x00000a65,0x00000000,m2c_efh_adc_status4
0x00000a67,0x00000000,m2c_efh_pll_status0
0x00000a69,0x00000000,m2c_efh_mbist_status0
0x00000a6b,0x00000000,m2c_efh_lbist_status0
0x00000a6d,0x00000000,m2c_efh_efuse_status0
0x00000a6e,0x00000000,m2c_efh_cfg_crc_status0
0x00000a6f,0x00000000,m2c_efh_ctrl0
0x00000a70,0x00000000,m2c_efh_ctrl1
0x00000a80,0x00000000,m2c_efh_status0
0x00000b3c,0x00000061,m2c_ana_d2a_tx_regnamedel0
0x00000b3d,0x00000000,m2c_ana_d2a_tx_regnamedel1
0x00000b3e,0x00000080,m2c_ana_d2a_tx_regnamedel2
0x00000b3f,0x00000000,m2c_ana_d2a_tx_regnamedel3
0x00000b40,0x00000089,m2c_ana_d2a_tx_regnamedel4
0x00000b41,0x00000060,m2c_ana_d2a_tx_regnamedel5
0x00000b42,0x0000007d,m2c_ana_d2a_tx_regnamedel6
0x00000b43,0x00000024,m2c_ana_d2a_tx_regnamedel7
0x00000b44,0x00000048,m2c_ana_d2a_tx_regnamedel8
0x00000b45,0x0000001f,m2c_ana_d2a_tx_regnamedel9
0x00000b46,0x00000080,m2c_ana_d2a_bcrx_regnamedel0
0x00000b47,0x000000a0,m2c_ana_d2a_bcrx_regnamedel1
0x00000b48,0x00000003,m2c_ana_d2a_bcrx_regnamedel2
0x00000b49,0x00000000,m2c_ana_d2a_bcrx_regnamedel3
0x00000b4a,0x00000010,m2c_ana_d2a_bcrx_regnamedel4
0x00000b4b,0x0000009c,m2c_ana_d2a_bcrx_regnamedel5
0x00000b4c,0x00000020,m2c_ana_d2a_bcrx_regnamedel6
0x00000b4d,0x000000f4,m2c_ana_d2a_bcrx_regnamedel7
0x00000b4e,0x00000000,m2c_ana_d2a_bcrx_regnamedel8
0x00000b4f,0x00000020,m2c_ana_d2a_bcrx_regnamedel9
0x00000b50,0x0000002c,m2c_ana_d2a_bcrx_regnamedel10
0x00000b51,0x0000000f,m2c_ana_d2a_bcrx_regnamedel11
0x00000b52,0x00000003,m2c_ana_d2a_bcrx_regnamedel12
0x00000b56,0x00000014,m2c_ana_d2a_vgpll_regnamedel0
0x00000b57,0x00000005,m2c_ana_d2a_vgpll_regnamedel1
0x00000b58,0x00000001,m2c_ana_d2a_vgpll_regnamedel2
0x00000b59,0x00000000,m2c_ana_d2a_vgpll_regnamedel3
0x00000b5a,0x00000000,m2c_ana_d2a_vgpll_regnamedel4
0x00000b5b,0x0000000c,m2c_ana_d2a_vgpll_regnamedel5
0x00000b5c,0x00000000,m2c_ana_d2a_vgpll_regnamedel6
0x00000b5d,0x00000000,m2c_ana_d2a_vgpll_regnamedel7
0x00000b5e,0x00000000,m2c_ana_d2a_vgpll_regnamedel8
0x00000b5f,0x00000000,m2c_ana_d2a_vgpll_regnamedel9
0x00000b60,0x000000d3,m2c_ana_d2a_vgpll_regnamedel10
0x00000b66,0x00000014,m2c_ana_d2a_refpll_regnamedel0
0x00000b67,0x00000005,m2c_ana_d2a_refpll_regnamedel1
0x00000b68,0x00000001,m2c_ana_d2a_refpll_regnamedel2
0x00000b69,0x00000000,m2c_ana_d2a_refpll_regnamedel3
0x00000b6a,0x00000080,m2c_ana_d2a_refpll_regnamedel4
0x00000b6b,0x0000002c,m2c_ana_d2a_refpll_regnamedel5
0x00000b6c,0x00000000,m2c_ana_d2a_refpll_regnamedel6
0x00000b6d,0x00000000,m2c_ana_d2a_refpll_regnamedel7
0x00000b6e,0x00000000,m2c_ana_d2a_refpll_regnamedel8
0x00000b6f,0x00000000,m2c_ana_d2a_refpll_regnamedel9
0x00000b70,0x000000d3,m2c_ana_d2a_refpll_regnamedel10
0x00000b71,0x000000e0,m2c_ana_d2a_refpll_regnamedel11
0x00000b76,0x00000015,m2c_ana_d2a_refvgpll_regnamedel0
0x00000b77,0x00000000,m2c_ana_d2a_refvgpll_regnamedel1
0x00000b7a,0x00000040,m2c_ana_d2a_linkpll_regnamedel0
0x00000b7b,0x00000000,m2c_ana_d2a_linkpll_regnamedel1
0x00000b7c,0x00000000,m2c_ana_d2a_linkpll_regnamedel2
0x00000b7d,0x00000000,m2c_ana_d2a_linkpll_regnamedel3
0x00000b7e,0x00000000,m2c_ana_d2a_linkpll_regnamedel4
0x00000b7f,0x00000000,m2c_ana_d2a_linkpll_regnamedel5
0x00000b80,0x00000000,m2c_ana_d2a_linkpll_regnamedel6
0x00000b81,0x00000000,m2c_ana_d2a_linkpll_regnamedel7
0x00000b82,0x00000000,m2c_ana_d2a_linkpll_regnamedel8
0x00000b83,0x00000070,m2c_ana_d2a_linkpll_regnamedel9
0x00000b84,0x00000000,m2c_ana_d2a_linkpll_regnamedel10
0x00000b85,0x0000008b,m2c_ana_d2a_linkpll_regnamedel11
0x00000b86,0x00000010,m2c_ana_d2a_linkpll_regnamedel12
0x00000b87,0x00000000,m2c_ana_d2a_linkpll_regnamedel13
0x00000b88,0x00000010,m2c_ana_d2a_linkpll_regnamedel14
0x00000b89,0x000000a8,m2c_ana_d2a_linkpll_regnamedel15
0x00000b8a,0x000000b8,m2c_ana_d2a_linkpll_regnamedel16
0x00000b8b,0x00000034,m2c_ana_d2a_linkpll_regnamedel17
0x00000b8c,0x000000fc,m2c_ana_d2a_linkpll_regnamedel18
0x00000b8d,0x00000038,m2c_ana_d2a_linkpll_regnamedel19
0x00000b8e,0x0000002c,m2c_ana_d2a_linkpll_regnamedel20
0x00000b8f,0x00000002,m2c_ana_d2a_linkpll_regnamedel21
0x00000b90,0x0000008a,m2c_ana_d2a_linkpll_regnamedel22
0x00000b9a,0x00000000,m2c_ana_d2a_bg_regnamedel0
0x00000b9b,0x00000000,m2c_ana_d2a_bg_regnamedel1
0x00000ba0,0x00000000,m2c_ana_d2a_digldo_regnamedel1
0x00000ba3,0x00000000,m2c_ana_d2a_xtal_regnamedel2
0x00000ba4,0x00000000,m2c_ana_d2a_sar_regnamedel0
0x00000ba5,0x00000008,m2c_ana_d2a_sar_regnamedel1
0x00000ba6,0x00000000,m2c_ana_d2a_sar_regnamedel2
0x00000baa,0x00000000,m2c_ana_d2a_lf_regnamedel0
0x00000bab,0x00000000,m2c_ana_d2a_lf_regnamedel1
0x00000bac,0x00000000,m2c_ana_d2a_lf_regnamedel2
0x00000bad,0x00000000,m2c_ana_d2a_lf_regnamedel3
0x00000bb0,0x00000000,m2c_ana_d2a_rsvd_regnamedel0
0x00000bb1,0x00000000,m2c_ana_d2a_rsvd_regnamedel1
0x00000bb2,0x00000000,m2c_ana_d2a_rsvd_regnamedel2
0x00000bb3,0x00000000,m2c_ana_d2a_rsvd_regnamedel3
0x00000bb4,0x00000000,m2c_ana_d2a_rsvd_regnamedel4
0x00000bb5,0x00000000,m2c_ana_d2a_rsvd_regnamedel5
0x00000bb6,0x00000000,m2c_ana_d2a_rsvd_regnamedel6
0x00000bb7,0x00000000,m2c_ana_d2a_rsvd_regnamedel7
0x00000bb8,0x00000000,m2c_ana_d2a_padtest_regnamedel0
0x00000bb9,0x00000000,m2c_ana_d2a_padtest_regnamedel1
0x00000bba,0x00000000,m2c_ana_a2d_tx_regnamedel0
0x00000bbd,0x00000000,m2c_ana_a2d_linkpll_regnamedel0
0x00000bbf,0x00000000,m2c_ana_a2d_rsvd_regnamedel0
0x00000bc0,0x00000000,m2c_ana_a2d_rsvd_regnamedel1
0x00000bc1,0x00000000,m2c_ana_a2d_rsvd_regnamedel2
0x00000bc2,0x00000000,m2c_ana_a2d_rsvd_regnamedel3
0x00000bcf,0x00000000,m2c_ana_a2d_xtal_regnamedel0
0x00000d00,0x00000000,m2c_adc_ctrl_adc_ctrl0
0x00000d01,0x00000000,m2c_adc_ctrl_adc_ctrl1
0x00000d02,0x00000000,m2c_adc_ctrl_adc_ctrl2[0]
0x00000d03,0x00000000,m2c_adc_ctrl_adc_ctrl2[1]
0x00000d04,0x00000002,m2c_adc_ctrl_adc_ctrl3
0x00000d05,0x00000001,m2c_adc_ctrl_adc_ctrl4
0x00000d06,0x000000ff,m2c_adc_ctrl_adc_ctrl5[0]
0x00000d07,0x000000ff,m2c_adc_ctrl_adc_ctrl5[1]
0x00000d08,0x000000ff,m2c_adc_ctrl_adc_ctrl5[2]
0x00000d09,0x000000ff,m2c_adc_ctrl_adc_ctrl5[3]
0x00000d0a,0x000000ff,m2c_adc_ctrl_adc_ctrl5[4]
0x00000d0b,0x000000ff,m2c_adc_ctrl_adc_ctrl5[5]
0x00000d0c,0x000000ff,m2c_adc_ctrl_adc_ctrl5[6]
0x00000d0d,0x000000ff,m2c_adc_ctrl_adc_ctrl5[7]
0x00000d0e,0x000000ff,m2c_adc_ctrl_adc_ctrl5[8]
0x00000d0f,0x000000ff,m2c_adc_ctrl_adc_ctrl5[9]
0x00000d10,0x000000ff,m2c_adc_ctrl_adc_ctrl5[10]
0x00000d11,0x000000ff,m2c_adc_ctrl_adc_ctrl5[11]
0x00000d12,0x000000ff,m2c_adc_ctrl_adc_ctrl5[12]
0x00000d13,0x000000ff,m2c_adc_ctrl_adc_ctrl5[13]
0x00000d14,0x000000ff,m2c_adc_ctrl_adc_ctrl5[14]
0x00000d15,0x000000ff,m2c_adc_ctrl_adc_ctrl5[15]
0x00000d16,0x00000007,m2c_adc_ctrl_adc_ctrl6[0]
0x00000d17,0x00000007,m2c_adc_ctrl_adc_ctrl6[1]
0x00000d18,0x00000007,m2c_adc_ctrl_adc_ctrl6[2]
0x00000d19,0x00000007,m2c_adc_ctrl_adc_ctrl6[3]
0x00000d1a,0x00000007,m2c_adc_ctrl_adc_ctrl6[4]
0x00000d1b,0x00000007,m2c_adc_ctrl_adc_ctrl6[5]
0x00000d1c,0x00000007,m2c_adc_ctrl_adc_ctrl6[6]
0x00000d1d,0x00000007,m2c_adc_ctrl_adc_ctrl6[7]
0x00000d1e,0x00000007,m2c_adc_ctrl_adc_ctrl6[8]
0x00000d1f,0x00000007,m2c_adc_ctrl_adc_ctrl6[9]
0x00000d20,0x00000007,m2c_adc_ctrl_adc_ctrl6[10]
0x00000d21,0x00000007,m2c_adc_ctrl_adc_ctrl6[11]
0x00000d22,0x00000007,m2c_adc_ctrl_adc_ctrl6[12]
0x00000d23,0x00000007,m2c_adc_ctrl_adc_ctrl6[13]
0x00000d24,0x00000007,m2c_adc_ctrl_adc_ctrl6[14]
0x00000d25,0x00000007,m2c_adc_ctrl_adc_ctrl6[15]
0x00000d26,0x00000000,m2c_adc_ctrl_adc_ctrl7[0]
0x00000d27,0x00000000,m2c_adc_ctrl_adc_ctrl7[1]
0x00000d28,0x00000000,m2c_adc_ctrl_adc_ctrl7[2]
0x00000d29,0x00000000,m2c_adc_ctrl_adc_ctrl7[3]
0x00000d2a,0x00000000,m2c_adc_ctrl_adc_ctrl7[4]
0x00000d2b,0x00000000,m2c_adc_ctrl_adc_ctrl7[5]
0x00000d2c,0x00000000,m2c_adc_ctrl_adc_ctrl7[6]
0x00000d2d,0x00000000,m2c_adc_ctrl_adc_ctrl7[7]
0x00000d2e,0x00000000,m2c_adc_ctrl_adc_ctrl7[8]
0x00000d2f,0x00000000,m2c_adc_ctrl_adc_ctrl7[9]
0x00000d30,0x00000000,m2c_adc_ctrl_adc_ctrl7[10]
0x00000d31,0x00000000,m2c_adc_ctrl_adc_ctrl7[11]
0x00000d32,0x00000000,m2c_adc_ctrl_adc_ctrl7[12]
0x00000d33,0x00000000,m2c_adc_ctrl_adc_ctrl7[13]
0x00000d34,0x00000000,m2c_adc_ctrl_adc_ctrl7[14]
0x00000d35,0x00000000,m2c_adc_ctrl_adc_ctrl7[15]
0x00000d36,0x00000000,m2c_adc_ctrl_adc_ctrl8[0]
0x00000d37,0x00000000,m2c_adc_ctrl_adc_ctrl8[1]
0x00000d38,0x00000000,m2c_adc_ctrl_adc_ctrl8[2]
0x00000d39,0x00000000,m2c_adc_ctrl_adc_ctrl8[3]
0x00000d3a,0x00000000,m2c_adc_ctrl_adc_ctrl8[4]
0x00000d3b,0x00000000,m2c_adc_ctrl_adc_ctrl8[5]
0x00000d3c,0x00000000,m2c_adc_ctrl_adc_ctrl8[6]
0x00000d3d,0x00000000,m2c_adc_ctrl_adc_ctrl8[7]
0x00000d3e,0x00000000,m2c_adc_ctrl_adc_ctrl8[8]
0x00000d3f,0x00000000,m2c_adc_ctrl_adc_ctrl8[9]
0x00000d40,0x00000000,m2c_adc_ctrl_adc_ctrl8[10]
0x00000d41,0x00000000,m2c_adc_ctrl_adc_ctrl8[11]
0x00000d42,0x00000000,m2c_adc_ctrl_adc_ctrl8[12]
0x00000d43,0x00000000,m2c_adc_ctrl_adc_ctrl8[13]
0x00000d44,0x00000000,m2c_adc_ctrl_adc_ctrl8[14]
0x00000d45,0x00000000,m2c_adc_ctrl_adc_ctrl8[15]
0x00000d46,0x00000000,m2c_adc_ctrl_adc_status0
0x00000d47,0x00000000,m2c_adc_ctrl_adc_status1[0]
0x00000d48,0x00000000,m2c_adc_ctrl_adc_status1[1]
0x00000d49,0x00000000,m2c_adc_ctrl_adc_status2[0]
0x00000d4a,0x00000000,m2c_adc_ctrl_adc_status2[1]
0x00000d4b,0x00000000,m2c_adc_ctrl_adc_status3[0]
0x00000d4c,0x00000000,m2c_adc_ctrl_adc_status3[1]
0x00000d4d,0x00000000,m2c_adc_ctrl_adc_status4[0]
0x00000d4e,0x00000000,m2c_adc_ctrl_adc_status4[1]
0x00000d4f,0x00000000,m2c_adc_ctrl_adc_status5
0x00000d50,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[0]
0x00000d51,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[1]
0x00000d52,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[2]
0x00000d53,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[3]
0x00000d54,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[4]
0x00000d55,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[5]
0x00000d56,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[6]
0x00000d57,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[7]
0x00000d58,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[8]
0x00000d59,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[9]
0x00000d5a,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[10]
0x00000d5b,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[11]
0x00000d5c,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[12]
0x00000d5d,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[13]
0x00000d5e,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[14]
0x00000d5f,0x00000000,m2c_adc_ctrl_adc_ch_data_0_7[15]
0x00000d60,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[0]
0x00000d61,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[1]
0x00000d62,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[2]
0x00000d63,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[3]
0x00000d64,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[4]
0x00000d65,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[5]
0x00000d66,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[6]
0x00000d67,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[7]
0x00000d68,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[8]
0x00000d69,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[9]
0x00000d6a,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[10]
0x00000d6b,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[11]
0x00000d6c,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[12]
0x00000d6d,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[13]
0x00000d6e,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[14]
0x00000d6f,0x00000000,m2c_adc_ctrl_adc_ch_data_8_10[15]
0x00000d70,0x00000000,m2c_adc_ctrl_adc_fault_clear[0]
0x00000d71,0x00000000,m2c_adc_ctrl_adc_fault_clear[1]
0x00000d72,0x00000000,m2c_adc_ctrl_adc_fault_clear[2]
0x00000d73,0x00000000,m2c_adc_ctrl_adc_fault_clear[3]
0x00000d74,0x00000000,m2c_adc_ctrl_adc_fault_clear[4]
0x00000d75,0x00000000,m2c_adc_ctrl_adc_fault_clear[5]
0x00000d76,0x00000000,m2c_adc_ctrl_adc_fault_clear[6]
0x00000d77,0x00000000,m2c_adc_ctrl_adc_fault_clear[7]
0x00000d78,0x00000000,m2c_adc_ctrl_adc_fault_clear[8]
0x00000d79,0x00000000,m2c_adc_ctrl_adc_fault_clear[9]
0x00000d7a,0x00000000,m2c_adc_ctrl_adc_fault_clear[10]
0x00000d7b,0x00000000,m2c_adc_ctrl_adc_fault_clear[11]
0x00000d7c,0x00000000,m2c_adc_ctrl_adc_fault_clear[12]
0x00000d7d,0x00000000,m2c_adc_ctrl_adc_fault_clear[13]
0x00000d7e,0x00000000,m2c_adc_ctrl_adc_fault_clear[14]
0x00000d7f,0x00000000,m2c_adc_ctrl_adc_fault_clear[15]
0x00000d80,0x00000000,m2c_adc_ctrl_adc_bist_status0[0]
0x00000d81,0x00000000,m2c_adc_ctrl_adc_bist_status0[1]
0x00000d82,0x00000000,m2c_adc_ctrl_adc_bist_status0[2]
0x00000d83,0x00000000,m2c_adc_ctrl_adc_bist_status0[3]
0x00000d84,0x00000000,m2c_adc_ctrl_adc_bist_status0[4]
0x00000d85,0x00000000,m2c_adc_ctrl_adc_bist_status0[5]
0x00000d86,0x00000000,m2c_adc_ctrl_adc_bist_status0[6]
0x00000d87,0x00000000,m2c_adc_ctrl_adc_bist_status0[7]
0x00000d88,0x00000000,m2c_adc_ctrl_adc_bist_status1[0]
0x00000d89,0x00000000,m2c_adc_ctrl_adc_bist_status1[1]
0x00000d8a,0x00000000,m2c_adc_ctrl_adc_bist_status1[2]
0x00000d8b,0x00000000,m2c_adc_ctrl_adc_bist_status1[3]
0x00000d8c,0x00000000,m2c_adc_ctrl_adc_bist_status1[4]
0x00000d8d,0x00000000,m2c_adc_ctrl_adc_bist_status1[5]
0x00000d8e,0x00000000,m2c_adc_ctrl_adc_bist_status1[6]
0x00000d8f,0x00000000,m2c_adc_ctrl_adc_bist_status1[7]
0x00000d90,0x00000000,m2c_adc_ctrl_adc_bist_ctrl
0x00000d91,0x00000002,m2c_adc_ctrl_adc_ctrl9
0x00000e00,0x0000000d,m2c_lf_ctrl0
0x00000e01,0x00000000,m2c_lf_status0[0]
0x00000e02,0x00000000,m2c_lf_status0[1]
0x00000e10,0x00000000,m2c_fusa_cfgcrc_ctrl0
0x00000e11,0x00000000,m2c_fusa_cfgcrc_ctrl1
0x00000e12,0x00000000,m2c_fusa_cfgcrc_status0
0x00000e13,0x00000000,m2c_fusa_cfgcrc_status1
0x00000e14,0x00000000,m2c_fusa_cfgcrc_status2
0x00000f00,0x00000000,m2c_test_glb_ctrl0
0x00000f01,0x00000000,m2c_test_scan_ctrl
0x00000f02,0x00000000,m2c_test_tx_prbs_ctrl
0x00000f03,0x00000000,m2c_test_rx_prbs_ctrl
0x00000f04,0x00000000,m2c_test_rx_prbs_status0
0x00000f05,0x00000000,m2c_test_rx_prbs_status1
0x00000f06,0x00000000,m2c_test_rx_prbs_status2
0x00000f07,0x0000000c,m2c_test_cfg_adc_ctrl[0]
0x00000f09,0x0000000c,m2c_test_cfg_adc_ctrl[1]
0x00000f08,0x00000000,m2c_test_cfg_adc_status[0]
0x00000f0a,0x00000000,m2c_test_cfg_adc_status[1]
0x00000f0b,0x00000000,m2c_test_fsm_ctrl
0x00000f0c,0x00000000,m2c_test_fsm_status0
0x00000f14,0x00000000,m2c_test_dbg_ctrl[0]
0x00000f15,0x00000000,m2c_test_dbg_ctrl[1]
0x00000f16,0x00000000,m2c_test_link_seq_ctrl
0x00000f17,0x00000000,m2c_test_link_dbg_status0
0x00000f18,0x00000000,m2c_test_link_dbg_status1
0x00000f19,0x00000000,m2c_test_link_dbg_status2
0x00000f1a,0x00000000,m2c_test_link_dbg_status3
0x00000f1b,0x00000000,m2c_test_link_dbg_status4[0]
0x00000f1c,0x00000000,m2c_test_link_dbg_status4[1]
0x00000f1d,0x00000000,m2c_test_link_dbg_status4[2]
0x00000f1e,0x00000000,m2c_test_link_dbg_status4[3]
0x00000f28,0x00000001,m2c_test_tx_link_ctrl0
0x00000f29,0x00000000,m2c_test_rx_link_ctrl0
0x00000f2a,0x00000000,m2c_test_tx_usr_ctrl
0x00000f2b,0x00000000,m2c_test_tx_usr_patn[0]
0x00000f2c,0x00000000,m2c_test_tx_usr_patn[1]
0x00000f2d,0x00000000,m2c_test_tx_usr_patn[2]
0x00000f2e,0x00000000,m2c_test_tx_usr_patn[3]
0x00000f2f,0x00000000,m2c_test_tx_usr_patn[4]
0x00000f30,0x00000000,m2c_test_tx_usr_patn[5]
0x00000f31,0x00000000,m2c_test_tx_usr_patn[6]
0x00000f32,0x00000000,m2c_test_tx_usr_patn[7]
0x00000f33,0x00000000,m2c_test_tx_usr_patn[8]
0x00000f34,0x00000000,m2c_test_tx_usr_patn[9]
0x00000f35,0x00000000,m2c_test_rx_raw_ctrl
0x00000f36,0x00000000,m2c_test_rx_raw_data[0]
0x00000f37,0x00000000,m2c_test_rx_raw_data[1]
0x00000f38,0x00000000,m2c_test_rx_raw_data[2]
0x00000f39,0x00000000,m2c_test_rx_raw_data[3]
0x00000f3a,0x00000000,m2c_test_rx_raw_data[4]
0x00000f3b,0x00000000,m2c_test_rx_raw_data[5]
0x00000f3c,0x00000000,m2c_test_rx_raw_data[6]
0x00000f3d,0x00000000,m2c_test_rx_raw_data[7]
0x00000f3e,0x00000000,m2c_test_rx_raw_data[8]
0x00000f3f,0x00000000,m2c_test_rx_raw_data[9]
0x00000f40,0x00000002,m2c_test_mbist_ctrl
0x00000f41,0x00000000,m2c_test_mbist_status
0x00000f42,0x00000012,m2c_test_mem_cfg
0x00000f43,0x00000000,m2c_test_dbg_ck_ctrl
0x00000f44,0x00000000,m2c_test_lf_raw
0x00000f45,0x00000000,m2c_test_i2c_tx_dbg0[0]
0x00000f46,0x00000000,m2c_test_i2c_tx_dbg0[1]
0x00000f47,0x00000000,m2c_test_i2c_tx_dbg0[2]
0x00000f48,0x00000000,m2c_test_i2c_tx_dbg1[0]
0x00000f49,0x00000000,m2c_test_i2c_tx_dbg1[1]
0x00000f4a,0x00000000,m2c_test_i2c_tx_dbg1[2]
0x00000f4b,0x00000000,m2c_test_uart_tx_dbg0[0]
0x00000f4c,0x00000000,m2c_test_uart_tx_dbg0[1]
0x00000f4d,0x00000000,m2c_test_uart_tx_dbg0[2]
0x00000f4f,0x00000000,m2c_test_uart_tx_dbg1[0]
0x00000f50,0x00000000,m2c_test_uart_tx_dbg1[1]
0x00000f51,0x00000000,m2c_test_uart_tx_dbg1[2]
0x00000f52,0x00000000,m2c_test_spi_tx_dbg0
0x00000f53,0x00000000,m2c_test_spi_tx_dbg1
0x00000f54,0x00000000,m2c_test_gpio_tx_dbg0
0x00000f55,0x00000000,m2c_test_gpio_tx_dbg1
0x00000f56,0x00000000,m2c_test_i2s_tx_dbg0
0x00000f57,0x00000000,m2c_test_i2s_tx_dbg1
0x00000f58,0x00000000,m2c_test_i2c_rx_dbg0[0]
0x00000f59,0x00000000,m2c_test_i2c_rx_dbg0[1]
0x00000f5a,0x00000000,m2c_test_i2c_rx_dbg0[2]
0x00000f5b,0x00000000,m2c_test_uart_rx_dbg0[0]
0x00000f5c,0x00000000,m2c_test_uart_rx_dbg0[1]
0x00000f5d,0x00000000,m2c_test_uart_rx_dbg0[2]
0x00000f5e,0x00000000,m2c_test_spi_rx_dbg0
0x00000f5f,0x00000000,m2c_test_gpio_rx_dbg0
0x00000f60,0x00000000,m2c_test_i2s_rx_dbg0
0x00000f61,0x00000000,m2c_test_dbg_gpio_fifo
0x00000f62,0x00000000,m2c_test_resid_ctrl0
0x00000f63,0x00000000,m2c_test_resid_status0
0x00000f64,0x00000000,m2c_test_resid_status1
0x00000f65,0x00000000,m2c_test_resid_status2
0x00000f66,0x00000000,m2c_test_tx_k_swing_down
0x00000f67,0x00000000,m2c_test_bcrx_lpfc_dacctr
0x00000f68,0x00000000,m2c_test_link_rate_ctrl0
0x00000f69,0x00000000,m2c_test_link_rate_dbg0
0x00000f6a,0x00000000,m2c_test_efuse_ctrl
0x00000f6b,0x00000000,m2c_test_spi_tx_data_dbg0
0x00000f6c,0x00000000,m2c_test_spi_tx_data_dbg1
0x00000f6d,0x00000000,m2c_test_spi_rx_data_dbg0
0x00000f6e,0x00000000,m2c_test_spi_rx_data_dbg1
0x00002000,0x00000000,m2c_efuse_shadow_data[0]
0x00002001,0x00000000,m2c_efuse_shadow_data[1]
0x00002002,0x00000000,m2c_efuse_shadow_data[2]
0x00002003,0x00000000,m2c_efuse_shadow_data[3]
0x00002004,0x00000000,m2c_efuse_shadow_data[4]
0x00002005,0x00000000,m2c_efuse_shadow_data[5]
0x00002006,0x00000000,m2c_efuse_shadow_data[6]
0x00002007,0x00000000,m2c_efuse_shadow_data[7]
0x00002008,0x00000000,m2c_efuse_shadow_data[8]
0x00002009,0x00000000,m2c_efuse_shadow_data[9]
0x0000200a,0x00000000,m2c_efuse_shadow_data[10]
0x0000200b,0x00000000,m2c_efuse_shadow_data[11]
0x0000200c,0x00000000,m2c_efuse_shadow_data[12]
0x0000200d,0x00000000,m2c_efuse_shadow_data[13]
0x0000200e,0x00000000,m2c_efuse_shadow_data[14]
0x0000200f,0x00000000,m2c_efuse_shadow_data[15]
0x00002010,0x00000000,m2c_efuse_shadow_data[16]
0x00002011,0x00000000,m2c_efuse_shadow_data[17]
0x00002012,0x00000000,m2c_efuse_shadow_data[18]
0x00002013,0x00000000,m2c_efuse_shadow_data[19]
0x00002014,0x00000000,m2c_efuse_shadow_data[20]
0x00002015,0x00000000,m2c_efuse_shadow_data[21]
0x00002016,0x00000000,m2c_efuse_shadow_data[22]
0x00002017,0x00000000,m2c_efuse_shadow_data[23]
0x00002018,0x00000000,m2c_efuse_shadow_data[24]
0x00002019,0x00000000,m2c_efuse_shadow_data[25]
0x0000201a,0x00000000,m2c_efuse_shadow_data[26]
0x0000201b,0x00000000,m2c_efuse_shadow_data[27]
0x0000201c,0x00000000,m2c_efuse_shadow_data[28]
0x0000201d,0x00000000,m2c_efuse_shadow_data[29]
0x0000201e,0x00000000,m2c_efuse_shadow_data[30]
0x0000201f,0x00000000,m2c_efuse_shadow_data[31]
0x00002020,0x00000000,m2c_efuse_shadow_data[32]
0x00002021,0x00000000,m2c_efuse_shadow_data[33]
0x00002022,0x00000000,m2c_efuse_shadow_data[34]
0x00002023,0x00000000,m2c_efuse_shadow_data[35]
0x00002024,0x00000000,m2c_efuse_shadow_data[36]
0x00002025,0x00000000,m2c_efuse_shadow_data[37]
0x00002026,0x00000000,m2c_efuse_shadow_data[38]
0x00002027,0x00000000,m2c_efuse_shadow_data[39]
0x00002028,0x00000000,m2c_efuse_shadow_data[40]
0x00002029,0x00000000,m2c_efuse_shadow_data[41]
0x0000202a,0x00000000,m2c_efuse_shadow_data[42]
0x0000202b,0x00000000,m2c_efuse_shadow_data[43]
0x0000202c,0x00000000,m2c_efuse_shadow_data[44]
0x0000202d,0x00000000,m2c_efuse_shadow_data[45]
0x0000202e,0x00000000,m2c_efuse_shadow_data[46]
0x0000202f,0x00000000,m2c_efuse_shadow_data[47]
0x00002030,0x00000000,m2c_efuse_shadow_data[48]
0x00002031,0x00000000,m2c_efuse_shadow_data[49]
0x00002032,0x00000000,m2c_efuse_shadow_data[50]
0x00002033,0x00000000,m2c_efuse_shadow_data[51]
0x00002034,0x00000000,m2c_efuse_shadow_data[52]
0x00002035,0x00000000,m2c_efuse_shadow_data[53]
0x00002036,0x00000000,m2c_efuse_shadow_data[54]
0x00002037,0x00000000,m2c_efuse_shadow_data[55]
0x00002038,0x00000000,m2c_efuse_shadow_data[56]
0x00002039,0x00000000,m2c_efuse_shadow_data[57]
0x0000203a,0x00000000,m2c_efuse_shadow_data[58]
0x0000203b,0x00000000,m2c_efuse_shadow_data[59]
0x0000203c,0x00000000,m2c_efuse_shadow_data[60]
0x0000203d,0x00000000,m2c_efuse_shadow_data[61]
0x0000203e,0x00000000,m2c_efuse_shadow_data[62]
0x0000203f,0x00000000,m2c_efuse_shadow_data[63]
0x00002040,0x00000000,m2c_efuse_shadow_data[64]
0x00002041,0x00000000,m2c_efuse_shadow_data[65]
0x00002042,0x00000000,m2c_efuse_shadow_data[66]
0x00002043,0x00000000,m2c_efuse_shadow_data[67]
0x00002044,0x00000000,m2c_efuse_shadow_data[68]
0x00002045,0x00000000,m2c_efuse_shadow_data[69]
0x00002046,0x00000000,m2c_efuse_shadow_data[70]
0x00002047,0x00000000,m2c_efuse_shadow_data[71]
0x00002048,0x00000000,m2c_efuse_shadow_data[72]
0x00002049,0x00000000,m2c_efuse_shadow_data[73]
0x0000204a,0x00000000,m2c_efuse_shadow_data[74]
0x0000204b,0x00000000,m2c_efuse_shadow_data[75]
0x0000204c,0x00000000,m2c_efuse_shadow_data[76]
0x0000204d,0x00000000,m2c_efuse_shadow_data[77]
0x0000204e,0x00000000,m2c_efuse_shadow_data[78]
0x0000204f,0x00000000,m2c_efuse_shadow_data[79]
0x00002050,0x00000000,m2c_efuse_shadow_data[80]
0x00002051,0x00000000,m2c_efuse_shadow_data[81]
0x00002052,0x00000000,m2c_efuse_shadow_data[82]
0x00002053,0x00000000,m2c_efuse_shadow_data[83]
0x00002054,0x00000000,m2c_efuse_shadow_data[84]
0x00002055,0x00000000,m2c_efuse_shadow_data[85]
0x00002056,0x00000000,m2c_efuse_shadow_data[86]
0x00002057,0x00000000,m2c_efuse_shadow_data[87]
0x00002058,0x00000000,m2c_efuse_shadow_data[88]
0x00002059,0x00000000,m2c_efuse_shadow_data[89]
0x0000205a,0x00000000,m2c_efuse_shadow_data[90]
0x0000205b,0x00000000,m2c_efuse_shadow_data[91]
0x0000205c,0x00000000,m2c_efuse_shadow_data[92]
0x0000205d,0x00000000,m2c_efuse_shadow_data[93]
0x0000205e,0x00000000,m2c_efuse_shadow_data[94]
0x0000205f,0x00000000,m2c_efuse_shadow_data[95]
0x00002060,0x00000000,m2c_efuse_shadow_data[96]
0x00002061,0x00000000,m2c_efuse_shadow_data[97]
0x00002062,0x00000000,m2c_efuse_shadow_data[98]
0x00002063,0x00000000,m2c_efuse_shadow_data[99]
0x00002064,0x00000000,m2c_efuse_shadow_data[100]
0x00002065,0x00000000,m2c_efuse_shadow_data[101]
0x00002066,0x00000000,m2c_efuse_shadow_data[102]
0x00002067,0x00000000,m2c_efuse_shadow_data[103]
0x00002068,0x00000000,m2c_efuse_shadow_data[104]
0x00002069,0x00000000,m2c_efuse_shadow_data[105]
0x0000206a,0x00000000,m2c_efuse_shadow_data[106]
0x0000206b,0x00000000,m2c_efuse_shadow_data[107]
0x0000206c,0x00000000,m2c_efuse_shadow_data[108]
0x0000206d,0x00000000,m2c_efuse_shadow_data[109]
0x0000206e,0x00000000,m2c_efuse_shadow_data[110]
0x0000206f,0x00000000,m2c_efuse_shadow_data[111]
0x00002070,0x00000000,m2c_efuse_shadow_data[112]
0x00002071,0x00000000,m2c_efuse_shadow_data[113]
0x00002072,0x00000000,m2c_efuse_shadow_data[114]
0x00002073,0x00000000,m2c_efuse_shadow_data[115]
0x00002074,0x00000000,m2c_efuse_shadow_data[116]
0x00002075,0x00000000,m2c_efuse_shadow_data[117]
0x00002076,0x00000000,m2c_efuse_shadow_data[118]
0x00002077,0x00000000,m2c_efuse_shadow_data[119]
0x00002078,0x00000000,m2c_efuse_shadow_data[120]
0x00002079,0x00000000,m2c_efuse_shadow_data[121]
0x0000207a,0x00000000,m2c_efuse_shadow_data[122]
0x0000207b,0x00000000,m2c_efuse_shadow_data[123]
0x0000207c,0x00000000,m2c_efuse_shadow_data[124]
0x0000207d,0x00000000,m2c_efuse_shadow_data[125]
0x0000207e,0x00000000,m2c_efuse_shadow_data[126]
0x0000207f,0x00000000,m2c_efuse_shadow_data[127]
0x00002200,0x00000000,m2c_efuse_efuse_key
0x00002201,0x0000002b,m2c_efuse_tpgm_ltiming
0x00002202,0x00000001,m2c_efuse_tpgm_htiming
0x00002203,0x00000000,m2c_efuse_tunit_timing
0x00002204,0x00000004,m2c_efuse_tread_timing
0x00002205,0x00000000,m2c_efuse_status
0x00002206,0x00000000,m2c_efuse_main_st
0x00002207,0x00000000,m2c_efuse_pgm_st
0x00002208,0x00000000,m2c_efuse_read_st
0x00002209,0x00000000,m2c_efuse_pwr_st
0x0000220a,0x00000000,m2c_efuse_rf_bits
0x0000220b,0x00000001,m2c_efuse_pwr_margin_ctrl
0x0000220c,0x00000000,m2c_efuse_redundency_bit_ctrl
0x0000220d,0x00000000,m2c_efuse_redundency_waddr
0x0000220e,0x00000000,m2c_efuse_redundency_baddr
0x0000220f,0x00000000,m2c_efuse_partial_addr
0x00002210,0x00000000,m2c_efuse_ppgm_data[0]
0x00002211,0x00000000,m2c_efuse_ppgm_data[1]
0x00002212,0x00000000,m2c_efuse_ppgm_data[2]
0x00002213,0x00000000,m2c_efuse_ppgm_data[3]
0x00002214,0x00000000,m2c_efuse_pread_data[0]
0x00002215,0x00000000,m2c_efuse_pread_data[1]
0x00002216,0x00000000,m2c_efuse_pread_data[2]
0x00002217,0x00000000,m2c_efuse_pread_data[3]
0x00002218,0x00000000,m2c_efuse_pgm_start
0x00002219,0x00000000,m2c_efuse_pgm_end
0x0000221a,0x00000000,m2c_efuse_int_en
0x0000221b,0x00000000,m2c_efuse_int_st
0x0000221c,0x00000000,m2c_efuse_crc_ecc_ctrl
0x0000221d,0x00000000,m2c_efuse_chk_st
0x0000221e,0x00000000,m2c_efuse_crc_cal[0]
0x0000221f,0x00000000,m2c_efuse_crc_cal[1]
0x00002220,0x00000000,m2c_efuse_crc_gold[0]
0x00002221,0x00000000,m2c_efuse_crc_gold[1]
0x00002222,0x00000000,m2c_efuse_ecc_1bit_err_addr
0x00002223,0x00000000,m2c_efuse_ecc_2bit_err_addr
0x00002224,0x00000000,m2c_efuse_double_err_addr
0x0000a000,0x0000002a,m2c_csi2_rx_csi_rx_version
0x0000a001,0x00000003,m2c_csi2_rx_csi_rx_n_lanes
0x0000a002,0x00000000,m2c_csi2_rx_csi_rx_csi2_resetn
0x0000a003,0x00000000,m2c_csi2_rx_csi_rx_phy_shutdownz
0x0000a004,0x00000000,m2c_csi2_rx_csi_rx_phy_rstz
0x0000a005,0x00000010,m2c_csi2_rx_csi_rx_phy_rx
0x0000a006,0x00000000,m2c_csi2_rx_csi_rx_vc_extension
0x0000a007,0x00000000,m2c_csi2_rx_csi_rx_scrambling
0x0000a008,0x00000008,m2c_csi2_rx_csi_rx_scrambling_seed1_0
0x0000a009,0x00000010,m2c_csi2_rx_csi_rx_scrambling_seed1_1
0x0000a00a,0x00000088,m2c_csi2_rx_csi_rx_scrambling_seed2_0
0x0000a00b,0x00000011,m2c_csi2_rx_csi_rx_scrambling_seed2_1
0x0000a00c,0x00000048,m2c_csi2_rx_csi_rx_scrambling_seed3_0
0x0000a00d,0x00000012,m2c_csi2_rx_csi_rx_scrambling_seed3_1
0x0000a00e,0x000000c8,m2c_csi2_rx_csi_rx_scrambling_seed4_0
0x0000a00f,0x00000013,m2c_csi2_rx_csi_rx_scrambling_seed4_1
0x0000a010,0x00000000,m2c_csi2_rx_csi_rx_int_main_status0
0x0000a011,0x0000001f,m2c_csi2_rx_csi_rx_int_main_en0
0x0000a012,0x00000000,m2c_csi2_rx_csi_rx_int_hdr_err_fatal
0x0000a013,0x00000001,m2c_csi2_rx_csi_rx_int_hdr_err_fatal_en
0x0000a014,0x00000000,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_0
0x0000a015,0x00000000,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_1
0x0000a016,0x00000000,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_2
0x0000a017,0x00000000,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_3
0x0000a018,0x000000ff,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_0_en
0x0000a019,0x000000ff,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_1_en
0x0000a01a,0x000000ff,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_2_en
0x0000a01b,0x000000ff,m2c_csi2_rx_csi_rx_int_pld_crc_fatal_err_3_en
0x0000a01c,0x00000000,m2c_csi2_rx_csi_rx_int_hdr_err_correct_0
0x0000a01d,0x00000000,m2c_csi2_rx_csi_rx_int_hdr_err_correct_1
0x0000a01e,0x00000000,m2c_csi2_rx_csi_rx_int_hdr_err_correct_2
0x0000a01f,0x00000000,m2c_csi2_rx_csi_rx_int_hdr_err_correct_3
0x0000a020,0x000000ff,m2c_csi2_rx_csi_rx_int_hdr_err_correct_0_en
0x0000a021,0x000000ff,m2c_csi2_rx_csi_rx_int_hdr_err_correct_1_en
0x0000a022,0x000000ff,m2c_csi2_rx_csi_rx_int_hdr_err_correct_2_en
0x0000a023,0x000000ff,m2c_csi2_rx_csi_rx_int_hdr_err_correct_3_en
0x0000a024,0x00000000,m2c_csi2_rx_debug_status0
0x0000a025,0x00000000,m2c_csi2_rx_debug_status1
0x0000a026,0x00000000,m2c_csi2_rx_debug_status2
0x0000a027,0x00000000,m2c_csi2_rx_debug_status3
0x0000a028,0x00000000,m2c_csi2_rx_debug_status4
0x0000a029,0x00000000,m2c_csi2_rx_debug_status5[0]
0x0000a02a,0x00000000,m2c_csi2_rx_debug_status5[1]
0x0000a02b,0x00000000,m2c_csi2_rx_debug_status6[0]
0x0000a02c,0x00000000,m2c_csi2_rx_debug_status6[1]
0x0000a02d,0x00000000,m2c_csi2_rx_debug_status6[2]
0x0000a02e,0x00000000,m2c_csi2_rx_debug_status6[3]
0x0000a02f,0x00000000,m2c_csi2_rx_debug_status6[4]
0x0000a030,0x00000000,m2c_csi2_rx_debug_status6[5]
0x0000a031,0x00000000,m2c_csi2_rx_debug_status6[6]
0x0000a032,0x00000000,m2c_csi2_rx_debug_status6[7]
0x0000a100,0x00000010,m2c_mipi_rx_mipi_dig_rx_ctrl00
0x0000a101,0x00000011,m2c_mipi_rx_mipi_dig_rx_ctrl01
0x0000a102,0x00000014,m2c_mipi_rx_mipi_dig_rx_ctrl02
0x0000a103,0x00000012,m2c_mipi_rx_mipi_dig_rx_ctrl03
0x0000a104,0x00000013,m2c_mipi_rx_mipi_dig_rx_ctrl04
0x0000a105,0x00000000,m2c_mipi_rx_mipi_dig_rx_ctrl1
0x0000a106,0x00000084,m2c_mipi_rx_mipi_dig_rx_res_cal0
0x0000a107,0x00000000,m2c_mipi_rx_mipi_dig_rx_res_cal1
0x0000a108,0x00000000,m2c_mipi_rx_mipi_dig_rx_res_cal2
0x0000a109,0x00000050,m2c_mipi_rx_mipi_dig_rx_dco_cal0
0x0000a10a,0x00000055,m2c_mipi_rx_mipi_dig_rx_dco_cal1
0x0000a10b,0x00000055,m2c_mipi_rx_mipi_dig_rx_dco_cal2
0x0000a10c,0x00000002,m2c_mipi_rx_mipi_dig_rx_dco_cal3
0x0000a10d,0x00000000,m2c_mipi_rx_mipi_dig_rx_dco_cal4
0x0000a10e,0x00000000,m2c_mipi_rx_mipi_dig_rx_offset_cal0[0]
0x0000a10f,0x00000000,m2c_mipi_rx_mipi_dig_rx_offset_cal0[1]
0x0000a110,0x00000000,m2c_mipi_rx_mipi_dig_rx_offset_cal0[2]
0x0000a111,0x00000000,m2c_mipi_rx_mipi_dig_rx_offset_cal0[3]
0x0000a112,0x00000000,m2c_mipi_rx_mipi_dig_rx_offset_cal0[4]
0x0000a113,0x0000007f,m2c_mipi_rx_mipi_dig_rx_offset_cal1[0]
0x0000a114,0x0000007f,m2c_mipi_rx_mipi_dig_rx_offset_cal1[1]
0x0000a115,0x0000007f,m2c_mipi_rx_mipi_dig_rx_offset_cal1[2]
0x0000a116,0x0000007f,m2c_mipi_rx_mipi_dig_rx_offset_cal1[3]
0x0000a117,0x0000007f,m2c_mipi_rx_mipi_dig_rx_offset_cal1[4]
0x0000a118,0x00000055,m2c_mipi_rx_mipi_dig_rx_boot0
0x0000a119,0x00000001,m2c_mipi_rx_mipi_dig_rx_boot1[0]
0x0000a11a,0x00000001,m2c_mipi_rx_mipi_dig_rx_boot1[1]
0x0000a11b,0x00000001,m2c_mipi_rx_mipi_dig_rx_boot1[2]
0x0000a11c,0x00000001,m2c_mipi_rx_mipi_dig_rx_boot1[3]
0x0000a11d,0x00000001,m2c_mipi_rx_mipi_dig_rx_boot1[4]
0x0000a11e,0x00000031,m2c_mipi_rx_mipi_dig_rx_boot2
0x0000a11f,0x00000013,m2c_mipi_rx_mipi_dig_rx_boot3
0x0000a120,0x00000000,m2c_mipi_rx_mipi_dig_rx_boot4
0x0000a121,0x00000000,m2c_mipi_rx_mipi_dig_rx_boot_status0
0x0000a122,0x00000000,m2c_mipi_rx_mipi_dig_rx_boot_status1[0]
0x0000a123,0x00000000,m2c_mipi_rx_mipi_dig_rx_boot_status1[1]
0x0000a124,0x00000000,m2c_mipi_rx_mipi_dig_rx_boot_status1[2]
0x0000a125,0x00000000,m2c_mipi_rx_mipi_dig_rx_boot_status1[3]
0x0000a126,0x00000000,m2c_mipi_rx_mipi_dig_rx_boot_status1[4]
0x0000a127,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk0[0]
0x0000a128,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk0[1]
0x0000a129,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk0[2]
0x0000a12a,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk0[3]
0x0000a12b,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk1[0]
0x0000a12c,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk1[1]
0x0000a12d,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk1[2]
0x0000a12e,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk1[3]
0x0000a12f,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_lsb[0]
0x0000a130,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_lsb[1]
0x0000a131,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_lsb[2]
0x0000a132,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_lsb[3]
0x0000a133,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_msb[0]
0x0000a134,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_msb[1]
0x0000a135,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_msb[2]
0x0000a136,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk2_msb[3]
0x0000a137,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk3[0]
0x0000a138,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk3[1]
0x0000a139,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk3[2]
0x0000a13a,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk3[3]
0x0000a13b,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk4[0]
0x0000a13c,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk4[1]
0x0000a13d,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk4[2]
0x0000a13e,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk4[3]
0x0000a13f,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk5[0]
0x0000a140,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk5[1]
0x0000a141,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk5[2]
0x0000a142,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk5[3]
0x0000a143,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk6[0]
0x0000a144,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk6[1]
0x0000a145,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk6[2]
0x0000a146,0x00000000,m2c_mipi_rx_mipi_dig_rx_test_chk6[3]
0x0000a147,0x00000031,m2c_mipi_rx_mipi_dig_rx_slv_init_time
0x0000a148,0x00000001,m2c_mipi_rx_mipi_dig_rx_mipi_ctrl
0x0000a149,0x0000009a,m2c_mipi_rx_mipi_dig_rx_prbs_seed0
0x0000a14a,0x0000009a,m2c_mipi_rx_mipi_dig_rx_prbs_seed1
0x0000a14b,0x0000009a,m2c_mipi_rx_mipi_dig_rx_prbs_seed2
0x0000a14c,0x0000009a,m2c_mipi_rx_mipi_dig_rx_prbs_seed3
0x0000a14d,0x00000007,m2c_mipi_rx_mipi_dig_rx_int_enable
0x0000a14e,0x00000000,m2c_mipi_rx_mipi_dig_rx_int_clear
0x0000a200,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_0[0]
0x0000a201,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_0[1]
0x0000a202,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_0[2]
0x0000a203,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_0[3]
0x0000a204,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_0[4]
0x0000a205,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_1[0]
0x0000a206,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_1[1]
0x0000a207,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_1[2]
0x0000a208,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_1[3]
0x0000a209,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_1[4]
0x0000a20a,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_2[0]
0x0000a20b,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_2[1]
0x0000a20c,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_2[2]
0x0000a20d,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_2[3]
0x0000a20e,0x00000000,m2c_mipi_rx_lane_module_rx_r_dbg_ctrl_2[4]
0x0000a20f,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_0[0]
0x0000a210,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_0[1]
0x0000a211,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_0[2]
0x0000a212,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_0[3]
0x0000a213,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_0[4]
0x0000a214,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_1[0]
0x0000a215,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_1[1]
0x0000a216,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_1[2]
0x0000a217,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_1[3]
0x0000a218,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_1[4]
0x0000a219,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_2[0]
0x0000a21a,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_2[1]
0x0000a21b,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_2[2]
0x0000a21c,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_2[3]
0x0000a21d,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_2[4]
0x0000a21e,0x000000b8,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_3
0x0000a21f,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_4[0]
0x0000a220,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_4[1]
0x0000a221,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_4[2]
0x0000a222,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_4[3]
0x0000a223,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_4[4]
0x0000a224,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_5[0]
0x0000a225,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_5[1]
0x0000a226,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_5[2]
0x0000a227,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_5[3]
0x0000a228,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_5[4]
0x0000a229,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_6[0]
0x0000a22a,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_6[1]
0x0000a22b,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_6[2]
0x0000a22c,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_6[3]
0x0000a22d,0x00000000,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_6[4]
0x0000a22e,0x0000000f,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_7[0]
0x0000a22f,0x0000000f,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_7[1]
0x0000a230,0x0000000f,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_7[2]
0x0000a231,0x0000000f,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_7[3]
0x0000a232,0x0000000f,m2c_mipi_rx_lane_module_rx_r_sw_force_cfg_7[4]
0x0000a233,0x00000000,m2c_mipi_rx_lane_module_rx_ro_status_0[0]
0x0000a234,0x00000000,m2c_mipi_rx_lane_module_rx_ro_status_0[1]
0x0000a235,0x00000000,m2c_mipi_rx_lane_module_rx_ro_status_0[2]
0x0000a236,0x00000000,m2c_mipi_rx_lane_module_rx_ro_status_0[3]
0x0000a237,0x00000000,m2c_mipi_rx_lane_module_rx_ro_status_0[4]
0x0000a238,0x00000000,m2c_mipi_rx_lane_module_rx_ro_error_indicator_0[0]
0x0000a239,0x00000000,m2c_mipi_rx_lane_module_rx_ro_error_indicator_0[1]
0x0000a23a,0x00000000,m2c_mipi_rx_lane_module_rx_ro_error_indicator_0[2]
0x0000a23b,0x00000000,m2c_mipi_rx_lane_module_rx_ro_error_indicator_0[3]
0x0000a23c,0x00000000,m2c_mipi_rx_lane_module_rx_ro_error_indicator_0[4]
0x0000a23d,0x00000000,m2c_mipi_rx_lane_module_rx_r_bit_fmt_cfg_0[0]
0x0000a23e,0x00000000,m2c_mipi_rx_lane_module_rx_r_bit_fmt_cfg_0[1]
0x0000a23f,0x00000000,m2c_mipi_rx_lane_module_rx_r_bit_fmt_cfg_0[2]
0x0000a240,0x00000000,m2c_mipi_rx_lane_module_rx_r_bit_fmt_cfg_0[3]
0x0000a241,0x00000000,m2c_mipi_rx_lane_module_rx_r_bit_fmt_cfg_0[4]
0x0000a242,0x00000003,m2c_mipi_rx_lane_module_rx_r_deskew_dly_win_cfg
0x0000a243,0x00000003,m2c_mipi_rx_lane_module_rx_r_deskew_det_win_cfg
0x0000a244,0x00000001,m2c_mipi_rx_lane_module_rx_r_deskew_dat_dly_adj_step
0x0000a245,0x00000001,m2c_mipi_rx_lane_module_rx_r_deskew_clk_dly_adj_step
0x0000a246,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_dat_dly_init_cfg
0x0000a247,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_clk_dly_init_cfg
0x0000a248,0x00000000,m2c_mipi_rx_lane_module_rx_r_deskew_sw_force_cfg[0]
0x0000a249,0x00000000,m2c_mipi_rx_lane_module_rx_r_deskew_sw_force_cfg[1]
0x0000a24a,0x00000000,m2c_mipi_rx_lane_module_rx_r_deskew_sw_force_cfg[2]
0x0000a24b,0x00000000,m2c_mipi_rx_lane_module_rx_r_deskew_sw_force_cfg[3]
0x0000a24c,0x00000000,m2c_mipi_rx_lane_module_rx_r_deskew_sw_force_cfg[4]
0x0000a24d,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_clk_val[0]
0x0000a24e,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_clk_val[1]
0x0000a24f,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_clk_val[2]
0x0000a250,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_clk_val[3]
0x0000a251,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_clk_val[4]
0x0000a252,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_dat_val[0]
0x0000a253,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_dat_val[1]
0x0000a254,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_dat_val[2]
0x0000a255,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_dat_val[3]
0x0000a256,0x00000000,m2c_mipi_rx_lane_module_rx_r_deslew_sw_force_dat_val[4]
0x0000a257,0x00000000,m2c_mipi_rx_lane_module_rx_ro_deskew_status[0]
0x0000a258,0x00000000,m2c_mipi_rx_lane_module_rx_ro_deskew_status[1]
0x0000a259,0x00000000,m2c_mipi_rx_lane_module_rx_ro_deskew_status[2]
0x0000a25a,0x00000000,m2c_mipi_rx_lane_module_rx_ro_deskew_status[3]
0x0000a25b,0x00000000,m2c_mipi_rx_lane_module_rx_ro_deskew_status[4]
0x0000a25c,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_clk_val[0]
0x0000a25d,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_clk_val[1]
0x0000a25e,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_clk_val[2]
0x0000a25f,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_clk_val[3]
0x0000a260,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_clk_val[4]
0x0000a261,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val[0]
0x0000a262,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val[1]
0x0000a263,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val[2]
0x0000a264,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val[3]
0x0000a265,0x00000000,m2c_mipi_rx_lane_module_rx_ro_dekskew_final_dat_val[4]
0x0000a266,0x00000000,m2c_mipi_rx_lane_module_rx_ro_ls_state[0]
0x0000a267,0x00000000,m2c_mipi_rx_lane_module_rx_ro_ls_state[1]
0x0000a268,0x00000000,m2c_mipi_rx_lane_module_rx_ro_ls_state[2]
0x0000a269,0x00000000,m2c_mipi_rx_lane_module_rx_ro_ls_state[3]
0x0000a26a,0x00000000,m2c_mipi_rx_lane_module_rx_ro_ls_state[4]
0x0000a26b,0x00000000,m2c_mipi_rx_lane_module_rx_ro_hs_state[0]
0x0000a26c,0x00000000,m2c_mipi_rx_lane_module_rx_ro_hs_state[1]
0x0000a26d,0x00000000,m2c_mipi_rx_lane_module_rx_ro_hs_state[2]
0x0000a26e,0x00000000,m2c_mipi_rx_lane_module_rx_ro_hs_state[3]
0x0000a26f,0x00000000,m2c_mipi_rx_lane_module_rx_ro_hs_state[4]
0x0000a270,0x00000000,m2c_mipi_rx_lane_module_rx_r_continous_clk_mode
0x0000a271,0x00000000,m2c_mipi_rx_lane_module_rx_r_lane_ls_settle[0]
0x0000a272,0x00000000,m2c_mipi_rx_lane_module_rx_r_lane_ls_settle[1]
0x0000a273,0x00000000,m2c_mipi_rx_lane_module_rx_r_lane_ls_settle[2]
0x0000a274,0x00000000,m2c_mipi_rx_lane_module_rx_r_lane_ls_settle[3]
0x0000a275,0x00000000,m2c_mipi_rx_lane_module_rx_r_lane_ls_settle[4]
0x0000a276,0x0000001f,m2c_mipi_rx_lane_module_rx_int_enable[0]
0x0000a277,0x0000001f,m2c_mipi_rx_lane_module_rx_int_enable[1]
0x0000a278,0x0000001f,m2c_mipi_rx_lane_module_rx_int_enable[2]
0x0000a279,0x0000001f,m2c_mipi_rx_lane_module_rx_int_enable[3]
0x0000a27a,0x0000001f,m2c_mipi_rx_lane_module_rx_int_enable[4]
0x0000a27b,0x00000000,m2c_mipi_rx_lane_module_rx_hsrx_timeout_cnt0
0x0000a27c,0x000000c0,m2c_mipi_rx_lane_module_rx_hsrx_timeout_cnt1
0x0000a27d,0x00000007,m2c_mipi_rx_lane_module_rx_ctrl00
0x0000a27e,0x00000000,m2c_mipi_rx_lane_module_rx_ro_offset_cal[0]
0x0000a27f,0x00000000,m2c_mipi_rx_lane_module_rx_ro_offset_cal[1]
0x0000a280,0x00000000,m2c_mipi_rx_lane_module_rx_ro_offset_cal[2]
0x0000a281,0x00000000,m2c_mipi_rx_lane_module_rx_ro_offset_cal[3]
0x0000a282,0x00000000,m2c_mipi_rx_lane_module_rx_ro_offset_cal[4]
0x0000a300,0x00000000,m2c_mipi_rx_dphy_rx_reg1
0x0000a301,0x00000010,m2c_mipi_rx_dphy_rx_reg2
0x0000a302,0x00000000,m2c_mipi_rx_dphy_rx_reg5[0]
0x0000a303,0x00000000,m2c_mipi_rx_dphy_rx_reg5[1]
0x0000a304,0x00000000,m2c_mipi_rx_dphy_rx_reg5[2]
0x0000a305,0x00000000,m2c_mipi_rx_dphy_rx_reg5[3]
0x0000a306,0x00000000,m2c_mipi_rx_dphy_rx_reg5[4]
0x0000a307,0x00000000,m2c_mipi_rx_dphy_rx_reg6[0]
0x0000a308,0x00000000,m2c_mipi_rx_dphy_rx_reg6[1]
0x0000a309,0x00000000,m2c_mipi_rx_dphy_rx_reg6[2]
0x0000a30a,0x00000000,m2c_mipi_rx_dphy_rx_reg6[3]
0x0000a30b,0x00000000,m2c_mipi_rx_dphy_rx_reg6[4]
0x0000a30c,0x00000000,m2c_mipi_rx_dphy_rx_reg7[0]
0x0000a30d,0x00000000,m2c_mipi_rx_dphy_rx_reg7[1]
0x0000a30e,0x00000000,m2c_mipi_rx_dphy_rx_reg7[2]
0x0000a30f,0x00000000,m2c_mipi_rx_dphy_rx_reg7[3]
0x0000a310,0x00000000,m2c_mipi_rx_dphy_rx_reg7[4]
0x0000a340,0x0000000c,m2c_mipi_rx_dphy_top_rx_ref
0x0000a341,0x00000004,m2c_mipi_rx_dphy_top_rx_dldo
