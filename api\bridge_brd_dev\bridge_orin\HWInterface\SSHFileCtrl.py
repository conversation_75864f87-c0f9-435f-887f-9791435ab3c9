# 需要安装下面模块
# pip3.9.exe install paramiko

# SSH 服务器的相关信息
import paramiko
import os
import stat

port = 22
def get_remote_directory_contents(host_ip, username="meritech", password="meritech", remote_path="."):
    """
    获取远程文件夹的内容信息
    """
    contents = []
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh.connect(host_ip, port, username, password)
        sftp = ssh.open_sftp()
        
        contents = sftp.listdir_attr(remote_path)

        sftp.close()
    except Exception as e:
        print(f"Failed to list directory contents: {str(e)}")
    finally:
        ssh.close()
        
    return contents

def download_directory_via_ssh(host_ip, username="meritech", password="meritech", remote_dir=".", local_dir="."):
    """
    下载整个远程文件夹到本地
    """
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh.connect(host_ip, port, username, password)
        sftp = ssh.open_sftp()

        def _download_dir(remote_directory, local_directory):
            if not os.path.exists(local_directory):
                os.makedirs(local_directory)
            
            dir_items = sftp.listdir_attr(remote_directory)
            for item in dir_items:
                remote_item = os.path.join(remote_directory, item.filename)
                local_item = os.path.join(local_directory, item.filename)
                
                if stat.S_ISDIR(item.st_mode):
                    _download_dir(remote_item, local_item)  # 递归下载子目录
                else:
                    print(f"Downloading file {remote_item} to {local_item}")
                    sftp.get(remote_item, local_item)

        _download_dir(remote_dir, local_dir)
        
        sftp.close()
        print("Directory downloaded successfully.")
    except Exception as e:
        print(f"Failed to download directory: {str(e)}")
    finally:
        ssh.close()

def download_file_via_ssh(host_ip, username="meritech", password="meritech", r_path="default.txt", l_path="default.txt"):
    ret = 0
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh.connect(host_ip, port, username, password)
        sftp = ssh.open_sftp()
        sftp.get(r_path, l_path)
        sftp.close()
        
        print("File downloaded successfully.")
        ret = 0
    except Exception as e:
        print(f"Failed to download file: {str(e)}")
        ret = -1
    finally:
        ssh.close()
    
    return ret

def clear_remote_directory(host_ip, username="meritech", password="meritech", remote_dir="."):
    """
    清除远程文件夹的内容
    """
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh.connect(host_ip, port, username, password)
        sftp = ssh.open_sftp()

        def _clear_dir(remote_directory):
            dir_items = sftp.listdir_attr(remote_directory)
            for item in dir_items:
                remote_item = os.path.join(remote_directory, item.filename)
                
                if stat.S_ISDIR(item.st_mode):
                    _clear_dir(remote_item)  # 递归清除子目录
                    sftp.rmdir(remote_item)  # 删除空子目录
                else:
                    print(f"Deleting file {remote_item}")
                    sftp.remove(remote_item)

        _clear_dir(remote_dir)
        print(f"Directory {remote_dir} cleared successfully.")
    except Exception as e:
        print(f"Failed to clear directory: {str(e)}")
    finally:
        ssh.close()

# 清空orin中的图片和tracelog日志文件
def clear_orin_picture_and_tracelog_folder(host_ip):
    HOST_IP = host_ip
    USERNAME = "meritech"
    PASSWORD = "meritech"

    # 清除Pictures文件夹
    REMOTE_DIR = "/home/<USER>/Pictures/"
    clear_remote_directory(HOST_IP, USERNAME, PASSWORD, REMOTE_DIR)

    # 清除tracelog文件夹
    REMOTE_DIR = "/home/<USER>/tracelog/"
    clear_remote_directory(HOST_IP, USERNAME, PASSWORD, REMOTE_DIR)


# 获取picture和tracelog文件
def get_orin_picture_and_tracelog_folder(host_ip, local_path):
    HOST_IP = host_ip
    USERNAME = "meritech"
    PASSWORD = "meritech"
    REMOTE_DIR = "/home/<USER>/Pictures/"
    LOCAL_DIR = local_path+"/Pictures/"

    # 获取保存的图片文件
    contents = get_remote_directory_contents(HOST_IP, USERNAME, PASSWORD, REMOTE_DIR)
    for item in contents:
        file_type = 'Directory' if stat.S_ISDIR(item.st_mode) else 'File'
        print(f"{item.filename} - {file_type} - Size: {item.st_size} bytes - Modified: {item.st_mtime}")

    # 下载整个远程目录到本地
    download_directory_via_ssh(HOST_IP, USERNAME, PASSWORD, REMOTE_DIR, LOCAL_DIR)

    # 获取Trace日志文件
    REMOTE_DIR = "/home/<USER>/tracelog/"
    LOCAL_DIR = local_path+"/tracelog/"

    # 获取远程目录内容信息
    contents = get_remote_directory_contents(HOST_IP, USERNAME, PASSWORD, REMOTE_DIR)
    for item in contents:
        file_type = 'Directory' if stat.S_ISDIR(item.st_mode) else 'File'
        print(f"{item.filename} - {file_type} - Size: {item.st_size} bytes - Modified: {item.st_mtime}")

    # 下载整个远程目录到本地
    download_directory_via_ssh(HOST_IP, USERNAME, PASSWORD, REMOTE_DIR, LOCAL_DIR)

