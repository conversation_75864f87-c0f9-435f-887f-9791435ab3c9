#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import serial
import serial.tools.list_ports
import time
import threading
from USBInterface import CmdFuncation
from threading import Lock

class USBDataDeal:
    def __init__(self):
        self.serial_port = serial.Serial(None, baudrate=115200, timeout=0)
        self.is_exit: bool = False
        self.usbdev_Lock = Lock()
        self.cmd_data = []
        self.cmd_idx: int = 0
        self.cmdFuncation = None

    def __del__(self):
        pass
    
    def setUSBDataDeal(self, in_cmdFuncation):
        self.cmdFuncation = in_cmdFuncation
    
    def check_cmd(self, cmd_buff: bytearray, cmd_len: int):
        return True
    
    # 处理完整指令
    def deal_cmd(self, cmd_buff: bytearray, cmd_len: int):
        if not self.check_cmd(cmd_buff, cmd_len):
            return False
    
        self.cmdFuncation.publish_cmd(cmd_buff, cmd_len)
    
    # 解析数据缓冲区
    def deal_cmd_buff(self, data_all):
        for byte in data_all:
            if self.cmd_idx != 0:
                self.cmd_data.append(byte)
                self.cmd_idx += 1
                # 判断cmd_data中是否存储了一条完整的cmd数据，如果是一条完整的数据，就进行cmd处理。
                if self.cmd_data[self.cmd_idx - 1] == 0xA5 and self.cmd_data[self.cmd_idx - 2] == 0xA5:
                    self.deal_cmd(bytearray(self.cmd_data), self.cmd_idx)
                    self.cmd_data.clear()
                    self.cmd_idx = 0
    
                # 当接收到1124个字节，还是没有找到cmd结束标记，说明数据已经出问题了，这里直接将数据丢弃，重新解析。
                if self.cmd_idx >= 1124:
                    self.cmd_idx = 0
            else:
                # 一条新的数据，必须由AA开头，否则就丢弃异常数据。
                if byte == 0xAA:
                    self.cmd_data.append(byte)
                    self.cmd_idx += 1
    
    
    # 接收usb传递过来的数据，放入到缓冲区中
    def usb_receive_data_thread(self):
        print("USB Port Receive Thread Start....")
    
        self.serial_port.flush()
        while not self.is_exit:
            # 获取当前可读的数据
            datas = self.serial_port.read(self.serial_port.inWaiting())    
            if datas:
                self.deal_cmd_buff(datas)
    
        print("USB Port Receive Thread Exit....\n")
    
    
    # 扫码符合要求的设备
    @staticmethod
    def ser_ee_dev_scan():
        usbdev_list:list = []
        port_list = list(serial.tools.list_ports.comports())
        for i in range(0, len(port_list)):
            if port_list[i].vid == 1155 and port_list[i].pid == 22336:
                usbdev_list.append(port_list[i])
    
        return usbdev_list
    
    
    # 打开设备
    def serial_open(self, port_name):
        if len(port_name) == 0:
            return -1
        self.serial_port.name = port_name
        self.serial_port = serial.Serial(port_name, baudrate=115200, timeout=0.5)
        if not self.serial_port.isOpen():  # 判断串口是否成功打开
            print("打开串口成功: ", self.serial_port.name)  # 输出串口号
        else:
            print("打开串口成功: ", self.serial_port.name)  # 输出串口号
    
        self.is_exit = False
        run_thread = threading.Thread(target=self.usb_receive_data_thread, name="serial_receive")
        # 设置为daemon线程，程序在退出的时候，线程自动退出
        run_thread.daemon = True
        run_thread.start()
    
        return 0
    
    
    # 打开设备
    def serial_showinfo(self):
        print("        serial_port:", self.serial_port)
        return 0
    
    
    # 关闭设备
    def serial_close(self):
    
        print("serial_close", self.serial_port.name)
        time.sleep(0.1)
        self.is_exit = True
        time.sleep(0.1)
        if self.serial_port.isOpen():
            self.serial_port.close()
            print("关闭串口成功: ", self.serial_port.name)  # 输出串口号
        else:
            print("串口已处于关闭状态: ", self.serial_port.name)
    
    
    # 发送函数
    def usb_send_buff(self, data):
        if not self.serial_port.isOpen():    # 串口是打开的，才进行数据传递
            return -1
    
        self.usbdev_Lock.acquire()
        self.serial_port.write(data)
        self.usbdev_Lock.release()
        return 0
