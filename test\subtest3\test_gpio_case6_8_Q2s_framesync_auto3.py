# -*- coding: utf-8 -*-
"""
GPIO Case-6: Bidirectional Signal Transmission Path - Q68 Chip Version

基于test_gpio_case6_8_s2q_auto2.py模板，适配Q68芯片测试。
支持双向信号测试：
1. S2Q方向：S68 GPIO → Q68 GPIO（串行测试，一个Link一个Link）
2. Q2S方向：Q68 GPIO → S68 GPIO（并行测试，所有Links同时）

主要修改：
1. 导入模块使用M65Q68_Common_Fuction_A0
2. MFN设置函数使用MFNSet_Q68E
3. 支持4个Link测试（Link0,1,2,3），当前Link1,2可用
4. 使用独立设备初始化，包含完整地址转换
5. 新增Q2S方向测试功能
"""
import logging
import time
import pytest
import os
import sys
import tkinter as tk
from tkinter import messagebox
# Add parent directory to path to import conftest
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
try:
    from conftest import get_universal_oscilloscope_screenshot, power_on
    print("✅ Successfully imported from conftest")
except ImportError as e:
    print(f"❌ Failed to import from conftest: {e}")
    # Define fallback functions if import fails
    def get_universal_oscilloscope_screenshot(oscilloscope):
        print("⚠️ Using fallback oscilloscope screenshot function")
        class FallbackScreenshot:
            def __init__(self, osc): self.osc = osc
            def execute_screenshot(self, **kwargs): return True
        return FallbackScreenshot(oscilloscope)

    def power_on():
        print("⚠️ Using fallback power_on function")
        return True


# 禁用pyvisa的DEBUG日志，减少冗余输出
logging.getLogger('pyvisa').setLevel(logging.WARNING)

# 添加Q68模块路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65d68_a0'))  # Q68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'))  # S68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # 示波器模块路径

# D68芯片专用导入
try:
    from Common.M65Q68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    print("✅ D68模块导入成功")
except ImportError as e:
    print(f"⚠️ D68模块导入失败: {e}")
    # print("🔄 尝试使用Q68模块作为兼容...")
    # try:
    #     from Common.M65Q68_Common_Fuction_A0 import *
    #     from Common_var.M66S68_Common_Fuction_A0 import *
    #     print("✅ 使用Q68模块作为D68兼容")
    # except ImportError as e2:
    #     print(f"❌ 所有模块导入失败: {e2}")
    #     # 定义空类以避免错误
    class M65Q68_A0: pass
    class M65Q68_A0_Remote_M66S68: pass

# D68设备初始化配置
D68_DEVICE_CONFIG = {
    'q68_iic_addr': 0x73,
    's68_iic_dev': [0x40, 0x40, 0x40, 0x40],
    's68_res_dev': [0x20, 0x21, 0x22, 0x23],
    's68_res_sensor_dev': [0x24, 0x25, 0x26, 0x27],
    'active_links': [0, 1, 2, 3],  # D68仅支持Link0和Link2
    'RATE':         [2, 2, 2, 2],
    'RATE_final':   [2, 2, 2, 2],
    'BCRATE':       [0, 0, 0, 0]  
}

# 全局停止标志
GLOBAL_STOP_FLAG = False

# 测试配置 - D68版本
TEST_CONFIG = {
    'active_links': [0, 1, 2, 3],                # D68版本仅使用Link0和Link2
    'signal_id': 11,                        # GPIO信号ID
    'observation_time': 1,                  # 观察时间(秒)
    's68_source_gpio': 8,                   # S68源GPIO (用于截图文件命名，每次测试都设置同一个)

    # 示波器自动截图配置
    'oscilloscope_config': {
        'enable_screenshot': True,          # 是否启用自动截图
        'test_mode': 'combined',            # 测试模式: 'fixed'=仅固定频率, 'sweep'=仅扫频, 'combined'=组合测试
        'trigger_source': 'C1',            # 触发源: C1-C4(模拟), D0-D15(数字), EX, EX5, LINE
        # 'waveform_type': 'SQUARE',          # 方波
        # 固定频率模式配置
        # 'frequency_list': [30, 1000, 10000, 50000, 100000],  # 固定频率列表 (Hz)
        # 'timebase_list': ['20ms', '1ms', '100us', '10us', '5us'],   # 对应的时基 - 优化100kHz时基
        # 'frequency_list': [30,1000 ],  # 固定频率列表 (Hz)
        # 'timebase_list': ['20ms','1ms' ],   # 对应的时基 - 优化100kHz时基
        # 扫频模式配置
        # 'frequency_range': {
        #     'start': 1210000,                    # 1210kHz
        #     'end': 1210000,                      # 1240kHz
        #     'step': 10000                       # 10kHz步进
        # },
        # 'sweep_timebase': '500ns',              # 扫频模式的时基
        'frequency_range': {
            'start': 450000,                    # 1210kHz
            'end': 470000,                      # 1240kHz
            'step': 10000                       # 10kHz步进
        },
        'sweep_timebase': '2us',              # 扫频模式的时基
        'amplitude': 1.8,                      # 1.8Vpp
        'offset': 0.9,                          # 900mVdc偏移
        'screenshot_folder_base': 'U-disk0/gpiotest/s68tod68_0_2',  # D68版本截图保存基础文件夹
        'screenshot_folder_base_d2s': 'U-disk0/gpiotest/d68tos68_0_2',  # D2S版本截图保存基础文件夹
        'screenshot_folder_base_framegen': 'U-disk0/gpiotest/d68tos68_0_2', 
        'persistence_mode': 'OFF',         # 余晖模式 (该机型唯一选项)
        'probe_wait_time': 1,                  # 第一次截图前等待插探头时间(秒)
        'freq_observation_time': 1.3,             # 每个频率的观察时间(秒)
        'enable_screenshot': True,              # 是否启用自动截图
        'trigger_source': 'D1',                # 触发源: D1数字通道
        'enable_power_cycle': True,             # 每个频率切换时是否断电重新初始化
    },

    # 动态生成configurations，使用统一的S68 GPIO变量
    'configurations': None, 
     # 将在运行时动态生成

    # FrameSync专用配置
    'framegen_frequencies': [12, 30, 50],  # 自定义频率列表 (Hz)
    'frequency_timebases': ['20ms', '10ms', '5ms'],  # 对应时基
    
    'rvs_test_frequencies': [ 30,50],  # rvs_dly测试频率（可自定义多个值）
    'rvs_dly_values': [0, 4, 10 ,20 ,30 ,40 ,50 ,60 ,63],  # rvs_dly测试值
    
    'duty_test_frequency': 30,  # 占空比测试频率
    'duty_cycle_values': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],  # 占空比值: 5%-50%
    
    'frame_sync_configs': [  # 3个frame sync配置，每个使用不同频率
        {'i': 0, 'gpio': 0, 'fs_tx_id': 11, 'frequency': 30},  # frame sync0 -> GPIO0, 12Hz
        {'i': 1, 'gpio': 1, 'fs_tx_id': 13, 'frequency': 30},  # frame sync1 -> GPIO1, 30Hz
        {'i': 2, 'gpio': 2, 'fs_tx_id': 15, 'frequency': 30},  # frame sync2 -> GPIO2, 50Hz
    ],
}

def calculate_framegen_params(target_freq):
    """智能频率配比计算FrameGen参数"""
    divider = {0:1, 1:2, 2:3, 3:6, 4:8, 5:10, 6:12, 7:16, 8:20, 9:24, 10:32, 11:48, 12:64, 13:80, 14:96, 15:128}

    best_params = None
    min_error = float('inf')

    for per_div in range(16):
        per_div_val = divider[per_div]
        period = int(25000000 / (target_freq * per_div_val))
        if period > 0 and period < 65536:
            actual_freq = 25000000 / (period * per_div_val)
            error = abs(actual_freq - target_freq)
            if error < min_error:
                min_error = error
                best_params = (per_div, period, actual_freq)

    return best_params

def d68_complete_initialization():
    """D68完整初始化函数 -  (初始化D68 Q68设备->初始化D68 Q68远程设备->D68特定初始化->设置链路速率->地址转换)"""

    config = D68_DEVICE_CONFIG
    q68 = M65Q68_A0(dongle='stm32', id=0, bus='i2c')  # 初始化D68 Q68设备
    q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')  # 初始化D68 Q68远程设备
    # q68.c2m.wr_test_glb_ctrl0_fields(key=0x5c)
    # q68.c2m.wr_test_tx_link_data_inv_fields(tx_polar_sel=0x6)
    # q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel=0x6)  # D68特定初始化
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])
    q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=config['BCRATE'][0], bc_rate1=config['BCRATE'][1], bc_rate2=config['BCRATE'][2], bc_rate3=config['BCRATE'][3])
    q68.Q68_C3_6G_Init(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])  # 设置链路速率
    for link in config['active_links']: 
        q68_remote.S68_AddrTrans(link=link, q68_iic_addr=0x73, s68_iic_addr=0x40, s68_retrans_addr=config['s68_res_dev'][link], sensor_addr=0x24, sensor_retrans_addr=0x24 + link)  # 地址转换
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])
    q68.Q68_C3_6G_Init(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])  # 设置链路速率
    try: from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X; oscilloscope = SiglentSDS5034X()
    except: oscilloscope = None
    return {'q68': q68, 'q68_remote': q68_remote, 's68_res_dev': config['s68_res_dev'], 'oscilloscope': oscilloscope, 'configure_links': config['active_links']}


class GPIO_S68_D68_AutoTester:
    """S68到D68的GPIO自动测试器"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def create_framegen_folders(self):
        """创建FrameGen文件夹 - 最少行数实现"""
        import os
        base = TEST_CONFIG['oscilloscope_config']['screenshot_folder_base_framegen']
        for freq in TEST_CONFIG['framegen_frequencies']: os.makedirs(f"{base}/{freq}Hz", exist_ok=True)
        for freq in TEST_CONFIG['rvs_test_frequencies']:
            os.makedirs(f"{base}/{freq}Hz", exist_ok=True)
        # 占空比测试文件夹
        os.makedirs(f"{base}/{TEST_CONFIG['duty_test_frequency']}Hz", exist_ok=True)
        # 多sync测试文件夹
        os.makedirs(f"{base}/multi_sync", exist_ok=True)

    def show_rvs_dly_confirmation_dialog(self, test_name, current, total):
        """rvs_dly专用确认对话框 - 5秒后自动选择是"""
        global GLOBAL_STOP_FLAG

        # 如果已经设置了全局停止标志，直接返回None
        if GLOBAL_STOP_FLAG:
            return None

        import threading

        result = [True]  # 默认为True

        def show_dialog():
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                root.attributes('-topmost', True)  # 置顶
                root.lift()  # 提升到最前面
                root.focus_force()  # 强制获取焦点

                message = f"测试 FrameSync {test_name}?\n进度: {current}/{total}\n\n5秒后自动开始..."

                # 使用简单的messagebox，更稳定
                from tkinter import messagebox
                dialog_result = messagebox.askyesnocancel("FrameSync rvs_dly测试确认", message, parent=root)

                # 设置结果
                result[0] = dialog_result

                # 如果用户点击取消，设置全局停止标志
                if dialog_result is None:
                    global GLOBAL_STOP_FLAG
                    GLOBAL_STOP_FLAG = True

                root.destroy()
            except:
                pass

        # 在新线程中显示对话框
        dialog_thread = threading.Thread(target=show_dialog, daemon=True)
        dialog_thread.start()

        # 等待5秒或用户操作
        dialog_thread.join(timeout=2.0)


        # 如果用户点击了取消，设置全局停止标志
        if result[0] is None:
            GLOBAL_STOP_FLAG = True

        return result[0]

    def full_system_reinit_d68(self):
        """完整系统重新初始化 - 从断电重新上电开始"""
        try:
            print(f"🔄 清理旧连接...")
            # 清理旧的设备连接
            try:
                if hasattr(self, 'q68') and self.q68: del self.q68
                if hasattr(self, 'q68_remote') and self.q68_remote: del self.q68_remote
            except: pass

            print(f"🔄 断电重新上电...")
            power_on()
            devices = d68_complete_initialization()
            self.q68 = devices['q68']
            self.q68_remote = devices['q68_remote']
            self.s68_res_dev = devices['s68_res_dev']
            self.oscilloscope = devices['oscilloscope']
            self.configure_links = devices['configure_links']
            print(f"✅ 重新上电初始化完成")
            return True
        except Exception as e:
            print(f"❌ 重新上电初始化失败: {e}")
            return False

    def test_single_framegen_all_link_s68(self, frequency, rvs_dly=0, tx_dly_en=0, duty_cycle=None, double_shot=False, is_rvs_test=False):
        """测试单个FrameGen频率到所有S68 GPIO - D2S版本（智能频率配比）"""
        try:
            signal_id = TEST_CONFIG['signal_id']
            active_links = TEST_CONFIG['active_links']

            # 智能计算FrameGen参数
            params = calculate_framegen_params(frequency)
            if not params:
                print(f"❌ 频率{frequency}Hz参数计算失败")
                return False

            per_div, period, actual_freq = params
            print(f"[FrameSync from Q68]: 目标频率={frequency}Hz, 实际频率={actual_freq:.2f}Hz, per_div={per_div}, period={period}")

            # S68端配置（接收端）- 同时配置所有活跃Links的所有GPIO
            for link in active_links:
                if link < len(self.s68_res_dev):
                    print(f"    📡 配置S68 Link{link} (地址: 0x{self.s68_res_dev[link]:02X})")
                    self.q68_remote.dongle.devAddr = self.s68_res_dev[link]
                    time.sleep(0.1)  # 地址切换后稳定时间

                    for gpio_n in range(9):  # S68 GPIO 0-8
                        self.q68_remote.M2CMFNSet(gpio=gpio_n, mfn=0)
                        self.q68_remote.M2CGPIORemoteRx(gpio=gpio_n, rx_id=signal_id)
                        self.q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly)
                        time.sleep(0.01)  # GPIO配置间隔

            # D68端配置（发送端）
            self.q68.MFNSet_Q68E(gpio=0, mfn=1)
            actual_duty_cycle = duty_cycle if duty_cycle is not None else 4
            self.q68.FrameSyncOutConifg(i=0,per_div=per_div, duty_cycle=actual_duty_cycle, period=period,
                                       fs_tx_id=signal_id, auto_fs=1, outen=1, tx_dly_en=tx_dly_en)

            # 示波器截图（仅截图，不产生信号）
            self.framegen_screenshot(frequency, rvs_dly, duty_cycle, double_shot, is_rvs_test)
            return True
        except Exception as e:
            print(f"❌ FrameGen {frequency}Hz测试失败: {e}")
            return False

    def framegen_screenshot(self, frequency, rvs_dly=0, duty_cycle=None, double_shot=False, is_rvs_test=False, is_multi_sync=False):
        """FrameGen专用截图方法"""
        if not self.oscilloscope or not TEST_CONFIG['oscilloscope_config'].get('enable_screenshot', True):
            return True

        try:
            # 文件夹路径
            if is_multi_sync:
                screenshot_folder = f"{TEST_CONFIG['oscilloscope_config']['screenshot_folder_base_framegen']}/multi_sync"
            else:
                screenshot_folder = f"{TEST_CONFIG['oscilloscope_config']['screenshot_folder_base_framegen']}/{frequency}Hz"
            os.makedirs(screenshot_folder, exist_ok=True)

            # 智能选择时基和触发源
            if duty_cycle is not None:  # 占空比测试
                timebase = '10ms'
                trigger_source = 'D0'
                print(f"    📊 占空比测试: 时基={timebase}, 触发源={trigger_source}")
            elif is_rvs_test:  # rvs测试（包括rvs_dly=0）
                timebase = '50us'
                trigger_source = 'C1'
                print(f"    📊 rvs测试: 时基={timebase}, 触发源={trigger_source}, rvs_dly={rvs_dly}")
            elif is_multi_sync:  # 多sync测试
                timebase = '20ms'
                trigger_source = 'D0'
                print(f"    📊 多sync测试: 时基={timebase}, 触发源={trigger_source}")
            else:  # 频率测试
                if frequency <= 30:
                    timebase = '20ms'
                elif frequency <= 60:
                    timebase = '10ms'
                else:
                    timebase = '5ms'
                trigger_source = 'D1'
                print(f"    📊 频率测试: 时基={timebase}, 触发源={trigger_source}")

            # 配置示波器
            config = TEST_CONFIG['oscilloscope_config']
            self.oscilloscope.Set_Timebase_Scale(timebase_scale=timebase)
            self.oscilloscope.Set_Trigger_Source(trigger_source)
            self.oscilloscope.Set_Display_Persistence(time=config['persistence_mode'])

            # 等待FrameSync信号稳定
            time.sleep(config['freq_observation_time'])

            # 截图前等待5秒
            time.sleep(2)
            print(f"    - 等待2秒后进行截图...")

            # 第一次截图
            timestamp = time.strftime('%m%d_%H%M%S')
            if duty_cycle is not None:
                duty_percent = (duty_cycle + 1) * 5
                test_name = f"{frequency}Hz_duty_{duty_percent}%"
            elif is_rvs_test:
                test_name = f"{frequency}Hz_rvs_dly_{rvs_dly}"
            elif is_multi_sync:
                test_name = "multi_sync_12_30_50Hz"
            else:
                test_name = f"{frequency}Hz"

            screenshot_filename = f"framegen_{test_name}_{timestamp}.png"
            screenshot_path = f"{screenshot_folder}/{screenshot_filename}"
            self.oscilloscope.Save_Image(filepath=screenshot_path, image_format="PNG", invert="OFF", menu="MOF")
            print(f"✅ FrameGen截图保存: {screenshot_path}")

            # 等待5秒
            time.sleep(5)
            print(f"    - 等待5秒后进行第二次截图（如果启用）...")

            # 双截图模式：第二次200ns时基截图
            if double_shot:
                self.oscilloscope.Set_Timebase_Scale(timebase_scale='200ns')
                time.sleep(1)
                screenshot_filename2 = f"framegen_{test_name}_200ns_{timestamp}.png"
                screenshot_path2 = f"{screenshot_folder}/{screenshot_filename2}"
                self.oscilloscope.Save_Image(filepath=screenshot_path2, image_format="PNG", invert="OFF", menu="MOF")
                print(f"✅ FrameGen截图保存(200ns): {screenshot_path2}")


            return True

        except Exception as e:
            print(f"❌ FrameGen截图失败: {e}")
            return False

    def test_multi_frame_sync_generators(self):
        """测试3个不同的frame sync生成器到不同S68 GPIO，每个使用不同频率"""
        try:
            active_links = TEST_CONFIG['active_links']
            sync_configs = TEST_CONFIG['frame_sync_configs']

            print(f"[Multi FrameSync]: 配置3个不同频率的frame sync生成器")

            # 配置3个frame sync生成器，每个使用不同频率
            for config in sync_configs:
                i, gpio, fs_tx_id, frequency = config['i'], config['gpio'], config['fs_tx_id'], config['frequency']

                # 计算当前频率的FrameGen参数
                params = calculate_framegen_params(frequency)
                if not params:
                    print(f"❌ 频率{frequency}Hz参数计算失败")
                    continue

                per_div, period, actual_freq = params
                print(f"    🔧 配置frame sync{i} -> S68 GPIO{gpio} (fs_tx_id={fs_tx_id}, {frequency}Hz)")

                # D68端配置
                self.q68.MFNSet_Q68E(gpio=0, mfn=1)  # 使用GPIO0作为输出
                self.q68.FrameSyncOutConifg(i=i, per_div=per_div, duty_cycle=4, period=period,
                                           fs_tx_id=fs_tx_id, auto_fs=1, outen=1)

                # S68端配置 - 只配置对应的GPIO
                for link in active_links:
                    if link < len(self.s68_res_dev):
                        self.q68_remote.dongle.devAddr = self.s68_res_dev[link]
                        time.sleep(0.1)
                        self.q68_remote.M2CMFNSet(gpio=gpio, mfn=0)
                        self.q68_remote.M2CGPIORemoteRx(gpio=gpio, rx_id=fs_tx_id)
                        self.q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly=0)

            # 截图 - 使用混合频率命名
            self.framegen_screenshot(0, is_multi_sync=True)  # frequency=0作为标识
            return True
        except Exception as e:
            print(f"❌ 多FrameSync测试失败: {e}")
            return False


@pytest.mark.fast
def test_create_framegen_folders():
    """预创建FrameGen截图文件夹 - 方便复制到U盘"""
    print("🔌 程序开始 - 上电初始化...")
    power_on()
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)
    tester.create_framegen_folders()
    print("✅ FrameGen文件夹创建完成，可以复制到U盘了")

@pytest.mark.fast
def test_framegen_custom_frequencies():
    """测试自定义FrameGen频率"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    print("\n🚀 开始FrameSync自定义频率测试")
    print("🔌 程序开始 - 上电初始化...")

    power_on()
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    frequencies = TEST_CONFIG['framegen_frequencies']
    enable_power_cycle = TEST_CONFIG['oscilloscope_config'].get('enable_power_cycle', True)

    for i, freq in enumerate(frequencies, 1):
        if GLOBAL_STOP_FLAG:
            break

        print(f"\n📍 测试频率: {freq}Hz ({i}/{len(frequencies)})")

        # 每个频率切换时断电重新初始化
        if i > 1 and enable_power_cycle:
            tester.full_system_reinit_d68()

        # 测试FrameGen频率（双截图模式）
        tester.test_single_framegen_all_link_s68(freq, double_shot=True)

        time.sleep(1)

    print("✅ FrameSync自定义频率测试完成")

@pytest.mark.fast
def test_framegen_rvs_dly():
    """测试FrameSync的rvs_dly参数（可自定义频率）"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    print("\n🚀 开始FrameSync rvs_dly测试")
    print("🔌 程序开始 - 上电初始化...")

    power_on()
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    test_frequencies = TEST_CONFIG['rvs_test_frequencies']
    rvs_dly_values = TEST_CONFIG['rvs_dly_values']
    enable_power_cycle = TEST_CONFIG['oscilloscope_config'].get('enable_power_cycle', True)

    for freq_index, freq in enumerate(test_frequencies, 1):
        print(f"\n🎯 测试频率: {freq}Hz (启用tx_dly_en) ({freq_index}/{len(test_frequencies)})")

        # 每个频率切换时断电重新初始化（跳过第一次，避免重复上电）
        if freq_index > 1 and enable_power_cycle:
            tester.full_system_reinit_d68()

        for i, rvs_dly in enumerate(rvs_dly_values, 1):
            if GLOBAL_STOP_FLAG:
                break

            test_name = f"{freq}Hz_rvs_dly_{rvs_dly}"

            # rvs_dly专用弹窗确认
            result = tester.show_rvs_dly_confirmation_dialog(test_name, i, len(rvs_dly_values))
            if result is None:
                break
            elif result is False:
                continue

            print(f"\n📍 测试{freq}Hz rvs_dly={rvs_dly} ({i}/{len(rvs_dly_values)})")

            # 测试FrameGen（启用tx_dly_en，设置rvs_dly）
            tester.test_single_framegen_all_link_s68(freq, rvs_dly=rvs_dly, tx_dly_en=1, is_rvs_test=True)

            time.sleep(1)

    print("✅ FrameSync rvs_dly测试完成")

@pytest.mark.fast
def test_framegen_duty_cycle():
    """测试FrameSync不同占空比 - D68 frame sync0→所有S68 GPIOs"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    print("\n🚀 开始FrameSync占空比测试")
    print("🔌 程序开始 - 上电初始化...")

    power_on()
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    frequency = TEST_CONFIG['duty_test_frequency']
    duty_cycles = TEST_CONFIG['duty_cycle_values']

    print(f"\n🎯 测试频率: {frequency}Hz")

    for i, duty in enumerate(duty_cycles, 1):
        if GLOBAL_STOP_FLAG:
            break

        duty_percent = (duty + 1) * 5  # 0->5%, 1->10%, ..., 9->50%
        print(f"\n📍 测试占空比: {duty_percent}% ({i}/{len(duty_cycles)})")

        # 测试FrameGen（使用不同占空比）
        tester.test_single_framegen_all_link_s68(frequency, duty_cycle=duty)

        time.sleep(1)

    print("✅ FrameSync占空比测试完成")

@pytest.mark.fast
def test_framegen_multi_sync():
    """测试3个不同frame sync生成器到不同S68 GPIO"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    print("\n🚀 开始多FrameSync生成器测试")
    print("🔌 程序开始 - 上电初始化...")

    power_on()
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    print(f"\n🎯 测试配置: frame sync0→GPIO0(12Hz), frame sync1→GPIO1(30Hz), frame sync2→GPIO2(50Hz)")

    # 测试多FrameSync生成器
    tester.test_multi_frame_sync_generators()

    print("✅ 多FrameSync生成器测试完成")


if __name__ == "__main__":
    """
    使用说明:

    🆕 D2S FrameSync内部信号生成测试:

    1. 自定义频率测试:
       pytest test_gpio_case6_8_d2s_framesync_auto3.py::test_framegen_custom_frequencies -v -s

    2. rvs_dly参数测试:
       pytest test_gpio_case6_8_d2s_framesync_auto3.py::test_framegen_rvs_dly -v -s

    3. 预创建文件夹:
       pytest test_gpio_case6_8_d2s_framesync_auto3.py::test_create_framegen_folders -v -s

    🔥 FrameSync特点:
    - 使用内部FrameSync信号生成，不依赖示波器产生频率
    - 智能频率配比计算，自动选择最佳per_div和period参数
    - 支持自定义频率测试：30Hz, 50Hz, 60Hz, 100Hz
    - 支持rvs_dly参数测试：[0, 4, 10, 20, 30, 40, 50, 60, 63]
    - 30Hz时启用tx_dly_en=1进行特殊测试
    - 示波器仅用于截图，自动选择合适时基
    - 保留弹窗逻辑，支持用户确认和跳过

    📊 技术实现:
    - 频率计算公式: Pll_Frequency = 25000000/(period*per_div_val) Hz
    - D68 GPIO0设置为mfn=1（发送端）
    - S68 GPIO0配置为接收端（rx_id=11）
    - 延迟配置: wr_gpios_ctrl1_fields(rvs_dly=值)

    📁 截图文件夹结构:
    - 频率测试: U-disk0/gpiotest/d68tos68_0_2/{freq}Hz/
    - rvs_dly测试: U-disk0/gpiotest/d68tos68_0_2/{freq}Hz_rvs_dly_{value}/

    - 最少代码量，最简洁结构，专注FrameSync测试
    """
    print("D2S FrameSync内部信号生成测试")
    print("请使用pytest运行测试")
