import time
import serial
import pyvisa as visa

# Must install GPIB driver if communicate btw IT6332B and PC, and diver address refer to git: \instr\document\datasheet\driver

class IT6322B():
    def __init__(self, address="15",port='com9', porttype='usb',usb_addr='USB0::0xFFFF::0x6300::600068011697710008::INSTR'):
        if porttype == 'gpib':
            # self.instr = visa.open_resource("GPIB0::%d::INSTR" % int(address))
            pass
        elif porttype == 'com':
            self.instr = serial.Serial(port, 115200, 8, 'N', 1)
            self.instr.write(':SYST:REM\r')  # remote serial communication format
        elif porttype == 'usb':
            myMgr = visa.ResourceManager()    #qury .dll file
            print(".dll file includes ",myMgr)    
            instr_scope =  myMgr.list_resources()   #query the all instr closed to PC
            print("scope instr type: ",instr_scope)    
            
            #USB0::0x2A8D::0x9016::**********::INSTR
            #USB0::10893::36886::**********::0::INSTR
            #USB0::0x05E6::0x2230::9211115::INSTR
            #USB0::0x05E6::0x2230::9210981::INSTR
            self.instr = myMgr.open_resource(usb_addr,timeout=300000,write_termination = "\n",read_termination = "\n")      
    
    def Source_mode(self,run_mode="REM"):   
        '''
            description: set source mode
                REM-> remote control mode
                LOC-> local control mode
            
            '''
        self.instr.write('SYST:'+str(run_mode)+'\r')
        
    def IDN_Inqury(self):   
        
        instr_info=self.instr.query('*IDN?\r')
        print("instr info including: "+str(instr_info))
        time.sleep(0.1)
        return instr_info


    #############################################################################
    '''
    used for unify different power supply, such as 2230G, IT6322B, GPP-4323
    2025-03-15
    '''
        
    def SetVoltages(self, v1, v2, v3):
        '''
        set voltage of ch1/2/3
        
        INPUT:
            v1, v2, v3:    voltage, units V 
        
        OUTPUT:
            NA
        
        '''
        
        self.instr.write('INST:NSEL %d'%1+'\r')
        self.instr.write('SOUR:VOLT '+str(v1)+str('V')+'\r')
        time.sleep(0.001)
        self.instr.write('INST:NSEL %d'%2+'\r')
        self.instr.write('SOUR:VOLT '+str(v2)+str('V')+'\r')
        time.sleep(0.001)
        self.instr.write('INST:NSEL %d'%3+'\r')
        self.instr.write('SOUR:VOLT '+str(v3)+str('V')+'\r')        
        time.sleep(0.001)

    def SetCurrents(self, i1,i2,i3):
        '''
        set current of ch1/2/3
        
        INPUT:
            i1,i2,i3:    voltage, units I 
        
        OUTPUT:
            NA
        
        '''

        self.instr.write('INST:NSEL %d'%1+'\r')
        self.instr.write('SOUR:CURR '+str(i1)+str('A')+'\r')
        time.sleep(0.001)
        self.instr.write('INST:NSEL %d'%2+'\r')
        self.instr.write('SOUR:CURR '+str(i2)+str('A')+'\r')
        time.sleep(0.001)
        self.instr.write('INST:NSEL %d'%3+'\r')
        self.instr.write('SOUR:CURR '+str(i3)+str('A')+'\r')      
        time.sleep(0.001)  
                   
    def TurnOutputsOn(self, state = 'ON'):
        '''
        Turns on all three outputs
    
        state: ON=1/OFF'=0
        
        '''
        
        self.instr.write('INST:NSEL %d'%1+'\r')
        self.instr.write(':OUTP  '+str(state)+'\r') 
        
        self.instr.write('INST:NSEL %d'%2+'\r')
        self.instr.write(':OUTP  '+str(state)+'\r') 
        
        self.instr.write('INST:NSEL %d'%3+'\r')
        self.instr.write(':OUTP  '+str(state)+'\r') 

        self.instr.write('OUTP ON')

    def TurnOutputsOff(self):
        '''Turns off all three outputs''' 
        
        self.instr.write('INST:NSEL %d'%1+'\r')
        self.instr.write(':OUTP  '+str(0)+'\r') 
        
        self.instr.write('INST:NSEL %d'%2+'\r')
        self.instr.write(':OUTP  '+str(0)+'\r') 
        
        self.instr.write('INST:NSEL %d'%3+'\r')
        self.instr.write(':OUTP  '+str(0)+'\r') 
        
        return self.instr.write('OUTP OFF')   

    def TurnOn(self,channel, power):
        '''Turns on/off each channel'''
              
        if power == 'ON':
            state = 1
        if power == 'OFF':
            state = 0

        self.instr.write('INST:NSEL %d'%channel+'\r')
        self.instr.write('SOUR:CHAN:OUTP %d'%state)

        
    def Meas_OneChannel(self,channel=1):
        '''
        description: measure specified channel voltage and current
        
        '''
        # self.instr.write(':OUTP  1'+'\r')     #enable output
        self.instr.write('INST:NSEL %d'%channel+'\r')   #select channel
        time.sleep(0.1)
        
        vol=self.instr.query('MEAS:VOLT?\r')
        curr=self.instr.query('MEAS:CURR?\r')
        print("measured channel"+str(channel)+" voltage is "+str(vol)+"V, and current is "+str(curr)+"A")
        return [float(x) for x in (vol,curr)]
    
    def Meas_AllChannel(self):
        '''
        description: measure specified channel voltage and current
        
        '''
        self.instr.write(':OUTP  1'+'\r')     #enable output
        time.sleep(0.1)
        
        vol=self.instr.query('MEAS:VOLT:ALL?\r')
        curr=self.instr.query('MEAS:CURR:ALL?\r')
        # print("all channel voltage: "+str(vol))
        # print("all channel current: "+str(curr))
        
        res_vol =  [float(x) for x in (vol.split(","))]
        res_curr =  [float(x) for x in (curr.split(","))]
        res = [float(x) for x in res_vol+res_curr]
        
        print("vol_ch1/2/3, and  curr_ch1/2/3 is :"+str(res))
         
        return res



def test_voltage():
    m=IT6322B()
    m.IDN_Inqury()
    m.SetVoltages(1, 1, 1)
    # m.SetVoltages(v1=1.2, v2=1.8, v3=1.8)
    # m.SetCurrents(i1=1.5, i2=0.2, i3=0.5)
    # m.TurnOutputsOn()
    # m.Meas_AllChannel()
    # m.TurnOutputsOff()
    # m.TurnOn(channel=1, power='OFF')
    # m.Meas_OneChannel(channel=1)