ldc_set_sysconfig {CONFIGIO_VOLTAGE_BANK0=1.8 CONFIGIO_VOLTAGE_BANK1=1.8}
ldc_set_vcc -bank 0 1.8
ldc_set_vcc -bank 1 1.8
ldc_set_vcc -bank 2 1.8
ldc_set_vcc -bank 3 1.8
ldc_set_vcc -bank 4 1.8
ldc_set_vcc -bank 5 1.8
ldc_set_vcc -bank 6 1.8
ldc_set_vcc -bank 7 1.8
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[0]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[1]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[2]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[3]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[4]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[5]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[6]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[7]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[8]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[9]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[10]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[11]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[12]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports {data_o[13]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[14]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[15]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[16]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[17]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[18]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[19]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[20]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[21]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[22]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[23]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[24]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[25]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[26]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[27]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[28]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[29]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[30]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports {data_o[31]}]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports pclk_o]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports lsync_o]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports fsync_o]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports dummy_out]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports reset_in]

ldc_set_port -iobuf {IO_TYPE=LVCMOS18H} [get_ports cam_ctrl_in]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports cam_xmaster_o]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports cam_reset_o]
ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports cam_pwr_en_o]

ldc_set_location -site {J12} [get_ports {data_o[0]}]
ldc_set_location -site {J11} [get_ports {data_o[1]}]
ldc_set_location -site {N12} [get_ports {data_o[2]}]
ldc_set_location -site {H12} [get_ports {data_o[3]}]
ldc_set_location -site {L11} [get_ports {data_o[4]}]
ldc_set_location -site {M12} [get_ports {data_o[5]}]
ldc_set_location -site {H13} [get_ports {data_o[6]}]
ldc_set_location -site {M11} [get_ports {data_o[7]}]
ldc_set_location -site {H14} [get_ports {data_o[8]}]
ldc_set_location -site {M13} [get_ports {data_o[9]}]
ldc_set_location -site {J15} [get_ports {data_o[10]}]
ldc_set_location -site {H15} [get_ports {data_o[11]}]
ldc_set_location -site {L12} [get_ports {data_o[12]}]
ldc_set_location -site {H16} [get_ports {data_o[13]}]
ldc_set_location -site {M15} [get_ports {data_o[14]}]
ldc_set_location -site {N11} [get_ports {data_o[15]}]
ldc_set_location -site {P14} [get_ports {data_o[16]}]
ldc_set_location -site {P11} [get_ports {data_o[17]}]
ldc_set_location -site {N15} [get_ports {data_o[18]}]
ldc_set_location -site {N14} [get_ports {data_o[19]}]
ldc_set_location -site {T12} [get_ports {data_o[20]}]
ldc_set_location -site {P16} [get_ports {data_o[21]}]
ldc_set_location -site {R13} [get_ports {data_o[22]}]
ldc_set_location -site {N13} [get_ports {data_o[23]}]
ldc_set_location -site {T11} [get_ports {data_o[24]}]
ldc_set_location -site {R14} [get_ports {data_o[25]}]
ldc_set_location -site {R11} [get_ports {data_o[26]}]
ldc_set_location -site {R12} [get_ports {data_o[27]}]
ldc_set_location -site {R10} [get_ports {data_o[28]}]
ldc_set_location -site {T13} [get_ports {data_o[29]}]
ldc_set_location -site {P10} [get_ports {data_o[30]}]
ldc_set_location -site {T14} [get_ports {data_o[31]}]
#ldc_set_port -iobuf {DRIVE=16} [get_ports {counter_out[1]}]
ldc_set_location -site {P15} [get_ports pclk_o]
ldc_set_location -site {J16} [get_ports lsync_o]
ldc_set_location -site {M14} [get_ports fsync_o]
ldc_set_location -site {N16} [get_ports reset_in]
ldc_set_location -site {M16} [get_ports cam_ctrl_in]
ldc_set_location -site {E15} [get_ports cam_xmaster_o]
ldc_set_location -site {E16} [get_ports cam_reset_o]
ldc_set_location -site {F16} [get_ports cam_pwr_en_o]

ldc_set_location -site {D1} [get_ports mipi_clk_p_in1]
ldc_set_location -site {D2} [get_ports mipi_clk_n_in1]
ldc_set_location -site {E1} [get_ports {mipi_data_p_in1[0]}]
ldc_set_location -site {E2} [get_ports {mipi_data_n_in1[0]}]
ldc_set_location -site {C1} [get_ports {mipi_data_p_in1[1]}]
ldc_set_location -site {C2} [get_ports {mipi_data_n_in1[1]}]
#ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports uart_cts_i]
#ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports uart_tx_o]
#ldc_set_port -iobuf {IO_TYPE=LVCMOS18} [get_ports uart_rx_i]
#ldc_set_location -site TDPHY_CORE2  [get_ports mipi_csi_phy_inst0/lscc_mipi_dphy_inst/RECEIVER.lscc_mipi_wrapper_rx/HARD_IP.NO_CIL.u_dphy_nocil.DPHY_inst]
#ldc_set_sysconfig {CONFIGIO_VOLTAGE_BANK0=NOT_SPECIFIED DONE_PORT=DISABLE INITN_PORT=DISABLE PROGRAMN_PORT=DISABLE BOOTMODE=SINGLE}



ldc_set_port -iobuf {DRIVE=8 SLEWRATE=FAST} [get_ports pclk_o]
ldc_set_port -iobuf {DRIVE=8 SLEWRATE=FAST} [get_ports lsync_o]