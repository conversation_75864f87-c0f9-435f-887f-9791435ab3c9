#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO测试文件夹简化生成脚本

快速创建GPIO测试所需的文件夹结构
"""
import os
from pathlib import Path

def create_s68_to_d68_folders():
    """创建S68到D68测试文件夹 (按Link分类)"""
    base_path = 'U-disk0/gpiotest/s68tod68_0_2'
    links = [0, 2]
    gpios = [6, 7, 8]
    
    print("🚀 创建S68到D68测试文件夹")
    Path(base_path).mkdir(parents=True, exist_ok=True)
    
    for link in links:
        for gpio in gpios:
            # 主文件夹
            folder = Path(base_path) / f"link{link}_gpio{gpio}"
            folder.mkdir(exist_ok=True)
            print(f"✅ {folder}")
            
            # sweep子文件夹
            sweep_folder = folder / "sweep"
            sweep_folder.mkdir(exist_ok=True)
            print(f"  ✅ sweep/")
    
    print(f"📁 文件夹位置: {base_path}")

def create_d68_to_s68_folders():
    """创建D68到S68测试文件夹 (简单GPIO分类)"""
    base_path = 'U-disk0/gpiotest/d68tos68_0_2'
    gpios = list(range(9))  # GPIO 0-8
    
    print("🚀 创建D68到S68测试文件夹")
    Path(base_path).mkdir(parents=True, exist_ok=True)
    
    for gpio in gpios:
        # 主文件夹
        folder = Path(base_path) / f"gpio{gpio}"
        folder.mkdir(exist_ok=True)
        print(f"✅ {folder}")
        
        # sweep子文件夹
        sweep_folder = folder / "sweep"
        sweep_folder.mkdir(exist_ok=True)
        print(f"  ✅ sweep/")
    
    print(f"📁 文件夹位置: {base_path}")

def show_structure():
    """显示文件夹结构"""
    print("📋 文件夹结构预览:")
    print()
    print("S68到D68 (按Link分类):")
    print("U-disk0/gpiotest/s68tod68_0_2/")
    print("├── link0_gpio6/")
    print("│   └── sweep/")
    print("├── link0_gpio7/")
    print("├── link0_gpio8/")
    print("├── link2_gpio6/")
    print("├── link2_gpio7/")
    print("└── link2_gpio8/")
    print()
    print("D68到S68 (简单分类):")
    print("U-disk0/gpiotest/d68tos68_0_2/")
    print("├── gpio0/")
    print("│   └── sweep/")
    print("├── gpio1/")
    print("├── gpio2/")
    print("└── ... (gpio0-8)")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 's2d':
            create_s68_to_d68_folders()
        elif command == 'd2s':
            create_d68_to_s68_folders()
        elif command == 'all':
            create_s68_to_d68_folders()
            print()
            create_d68_to_s68_folders()
        elif command == 'preview':
            show_structure()
        else:
            show_usage()
    else:
        show_usage()

def show_usage():
    """显示使用说明"""
    print("GPIO测试文件夹简化生成脚本")
    print()
    print("用法:")
    print("  python create_folders_simple.py s2d      # 创建S68到D68文件夹")
    print("  python create_folders_simple.py d2s      # 创建D68到S68文件夹")
    print("  python create_folders_simple.py all      # 创建所有文件夹")
    print("  python create_folders_simple.py preview  # 预览文件夹结构")

if __name__ == "__main__":
    main()
