#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 用于交互中的指令响应
import time

from HWInterface import HWImpl
from HWInterface import USBDataDeal
from HWInterface import TCPDataDeal
from threading import Event

TYPE_NETWORK = 0
TYPE_USBSER = 1

type_mode = TYPE_USBSER

res_nodata_event = Event()
global res_nodata_data
res_0000_event = Event()
global res_0000_data
res_0002_event = Event()
global res_0002_data
res_0004_event = Event()
global res_0004_data
res_0306_event = Event()
global res_0306_data
res_0307_event = Event()
global res_0307_data
###########################################################################
# 和底层设备相关的函数

# 打开通信设备
def comm_open(port_name=None, ip_addr=None):
    global type_mode

    if port_name is not None:
        USBDataDeal.serial_open(port_name)
        type_mode = TYPE_USBSER
        print("TYPE_USBSER: ", port_name)
    elif ip_addr is not None:
        TCPDataDeal.network_open(ip_addr)
        type_mode = TYPE_NETWORK
        print("TYPE_NETWORK: ", ip_addr)

# 关闭通信设备
def comm_close():
    if type_mode == TYPE_NETWORK:
        TCPDataDeal.network_close()
    else:
        USBDataDeal.serial_close()

# 发送数据的函数
def comm_send_buff(data):
    hex_data = ' '.join(f'{byte:02x}' for byte in bytearray(data))
    # print("comm_send_buff :", hex_data)

    if type_mode == TYPE_NETWORK:
        ret = TCPDataDeal.net_send_buff(data)
        if ret != 0:
            return -1
    else:
        ret = USBDataDeal.usb_send_buff(data)
        if ret != 0:
            return -1
    return 0
###########################################################################

###########################################################################
# 处理需要发送的信息


# 响应指令【无携带数据】
def send_cmd_respond_nodata(cmd_id, result):
    global type_mode

    data = bytearray(12)
    # 帧头
    data[0] = 0xAA
    data[1] = 0x55

    # 回复指令ID: 0xFFFE
    data[2] = 0xFF
    data[3] = 0xFE

    # 回复对应的指令
    data[4] = cmd_id >> 8 & 0xFF
    data[5] = cmd_id & 0xFF
    data[6] = result

    # 数据长度
    data[7] = 0x00
    data[8] = 0x00

    # CRC
    data[9] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8]

    # 帧尾
    data[10] = 0xA5
    data[11] = 0xA5

    comm_send_buff(data)


# ID:0x0000: 获取设备SN号
def send_cmd_get_sn():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 指令ID
    data[3] = 0x00  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度
    data[6] = data[2]^data[3]^data[4]^data[5]

    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾
    
    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_0000_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_get_sn send_buff error")
        return -1, -1

    # 等待响应
    if not res_0000_event.wait(0.1):
        print("send_cmd_get_sn respond error")
        return -1, -1

    if res_0000_data[2] != 0:
        print("send_cmd_get_sn cmd ret error:", res_0000_data[2])
        return -1, -1
    else:
        return -res_0000_data[2], res_0000_data[3:21]


# ID:0x0001: 设置指定输出引脚的值
def send_cmd_set_gpio(pin_num: int, pin_val: int):
    data = bytearray(11)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 指令ID
    data[3] = 0x01  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x02  # 数据长度
    data[6] = pin_num
    data[7] = pin_val
    data[8] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7]

    data[9] = 0xA5  # 帧尾
    data[10] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_set_gpio send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_gpio respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_set_gpio cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0002: 获取指定输入引脚的值
def send_cmd_get_gpio(pin_num: int):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 指令ID
    data[3] = 0x02  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = pin_num
    data[7] = data[2]^data[3]^data[4]^data[5]^data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_0002_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_get_gpio send_buff error")
        return -1,-1

    # 等待响应
    if not res_0002_event.wait(0.1):
        print("send_cmd_get_gpio respond error")
        return -1, -1

    if res_0002_data[2] != 0:
        print("send_cmd_get_gpio cmd ret error:", res_0002_data[2])
        return -1, -1
    else:
        return -res_0002_data[2], res_0002_data[5]


# ID:0x0003:设置I2C寄存器的值
# mode:0:r8v8 1:r16v8
def send_cmd_i2c_set(mode: int, bus: int, dev_addr: int, reg_addr: int, reg_val: int):
    data = bytearray(16)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 指令ID
    data[3] = 0x03  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x07  # 数据长度
    data[6] = mode
    data[7] = bus
    data[8] = dev_addr
    data[9] = reg_addr >> 8 & 0xFF
    data[10] = reg_addr & 0xFF
    data[11] = reg_val >> 8 & 0xFF
    data[12] = reg_val & 0xFF

    data[13] = (data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9]
                ^ data[10] ^ data[11] ^ data[12])

    data[14] = 0xA5  # 帧尾
    data[15] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_i2c_set send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_i2c_set respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_i2c_set cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0004:获取I2C寄存器的值
# return: ret,val 第一个值是ret值，第二个值为获取的寄存器值
def send_cmd_i2c_get(mode: int, bus: int, dev_addr: int, reg_addr: int):
    data = bytearray(14)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 指令ID
    data[3] = 0x04  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x05  # 数据长度
    data[6] = mode
    data[7] = bus
    data[8] = dev_addr
    data[9] = reg_addr >> 8 & 0xFF
    data[10] = reg_addr & 0xFF

    data[11] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10]

    data[12] = 0xA5  # 帧尾
    data[13] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_0004_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_i2c_get send_buff error")
        return -1, -1

    # 等待响应
    if not res_0004_event.wait(0.1):
        print("send_cmd_i2c_get respond error")
        return -1, -1

    if res_0004_data[2] != 0:
        print("send_cmd_i2c_get cmd ret error:", res_0004_data[2])
        return -1, -1
    else:
        return -res_0004_data[2], (res_0004_data[5] << 8) +  res_0004_data[6]

# ID:0x0005: 设置数据类型，0:真实数据，1:模拟数据
def send_cmd_set_mode(mode: int):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x00  # 指令ID
    data[3] = 0x05  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = mode
    data[7] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_set_mode send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_mode respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_set_mode cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0100:设置指定CAM通道宽高及格式 channel:255是所有通道，其它是单独通道
def send_cmd_set_video_cam(cam_w: int, cam_h: int, cam_fmt: int, channel: int=0xFF):
    data = bytearray(16)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x00  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x07  # 数据长度
    data[6] = channel
    data[7] = cam_w >> 8 & 0xFF
    data[8] = cam_w & 0xFF
    data[9] = cam_h >> 8 & 0xFF
    data[10] = cam_h & 0xFF
    data[11] = cam_fmt >> 8 & 0xFF
    data[12] = cam_fmt & 0xFF
    crcval = 0
    for i in range(2,12):
        crcval = crcval ^ data[i]
    
    data[13] = crcval
    data[14] = 0xA5  # 帧尾
    data[15] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_set_cam_wxh send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_cam_wxh respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_set_cam_wxh cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0101: 重启设备
def send_cmd_reboot():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x01  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度
    data[6] = data[2] ^ data[3] ^ data[4] ^ data[5]

    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_reboot send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_reboot respond error")
        return -1
    
    time.sleep(3)

    if res_nodata_data[2] != 0:
        print("send_cmd_reboot cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0102:设置指定WIN通道宽高和位置 channel:255是所有通道，其它是单独通道
def send_cmd_set_video_win(win_w: int, win_h: int, win_x: int, win_y: int, channel: int = 0xFF):
    data = bytearray(18)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x02  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x09  # 数据长度
    data[6] = channel
    data[7] = win_w >> 8 & 0xFF
    data[8] = win_w & 0xFF
    data[9] = win_h >> 8 & 0xFF
    data[10] = win_h & 0xFF
    data[11] = win_x >> 8 & 0xFF
    data[12] = win_x & 0xFF
    data[13] = win_y >> 8 & 0xFF
    data[14] = win_y & 0xFF

    crcval = 0
    for i in range(2,14):
        crcval = crcval ^ data[i]
    
    data[15] = crcval
    data[16] = 0xA5  # 帧尾
    data[17] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_set_win_wxh send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_win_wxh respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_set_win_wxh cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0103: 打开指定通道的视频
def send_cmd_open_video(index: int = 0):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x03  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = index
    data[7] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_open_video send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_open_video respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_open_video cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0104: 关闭指定通道的视频
def send_cmd_close_video(index: int = 0):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x04  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = index
    data[7] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_close_video send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_close_video respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_close_video cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0105: 选择指定的设备树
def send_cmd_select_dtb(index: int = 0):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x05  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = index
    data[7] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_select_dtb send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_select_dtb respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_select_dtb cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0106: 保存指定通道的一帧数据
def send_cmd_save_one_videoframe(index: int = 0):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x06  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = index
    data[7] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6]

    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_save_one_videoframe send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_save_one_videoframe respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_save_one_videoframe cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0107: 切换设备上显示程序的页面
def send_cmd_show_form_index(index: int = 0):
    data = bytearray(11)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x01  # 指令ID
    data[3] = 0x07  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x02  # 数据长度
    data[6] = index >> 8 & 0xFF
    data[7] = index & 0xFF
    data[8] = data[2] ^ data[3] ^ data[4] ^ data[5] ^ data[6] ^ data[7]

    data[9] = 0xA5  # 帧尾
    data[10] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_show_form_index send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_show_form_index respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_show_form_index cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]
###########################################################################


###########################################################################
# ID:0x0300: 设置MIPI: csiindex -> csiport phy_mode bus_width discontinuous_clk pix_clk_hz serdes_pix_clk_hz mclk_multiplier
# 设置 csi控制器 对应的引脚，及lane/trio数和时钟参数
# phy_mode: 0:DPHY 1:CPHY
# discontinuous_clk: 0:discontinuous 1:continuous
def send_cmd_set_csi_param(csiindex:int = 0, csiport:int = 0, phy_mode:int = 0, bus_width:int = 1, discontinuous_clk:int = 0, pix_clk_hz:int = 74250000, serdes_pix_clk_hz:int = 156250000, mclk_multiplier:float = 3.01):
    data = bytearray(24)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x00  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x0F  # 数据长度
    data[6] = csiindex
    data[7] = csiport
    data[8] = phy_mode
    data[9] = bus_width
    data[10] = discontinuous_clk
    data[11] = pix_clk_hz >> 24 & 0xFF
    data[12] = pix_clk_hz >> 16 & 0xFF
    data[13] = pix_clk_hz >> 8 & 0xFF
    data[14] = pix_clk_hz & 0xFF
    data[15] = serdes_pix_clk_hz >> 24 & 0xFF
    data[16] = serdes_pix_clk_hz >> 16 & 0xFF
    data[17] = serdes_pix_clk_hz >> 8 & 0xFF
    data[18] = serdes_pix_clk_hz & 0xFF
    data[19] = int(mclk_multiplier*100) >> 8 & 0xFF
    data[20] = int(mclk_multiplier*100) & 0xFF

    crcval = 0
    for i in range(2,20):
        crcval = crcval ^ data[i]
    
    data[21] = crcval
    data[22] = 0xA5  # 帧尾
    data[23] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_set_csi_param send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_csi_param respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_set_csi_param cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]

# ID:0x0301: 设置MIPI: csiindex channel(start-end) -> vcid active_w active_h line_length data_fmt
# 每一个 csi控制器 预分配了四个channel, 所以channel(start-end)在0~3.
# 0:UYVY
def send_cmd_set_csi_channel_param(csiindex:int = 0, channel:int = 0, vcid:int = 0, active_w:int = 1280, active_h:int = 720, line_length:int = 2560, data_fmt:int = 0):
    data = bytearray(25)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x01  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x10  # 数据长度

    data[6] = csiindex
    data[7] = channel
    data[8] = vcid
    data[9] = active_w >> 24 & 0xFF
    data[10] = active_w >> 16 & 0xFF
    data[11] = active_w >> 8 & 0xFF
    data[12] = active_w & 0xFF
    data[13] = active_h >> 24 & 0xFF
    data[14] = active_h >> 16 & 0xFF
    data[15] = active_h >> 8 & 0xFF
    data[16] = active_h & 0xFF
    data[17] = line_length >> 24 & 0xFF
    data[18] = line_length >> 16 & 0xFF
    data[19] = line_length >> 8 & 0xFF
    data[20] = line_length & 0xFF
    data[21] = data_fmt    
    crcval = 0
    for i in range(2,21):
        crcval = crcval ^ data[i]
    
    data[22] = crcval
    data[23] = 0xA5  # 帧尾
    data[24] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_set_csi_channel_param send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_csi_channel_param respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_set_csi_channel_param cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]

# ID:0x0302: 保存MIPI配置
def send_cmd_save_mipi_param():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x02  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度   
    crcval = 0
    for i in range(2,5):
        crcval = crcval ^ data[i]
    
    data[6] = crcval
    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_save_mipi_param send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(10):
        print("send_cmd_save_mipi_param respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_save_mipi_param cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0303: 启动日志保存
def send_cmd_start_tracelog():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x03  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度   
    crcval = 0
    for i in range(2,5):
        crcval = crcval ^ data[i]
    
    data[6] = crcval
    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_start_tracelog send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(10):
        print("send_cmd_start_tracelog respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_start_tracelog cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]


# ID:0x0304: 关闭日志保存
def send_cmd_stop_tracelog():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x04  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度   
    crcval = 0
    for i in range(2,5):
        crcval = crcval ^ data[i]
    
    data[6] = crcval
    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_stop_tracelog send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(10):
        print("send_cmd_stop_tracelog respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_stop_tracelog cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]

# ID:0x0305: 清除缓存的日志内容和解析状态，从当前时刻开始解析
def send_cmd_clear_tracelog_status():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x05  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度   
    crcval = 0
    for i in range(2,5):
        crcval = crcval ^ data[i]
    
    data[6] = crcval
    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_clear_tracelog_status send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(10):
        print("send_cmd_clear_tracelog_status respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_clear_tracelog_status cmd ret error:", res_nodata_data[2])

    return -res_nodata_data[2]

# ID:0x0306: 获取mipi_log解析结果
def send_cmd_get_tracelog_status():
    data = bytearray(9)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x06  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x00  # 数据长度

    crcval = 0
    for i in range(2,5):
        crcval = crcval ^ data[i]
    
    data[6] = crcval
    data[7] = 0xA5  # 帧尾
    data[8] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_0306_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_get_tracelog_status send_buff error")
        return -1,-1

    # 等待响应
    if not res_0306_event.wait(0.1):
        print("send_cmd_get_tracelog_status respond error")
        return -1, -1

    if res_0306_data[2] != 0:
        print("send_cmd_get_tracelog_status cmd ret error:", res_0306_data[2])
        return -1, -1
    else:
        return -res_0306_data[2], res_0306_data[5]


# ID:0x0307: 获取对应通道帧率
def send_cmd_get_video_info(channel):
    data = bytearray(10)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x07  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x01  # 数据长度
    data[6] = channel

    crcval = 0
    for i in range(2,6):
        crcval = crcval ^ data[i]
    
    data[7] = crcval
    data[8] = 0xA5  # 帧尾
    data[9] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_0307_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_get_video_fps send_buff error")
        return -1,-1,-1,-1,-1

    # 等待响应
    if not res_0307_event.wait(1):
        print("send_cmd_get_video_fps respond error")
        return -1,-1,-1,-1,-1

    if res_0307_data[2] != 0:
        print("send_cmd_get_video_fps cmd ret error:", res_0307_data[2])
        return -1,-1,-1,-1,-1
    else:
        width  = (res_0307_data[5] << 8) +  (res_0307_data[6])
        height = (res_0307_data[7] << 8) +  (res_0307_data[8])
        fmt    = (res_0307_data[9] << 8) +  (res_0307_data[10])
        fps    = res_0307_data[11]
        return -res_0307_data[2], width, height, fmt, fps


# ID:0x0308: 设置tracelog 单个文件大小和总日志大小的限制。单位MByte
def send_cmd_set_tracelog_info(total_size:int = 10240, file_size:int = 2):
    data = bytearray(17)
    data[0] = 0xAA  # 帧头
    data[1] = 0x55  # 帧头
    data[2] = 0x03  # 指令ID
    data[3] = 0x08  # 指令ID

    data[4] = 0x00  # 数据长度
    data[5] = 0x08  # 数据长度
    data[6] = total_size >> 24 & 0xFF
    data[7] = total_size >> 16 & 0xFF
    data[8] = total_size >> 8 & 0xFF
    data[9] = total_size & 0xFF
    data[10] = file_size >> 24 & 0xFF
    data[11] = file_size >> 16 & 0xFF
    data[12] = file_size >> 8 & 0xFF
    data[13] = file_size & 0xFF

    crcval = 0
    for i in range(2,13):
        crcval = crcval ^ data[i]
    
    data[14] = crcval
    data[15] = 0xA5  # 帧尾
    data[16] = 0xA5  # 帧尾

    # 防止响应数据过快，导致异常标记被错误清除，所以这里先清空接收标记
    res_nodata_event.clear()
    ret = comm_send_buff(data)
    if ret != 0:
        print("send_cmd_set_tracelog_info send_buff error")
        return -1

    # 等待响应
    if not res_nodata_event.wait(0.1):
        print("send_cmd_set_tracelog_info respond error")
        return -1

    if res_nodata_data[2] != 0:
        print("send_cmd_set_tracelog_info cmd ret error:", res_nodata_data[2])
        return -1
    else:
        return -res_0307_data[2]


###########################################################################

###########################################################################
# 处理接收到的信息

# 处理回应数据的子信息
def respond_cmd_nodata_fun(cmd_buff: bytearray, cmd_len: int):
    global res_nodata_data
    res_nodata_data = cmd_buff[4:7]
    res_nodata_event.set()


# get sn
def respond_cmd_0000_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0000_data
    res_0000_data = cmd_buff[4:25]
    res_0000_event.set()


# get gpio
def respond_cmd_0002_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0002_data
    res_0002_data = cmd_buff[4:10]
    res_0002_event.set()


# get i2c
def respond_cmd_0004_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0004_data
    res_0004_data = cmd_buff[4:11]
    res_0004_event.set()

# get trace log result
def respond_cmd_0306_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0306_data
    res_0306_data = cmd_buff[4:10]
    res_0306_event.set()

# get video channel fps
def respond_cmd_0307_fun(cmd_buff: bytearray, cmd_len: int):
    global res_0307_data
    res_0307_data = cmd_buff[4:16]
    res_0307_event.set()


# 处理回应过来的子指令
function_cmd_respond_map = {
    0x0000: respond_cmd_0000_fun,       # get sn
    0x0001: respond_cmd_nodata_fun,     # set gpio
    0x0002: respond_cmd_0002_fun,       # get gpio
    0x0003: respond_cmd_nodata_fun,     # set i2c
    0x0004: respond_cmd_0004_fun,       # get i2c
    0x0005: respond_cmd_nodata_fun,     # set data mode
    0x0100: respond_cmd_nodata_fun,     # set cam hw wxh
    0x0101: respond_cmd_nodata_fun,     # hw reboot
    0x0102: respond_cmd_nodata_fun,     # set win hw wxh
    0x0103: respond_cmd_nodata_fun,     # open channel video
    0x0104: respond_cmd_nodata_fun,     # close channel video
    0x0105: respond_cmd_nodata_fun,     # select dtb
    0x0106: respond_cmd_nodata_fun,     # save channel video on frame
    0x0107: respond_cmd_nodata_fun,     # Switch the displayed page on the device.
    
    0x0300: respond_cmd_nodata_fun,     # set mipi param
    0x0301: respond_cmd_nodata_fun,     # set mipi channel param
    0x0302: respond_cmd_nodata_fun,     # save mipi param
    0x0303: respond_cmd_nodata_fun,     # start trace log
    0x0304: respond_cmd_nodata_fun,     # stop trace log
    0x0305: respond_cmd_nodata_fun,     # clear trace log result
    0x0306: respond_cmd_0306_fun,       # get trace log result
    0x0307: respond_cmd_0307_fun,       # get video channel fps
    0x0308: respond_cmd_nodata_fun,     # set tracelog 
    # 添加更多的函数映射
}

# 处理HW对指令的响应，按照指令ID,使用对应的respond处理函数进行处理
def receive_cmd_ffff_fun(cmd_buff: bytearray, cmd_len: int):
    hex_data = ' '.join(f'{byte:02x}' for byte in cmd_buff)
    # print("receive_cmd_ffff_fun:", hex_data)
    
    target_function = function_cmd_respond_map.get((cmd_buff[4]<<8) | cmd_buff[5])
    if target_function is not None:
        target_function(cmd_buff, cmd_len)
    else:
        print("receive_cmd_ffff_fun ID not support:", "[" , hex((cmd_buff[4]<<8) | cmd_buff[5]), "]")
###########################################################################
