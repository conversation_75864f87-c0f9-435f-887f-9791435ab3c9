import pyvisa as visa
'''
visa syntax:
https://pyvisa.readthedocs.io/en/1.8/tutorial.html
if NI MAX can't find instr, but KEYSIGHT io can find instr, so refer to https://blog.csdn.net/bjcyck/article/details/*********
'''
import collections
eyedic = collections.OrderedDict()
import time

class Keithley2230():
    def __init__(self,porttype='usb', gpib_addr=1, usb_addr="USB0::0x05E6::0x2230::9211138::INSTR"):
        
        '''
        description: this function to initi instr
        author:xzx
        input:
            select communicated type,including USB/GPIB/LAN
                usb: "USB0::0x2A8D::0x9016::**********::INSTR",query it by scope or NIMAX
                GPIB: addr=7, and need further confirm due lack GPIB card
                LAN: TBD 
        output:
            1. initi scope instr
            2. return scope type by qurey command
        '''
        
        if porttype=='gpib':
            myMgr = visa.ResourceManager()    #qury .dll file
            print(".dll file includes ",myMgr)    
            my_instr =  myMgr.list_resources()   #query the all instr closed to PC
            print("my instr type: ",my_instr) 
            
            self.instr = myMgr.open_resource("GPIB0::%d::INSTR" % int(gpib_addr))
            
        if porttype=='lan':
            pass
            #self.instr = visa.instrument("TCPIP0::***********::inst0::INSTR")
        elif porttype == 'usb':
            
            myMgr = visa.ResourceManager()    #qury .dll file
            print(".dll file includes ",myMgr)    
            instr_scope =  myMgr.list_resources()   #query the all instr closed to PC
            print("scope instr type: ",instr_scope)    
    
            self.instr = myMgr.open_resource(usb_addr,timeout=300000,write_termination = "\n",read_termination = "\n")  
           
        device_str = self.instr.query("*IDN?")    #his command requry scope instrument
        print ("Queried the instr is: ",device_str)

    def GetVoltage1Setting(self):
        '''This function gets the Keithley2230 PS1 Voltage Setting'''
        self.instr.write('INST:NSEL 1')
        return float(self.instr.query('VOLT?'))
    
    def GetVoltage1(self):
        '''This function gets the Keithley2230 PS1 Voltage'''
        self.instr.write('INST:NSEL 1')
        return float(self.instr.query('MEAS:VOLT?'))
    
    def SetVoltage1(self, v1):
        '''This function sets the Keithley2230 PS1 Voltage'''
       
        self.instr.write('INST:NSEL 1')
        self.instr.write('VOLT ' + str(v1))
        time.sleep(0.1)

    def GetVoltage2Setting(self):
        '''This function gets the Keithley2230 PS1 Voltage Setting'''
        self.instr.write('INST:NSEL 2')
        return float(self.instr.query('VOLT?'))
    
    def GetVoltage2(self):
        '''This function gets the Keithley2230 PS2 Voltage'''
        self.instr.write('INST:NSEL 2')
        return float(self.instr.query('MEAS:VOLT?'))
    
    def SetVoltage2(self, v2):
        '''This function sets the Keithley2230 PS2 Voltage'''
        
        self.instr.write('INST:NSEL 2')
        self.instr.write('VOLT ' + str(v2))
        time.sleep(0.1)

    def GetVoltage3Setting(self):
        '''This function gets the Keithley2230 PS1 Voltage Setting'''
        self.instr.write('INST:NSEL 3')
        return float(self.instr.query('VOLT?'))
    
    def GetVoltage3(self):
        '''This function gets the Keithley2230 PS3 Voltage'''
        self.instr.write('INST:NSEL 3')
        return float(self.instr.query('MEAS:VOLT?'))
    
    def SetVoltage3(self, v3):
        '''This function sets the Keithley2230 PS3 Voltage'''
       
        self.instr.write('INST:NSEL 3')
        self.instr.write('VOLT ' + str(v3))
        time.sleep(0.1)

    def GetVoltageByChan(self, chan):
        '''This function gets the voltage for specifed channel'''    
        
        self.instr.write('INST:NSEL ' + str(chan))
        return float(self.instr.query('MEAS:VOLT?'))

    def SetVoltages(self, v1, v2, v3):
        '''This function sets all three voltages simultaneously'''
      
        self.instr.write('INST:NSEL 1')
        self.instr.write('VOLT ' + str(v1))
        self.instr.write('INST:NSEL 2')
        self.instr.write('VOLT ' + str(v2))
        self.instr.write('INST:NSEL 3')
        self.instr.write('VOLT ' + str(v3))  
        time.sleep(0.1)      

    def GetCurrent1(self):
        '''This function gets the Keithley2230 PS1 Current'''
        self.instr.write('INST:NSEL 1')
        return float(self.instr.query('MEAS:CURR?'))

    def SetCurrent1(self, current):
        '''This function sets the Keithley2230 PS1 Current'''
        
        self.instr.write('INST:NSEL 1')
        self.instr.write('CURR ' + str(current))
        time.sleep(0.1)

    def GetCurrent2(self):
        '''This function gets the Keithley2230 PS2 Current'''
        self.instr.write('INST:NSEL 2')
        return float(self.instr.query('MEAS:CURR?'))

    def SetCurrent2(self, current):
        '''This function sets the Keithley2230 PS1 Current'''
        
        self.instr.write('INST:NSEL 2')
        self.instr.write('CURR ' + str(current))
        time.sleep(0.1)

    def GetCurrent3(self):
        '''This function gets the Keithley2230 PS2 Current'''
        self.instr.write('INST:NSEL 3')
        return float(self.instr.query('MEAS:CURR?').lower())

    def SetCurrent3(self, current):
        '''This function sets the Keithley2230 PS3 Current'''
     
        self.instr.write('INST:NSEL 3')
        self.instr.write('CURR ' + str(current))
        time.sleep(0.1)

    def SetCurrents(self, i1,i2,i3):
        '''This function sets all three currents simultaneously'''
       
        self.instr.write('INST:NSEL 1')
        self.instr.write('CURR ' + str(i1))
        self.instr.write('INST:NSEL 2')
        self.instr.write('CURR ' + str(i2))
        self.instr.write('INST:NSEL 3')
        self.instr.write('CURR ' + str(i3))
        time.sleep(0.1)

    def TurnOutputsOn(self):
        '''Turns on all three outputs'''
        self.instr.write('OUTP ON')
        print('power on.')

    def TurnOutputsOff(self):
        '''Turns off all three outputs''' 
        self.instr.write('OUTP OFF')   
        print('power off.')

    def TurnOn(self,channel, power):
        '''Turns on/off each channel'''
        
        if channel == 1:
            self.instr.write(str('INST:NSEL 1'))
        if channel == 2:
            self.instr.write(str('INST:NSEL 2'))
        if channel == 3:
            self.instr.write(str('INST:NSEL 3'))
                    
        if power == 'ON':
            self.instr.write('CHAN:OUTP ON') 
        if power == 'OFF':
            self.instr.write('CHAN:OUTP OFF')   

    def __SetEnable__(self, Enabled):
        '''This function enables the On/Off for the Keithley2230'''
        if Enabled:
            self.instr.write('OUTP ON')
        else:
            self.instr.write('OUTP OFF')

    def GetOutputState(self):
        '''This function returns the On/Off state for the Keithley2230'''
        status = self.instr.query('OUTP?')
        if (status == "1" or status == "ON" ):
            return True
        return False

    def __SetDelay__(self, d):
        '''Sets this instruments settling time'''
        self.__delay__ = d

    def __GetDelay__(self):
        '''Gets this instruments settling time'''
        return True

    def Clear(self):
        self.instr.write('*CLS')
    
    def Reset(self):
        self.instr.write('*RST')




    #############################################################################
    '''
    used for unify different power supply, such as 2230G, IT6322B, GPP-4323
    2025-03-15
    '''

    def SetChx(self, ch = 1, vlotage = 0, current = 0):
        '''
        set voltage and current of ch1/2/3
        INPUT:
            ch:          channel 
            voltage:     units: V
            current:     units: A
        
        OUTPUT:
        
        '''
        if ch == 1:
            self.instr.write('INST:NSEL 1')
            self.instr.write('VOLT ' + str(vlotage))
            
        if ch == 2:
            self.instr.write('INST:NSEL 2')
            self.instr.write('VOLT ' + str(vlotage))
            
        if ch == 2:
            self.instr.write('INST:NSEL 3')
            self.instr.write('VOLT ' + str(vlotage))   
                     
        time.sleep(0.1)

    def GetChxSetting(self, ch = 1):
        '''
        read back voltage and current setting of ch1/2/3
        INPUT:
            ch:          channel 
            voltage:     units: V
            current:     units: A
        
        OUTPUT:
        
        '''
        if ch == 1:
            self.instr.write('INST:NSEL 1')
            vol = float(self.instr.query('VOLT?'))
            
            self.instr.write('INST:NSEL 1')
            cur = float(self.instr.query('MEAS:CURR?'))
            
            return [vol, cur]
         
        if ch == 2:
            self.instr.write('INST:NSEL 2')
            vol = float(self.instr.query('VOLT?'))
            
            self.instr.write('INST:NSEL 2')
            cur = float(self.instr.query('MEAS:CURR?'))
            
            return [vol, cur]
            
        if ch == 2:
            self.instr.write('INST:NSEL 2')
            vol = float(self.instr.query('VOLT?'))
            
            self.instr.write('INST:NSEL 2')
            cur = float(self.instr.query('MEAS:CURR?'))
            
            return [vol, cur]
                     
    def Meas_AllChannel(self):
        '''
        measure voltage and current of ch1/2/3
        
        retrun:
            [vol_ch1,vol_ch2,vol_ch3, cur_ch1,cur_ch2,cur_ch3]
            units: V, A
        
        '''
        
        VOL = self.instr.query('MEAS:VOLT? ALL')
        time.sleep(0.1)
        CUR = self.instr.query('MEAS:CURR? ALL')
        [vol_ch1,vol_ch2,vol_ch3] = [float(x) for x in VOL.split(",")]
        [cur_ch1,cur_ch2,cur_ch3] = [float(x) for x in CUR.split(",")]
       
        rslt = [vol_ch1,vol_ch2,vol_ch3, cur_ch1,cur_ch2,cur_ch3]
        print('V1(V), V2(V), V3(V), I1(A), I2(A), I3(A) = ', rslt)
                 
        return rslt

    def Meas_OneChannel(self,channel=1):
        '''
        description: measure specified channel voltage and current
        
        OUTPUTE:
            [vol, cur]:  units V, A
        
        '''
        if channel == 1:
            self.instr.write('INST:NSEL 1')
            vol = float(self.instr.query('MEAS:VOLT?'))
            time.sleep(0.2)            
            self.instr.write('INST:NSEL 1')
            curr = float(self.instr.query('MEAS:CURR?'))
            
        if channel == 2:
            self.instr.write('INST:NSEL 2')
            curr = float(self.instr.query('MEAS:VOLT?'))
            time.sleep(0.2)    
            self.instr.write('INST:NSEL 2')
            curr = float(self.instr.query('MEAS:CURR?'))
            
        if channel == 3:
            self.instr.write('INST:NSEL 3')
            vol = float(self.instr.query('MEAS:VOLT?'))
            time.sleep(0.2) 
            self.instr.write('INST:NSEL 3')
            curr = float(self.instr.query('MEAS:CURR?'))
        
        time.sleep(0.5)     
        print("measured channel"+str(channel)+" voltage is "+str(vol)+"V, and current is "+str(curr)+"A")
        
        return [float(x) for x in (vol,curr)]
    




# m = Keithley2230()
# # m.IDN_Inqury()
# m.SetVoltages(v1=1.2, v2=1.8, v3=1.8)
# m.SetCurrents(i1=1.5, i2=0.2, i3=0.5)
# m.TurnOutputsOn()
# m.Meas_AllChannel()
# m.TurnOutputsOff()
# m.TurnOn(channel=1, power='OFF')
# m.Meas_OneChannel(channel=1)