#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from HWInterface import HWImpl

# SSHFileCtrl需要安装下面模块
# pip3.9.exe install paramiko
from HWInterface import SSHFileCtrl 

import time

if __name__ == '__main__':
    # 使用第一个设备
    HWImpl.hw_open(ip_addr="*************")

    ret = HWImpl.hw_waithw_ok(100)
    if ret == 0:
        print("hw device have ok")
    else:
        print("wait hw device timeout")
    
    ret = HWImpl.hw_set_video_win(720, 360, 100, 100, 0xFF)
    print("operation=",ret," hw_set_video_win")
    ret = HWImpl.hw_set_video_cam(1920, 1551, 0, 0xFF)
    print("operation=",ret," hw_set_video_cam")
    
    time.sleep(1)
    ret = HWImpl.hw_open_video(0)
    print("operation=",ret," hw_open_video")
    
    ret = HWImpl.hw_open_video(1)
    print("operation=",ret," hw_open_video")
    
    time.sleep(1)
    
    ret = HWImpl.hw_save_one_videoframe(0)
    print("operation=",ret," hw_save_one_videoframe")
    
    ret = SSHFileCtrl.download_file_via_ssh(host_ip="*************", r_path="/home/<USER>/Pictures/video0_2024-05-25-04-46-35.jpg", l_path="C:/Users/<USER>/Pictures/video0_2024-05-25-04-46-35.jpg")
    print("operation=",ret," download_file_via_ssh")
    
    time.sleep(4)
    ret = HWImpl.hw_close_video(0)
    print("operation=",ret," hw_close_video")
    
    ret = HWImpl.hw_close_video(1)
    print("operation=",ret," hw_close_video")
    
    # 关闭设备
    HWImpl.hw_close()
