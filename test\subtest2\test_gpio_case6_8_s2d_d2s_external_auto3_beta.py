# -*- coding: utf-8 -*-
"""
GPIO Case-6: Bidirectional Signal Transmission Path - D68 Chip Version

基于test_gpio_case6_8_s2q_auto2.py模板，适配D68芯片测试。
支持双向信号测试：
1. S2D方向：S68 GPIO → D68 Q68 GPIO（串行测试，一个Link一个Link）
2. D2S方向：D68 Q68 GPIO → S68 GPIO（并行测试，所有Links同时）

主要修改：
1. 导入模块改为M65D68_Common_Fuction_A0
2. MFN设置函数改为MFNSet_D68
3. 添加D68特定初始化代码
4. 仅使用Link0和Link2进行测试
5. 使用独立设备初始化，包含完整地址转换
6. 新增D2S方向测试功能
"""
import logging
import time
import pytest
import os
import sys
import tkinter as tk
from tkinter import messagebox

# Add parent directory to path to import conftest
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
try:
    from conftest import get_universal_oscilloscope_screenshot, power_on
    print("✅ Successfully imported from conftest")
except ImportError as e:
    print(f"❌ Failed to import from conftest: {e}")
    # Define fallback functions if import fails
    def get_universal_oscilloscope_screenshot(oscilloscope):
        print("⚠️ Using fallback oscilloscope screenshot function")
        class FallbackScreenshot:
            def __init__(self, osc): self.osc = osc
            def execute_screenshot(self, **kwargs): return True
        return FallbackScreenshot(oscilloscope)

    def power_on():
        print("⚠️ Using fallback power_on function")
        return True


# 禁用pyvisa的DEBUG日志，减少冗余输出
logging.getLogger('pyvisa').setLevel(logging.WARNING)

# 添加D68模块路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm65d68_a0'))  # D68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'm66s68_a0'))  # S68模块路径
sys.path.append(os.path.join(PROJECT_ROOT, 'api', 'instr'))      # 示波器模块路径

# D68芯片专用导入
try:
    from Common_d.M65D68_Common_Fuction_A0 import *
    from Common_var.M66S68_Common_Fuction_A0 import *
    print("✅ D68模块导入成功")
except ImportError as e:
    print(f"⚠️ D68模块导入失败: {e}")
    print("🔄 尝试使用Q68模块作为兼容...")
    try:
        from Common.M65Q68_Common_Fuction_A0 import *
        from Common_var.M66S68_Common_Fuction_A0 import *
        print("✅ 使用Q68模块作为D68兼容")
    except ImportError as e2:
        print(f"❌ 所有模块导入失败: {e2}")
        # 定义空类以避免错误
        class M65Q68_A0: pass
        class M65Q68_A0_Remote_M66S68: pass

# D68设备初始化配置
D68_DEVICE_CONFIG = {
    'q68_iic_addr': 0x73,
    's68_iic_dev': [0x40, 0x40, 0x40, 0x40],
    's68_res_dev': [0x20, 0x21, 0x22, 0x23],
    's68_res_sensor_dev': [0x24, 0x25, 0x26, 0x27],
    'active_links': [0, 2],  # D68仅支持Link0和Link2
    'RATE':         [2, 2, 2, 2],
    'RATE_final':   [2, 2, 2, 2],
    'BCRATE':       [0, 0, 0, 0]  
}

# 全局停止标志
GLOBAL_STOP_FLAG = False

# 测试配置 - D68版本
TEST_CONFIG = {
    'active_links': [0, 2],                 # D68版本仅使用Link0和Link2
    'signal_id': 11,                        # GPIO信号ID
    'observation_time': 1,                  # 观察时间(秒)
    's68_source_gpio': 8,                   # S68源GPIO (用于截图文件命名，每次测试都设置同一个)

    # 示波器自动截图配置
    'oscilloscope_config': {
        'enable_screenshot': True,          # 是否启用自动截图
        'test_mode': 'fixed',            # 测试模式: 'fixed'=仅固定频率, 'sweep'=仅扫频, 'combined'=组合测试
        'trigger_source': 'D1',            # 触发源: C1-C4(模拟), D0-D15(数字), EX, EX5, LINE
        'waveform_type': 'SQUARE',          # 方波
        # 固定频率模式配置
        'frequency_list': [30, 1000, 10000, 50000, 100000],  # 固定频率列表 (Hz)
        'timebase_list': ['20ms', '1ms', '100us', '10us', '5us'],   # 对应的时基 - 优化100kHz时基
        # 'frequency_list': [30,1000 ],  # 固定频率列表 (Hz)
        # 'timebase_list': ['20ms','1ms' ],   # 对应的时基 - 优化100kHz时基
        # 扫频模式配置
        # 'frequency_range': {
        #     'start': 1210000,                    # 1210kHz
        #     'end': 1210000,                      # 1240kHz
        #     'step': 10000                       # 10kHz步进
        # },
        # 'sweep_timebase': '500ns',              # 扫频模式的时基
        'frequency_range': {
            'start': 450000,                    # 1210kHz
            'end': 470000,                      # 1240kHz
            'step': 10000                       # 10kHz步进
        },
        'sweep_timebase': '2us',              # 扫频模式的时基
        'amplitude': 1.8,                      # 1.8Vpp
        'offset': 0.9,                          # 900mVdc偏移
        'screenshot_folder_base': 'U-disk0/gpiotest/s68tod68_0_2',  # D68版本截图保存基础文件夹
        'screenshot_folder_base_d2s': 'U-disk0/gpiotest/d68tos68_0_2',  # D2S版本截图保存基础文件夹
        'persistence_mode': 'INFinite',         # 余晖模式 (该机型唯一选项)
        'probe_wait_time': 1,                  # 第一次截图前等待插探头时间(秒)
        'freq_observation_time': 1.3,             # 每个频率的观察时间(秒)
    },

    # 动态生成configurations，使用统一的S68 GPIO变量
    'configurations': None,  # 将在运行时动态生成

    # RVS延迟补偿测试配置 - D2S方向
    'rvs_test_config': {
        'frequency': 30,                    # 固定30Hz频率
        'timebase': '50us',                 # 50us时基
        'rvs_dly_values': [ 10, 60],  # rvs_dly测试值序列
                # 'rvs_dly_values': [0, 4, 10, 20, 30, 40, 50, 60, 63],  # rvs_dly测试值序列
        'trigger_source': 'C1',             # RVS测试使用C1触发源
        'waveform_type': 'SQUARE',          # 方波
        'amplitude': 1.8,                   # 1.8Vpp
        'offset': 0.9,                      # 900mVdc偏移
        'enable_screenshot': True,          # 启用截图
        'freq_observation_time': 0.5,         # 每个延迟值的观察时间(秒)
        'enable_dly_comp': True,            # 启用延迟补偿
        'screenshot_folder_base': 'U-disk0/gpiotest/d68tos68_0_2',  # RVS测试截图文件夹
    }
}


def d68_complete_initialization():
    """D68完整初始化函数 -  (上电->初始化D68 Q68设备->初始化D68 Q68远程设备->D68特定初始化->设置链路速率->地址转换)"""

    config = D68_DEVICE_CONFIG
    q68 = M65Q68_A0(dongle='stm32', id=0, bus='i2c')  # 初始化D68 Q68设备
    q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')  # 初始化D68 Q68远程设备
    q68.c2m.wr_test_glb_ctrl0_fields(key=0x5c)
    q68.c2m.wr_test_tx_link_data_inv_fields(tx_polar_sel=0x6)
    q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel=0x6)  # D68特定初始化
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])
    q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=config['BCRATE'][0], bc_rate1=config['BCRATE'][1], bc_rate2=config['BCRATE'][2], bc_rate3=config['BCRATE'][3])
    q68.Q68_C3_6G_Init(rate0=config['RATE'][0], rate1=config['RATE'][1], rate2=config['RATE'][2], rate3=config['RATE'][3])  # 设置链路速率
    for link in config['active_links']: 
        q68_remote.S68_AddrTrans(link=link, q68_iic_addr=0x73, s68_iic_addr=0x40, s68_retrans_addr=config['s68_res_dev'][link], sensor_addr=0x24, sensor_retrans_addr=0x24 + link)  # 地址转换
    q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])
    q68.Q68_C3_6G_Init(rate0=config['RATE_final'][0], rate1=config['RATE_final'][1], rate2=config['RATE_final'][2], rate3=config['RATE_final'][3])  # 设置链路速率
    try: from api.instr.instr_drv.SiglentSDS5Xserial_20250605 import SiglentSDS5034X; oscilloscope = SiglentSDS5034X()
    except: oscilloscope = None
    return {'q68': q68, 'q68_remote': q68_remote, 's68_res_dev': config['s68_res_dev'], 'oscilloscope': oscilloscope, 'configure_links': config['active_links']}


class GPIO_S68_D68_AutoTester:
    """S68到D68的GPIO自动测试器"""

    def __init__(self, devices):
        self.devices = devices
        self.q68 = devices['q68']
        self.q68_remote = devices['q68_remote']
        self.s68_res_dev = devices['s68_res_dev']
        self.configure_links = devices['configure_links']
        self.oscilloscope = devices.get('oscilloscope')

    def show_confirmation_dialog(self, gpio_num, current_test=0, total_tests=1, current_link=None, test_type="GPIO"):
        """通用确认对话框 - 5秒自动确认"""
        global GLOBAL_STOP_FLAG
        if GLOBAL_STOP_FLAG: return None

        import threading
        result = [True]

        def show_dialog():
            try:
                root = tk.Tk()
                root.withdraw()
                root.attributes('-topmost', True)
                link_info = f"Link{current_link} " if current_link else ""
                message = f"测试 {test_type} GPIO{gpio_num}?\n{link_info}进度: {current_test}/{total_tests}\n\n5秒后自动开始..."
                result[0] = messagebox.askyesnocancel("GPIO测试", message, parent=root)
                if result[0] is None:
                    global GLOBAL_STOP_FLAG
                    GLOBAL_STOP_FLAG = True
                root.destroy()
            except: pass

        thread = threading.Thread(target=show_dialog, daemon=True)
        thread.start()
        thread.join(timeout=5.0)
        if result[0] is None: GLOBAL_STOP_FLAG = True
        return result[0]

    def create_all_screenshot_folders(self):
        """预创建所有截图文件夹"""
        s2d_base = TEST_CONFIG['oscilloscope_config']['screenshot_folder_base']
        d2s_base = TEST_CONFIG['oscilloscope_config']['screenshot_folder_base_d2s']
        active_links = TEST_CONFIG['active_links']

        # S2D文件夹
        for link in active_links:
            for gpio in range(9):
                os.makedirs(f"{s2d_base}/link{link}_gpio{gpio}", exist_ok=True)

        # D2S文件夹
        all_links_str = ''.join(map(str, active_links))
        for gpio in range(11):
            os.makedirs(f"{d2s_base}/link{all_links_str}_gpio{gpio}", exist_ok=True)
            for link in active_links:
                os.makedirs(f"{d2s_base}/link{link}_gpio{gpio}", exist_ok=True)


    def full_system_reinit_d68(self):
        """完整系统重新初始化 - 从断电重新上电开始"""
        try:
            print(f"🔄 清理旧连接...")
            # 清理旧的设备连接
            try:
                if hasattr(self, 'q68') and self.q68: del self.q68
                if hasattr(self, 'q68_remote') and self.q68_remote: del self.q68_remote
            except: pass

            print(f"🔄 断电重新上电...")
            power_on()
            devices = d68_complete_initialization()
            self.q68 = devices['q68']
            self.q68_remote = devices['q68_remote']
            self.s68_res_dev = devices['s68_res_dev']
            self.oscilloscope = devices['oscilloscope']
            self.configure_links = devices['configure_links']
            print(f"✅ 重新上电初始化完成")
            return True
        except Exception as e:
            print(f"❌ 重新上电初始化失败: {e}")
            return False

    def oscilloscope_screenshot_universal(self, gpio_num, active_links=None, test_mode='combined', direction='s2d', rvs_dly=None):
        """统一示波器截图 - 支持S2D、D2S和RVS"""
        # 检查示波器对象
        if not self.oscilloscope:
            print(f"    ⚠️ 示波器未连接，跳过截图")
            return True

        osc_config = TEST_CONFIG['oscilloscope_config'].copy()
        if direction == 'd2s':
            osc_config['screenshot_folder_base'] = TEST_CONFIG['oscilloscope_config']['screenshot_folder_base_d2s']
        elif direction == 'd2s_rvs':
            print(f"    🎯 配置RVS示波器参数...")
            osc_config['screenshot_folder_base'] = TEST_CONFIG['rvs_test_config']['screenshot_folder_base']
            osc_config['frequency_list'] = [TEST_CONFIG['rvs_test_config']['frequency']]
            osc_config['timebase_list'] = [TEST_CONFIG['rvs_test_config']['timebase']]
            osc_config['persistence_mode'] = 'OFF'  # RVS测试余辉模式设为OFF
            osc_config['freq_observation_time'] = TEST_CONFIG['rvs_test_config']['freq_observation_time']  # RVS测试截图前等待1秒
            osc_config['trigger_source'] = TEST_CONFIG['rvs_test_config']['trigger_source']  # RVS测试触发源
            osc_config['waveform_type'] = TEST_CONFIG['rvs_test_config']['waveform_type']
            osc_config['amplitude'] = TEST_CONFIG['rvs_test_config']['amplitude']
            osc_config['offset'] = TEST_CONFIG['rvs_test_config']['offset']
            osc_config['enable_screenshot'] = TEST_CONFIG['rvs_test_config']['enable_screenshot']
            osc_config['rvs_dly'] = rvs_dly  # 传递rvs_dly值用于文件名
            test_mode = 'fixed'  # RVS固定30Hz
            print(f"    📊 RVS配置: 频率={osc_config['frequency_list']}, 时基={osc_config['timebase_list']}, rvs_dly={rvs_dly}")

        target_links = active_links or TEST_CONFIG['active_links']
        if len(target_links) > 1:
            target_links = [''.join(map(str, target_links))]

        try:
            result = get_universal_oscilloscope_screenshot(self.oscilloscope).execute_screenshot(
                gpio_num=gpio_num, active_links=target_links, test_mode=test_mode, osc_config=osc_config
            )
            print(f"    📷 示波器截图执行结果: {'✅' if result else '❌'}")
            return result
        except Exception as e:
            print(f"    ❌ 示波器截图执行异常: {e}")
            return False
        
    def test_single_s68_gpio_single_link_d68(self, s68_gpio, target_link):
        """测试单个S68 GPIO到所有Q68 GPIO - D68版本（单个Link）"""
        try:
            signal_id = TEST_CONFIG['signal_id']
            if target_link < len(self.s68_res_dev):
                self.q68_remote.dongle.devAddr = self.s68_res_dev[target_link]
                self.q68_remote.M2CMFNSet(gpio=s68_gpio, mfn=0)
                self.q68_remote.M2CGPIORemoteTx(gpio=s68_gpio, tx_id=signal_id)
                for q68_gpio in range(11):
                    self.q68.MFNSet_D68(gpio=q68_gpio, mfn=0)
                    self.q68.GPIORemoteRx(gpio=q68_gpio, rx_id=signal_id)
            self.oscilloscope_screenshot_universal(s68_gpio, [target_link], TEST_CONFIG['oscilloscope_config']['test_mode'], direction='s2d')
            return True
        except Exception as e:
            print(f"❌ Link{target_link}测试失败: {e}")
            return False

    def test_single_d68_gpio_all_link_s68(self, d68_gpio, link_id=0, rvs_dly_value=0, dly_comp_en=False, is_rvs_test=False):
        """测试单个D68 GPIO到所有S68 GPIO - D2S版本（所有Links并行）"""
        try:
            signal_id = TEST_CONFIG['signal_id']
            active_links = TEST_CONFIG['active_links']

            # S68端配置（接收端）- 同时配置所有活跃Links的所有GPIO
            for link in active_links:
                if link < len(self.s68_res_dev):
                    print(f"    📡 配置S68 Link{link} (地址: 0x{self.s68_res_dev[link]:02X})")
                    self.q68_remote.dongle.devAddr = self.s68_res_dev[link]
                    time.sleep(0.1)  # 地址切换后稳定时间

                    for gpio_n in range(9):  # S68 GPIO 0-8
                        self.q68_remote.M2CMFNSet(gpio=gpio_n, mfn=0)
                        self.q68_remote.M2CGPIORemoteRx(gpio=gpio_n, rx_id=signal_id)
                        self.q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly=rvs_dly_value)
                        time.sleep(0.01)  # GPIO配置间隔

            # D68端配置（发送端）- 单个GPIO发送
            self.q68.MFNSet_D68(gpio=d68_gpio, mfn=0)
            self.q68.GPIORemoteTx(gpio=d68_gpio, tx_id=signal_id, link_id=link_id, dly_comp_en=dly_comp_en)

            # 示波器测试 - RVS测试时使用不同文件夹
            direction = 'd2s_rvs' if is_rvs_test else 'd2s'
            test_mode = 'fixed' if is_rvs_test else TEST_CONFIG['oscilloscope_config']['test_mode']

            if is_rvs_test:
                print(f"    🎯 RVS示波器测试: GPIO{d68_gpio}, rvs_dly={rvs_dly_value}, 方向={direction}")

            try:
                # 为RVS测试传递rvs_dly值用于文件名
                screenshot_result = self.oscilloscope_screenshot_universal(
                    d68_gpio, TEST_CONFIG['active_links'], test_mode, direction=direction, rvs_dly=rvs_dly_value if is_rvs_test else None
                )
                print(f"    📷 示波器截图: {'✅' if screenshot_result else '❌'}")
                return screenshot_result
            except Exception as e:
                print(f"    ❌ 示波器截图异常: {e}")
                return False
        except Exception as e:
            print(f"❌ D68 GPIO{d68_gpio}测试失败: {e}")
            return False
    
    def test_all_s68_gpios_by_link_d68(self, s68_gpio_range, enable_dialog=True):
        """S68→D68 GPIO测试 - 按Link遍历"""
        all_results = {}
        for current_link in TEST_CONFIG['active_links']:
            for s68_gpio in s68_gpio_range:
                global GLOBAL_STOP_FLAG
                if GLOBAL_STOP_FLAG: break

                test_key = f"Link{current_link}_S68_GPIO{s68_gpio}"

                if enable_dialog:
                    dialog_result = self.show_confirmation_dialog(s68_gpio, 0, 1, current_link, "S68")
                    if dialog_result is None: break
                    elif dialog_result is False: continue

                if not self.full_system_reinit_d68():
                    all_results[test_key] = False
                    continue

                try:
                    result = self.test_single_s68_gpio_single_link_d68(s68_gpio, current_link)
                    all_results[test_key] = result
                except Exception:
                    all_results[test_key] = False

                time.sleep(1)
            if GLOBAL_STOP_FLAG: break

        completed = [r for r in all_results.values() if r in [True, False]]
        passed = [r for r in all_results.values() if r == True]
        return {
            'results': all_results,
            'summary': {'total': len(all_results), 'completed': len(completed), 'passed': len(passed)}
        }

    def test_all_d68_gpios_all_link_s68(self, d68_gpio_range, enable_dialog=True, test_mode='normal'):
        """D68→S68 GPIO测试 - 所有Links并行，支持normal/rvs模式"""
        all_results = {}
        for d68_gpio in d68_gpio_range:
            global GLOBAL_STOP_FLAG
            if GLOBAL_STOP_FLAG: break

            test_key = f"D68_GPIO{d68_gpio}"

            if enable_dialog:
                dialog_result = self.show_confirmation_dialog(d68_gpio, 0, 1, None, "D68")
                if dialog_result is None: break
                elif dialog_result is False: continue

            if not self.full_system_reinit_d68():
                all_results[test_key] = False
                continue

            try:
                if test_mode == 'rvs':
                    # RVS模式：每个rvs_dly值单独调用一次test_single_d68_gpio_all_link_s68
                    rvs_values = TEST_CONFIG['rvs_test_config']['rvs_dly_values']
                    for rvs_dly in rvs_values:
                        rvs_key = f"{test_key}_rvs{rvs_dly}"
                        print(f"      🎯 RVS测试: rvs_dly={rvs_dly} ({rvs_dly * 3.5:.1f}us)")

                        # 单独调用，只传入不同的rvs参数
                        result = self.test_single_d68_gpio_all_link_s68(
                            d68_gpio, rvs_dly_value=rvs_dly, dly_comp_en=True, is_rvs_test=True
                        )
                        all_results[rvs_key] = result
                        print(f"      {'✅' if result else '❌'} RVS结果: rvs_dly={rvs_dly}")

                        # rvs值之间的等待时间
                        time.sleep(1)
                else:
                    # 普通模式
                    result = self.test_single_d68_gpio_all_link_s68(d68_gpio)
                    all_results[test_key] = result
            except Exception:
                all_results[test_key] = False

            time.sleep(1)

        completed = [r for r in all_results.values() if r in [True, False]]
        passed = [r for r in all_results.values() if r == True]
        return {
            'results': all_results,
            'summary': {'total': len(all_results), 'completed': len(completed), 'passed': len(passed)}
        }




@pytest.mark.fast
def test_gpio_d68_s68_rvs_compensation():
    """D68→S68 GPIO RVS测试 - 复用D2S方法的rvs模式"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    # 直接复用现有D2S方法，选择rvs模式 [0,1,2,3,4,5,6,7,8,9,10]
    result = tester.test_all_d68_gpios_all_link_s68([4,9], enable_dialog=True, test_mode='rvs')

    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何RVS测试"
    print(f"✅ RVS测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_gpio_s68_d68_auto_traverse_by_link():
    """S68→D68 GPIO测试 - 按Link遍历"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    result = tester.test_all_s68_gpios_by_link_d68([0,1,2,3,4,5,6,7,8], enable_dialog=True)

    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试"
    print(f"✅ S68→D68测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_gpio_d68_s68_auto_traverse_all_links():
    """D68→S68 GPIO测试 - 所有Links并行"""
    global GLOBAL_STOP_FLAG
    GLOBAL_STOP_FLAG = False

    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)

    result = tester.test_all_d68_gpios_all_link_s68([0,1,2,3,4,5,6,7,8,9,10], enable_dialog=True)

    summary = result['summary']
    assert summary['completed'] > 0, f"没有完成任何测试"
    print(f"✅ D68→S68测试完成，通过率: {summary['passed']}/{summary['completed']}")


@pytest.mark.fast
def test_create_all_folders():
    """预创建所有截图文件夹 - 方便复制到U盘"""
    devices = d68_complete_initialization()
    tester = GPIO_S68_D68_AutoTester(devices)
    tester.create_all_screenshot_folders()
    print("✅ 所有文件夹创建完成，可以复制到U盘了")


if __name__ == "__main__":
    """
    使用说明:

    🆕 D68芯片GPIO双向测试功能:

    1. S2D方向测试 (S68→D68，串行测试):
       pytest test_gpio_case6_8_s2d_auto3.py::test_gpio_s68_d68_auto_traverse_by_link -v -s

    2. D2S方向测试 (D68→S68，并行测试):
       pytest test_gpio_case6_8_s2d_auto3.py::test_gpio_d68_s68_auto_traverse_all_links -v -s

    3. D2S RVS延迟补偿测试 (固定30Hz频率):
       pytest test_gpio_case6_8_s2d_d2s_external_auto3.py::test_gpio_d68_s68_rvs_compensation -v -s

    🔥 D68版本特点:
    - 基于test_gpio_case6_8_s2q_auto2.py适配D68芯片
    - 支持双向信号测试：S2D（串行）和D2S（并行）
    - 仅使用Link0和Link2 (D68硬件限制)
    - 使用MFNSet_D68方法替代MFNSet
    - 使用d68_complete_initialization()紧凑初始化函数
    - 包含完整的地址转换设置
    - 弹窗确认每个GPIO测试，可选择跳过或取消
    - 每个GPIO测试前完整系统重新初始化
    - 固定频率测试: 30Hz, 1kHz, 10kHz, 50kHz, 100kHz
    - 扫频测试: 1210kHz-1240kHz, 10kHz步进

    📊 测试策略差异:
    - S2D: 串行测试（一个Link一个Link）
    - D2S: 并行测试（所有Links同时）
    - RVS: D2S方向延迟补偿测试（固定30Hz，rvs_dly参数扫描）

    📁 截图文件夹结构:
    S2D: U-disk0/gpiotest/s68tod68_0_2/link{id}_gpio{n}/
    D2S: U-disk0/gpiotest/d68tos68_0_2/gpio{n}/
    RVS: U-disk0/gpiotest/d68tos68_rvs_0_2/gpio{n}/

    🎯 RVS测试特点:
    - 固定30Hz频率，50us时基
    - rvs_dly值: [0,4,10,20,30,40,50,60,63] (0-220.5us延迟范围)
    - 启用dly_comp_en延迟补偿
    - 每个GPIO测试前完整系统重新初始化
    - 专用RVS确认对话框

    - 完全兼容原有代码流程，针对D68芯片优化
    """
    print("D68芯片GPIO双向测试用例 (S2D + D2S)")
    print("请使用pytest运行测试")
