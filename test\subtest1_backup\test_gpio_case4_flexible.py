# -*- coding: utf-8 -*-
"""
GPIO Case-4: 所有S68的多个GPIO帧同步测试 (灵活Link版本)

验证Q68可以向所有S68芯片的多个GPIO发送帧同步信号
"""
import logging
import time
import pytest

# 测试配置 - 可以手动修改
TEST_CONFIG = {
    'active_links': [0, 1, 2, 3],  # 默认使用所有Link (Case4需要测试所有Link)
    'signal_configs': [
        {'signal_id': 11, 'gpio': 0, 'freq': '30Hz'},
        {'signal_id': 12, 'gpio': 1, 'freq': '25Hz'},
        {'signal_id': 13, 'gpio': 2, 'freq': '20Hz'},
        {'signal_id': 14, 'gpio': 3, 'freq': '15Hz'},
    ],
    'observation_time': 5  # 观察时间(秒)
}

@pytest.mark.fast
def test_gpio_case4_multi_sync_all_s68(devices):
    """
    Case4: 所有S68的多个GPIO帧同步测试
    
    测试步骤:
    0. 配置指定的Links
    1. 配置Q68生成多路帧同步信号
    2. 配置所有激活Links上的S68接收信号
    3. 验证链路状态
    """
    print(f"\n[CASE-4] 开始执行: 所有S68的多个GPIO帧同步测试")
    print(f"测试配置: 激活Links {TEST_CONFIG['active_links']}")
    print(f"信号配置: {len(TEST_CONFIG['signal_configs'])} 路信号")

    # 获取设备对象和配置函数
    q68 = devices['q68']
    q68_remote = devices['q68_remote']
    s68_res_dev = devices['s68_res_dev']
    configure_links = devices['configure_links']

    # ---------------------------------------------------------------------
    # 步骤0: 配置测试专用Links
    # ---------------------------------------------------------------------
    print(f"\n步骤0: 配置测试专用Links {TEST_CONFIG['active_links']}")
    link_status = configure_links(TEST_CONFIG['active_links'])
    print(f"  - Links配置完成: {link_status}")

    # ---------------------------------------------------------------------
    # 步骤1: 配置Q68生成多路帧同步信号
    # ---------------------------------------------------------------------
    print(f"\n步骤1: 配置Q68生成{len(TEST_CONFIG['signal_configs'])}路帧同步信号...")
    
    for i, config in enumerate(TEST_CONFIG['signal_configs']):
        # 根据频率计算period值 (这里使用示例值，实际需要根据具体频率计算)
        period_map = {
            '30Hz': 17361,
            '25Hz': 20833,
            '20Hz': 26042,
            '15Hz': 34722
        }
        period = period_map.get(config['freq'], 17361)
        
        q68.FrameSyncOutConifg(
            i=i, 
            per_div=0x0B, 
            duty_cycle=4,
            period=period, 
            fs_tx_id=config['signal_id'], 
            auto_fs=1, 
            outen=1
        )
        print(f"  - 信号发生器{i}: {config['freq']}, ID={config['signal_id']}, GPIO={config['gpio']}")

    # ---------------------------------------------------------------------
    # 步骤2: 配置所有激活Links上的S68接收信号
    # ---------------------------------------------------------------------
    print(f"\n步骤2: 配置激活Links上的S68接收多路帧同步信号...")
    
    for link_idx in TEST_CONFIG['active_links']:
        if link_idx < len(s68_res_dev):
            target_s68_addr = s68_res_dev[link_idx]
            print(f"  - 配置S68 Link-{link_idx} (地址: {hex(target_s68_addr)})...")
            q68_remote.dongle.devAddr = target_s68_addr
            
            for config in TEST_CONFIG['signal_configs']:
                q68_remote.M2CMFNSet(gpio=config['gpio'], mfn=0)
                q68_remote.M2CGPIORemoteRx(gpio=config['gpio'], rx_id=config['signal_id'])
                print(f"    * GPIO{config['gpio']} 接收信号ID{config['signal_id']} ({config['freq']})")
        else:
            print(f"  - 警告: Link{link_idx} 超出s68_res_dev范围，跳过")

    # ---------------------------------------------------------------------
    # 步骤3: 观察和验证
    # ---------------------------------------------------------------------
    print(f"\n步骤3: 等待{TEST_CONFIG['observation_time']}秒进行信号观察...")
    time.sleep(TEST_CONFIG['observation_time'])

    # 验证激活Links的状态
    print(f"\n步骤4: 验证激活Links {TEST_CONFIG['active_links']} 的状态...")
    link_status_funcs = [
        q68.c2m.rd_test_fsm_status1_link0,
        q68.c2m.rd_test_fsm_status1_link1,
        q68.c2m.rd_test_fsm_status2_link2,
        q68.c2m.rd_test_fsm_status2_link3,
    ]
    
    active_statuses = {}
    for link_id in TEST_CONFIG['active_links']:
        if 0 <= link_id <= 3:
            status = link_status_funcs[link_id]()
            active_statuses[f'link{link_id}'] = status
            print(f"    - Link{link_id} 状态: {status}")
    
    # 验证所有激活Links的状态
    failed_links = [link for link, status in active_statuses.items() if status != 5]
    assert len(failed_links) == 0, \
        f"测试失败: Links {failed_links} 未处于LINKED状态(5)。状态: {active_statuses}"
    
    print(f"\n==> [CASE-4] 测试通过: {len(TEST_CONFIG['signal_configs'])}路帧同步信号已配置到Links {TEST_CONFIG['active_links']}，链路状态正常。")


@pytest.mark.parametrize("link_combination", [
    [0],           # 只测试Link0
    [1],           # 只测试Link1
    [0, 1],        # 测试Link0和Link1
    [0, 1, 2, 3],  # 测试所有Link (默认)
])
def test_gpio_case4_parametrized(devices, link_combination):
    """参数化测试 - 可以测试不同的Link组合"""
    # 临时修改配置
    original_links = TEST_CONFIG['active_links']
    TEST_CONFIG['active_links'] = link_combination
    
    try:
        test_gpio_case4_multi_sync_all_s68(devices)
        print(f"✅ Link组合 {link_combination} 测试通过")
    finally:
        # 恢复原始配置
        TEST_CONFIG['active_links'] = original_links


if __name__ == "__main__":
    """
    使用说明:
    
    1. 测试默认配置 (所有Link):
       pytest test_gpio_case4_flexible.py::test_gpio_case4_multi_sync_all_s68 -v -s
    
    2. 参数化测试特定Link组合:
       pytest test_gpio_case4_flexible.py::test_gpio_case4_parametrized[link_combination0] -v -s
    
    3. 测试所有Link组合:
       pytest test_gpio_case4_flexible.py::test_gpio_case4_parametrized -v -s
    
    4. 手动修改TEST_CONFIG中的active_links来测试特定组合
    """
    print("GPIO Case-4 灵活Link测试")
    print("请使用pytest运行测试")
