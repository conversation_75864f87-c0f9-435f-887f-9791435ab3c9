'''
Revision:
    1. update to A0
    2. puts log into 'Common_Fucntion'

Note:
    1. no power supply control.
    
'''

# import wxP.1ytho.1nxFF
import sys
import csv
import os
import time
import datetime
# import wx

from Common.M65D68_Common_Fuction_A0 import *
from Common_var.M66S68_Common_Fuction_A0 import *
# from constants import TRUE

from instr_drv.IT6322B import *
power_camera = IT6322B(usb_addr="USB0::0x2EC7::0x6300::800068020757210071::INSTR")
from instr_drv.Keithley2230_py3 import *
power_q68 = <PERSON>ley2230(usb_addr="USB0::0x05E6::0x2230::9211112::INSTR")

# from oven_doaho import *  #温箱
# oven=OVEN(port='com6')
#==================================================================================/
# from tkinter import *
# BOARDID = 0 #define global parameter to set boardid
# CHIPID = 0 #define global parameter to set chipid
# root=Tk()
# root.geometry('300x300')
#
# l1=Label(root,text='BOARD ID')
# l1.pack()
# boardid_text = StringVar()
# boardid_set = Entry(root,textvariable=boardid_text)
# boardid_text.set("")
# boardid_set.pack()
#
# l2=Label(root,text='CHIP ID')
# l2.pack()
# chipid_text = StringVar()
# chipid_set = Entry(root,textvariable=chipid_text)
# chipid_text.set("")
# chipid_set.pack()

# def on_click():
#     global BOARDID
#     global CHIPID
#     BOARDID = boardid_text.get()
#     CHIPID = chipid_text.get()
#     string=str("sheet is:%s"%(BOARDID))
#     string1=str("chipid is:%s"%(CHIPID))
#     print(string)
#     print(string1)
#     root.destroy()
#
# Button(root,text='press',command=on_click).pack()
# root.mainloop()

#==================================================================================/
STEMP               =   (25,)
VOLTAGE             =   [(1.14,1.71,1.71),(1.2,1.8,1.8)]
DRIFT               =   (25,)
Drift_type          =   {-40:(-40,85,25), 25:(25,-40,85),85:(85,25,-40)}
VIDEO_WIN = [0,5,10,15]     # orin video window

cable_length = ['5m_coax', '5m_coax', '5m_coax', '5m_coax']      # information: link/cable 
camera_id = {0:'1#',1:'2#',2:'3#',3:'4#',}                          # information: camera SN#
#==================================================================================/
KLINK        =   [0,2]              # 表示接几个c3的lane link0~3

RATE         =   [2,2,2,2]              # link rate,  initial(default:cfg1), 1-3G, 2-6G; link0, link1, link2, link3
RATE_final   =   [2,2,2,2]               # link rate,  finally set(change when Linked)
BCRATE       =   [0,0,0,0]              # q68 -> S68 data rate: 0:150M, 1:187.5M, 2/3:200M  
DTbypass     =   [0,0,0,0]              # 多data type时必须改为1; link0, link1, link2, link3
vcid         =   [0,1,2,3]              # set vcid of each link(active when Camera's vcid=0)

pcs_set      =   [0,0,0,0]              # 0:8b/10b (0,0,0,0),  1: 66/64d [(1,1,1,1)
fec_bypass   =   [0,0,0,0]              # 1: fec bypass
#==================================================================================================================/

link_camera = {0:'Huayang_031', 1:'Huayang_031', 2:'Huayang_031', 3:'Huayang_031',}     #需要根据link上接的camera进行修改
# link_camera  = {0:'s68_VG',1: 's68_VG',2:'s68_VG',3:'s68_VG',}                       # S68 video generator, with Fsync
# link_camera = {0:'q68_VG',1: 'q68_VG',2:'q68_VG',3:'q68_VG',}                       # Q68 video generator, instead of Link data
                                        
q68_iic_addr =  0x73                        # Q68地址, 0x31#0x73
s68_iic_dev  =  [0x40, 0x40, 0x40, 0x40]    # s68地址，需要根据实际s68地址进行改动
#==================================================================================================================/
aggregation_mode = 'RoundRobin_2csi'        # 'RoundRobin_1csi' '4W1H_2csi'  #'1W4H_2csi' #'2W1H_2csi'  '3W1H_2csi' #'RoundRobin_4csi_1video'  '2W1H_4csi'
csi_mode = 'dphy_2csi_1c4d'                 # 'dphy_4csi_1c2d'  #'dphy_2csi_1c4d' dphy_1csi_1c4d 'dphy_cphy_2csi_1c4d' # 'cphy_2csi_4trio'  'cphy_4csi_2trio'
#==================================================================================================================/
MIPI_timing_dterm    = [0,0,0,0]            # S68 MIPI timing: d-term-en
MIPI_timing_hssettle = [0,0,0,0]            # S68 MIPI timing: hs-settle
#==================================================================================================================/
s68_res_dev        = [0x20, 0x21, 0x22, 0x23]           # s68转译地址可自行定义，link0,link1,link2,link3
s68_res_sensor_dev = [0x24, 0x25, 0x26, 0x27]           # sensor转译地址可自行定义，link0,link1,link2,link3
#===================================================================================================================/
sensor_dev = {'NIO_031':0, 'PHYbrd_NIO_031':0, 'xiaopeng_031_RAW12': 0x1A,'NIO_1Mega':0,'NIO_2Mega':0x36,'NIO_8Mega':16, 'x8B':0x36, 'SG2_IMX390C':0, 'Huayang_031':0, 's68_VG':0, 'q68_VG':0}    # sensor iic address, set to '0' if no register access needed
s68_sensor_dev = [sensor_dev[link_camera[0]], sensor_dev[link_camera[1]], sensor_dev[link_camera[2]], sensor_dev[link_camera[3]]] 
#==================================================================================================================/

q68 =  M65Q68_A0(dongle='stm32', id=0, bus='i2c')# G9PH DS board  
q68_remote = M65Q68_A0_Remote_M66S68(dongle='stm32', id=0, bus='i2c')
# s680 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link0
# s681 = M66S68_A0(dongle_id=b'\x00Q\x00$41Q\x197997', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link1
# s682 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=2,  bus='i2c', acc='L', optype='manual')  #link2
# s683 = M66S68_A0(dongle_id=b'\x00:\x00&41Q\x024590', bus_chan=3,  bus='i2c', acc='L', optype='manual')  #link3
# s68_0 = M66S68_A0(dongle='stm32', id=1, bus='i2c',acc='L',dongle_id=b'\x00N\x00$41Q\x197997', bus_chan=2, optype='auto')

# data_address = "D:\\project\\m65q68_a0\\Raw_Data\\01_test\\Camera\\" + str(BOARDID) + "_" + "Camera" + "_" + datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S') + ".csv"  # ('%Y-%m-%d_%H-%M-%S') 

       
def TEST():
    
    global s68_iic_dev  # 添加这行声明使用全局变量
    
    for vdd10,vddio,vdd18 in VOLTAGE:
        # Keithley2230G1.SetVoltages(v1=vdd10, v2=vddio, v3=vdd18)
        for starttemp in STEMP:     
              
            for powercycle in range(1):
                                
                '''# Step1: 设置 Q68 link速率，以匹配S68速率'''
                q68.c2m.wr_test_glb_ctrl0_fields(key=0x5c)  # Test register write access key. When the key equals to 0x5C, the test registers are accessible.
                q68.c2m.wr_test_tx_link_data_inv_fields(tx_polar_sel=0x6)    #R5/R7 polarity
                q68.c2m.wr_test_rx_link_data_inv_fields(rx_polar_sel =0x6) 
                                  
                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3])  # set forward data rate
                q68.c2m.wr_sys_cfg_link_ctrl3_fields(bc_rate0=BCRATE[0], bc_rate1=BCRATE[1], bc_rate2=BCRATE[2], bc_rate3=BCRATE[3])   # set backward data rate
        
                '''# Step2： 设置 link参数、编码方式(FEC, 8b10b, 64b66b)'''
                
                q68.Q68_C3_6G_Init(rate0=RATE[0], rate1=RATE[1], rate2=RATE[2], rate3=RATE[3]) #link parameters
                
                # for link in KLINK:
                #     q68.FECcoding(link=link, pcs_set=pcs_set[link], fec_bypass=fec_bypass[link])
                
                '''# Step3： 设置 4路S68、sensor I2C转译地址'''                    
  
                for link in KLINK:
                    q68_remote.S68_AddrTrans(link=link, q68_iic_addr=q68_iic_addr ,s68_iic_addr=s68_iic_dev[link], s68_retrans_addr=s68_res_dev[link], sensor_addr=s68_sensor_dev[link], sensor_retrans_addr=s68_res_sensor_dev[link])

                # '''# Step4: 设置 S68 编码方式(FEC, 8b10b, 64b66b) '''                 
                #
                # for link in KLINK:
                #     q68_remote.S68_FECCoding(s68_res_dev=s68_res_dev[link], pcs_set=pcs_set[link],fec_bypass=fec_bypass[link], RATE_final=RATE_final[link])       

                '''# Step5: 设置 link速率(final)，参数 '''  
                    
                q68.c2m.wr_sys_cfg_link_ctrl1_fields(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])
                q68.Q68_C3_6G_Init(rate0=RATE_final[0], rate1=RATE_final[1], rate2=RATE_final[2], rate3=RATE_final[3])  #for 6G init

                print('link0 status is: ', q68.c2m.rd_test_fsm_status1_link0()) 
                print('link1 status is: ', q68.c2m.rd_test_fsm_status1_link1()) 
                print('link2 status is: ', q68.c2m.rd_test_fsm_status2_link2()) 
                print('link3 status is: ', q68.c2m.rd_test_fsm_status2_link3())
                
                '''# frame sync test with 3 frame sync gen: Q68->4S68
                for link in KLINK:
                    s68_iic_dev = s68_res_dev[link]
                    q68_remote.dongle.devAddr = s68_iic_dev
                    # q68_remote.M2CGPIORemoteRx(gpio=0)       #enable frame sync output to camera
                    # q68_remote.m2c.wr_gpios_ctrl0_fields(fwd_dly = 10)
                    # q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly = 0)
                    q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=11)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=1, rx_id=13)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=2, rx_id=15)       #enable frame sync output to camera
                    q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=11)       #enable frame sync output to camera
                    q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=11)       #enable frame sync output to camera
                    # q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly = 30)
                    # s68_iic_dev = s68_res_dev[0]   
                    # q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=11)       #enable frame sync output to camera 
                    # s68_iic_dev = s68_res_dev[1]
                    # q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=13)       #enable frame sync output to camera
                    # s68_iic_dev = s68_res_dev[2]
                    # q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=15)       #enable frame sync output to camera
                    # s68_iic_dev = s68_res_dev[3]
                    # q68_remote.M2CMFNSet(gpio=3, mfn=0)
                    # q68_remote.M2CGPIORemoteRx(gpio=3, rx_id=11)       #enable frame sync output to camera
                

                
                # q68.FrameSyncOutConifg(per_div=0x0B, duty_cycle=4, period=17361, fs_tx_id=11, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data
                # q68.FrameSyncOutConifg(i=1, per_div=0x0A,  duty_cycle=4, period=15625, fs_tx_id=13, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data
                # q68.FrameSyncOutConifg(i=2, per_div=0x0B,duty_cycle=4,  period=43403, fs_tx_id=15, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data
                # 
                # 3 7 10 11
                # 4 6 12 13
                q68.MFNSet_D68(gpio=0, mfn=1)
                q68.FrameSyncOutConifg(per_div=0x0B, duty_cycle=4, period=17361, fs_tx_id=11, auto_fs=1, outen=1 , tx_dly_en = 0 , frame_comp_sync_man=0, frame_man_trans=0)     #configure and enable fsync, 30 Hz , camera send out data
                # q68.FrameSyncOutConifg(i=1, per_div=0x0B,  duty_cycle=4, period=17361, fs_tx_id=13, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data
                # q68.FrameSyncOutConifg(i=2, per_div=0x0B,duty_cycle=4,  period=17361, fs_tx_id=15, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data
                # '''
                
                # # 设置 en=1 (Enable)
                # q68.c2m.wr_fh_lock_ctrl0_fields(en=1)
                #
                # # 读取 en 字段的值
                # en_value = q68.c2m.rd_fh_lock_ctrl0_en()
                # if en_value == 1:
                #     print("Lockpin 已成功使能！")
                # else:
                #     print("Lockpin 未使能，当前值:", en_value)
                
                # for link in KLINK:
                #     s68_iic_dev = s68_res_dev[link]
                #     q68_remote.dongle.devAddr = s68_iic_dev
                #     q68_remote.M2CMFNSet(gpio=0, mfn=0)
                #     q68_remote.M2CGPIORemoteRx(gpio=0,rx_id=11)       #enable frame sync output to camera
                #     q68_remote.m2c.wr_gpios_ctrl0_fields(fwd_dly = 5)
                #     q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly = 5)
                #     q68_remote.m2c.rd_gpios_ctrl0_fwd_dly()
                #     # q68_remote.M2CGPIORemoteRx(gpio=3, rx_id=11)       #enable frame sync output to camera
                #     # q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=10)       #enable frame sync output to camera
                #     # q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=13)       #enable frame sync output to camera
                #     # q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=15)       #enable frame sync output to camera
                # q68.c2m.wr_gpios_ctrl0_fields(fwd_dly = 5)  
                # q68.c2m.wr_gpios_ctrl1_fields(rvs_dly = 5) 
                # q68.MFNSet_D68(gpio=0, mfn=0)
                # q68.GPIORemoteTx(gpio=0, tx_id = 11, link_id=0)
                # print('done')
                #

                # #
                # q68.c2m.wr_gpios_ctrl0_fields(fwd_dly = 0)  
                # q68.c2m.wr_gpios_ctrl1_fields(rvs_dly = 0)    
                # q68.MFNSet_D68(gpio=4, mfn=0)
                # q68.GPIORemoteRx(gpio=4, rx_id=11)
                # # q68.MFNSet(gpio=1, mfn=0)
                # # q68.GPIORemoteRx(gpio=1, rx_id=11)
                # # q68.MFNSet(gpio=2, mfn=0)
                # # q68.GPIORemoteRx(gpio=2, rx_id=11)
                # # q68.MFNSet(gpio=3, mfn=0)
                # # q68.GPIORemoteRx(gpio=3, rx_id=11)
                # # q68.MFNSet(gpio=4, mfn=0)
                # # q68.GPIORemoteRx(gpio=4, rx_id=11)
                #
                #
                #
                #
                # for link in KLINK:
                s68_iic_dev = s68_res_dev[2]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_gpios_ctrl0_fields(fwd_dly = 0)
                q68_remote.m2c.wr_gpios_ctrl1_fields(rvs_dly = 0)
                
                q68_remote.M2CMFNSet(gpio=1, mfn=0)
                # q68_remote.m2c.wr_gpios_gpio_b_1_fields(out_type = 1)
                # q68_remote.m2c.wr_gpios_gpio_a_1_fields(rx_init_out = 0, dly_comp_en = 0, out = 0, rx_en = 0, tx_en = 1, out_dis = 0) 
                # q68_remote.m2c.wr_gpios_gpio_a_1_fields(rx_init_out = 0, dly_comp_en = 0, out = 1, rx_en = 0, tx_en = 1, out_dis = 0) 
                q68_remote.M2CGPIORemoteTx(gpio=1, tx_id=11)
                    # q68_remote.M2CGPIORemoteRx(gpio=0)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=3, rx_id=11)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=10)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=13)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=15)       #enable frame sync output to camera
                # q68.c2m.wr_gpios_ctrl0_fields(fwd_dly = 0)  
                # q68.c2m.wr_gpios_ctrl1_fields(rvs_dly = 0)    
                q68.MFNSet_D68(gpio=9, mfn=0)
                q68.GPIORemoteRx(gpio=9, rx_id=11)
                # q68.MFNSet(gpio=1, mfn=0)
                # q68.GPIORemoteRx(gpio=1, rx_id=11)
                # q68.MFNSet(gpio=2, mfn=0)
                # q68.GPIORemoteRx(gpio=2, rx_id=11)
                # q68.MFNSet(gpio=3, mfn=0)
                # q68.GPIORemoteRx(gpio=3, rx_id=11)

                print('done')

                '''
                # for link in KLINK:
                s68_iic_dev = s68_res_dev[0]
                q68_remote.dongle.devAddr = s68_iic_dev
                # q68_remote.M2CMFNSet(gpio=3, mfn=2)
                q68_remote.Efhdis()
                q68_remote.m2c.wr_efh_ctrl0_fields(tx_id = 7, tx_en = 1)
                
                s68_iic_dev = s68_res_dev[1]
                q68_remote.dongle.devAddr = s68_iic_dev
                # q68_remote.M2CMFNSet(gpio=3, mfn=2)
                q68_remote.Efhdis()
                q68_remote.m2c.wr_efh_ctrl0_fields(tx_id = 8, tx_en = 1)
                
                s68_iic_dev = s68_res_dev[2]
                q68_remote.dongle.devAddr = s68_iic_dev
                # q68_remote.M2CMFNSet(gpio=3, mfn=2)
                q68_remote.Efhdis()
                q68_remote.m2c.wr_efh_ctrl0_fields(tx_id = 9, tx_en = 1)
                
                s68_iic_dev = s68_res_dev[3]
                q68_remote.dongle.devAddr = s68_iic_dev
                # q68_remote.M2CMFNSet(gpio=3, mfn=2)
                q68_remote.Efhdis()
                q68_remote.m2c.wr_efh_ctrl0_fields(tx_id = 10, tx_en = 1)
                    # q68_remote.M2CGPIORemoteTx(gpio=3, tx_id=11)
                    # q68_remote.M2CGPIORemoteRx(gpio=0)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=3, rx_id=11)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=0, rx_id=10)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=7, rx_id=13)       #enable frame sync output to camera
                    # q68_remote.M2CGPIORemoteRx(gpio=8, rx_id=15)       #enable frame sync output to camera
                q68.c2m.wr_fh_rem_event_en_ctrl0(255)
                print('fh_rem_event is: ',q68.c2m.rd_fh_rem_event_en_ctrl0())
                q68.c2m.wr_fh_rem_event_en_ctrl1(255)
                print('fh_rem_event is: ',q68.c2m.rd_fh_rem_event_en_ctrl1())
                # q68.MFNSet(gpio=0, mfn=0)
                q68.GPIORemoteRx(gpio=18, rx_id = 7)  # set q68 gpio28 rx id to 7
                q68.GPIORemoteRx(gpio=19, rx_id = 8)  # set q68 gpio28 rx id to 7
                q68.GPIORemoteRx(gpio=20, rx_id = 9)  # set q68 gpio28 rx id to 7
                q68.GPIORemoteRx(gpio=21, rx_id = 10)  # set q68 gpio28 rx id to 7
                q68.GPIORemoteLogic(gpio=18, logic_gpio=0, group=0, logic_mode=0, c2mlogicinv=0) #set group1 logic0 to gpio28
                q68.GPIORemoteLogic(gpio=19, logic_gpio=1, group=0, logic_mode=0, c2mlogicinv=0) #set group1 logic1 to gpio29
                q68.GPIORemoteLogic(gpio=20, logic_gpio=2, group=0, logic_mode=0, c2mlogicinv=0) #set group1 logic2 to gpio30
                q68.GPIORemoteLogic(gpio=21, logic_gpio=3, group=0, logic_mode=0, c2mlogicinv=0) #set group1 logic3 to gpio31
                
                
                
                
                s68_iic_dev = s68_res_dev[0]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(1)
                
                s68_iic_dev = s68_res_dev[1]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(1)
                
                s68_iic_dev = s68_res_dev[2]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(1)
                
                s68_iic_dev = s68_res_dev[3]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(1)
                print('rem_event_status0 is: ', q68.c2m.rd_fh_rem_event_status0())
                print('rem_event_status1 is: ', q68.c2m.rd_fh_rem_event_status1())
                
                s68_iic_dev = s68_res_dev[0]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(0)
                
                s68_iic_dev = s68_res_dev[1]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(0)
                
                s68_iic_dev = s68_res_dev[2]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(0)
                
                s68_iic_dev = s68_res_dev[3]
                q68_remote.dongle.devAddr = s68_iic_dev
                q68_remote.m2c.wr_efh_ctrl1(0)
                print('rem_event_status0 is: ', q68.c2m.rd_fh_rem_event_status0())
                print('rem_event_status1 is: ', q68.c2m.rd_fh_rem_event_status1())
                '''
                
                print('done')
                
                


        #         ''' # Step6: 配置 Q68 Router、MIPI'''
        #
        #         if 'q68_VG' in [link_camera[0],link_camera[1],link_camera[2],link_camera[3]]:
        #             RouterClk = 4     #when VG used,select clock from VG
        #         else:
        #             RouterClk = RATE_final.index(max(RATE_final)) 
        #
        #         q68.Router_Config(vcid=vcid, aggregation_mode=aggregation_mode, DTbypass=DTbypass, RouterClk=RouterClk, KLINK=KLINK)  # '2W1H'
        #
        #         q68.Q68_MIPI_Init(csi_mode= csi_mode)       #'cphy_2csi_4trio'
        #
        #
        #         ''' # Step7: 复位 sensor，并初始化S68 MIPI'''                   
        #
        #         # 7.1 Camera resset 
        #         for link in KLINK:
        #             ''' camera kept Reset mode during s68 init '''
        #             q68_remote.Camera_Init(s68_iic_dev = s68_res_dev[link], Camera_type=link_camera[link], sensor_dev=s68_res_sensor_dev[link])
        #
        #         # 7.2 S68 MIPI init
        #         for link in KLINK:
        #             q68_remote.S68_Init(s68_iic_dev = s68_res_dev[link], dtbypass=DTbypass[link])   
        #
        #
        #         ''' # Step8: camera 发数据，并且使能Frame sync'''     
        #
        #         if 'q68_VG' in [link_camera[0],link_camera[1],link_camera[2],link_camera[3]]:   
        #
        #             q68.Q68_VG_Gen(HV_res=19201280)        # generate VideGen directly,3840:3840x2160, 19201280:1920x1280,
        #             print("[Q68] Generate Q68 VG")
        #
        #         else:                         
        #             # Camera data output enable(when fsync used)'''
        #             for link in KLINK:
        #                 q68_remote.Camera_DateOut(s68_iic_dev = s68_res_dev[link], Camera_type=link_camera[link],sensor_dev=s68_res_sensor_dev[link],MIPI_timing_dterm=MIPI_timing_dterm[link],MIPI_timing_hssettle=MIPI_timing_hssettle[link])    
        #
        #             # q68.MFNSet(gpio=2, mfn=1)                                                             # watch frame sync on Q68 PIN                                       
        #             q68.FrameSyncOutConifg(per_div=0x0b, period=17361, fs_tx_id=11, auto_fs=1, outen=1)     #configure and enable fsync, 30 Hz , camera send out data
        #             # q68.FrameSyncOutConifg(per_div=0x0b, period=20900, fs_tx_id=11, auto_fs=1, outen=1)     #configure and enable fsync, 25 Hz
        #             print("[Q68+S68] Generate Camera Data, with FrameSync")
        #
        # #==================================================================================================================/
        #         ''' clear initial ERROR not cared '''
        #         time.sleep(0.1)  # initial error
        #         for link in KLINK:
        #             q68_remote.dongle.devAddr = s68_res_dev[link]   
        #             q68_remote.m2c.wr_mipi_rx_mipi_dig_rx_int_clear_fields(mipi_int=1)      #clear MIPI interrupt
        #             q68_remote.m2c.wr_mipi_rx_mipi_dig_rx_int_clear_fields(mipi_int=0)
        #
        #             q68_remote.EfhStatusClr()          # clear s68 fault
        #             q68.EfhTRxStatusCheck(i=link)      # enable fault detect: link
        #
        #         q68.EfhRouterStatusCheck()             # enable fault detect: pipe0~7          
        #         q68.EfhStatusClr()                     # clear fault(link/router/com/gpio..) one time
        # #==================================================================================================================/
        #         ''' check link/s68 mipi status'''
        #         for i in range(3):
        #
        #             for csi in range(4):                
        #                 for vc in range(4):
        #                     q68.FrameCount(csi = csi, vc=vc)    # frame counter: csi 0~3, vcid 0~7                         
        #
        #             link0_status = q68.c2m.rd_test_fsm_status1_link0()
        #             link1_status = q68.c2m.rd_test_fsm_status1_link1()
        #             link2_status = q68.c2m.rd_test_fsm_status2_link2()
        #             link3_status = q68.c2m.rd_test_fsm_status2_link3()
        #             print('Link Status:',link0_status,link1_status,link2_status,link3_status)
        #             time.sleep(1)
        #
        #             #check s68 ecc
        #             for link in KLINK:
        #                 q68_remote.S68_MIPI_Check(s68_iic_dev = s68_res_dev[link]) 
                        
        #==================================================================================================================/                                
                # ''' STart LOG '''    
                #
                #
                # ''' TILTE '''
                # title_info = ['BOARDID','CHIPID','aggregation_mode','csi_mode','camera_0','camera_1','camera_2','camera_3','camera_0_sn#','camera_1_sn#','camera_2_sn#','camera_3_sn#']
                # title_VI = ['V1(V)','V2(V)','V3(V)','I1(A)','I2(A)','I3(A)']
                #
                # title = title_VI +\
                #         q68.Log_Q68_Status(title = 1) + q68_remote.Log_S68_Status(title = 1) + q68.Log_FaultHandler(title = 1)+\
                #         title_info
                #
                # q68.Logging(filename=data_address, data = title )       # save title
                #
                # test_info = [str(BOARDID),str(CHIPID),aggregation_mode,csi_mode, link_camera[0],link_camera[1],link_camera[2],link_camera[3],camera_id[0],camera_id[1],camera_id[2],camera_id[3]]
                #
                #
                # ''' LOG '''
                # for count_i in range(10000):         
                #
                #     ''' voltage/current measure '''
                #     rslt_VI  = power_q68.Meas_AllChannel()
                #
                #     test_rslt = rslt_VI + \
                #                 q68.Log_Q68_Status() + q68_remote.Log_S68_Status() + q68.Log_FaultHandler() + \
                #                 test_info
                #
                #     q68.Logging(filename=data_address, data = test_rslt)  # save data
                #
                #     time.sleep(3)
#==================================================================================================================/
        
if __name__ == "__main__":

    # #============================= '' TILTE ''' ============================= '
    # title_info = ['BOARDID','CHIPID','aggregation_mode','csi_mode','camera_0','camera_1','camera_2','camera_3','camera_0_sn#','camera_1_sn#','camera_2_sn#','camera_3_sn#']
    # title_VI = ['V1(V)','V2(V)','V3(V)','I1(A)','I2(A)','I3(A)']
    # title_temp = ['Temp(C)']
    #
    # title = title_VI + title_temp +\
    #         q68.Log_Q68_Status(title = 1) + q68_remote.Log_S68_Status(title = 1) + q68.Log_FaultHandler(title = 1)+\
    #         title_info
    #
    # q68.Logging(filename=data_address, data = title )       # save title
    #
    # test_info = [str(BOARDID),str(CHIPID),aggregation_mode,csi_mode, link_camera[0],link_camera[1],link_camera[2],link_camera[3],camera_id[0],camera_id[1],camera_id[2],camera_id[3]]
    #========================================================================== '

    '''start oven '''
    # oven.start()                 # start oven

    for starttemp in STEMP:        
                       
        # oven.settemp(starttemp)  # set temperature and wait for stable
        
        """ power down -> on """    
        power_q68.TurnOutputsOff()
        power_camera.TurnOutputsOff()
    
        time.sleep(2)
        power_q68.TurnOutputsOn()
        power_camera.TurnOutputsOn()
        time.sleep(3)    
        
        """ config Q68 + S68 """
        link0_status = q68.c2m.rd_test_fsm_status1_link0()                        
        link1_status = q68.c2m.rd_test_fsm_status1_link1()                        
        link2_status = q68.c2m.rd_test_fsm_status2_link2()                        
        link3_status = q68.c2m.rd_test_fsm_status2_link3()                        
        print('Link Status:',link0_status,link1_status,link2_status,link3_status) 
        #

        TEST()      # main config


        ''' temperature cycle, log '''
        # TEMP = Drift_type[starttemp]
        # print (TEMP)
        #
        # for tempdrif in TEMP:
        #
        #     # oven.settemp_and_go(tempdrif)   # set temperature without wait 
        #
        #     #============================= '' LOG ''' ============================= '
        #     for count_i in range(400):         
        #
        #         ''' voltage/current measure '''
        #         rslt_VI  = power_q68.Meas_AllChannel()
        #
        #         # temp_read = oven.tempread() 
        #
        #         test_rslt = rslt_VI +\
        #                     q68.Log_Q68_Status() + q68_remote.Log_S68_Status() + q68.Log_FaultHandler() + \
        #                     test_info
        #
        #         q68.Logging(filename=data_address, data = test_rslt)  # save data
        #
        #         print('[Time Stamp]  ', datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))
        #         time.sleep(3)

