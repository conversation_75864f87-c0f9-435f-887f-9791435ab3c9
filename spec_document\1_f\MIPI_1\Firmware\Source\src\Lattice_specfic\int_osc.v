
/*******************************************************************************
    Verilog netlist generated by IPGEN Radiant Software (64-bit) 2.0.1.281.2
    Soft IP Version: 1.0.0
    Sun Jul 10 15:47:31 2022
*******************************************************************************/
/*******************************************************************************
    Wrapper Module generated per user settings.
*******************************************************************************/
module int_osc (hf_out_en_i, 
        hf_clk_out_o, 
        lf_clk_out_o) ;
    input hf_out_en_i ; 
    output hf_clk_out_o ; 
    output lf_clk_out_o ; 
    int_osc_ipgen_lscc_osc #(.HF_OSC_EN("ENABLED"),
            .HF_CLK_DIV_DEC(5),
            .HF_CLK_DIV("4"),
            .HF_CFG_EN("ENABLED"),
            .LF_OUTPUT_EN("ENABLED"),
            .SEDCLK_EN(0),
            .HF_SED_SEC_DIV_DEC(2),
            .HF_SED_SEC_DIV("1")) lscc_osc_inst (.hf_out_en_i(hf_out_en_i), 
                .hf_sed_sec_en_i(1'b0), 
                .hf_clk_out_o(hf_clk_out_o), 
                .lf_clk_out_o(lf_clk_out_o), 
                .hf_sed_sec_out_o(), 
                .hf_clk_config_o()) ; 
endmodule



// =============================================================================
// >>>>>>>>>>>>>>>>>>>>>>>>> COPYRIGHT NOTICE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
// -----------------------------------------------------------------------------
//   Copyright (c) 2018 by Lattice Semiconductor Corporation
//   ALL RIGHTS RESERVED 
// -----------------------------------------------------------------------------
//
//   Permission:
//
//      Lattice SG Pte. Ltd. grants permission to use this code
//      pursuant to the terms of the Lattice Reference Design License Agreement. 
//
//
//   Disclaimer:
//
//      This VHDL or Verilog source code is intended as a design reference
//      which illustrates how these types of functions can be implemented.
//      It is the user's responsibility to verify their design for
//      consistency and functionality through the use of formal
//      verification methods.  Lattice provides no warranty
//      regarding the use or functionality of this code.
//
// -----------------------------------------------------------------------------
//
//                  Lattice SG Pte. Ltd.
//                  101 Thomson Road, United Square #07-02 
//                  Singapore 307591
//
//
//                  TEL: 1-800-Lattice (USA and Canada)
//                       +65-6631-2000 (Singapore)
//                       ******-268-8001 (other locations)
//
//                  web: http://www.latticesemi.com/
//                  email: <EMAIL>
//
// -----------------------------------------------------------------------------
//
// =============================================================================
//                         FILE DETAILS         
// Project               : 
// File                  : lscc_osc.v
// Title                 : 
// Dependencies          : OSC module
// Description           : LIFCL Oscillator.
// =============================================================================
//                        REVISION HISTORY
// Version               : 1.0.0.
// Author(s)             : 
// Mod. Date             : 
// Changes Made          : Initial release.
// =============================================================================
module int_osc_ipgen_lscc_osc #(parameter LF_OUTPUT_EN = "DISABLED", 
        parameter HF_CLK_DIV_DEC = 2, 
        parameter HF_CLK_DIV = "1", 
        parameter HF_OSC_EN = "ENABLED", 
        parameter HF_CFG_EN = "ENABLED", 
        parameter SEDCLK_EN = 0, 
        parameter HF_SED_SEC_DIV_DEC = 2, 
        parameter HF_SED_SEC_DIV = "1", 
        parameter FAMILY = "LIFCL") (
    // -----------------------------------------------------------------------------
    // Module Parameters
    // -----------------------------------------------------------------------------
    // -----------------------------------------------------------------------------
    // Input/Output Ports
    // -----------------------------------------------------------------------------
    input hf_out_en_i, 
    input hf_sed_sec_en_i, 
    output hf_clk_out_o, 
    output lf_clk_out_o, 
    output hf_clk_config_o, 
    output hf_sed_sec_out_o) ;
    // ---------------------------------------
    // OSC Module Instantiation
    // --------------------------------------- 
    OSCA #(.HF_CLK_DIV(HF_CLK_DIV),
            .HF_SED_SEC_DIV(HF_SED_SEC_DIV),
            .HF_OSC_EN(HF_OSC_EN),
            .LF_OUTPUT_EN(LF_OUTPUT_EN)) u_OSC (//Inputs                   
            .HFOUTEN(hf_out_en_i), 
                .HFSDSCEN(hf_sed_sec_en_i), 
                //Outputs                  
            .HFCLKOUT(hf_clk_out_o), 
                .LFCLKOUT(lf_clk_out_o), 
                .HFCLKCFG(hf_clk_config_o), 
                .HFSDCOUT(hf_sed_sec_out_o)) ; 
endmodule


